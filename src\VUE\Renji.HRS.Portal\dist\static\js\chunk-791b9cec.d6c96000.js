(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-791b9cec"],{"06c5":function(e,t,i){"use strict";i.d(t,"a",(function(){return s}));i("a630"),i("fb6a"),i("b0c0"),i("d3b7"),i("25f0"),i("3ca3");var a=i("6b75");function s(e,t){if(e){if("string"===typeof e)return Object(a["a"])(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?Object(a["a"])(e,t):void 0}}},"1e53":function(e,t,i){"use strict";var a=i("eabd"),s=i.n(a);s.a},"2b64":function(e,t,i){"use strict";i.r(t);var a,s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container "},[i("layout1",{scopedSlots:e._u([{key:"header",fn:function(){return[i("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:e.addData}},[e._v("添加")]),i("el-button",{attrs:{type:"primary",icon:"el-icon-delete"},on:{click:e.deleteData}},[e._v("删除")]),i("el-button",{attrs:{type:"primary",icon:"el-icon-edit",disabled:e.modifyBtnDisabled},on:{click:e.modifyData}},[e._v("修改")]),i("el-button",{attrs:{type:"primary",icon:"el-icon-document"},on:{click:e.checkSee}},[e._v("查看")])]},proxy:!0},{key:"aside",fn:function(){return[i("c-tree",{attrs:{options:e.treeData,props:e.treeProps,"expand-all":!0,"expanded-keys":e.treeExpandedKeys},on:{nodeClick:e.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[i("el-card",[i("div",{staticClass:"filter-container"},[i("el-form",{attrs:{inline:!0,model:e.listQuery}},[i("el-form-item",[i("el-select",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{placeholder:"请选择"},on:{change:e.entityColumnChange},model:{value:e.listQuery.entityColumn,callback:function(t){e.$set(e.listQuery,"entityColumn",t)},expression:"listQuery.entityColumn"}},e._l(e.dataColumns,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e}})})),1)],1),i("el-form-item",[i("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{placeholder:"请选择操作符"},model:{value:e.listQuery.queryCondition.EnumOperation,callback:function(t){e.$set(e.listQuery.queryCondition,"EnumOperation",t)},expression:"listQuery.queryCondition.EnumOperation"}},e._l(e.selectConditionOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),i("el-form-item",[e.selectTextBoxVisible?i("el-input",{attrs:{clearable:""},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}}):e._e(),e.selectComboBoxVisible?i("el-select",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{placeholder:"请选择",clearable:""},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}},e._l(e.selectConditionValues,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1):e._e(),e.selectDateBoxVisible?i("el-date-picker",{attrs:{type:"date",placeholder:"请选择日期","value-format":"yyyy-MM-dd"},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}}):e._e(),e.selectDeptTreeVisible?i("c-select-tree",{attrs:{options:e.depTreeData,"tree-props":e.depTreeProps},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}}):e._e()],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.searchQueryList}},[e._v("查询")]),i("el-button",{attrs:{type:"primary",icon:"el-icon-download"},on:{click:e.exportExcel}},[e._v("导出到Excel")])],1)],1)],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}}},e._l(e.tableHead,(function(t,a){return i("el-table-column",{key:a,attrs:{label:t.displayName,prop:t.propName},scopedSlots:e._u([{key:"default",fn:function(a){var s=a.row;return["唯一码"==t.displayName?i("span",{staticClass:"link-type",on:{click:function(t){return e.handleWatchEmpInfo(s)}}},[e._v(e._s(s[t.propName]))]):i("span",[e._v(e._s(s[t.propName]))])]}}],null,!0)})})),1),i("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.queryList}})],1)]},proxy:!0}])}),e.dialogAppInfoVisible?i("el-dialog",{staticClass:"empManager",attrs:{title:e.empDialogTitle,visible:e.dialogAppInfoVisible,width:"90%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogAppInfoVisible=t}}},[i("empInfo",{ref:"empInfo",attrs:{"dept-id":e.deptId,"emp-id":e.empId,"user-permission-prop":e.userPermission},on:{updateEmpId:e.updateEmpId}})],1):e._e(),i("el-dialog",{staticClass:"empManager",attrs:{title:"高级查询设置",visible:e.dialogAdvancedSettingAddVisible,width:"90%","before-close":e.settingAddClose},on:{"update:visible":function(t){e.dialogAdvancedSettingAddVisible=t}}},[i("advancedSetting",{ref:"advancedSettingAdd",on:{CloseDialog:e.settingAddClose}})],1),i("el-dialog",{staticClass:"empManager",attrs:{title:"高级查询设置",visible:e.dialogAdvancedSettingUptVisible,width:"90%","before-close":e.settingUptClose},on:{"update:visible":function(t){e.dialogAdvancedSettingUptVisible=t}}},[i("advancedSetting",{ref:"advancedSettingModify",attrs:{"emp-list-id":e.empListId},on:{CloseDialog:e.settingUptClose}})],1)],1)},n=[],o=(i("d3b7"),i("3ca3"),i("498a"),i("ddb0"),i("2b3d"),function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("layout3",{scopedSlots:e._u([{key:"aside",fn:function(){return[i("c-tree",{attrs:{options:e.treeData,props:e.treeProps,"expanded-keys":e.treeExpandedKeys},on:{nodeClick:e.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[i("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempFormModel,"label-position":"right","label-width":"100px"}},[i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"命名",prop:"listName"}},[i("el-input",{staticStyle:{width:"600px"},attrs:{placeholder:"请输入命名"},model:{value:e.tempFormModel.listName,callback:function(t){e.$set(e.tempFormModel,"listName",t)},expression:"tempFormModel.listName"}})],1)],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{staticStyle:{width:"600px"},attrs:{label:"选中字段",prop:"FieldsName"}},[e._v(" "+e._s(e.tempFormModel.fieldsName)+" ")])],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"所属查询分类",prop:"QueryType"}},[i("el-select",{staticStyle:{width:"600px"},attrs:{placeholder:"请选择所属查询分类"},model:{value:e.tempFormModel.queryTypeId,callback:function(t){e.$set(e.tempFormModel,"queryTypeId",t)},expression:"tempFormModel.queryTypeId"}},e._l(e.queryTypeOptions,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:2}},[i("div",{staticStyle:{height:"300px","vertical-align":"middle","line-height":"300px"}},[i("el-button",{staticClass:"el-icon-right",attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.addToTable()}}})],1)]),i("el-col",{attrs:{span:22}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.columnList,border:"",stripe:"",fit:"","highlight-current-row":"",height:"300","header-cell-style":{background:"#F5F7FA",color:"#606266"}}},[i("el-table-column",{attrs:{label:"操作",width:"230",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{staticClass:"el-icon-back",attrs:{size:"mini",type:"primary"},on:{click:function(i){return e.deleteFromTable(t)}}}),i("el-button",{staticClass:"el-icon-top",attrs:{size:"mini",type:"primary"},on:{click:function(i){return e.moveUpInTable(t)}}}),i("el-button",{staticClass:"el-icon-bottom",attrs:{size:"mini",type:"primary"},on:{click:function(i){return e.moveDownInTable(t)}}})]}}])}),i("el-table-column",{attrs:{label:"字段名","min-width":"80px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.columnDesc))])]}}])})],1)],1)],1),i("el-row",{staticStyle:{"padding-top":"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"逻辑关系",prop:"LogicRelationship"}},[i("el-select",{staticStyle:{width:"600px"},attrs:{placeholder:"请选择逻辑关系"},model:{value:e.listQuery.queryCondition.LogicRelationship,callback:function(t){e.$set(e.listQuery.queryCondition,"LogicRelationship",t)},expression:"listQuery.queryCondition.LogicRelationship"}},e._l(e.logicRelationshipOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1)],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"字段"}},[i("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择字段",readonly:""},model:{value:e.listQuery.queryCondition.FieldDesc,callback:function(t){e.$set(e.listQuery.queryCondition,"FieldDesc",t)},expression:"listQuery.queryCondition.FieldDesc"}}),i("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请选择"},model:{value:e.listQuery.queryCondition.EnumOperation,callback:function(t){e.$set(e.listQuery.queryCondition,"EnumOperation",t)},expression:"listQuery.queryCondition.EnumOperation"}},e._l(e.selectConditionOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1),e.selectTextBoxVisible?i("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入条件值"},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}}):e._e(),e.selectComboBoxVisible?i("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请选择"},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}},e._l(e.selectConditionValues,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1):e._e(),e.selectDateBoxVisible?i("el-date-picker",{staticStyle:{width:"200px"},attrs:{type:"date",placeholder:"请选择日期","value-format":"yyyy-MM-dd"},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}}):e._e(),e.selectDeptTreeVisible?i("c-select-tree",{staticStyle:{width:"200px"},attrs:{options:e.depTreeData,"tree-props":e.depTreeProps},on:{change2:e.selectDeptTreeChange},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}}):e._e(),e.selectComboBoxBenefitVisible?i("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请选择",clearable:""},on:{change:e.BenefitChange},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}},e._l(e.selectBenefitConditionValues,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1):e._e(),e.selectPartyPositionVisible?i("el-select",{attrs:{multiple:"","collapse-tags":"",clearable:"",placeholder:"请选择党内职务"},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}},e._l(e.partyPositionsList,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1):e._e(),e.selectPartyPositionVisible?i("el-select",{attrs:{multiple:"","collapse-tags":"",clearable:"",placeholder:"请选择党内职务"},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}},e._l(e.partyPositionsList,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1):e._e()],1)],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"条件"}},[i("el-input",{attrs:{type:"textarea",rows:4},model:{value:e.tempFormModel.condition,callback:function(t){e.$set(e.tempFormModel,"condition",t)},expression:"tempFormModel.condition"}})],1)],1)],1),i("el-row",{staticStyle:{padding:"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:24,align:"center"}},[i("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.addCondition()}}},[e._v("添加")]),i("el-button",{attrs:{type:"primary",icon:"el-icon-document"},on:{click:function(t){return e.save()}}},[e._v("保存")]),i("el-button",{attrs:{type:"warning",icon:"el-icon-refresh-left"},on:{click:function(t){return e.cancle()}}},[e._v("取消")])],1)],1)],1)]},proxy:!0}])})],1)}),l=[],r=(i("c975"),i("45fc"),i("a434"),i("b0c0"),i("ade3")),c=i("b85c"),u=i("e44c"),d=i("f9ac"),m=i("d368"),h={name:"",components:{},props:{empListId:{type:String,default:""}},data:function(){return{selectDeptTreeData:{label:"",value:null,uid:0},depTreeData:[],depTreeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeData:[],treeExpandedKeys:[],treeProps:{parent:"parentId",value:"id",label:"columnDesc",children:"children"},listQuery:{queryCondition:{Keywords:"",FieldDesc:""},pageIndex:1,pageSize:5},listLoading:!1,rules:{listName:[{required:!0,type:"string",message:"命名必须输入",trigger:"blur"},{required:!0,type:"string",max:100,message:"超长",trigger:"blur"}]},tempFormModel:{listName:"",fieldsName:"",queryTypeId:"",condition:""},columnList:[],tempColumn:{},queryTypeOptions:[],logicRelationshipOptions:[],selected:"",currentData:{},currentNode:{},includeDownDept:!1,dataColumns:[],gender:[],nationality:[],marry:[],registerType:[],degree:[],education:[],party:[],employeeStatus:[],hireStyle:[],leaveStyle:[],recruitmentCategory:[],recruitmentCompany:[],officialRank:[],rank:[],level:[],graduation:[],learnWay:[],abroadType:[],contractType:[],trainLevel:[],evaluateResult:[],incentType:[],incentLevel:[],accidentType:[],incomeType:[],classLevel:[],teacherType:[],awardLevel:[],socialSecurityType:[],payRollOrgClass:[],payRollCompGroup:[],payRollOrgSalary:[],payRollOrgSalaryLevel:[],payRollOrgPositionSalarys:[],workState:[],selectConditionOptions:[],selectPartyOperationOptions:[],selectConditionOptionsBak:[],selectConditionValues:[],selectBenefitConditionValues:[],selectTextBoxVisible:!0,selectComboBoxVisible:!1,selectDateBoxVisible:!1,selectDeptTreeVisible:!1,selectComboBoxBenefitVisible:!1,partyPositionsList:[],selectedPartyPositions:[],selectPartyPositionVisible:!1,yesNo:[{id:"1",name:"是"},{id:"0",name:"否"}]}},watch:{},created:function(){this.loadTree(),this.loadAdvancedQueryType(),this.loadConditions(),this.LoadPayRollOrgClass(),this.LoadPayRollCompGroup(),this.LoadPayRollOrgSalary(),this.LoadPayRollOrgSalaryLevel(),this.LoadPayRollOrgPositionSalarys(),this.loadDictSetting(),this.loadData(),this.loadDeptTree(),this.loadPartyPositionsList()},mounted:function(){},methods:(a={loadDeptTree:function(){var e=this;m["a"].queryDeptByUser({}).then((function(t){e.depTreeData=t.data})).catch((function(e){console.log(e)}))},loadData:function(){this.empListId&&this.getEmployeeList(this.empListId)},getEmployeeList:function(e){var t=this;u["a"].getEmployeeList({id:e}).then((function(e){e.data&&(t.tempFormModel=e.data,t.columnList=e.data.columnInfos)})).catch((function(e){console.log(e)}))},loadTree:function(){var e=this;u["a"].queryEmployeeListSettingTree({}).then((function(t){e.treeData=t.data,e.treeData&&e.treeData.length>0&&e.treeExpandedKeys.push(e.treeData[0].id)})).catch((function(e){console.log(e)})),this.resetCurrentNode()},loadConditions:function(){var e=this;d["a"].getEnumInfos({enumType:"Operations"}).then((function(t){e.selectConditionOptions=t.data.datas,e.selectConditionOptionsBak=t.data.datas})).catch((function(e){console.log(e)})),d["a"].getEnumInfos({enumType:"PartyOperations"}).then((function(t){console.log(t.data.datas),e.selectPartyOperationOptions=t.data.datas})).catch((function(e){console.log(e)})).catch((function(e){console.log(e)})),d["a"].getEnumInfos({enumType:"LogicRelationships"}).then((function(t){e.logicRelationshipOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadDictSetting:function(){var e=this;u["a"].queryDictsSetting({}).then((function(t){e.gender=t.gender,e.nationality=t.nationality,e.marry=t.marry,e.registerType=t.registerType,e.degree=t.degree,e.education=t.education,e.party=t.party,e.employeeStatus=t.employeeStatus,e.hireStyle=t.hireStyle,e.leaveStyle=t.leaveStyle,e.recruitmentCategory=t.recruitmentCategory,e.recruitmentCompany=t.recruitmentCompany,e.officialRank=t.officialRank,e.rank=t.rank,e.level=t.level,e.graduation=t.graduation,e.learnWay=t.learnWay,e.abroadType=t.abroadType,e.contractType=t.contractType,e.trainLevel=t.trainLevel,e.evaluateResult=t.evaluateResult,e.incentType=t.incentType,e.incentLevel=t.incentLevel,e.accidentType=t.accidentType,e.incomeType=t.incomeType,e.classLevel=t.classLevel,e.teacherType=t.teacherType,e.awardLevel=t.awardLevel,e.socialSecurityType=t.socialSecurityType,e.workState=t.workState})).catch((function(e){console.log(e)}))},LoadPayRollOrgClass:function(){var e=this;u["a"].queryPayRollOrgClass().then((function(t){e.payRollOrgClass=t.data})).catch((function(e){console.log(e)}))},LoadPayRollCompGroup:function(){var e=this;u["a"].queryPayRollCompGroup().then((function(t){e.payRollCompGroup=t.data})).catch((function(e){console.log(e)}))},LoadPayRollOrgSalary:function(e){var t=this;u["a"].queryPayRollOrgSalary({condition:e}).then((function(e){t.payRollOrgSalary=e.data})).catch((function(e){console.log(e)}))},LoadPayRollOrgSalaryLevel:function(e){var t=this;u["a"].queryPayRollOrgSalaryLevel({condition:e}).then((function(e){t.payRollOrgSalaryLevel=e.data})).catch((function(e){console.log(e)}))},LoadPayRollOrgPositionSalarys:function(e){var t=this;u["a"].queryPayRollOrgPositionSalarys({condition:e}).then((function(e){t.payRollOrgPositionSalarys=e.data})).catch((function(e){console.log(e)}))},BenefitChange:function(e){""!==this.currentData.dataType&&this.currentData.tableName+"."+this.currentData.columnName==="EmployeeBenefit.OrgClassId"&&(this.LoadPayRollOrgSalary(e),this.LoadPayRollOrgSalaryLevel(e),this.LoadPayRollOrgPositionSalarys(e))},resetCurrentNode:function(){this.currentNode=null},resetData:function(){this.tempFormModel.id="00000000-0000-0000-0000-000000000000",this.tempFormModel.listName="",this.tempFormModel.fieldsName="",this.tempFormModel.queryTypeId="",this.tempFormModel.condition="",this.listQuery.queryCondition.FieldDesc="",this.listQuery.queryCondition.Keywords="",this.columnList.length=0},treeNodeClick:function(e,t){this.currentNode=t,this.currentData=e,2===this.currentNode.level&&(this.listQuery.queryCondition.FieldDesc=this.currentData.columnDesc),"PartyPositionName"===this.currentData.columnName?this.selectConditionOptions=JSON.parse(JSON.stringify(this.selectPartyOperationOptions)):this.selectConditionOptions=JSON.parse(JSON.stringify(this.selectConditionOptionsBak)),this.entityColumnChange()},entityColumnChange:function(){""!==this.currentData.dataType&&(this.listQuery.queryCondition.Keywords="",this.selectConditionValues=[],this.selectBenefitConditionValues=[],"3"===this.currentData.dataType||"4"===this.currentData.dataType?(this.selectTextBoxVisible=!1,this.selectComboBoxVisible=!0,this.selectDateBoxVisible=!1,this.selectDeptTreeVisible=!1,this.selectComboBoxBenefitVisible=!1,this.selectPartyPositionVisible=!1,"3"===this.currentData.dataType?this.selectConditionValues=this.yesNo:this.currentData.tableName+"."+this.currentData.columnName==="Employee.EnumGender"?this.selectConditionValues=this.gender:this.currentData.tableName+"."+this.currentData.columnName==="Employee.NationalityId"?this.selectConditionValues=this.nationality:this.currentData.tableName+"."+this.currentData.columnName==="Employee.MarryId"?this.selectConditionValues=this.marry:this.currentData.tableName+"."+this.currentData.columnName==="Employee.RegisterTypeId"?this.selectConditionValues=this.registerType:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeHR.DegreeId"||this.currentData.tableName+"."+this.currentData.columnName==="EmployeeEducation.DegreeId"?this.selectConditionValues=this.degree:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeHR.EducationId"||this.currentData.tableName+"."+this.currentData.columnName==="EmployeeEducation.EduLevelId"?this.selectConditionValues=this.education:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeHR.PartyId"?this.selectConditionValues=this.party:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeHR.EmpStatusId"?this.selectConditionValues=this.employeeStatus:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeHR.HireStyleId"?this.selectConditionValues=this.hireStyle:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeHR.LeaveStyleId"?this.selectConditionValues=this.leaveStyle:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeHR.RecruitmentCategoryId"?this.selectConditionValues=this.recruitmentCategory:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeHR.RecruitmentCompanyId"?this.selectConditionValues=this.recruitmentCompany:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeHR.OfficialRankId"?this.selectConditionValues=this.officialRank:this.currentData.tableName+"."+this.currentData.columnName==="ViewEmployeeStation.RankId"?this.selectConditionValues=this.rank:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeCertify.LevelId"?this.selectConditionValues=this.level:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeEducation.GraduationId"?this.selectConditionValues=this.graduation:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeEducation.LearnWayId"?this.selectConditionValues=this.learnWay:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeAbroadInfo.TypeId"?this.selectConditionValues=this.abroadType:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeContract.TypeId"?this.selectConditionValues=this.contractType:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeTrain.LevelId"?this.selectConditionValues=this.trainLevel:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeAssessment.ResultId"?this.selectConditionValues=this.evaluateResult:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeIncentive.TypeId"?this.selectConditionValues=this.incentType:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeIncentive.LevelId"?this.selectConditionValues=this.incentLevel:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeAccident.TypeId"?this.selectConditionValues=this.accidentType:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeArticle.IncomeId"?this.selectConditionValues=this.incomeType:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeClass.LevelId"?this.selectConditionValues=this.classLevel:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeTeacher.TypeId"?this.selectConditionValues=this.teacherType:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeAward.LevelId"?this.selectConditionValues=this.awardLevel:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeSocialInsurance.SIType"?this.selectConditionValues=this.socialSecurityType:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeBenefit.WorkStateId"&&(this.selectConditionValues=this.workState)):"0"===this.currentData.dataType||"1"===this.currentData.dataType?(this.selectTextBoxVisible=!0,this.selectComboBoxVisible=!1,this.selectDateBoxVisible=!1,this.selectDeptTreeVisible=!1,this.selectComboBoxBenefitVisible=!1,this.selectPartyPositionVisible=!1,this.currentData.tableName+"."+this.currentData.columnName==="ViewEmployeeHRDict.PartyPositionName"&&(this.selectPartyPositionVisible=!0,this.selectTextBoxVisible=!1,this.selectComboBoxVisible=!1,this.selectDateBoxVisible=!1,this.selectDeptTreeVisible=!1,this.selectComboBoxBenefitVisible=!1)):"5"===this.currentData.dataType?(this.selectTextBoxVisible=!1,this.selectComboBoxVisible=!1,this.selectDateBoxVisible=!1,this.selectDeptTreeVisible=!0,this.selectComboBoxBenefitVisible=!1,this.selectPartyPositionVisible=!1):"2"===this.currentData.dataType?(this.selectTextBoxVisible=!1,this.selectComboBoxVisible=!1,this.selectDateBoxVisible=!0,this.selectDeptTreeVisible=!1,this.selectComboBoxBenefitVisible=!1,this.selectPartyPositionVisible=!1):"6"!==this.currentData.dataType&&"7"!==this.currentData.dataType&&"8"!==this.currentData.dataType&&"9"!==this.currentData.dataType&&"10"!==this.currentData.dataType||(this.selectTextBoxVisible=!1,this.selectComboBoxVisible=!1,this.selectDateBoxVisible=!1,this.selectDeptTreeVisible=!1,this.selectComboBoxBenefitVisible=!0,this.selectPartyPositionVisible=!1,this.currentData.tableName+"."+this.currentData.columnName==="EmployeeBenefit.OrgClassId"?this.selectBenefitConditionValues=this.payRollOrgClass:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeBenefit.CompGroupId"?this.selectBenefitConditionValues=this.payRollCompGroup:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeBenefit.OrgSalaryId"?this.selectBenefitConditionValues=this.payRollOrgSalary:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeBenefit.OrgSalaryLevelId"?this.selectBenefitConditionValues=this.payRollOrgSalaryLevel:this.currentData.tableName+"."+this.currentData.columnName==="EmployeeBenefit.OrgPositionSalaryId"&&(this.selectBenefitConditionValues=this.payRollOrgPositionSalarys)))},loadAdvancedQueryType:function(){var e=this;u["a"].queryAdvancedQueryType().then((function(t){e.queryTypeOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadPartyPositionsList:function(){var e=this;u["a"].queryDictByParentCode({code:"00081"}).then((function(t){e.partyPositionsList=t.data.datas})).catch((function(e){console.log(e)}))}},Object(r["a"])(a,"loadPartyPositionsList",(function(){var e=this;u["a"].queryDictByParentCode({code:"00081"}).then((function(t){e.partyPositionsList=t.data.datas})).catch((function(e){console.log(e)}))})),Object(r["a"])(a,"addCondition",(function(){if(this.listQuery.queryCondition.LogicRelationship)if(this.currentData.columnDesc)if(this.listQuery.queryCondition.EnumOperation)if(this.listQuery.queryCondition.Keywords){var e="";switch(e=10===this.listQuery.queryCondition.LogicRelationship?" And ":" Or ",e+=this.currentData.tableDesc+"."+this.currentData.columnDesc,this.listQuery.queryCondition.EnumOperation){case 10:e+=" = ";break;case 15:e+=" != ";break;case 20:e+=" > ";break;case 25:e+=" >= ";break;case 30:e+=" < ";break;case 35:e+=" <= ";break;case 80:e+=" in ( ";break;case 90:e+=" any ( ";break;case 100:e+=" all ( ";break}if("3"===this.currentData.dataType||"4"===this.currentData.dataType){var t,i=Object(c["a"])(this.selectConditionValues);try{for(i.s();!(t=i.n()).done;){var a=t.value;if(a.id===this.listQuery.queryCondition.Keywords){e+=" '["+a.name+"]' ";break}}}catch(r){i.e(r)}finally{i.f()}}else if("5"===this.currentData.dataType)e+=" '["+this.selectDeptTreeData.label+"]("+this.selectDeptTreeData.uid+")' ";else if("0"===this.currentData.dataType&&this.currentData.tableName+"."+this.currentData.columnName==="ViewEmployeeHRDict.PartyPositionName"){var s,n="",o=Object(c["a"])(this.partyPositionsList);try{for(o.s();!(s=o.n()).done;){var l=s.value;this.listQuery.queryCondition.Keywords.indexOf(l.id)>-1&&(n&&n.length>0&&(n+=" , "),n+=" '["+l.name+"]' ")}}catch(r){o.e(r)}finally{o.f()}e+=n}else e+=" '"+this.listQuery.queryCondition.Keywords+"' ";80!==this.listQuery.queryCondition.EnumOperation&&90!==this.listQuery.queryCondition.EnumOperation&&100!==this.listQuery.queryCondition.EnumOperation||(e+=" ) "),this.tempFormModel.condition+=e}else this.$notice.message("条件值必须输入或选择","info");else this.$notice.message("操作符必须选择","info");else this.$notice.message("字段必须选择","info");else this.$notice.message("逻辑关系必须选择","info")})),Object(r["a"])(a,"cancle",(function(){this.$emit("CloseDialog")})),Object(r["a"])(a,"save",(function(){var e=this;this.tempFormModel.listName?this.tempFormModel.listName.length>=100||(this.tempFormModel.queryTypeId?this.columnList&&0!==this.columnList.length?(this.tempFormModel.ColumnInfos=this.columnList,console.log(this.tempFormModel.ColumnInfos),u["a"].saveEmployeeList(this.tempFormModel).then((function(t){t.succeed?e.$notice.message("高级查询设置更新成功","success"):t.succeed||e.$notice.message("高级查询设置更新失败，请联系管理员","info")})).catch((function(e){console.log(e)}))):this.$notice.message("请选择要显示的字段","info"):this.$notice.message("所属查询分类必须选择","info")):this.$notice.message("命名必须输入","info")})),Object(r["a"])(a,"clear",(function(){this.$refs["dataForm"].resetFields()})),Object(r["a"])(a,"addToTable",(function(){var e=this;null!==this.currentNode&&2===this.currentNode.level&&(this.columnList&&this.columnList.some((function(t){return t.id===e.currentData.id}))||this.columnList.push(this.currentData))})),Object(r["a"])(a,"deleteFromTable",(function(e){if(this.columnList){var t=this.columnList.indexOf(e.row);this.columnList.splice(t,1)}})),Object(r["a"])(a,"moveUpInTable",(function(e){if(this.columnList){var t=this.columnList.indexOf(e.row);0!==t&&(this.columnList.splice(t,1),this.columnList.splice(t-1,0,e.row))}})),Object(r["a"])(a,"moveDownInTable",(function(e){if(this.columnList){var t=this.columnList.indexOf(e.row);t!==this.columnList.length-1&&(this.columnList.splice(t,1),this.columnList.splice(t+1,0,e.row))}})),Object(r["a"])(a,"selectDeptTreeChange",(function(e){this.selectDeptTreeData.label=e.name,this.selectDeptTreeData.value=e.id,this.selectDeptTreeData.uid=e.uid})),a)},p=h,y=(i("93fe"),i("2877")),b=Object(y["a"])(p,o,l,!1,null,"14b0c273",null),f=b.exports,g=i("91a7"),C={components:{empInfo:g["a"],advancedSetting:f},data:function(){return{depTreeData:[],depTreeProps:{parent:"parentId",value:"id",label:"name",children:"children"},modifyBtnDisabled:!0,tableHead:[],tableData:[],dialogAdvancedSettingAddVisible:!1,dialogAdvancedSettingUptVisible:!1,treeData:[],treeExpandedKeys:[],treeProps:{parent:"parentId",value:"id",label:"listName",children:"children"},selected:"",currentData:{},currentNode:{},includeDownDept:!1,dataColumns:[],gender:[],nationality:[],marry:[],registerType:[],degree:[],education:[],party:[],employeeStatus:[],hireStyle:[],leaveStyle:[],recruitmentCategory:[],recruitmentCompany:[],officialRank:[],rank:[],level:[],graduation:[],learnWay:[],abroadType:[],contractType:[],trainLevel:[],evaluateResult:[],incentType:[],incentLevel:[],accidentType:[],incomeType:[],classLevel:[],teacherType:[],awardLevel:[],selectConditionOptions:[],selectConditionValues:[],selectTextBoxVisible:!0,selectComboBoxVisible:!1,selectDateBoxVisible:!1,selectDeptTreeVisible:!1,yesNo:[{id:"1",name:"是"},{id:"0",name:"否"}],empList:[],pageEmpList:[],total:1,listQuery:{entityColumn:null,queryCondition:{EnumOperation:null,Keywords:""},pageIndex:1,pageSize:10},listLoading:!1,treeLoading:!1,empId:"",deptId:"",empListId:"",dialogAppInfoVisible:!1,userPermission:{}}},watch:{empListId:function(e,t){e&&""!==e.trim()?this.modifyBtnDisabled=!1:this.modifyBtnDisabled=!0}},created:function(){this.loadTree(),this.loadConditions(),this.loadDictSetting(),this.loadButtonPermission(),this.loadDeptTree()},mounted:function(){},methods:{loadDeptTree:function(){var e=this;m["a"].queryDeptByUser({}).then((function(t){e.depTreeData=t.data})).catch((function(e){console.log(e)}))},loadTree:function(){var e=this;u["a"].queryEmployeeListTree({}).then((function(t){e.treeData=t.data,e.treeData&&e.treeData.length>0&&e.treeExpandedKeys.push(e.treeData[0].id)})).catch((function(e){console.log(e)})),this.resetCurrentNode(),this.clear()},loadConditions:function(){var e=this;d["a"].getEnumInfos({enumType:"Operations"}).then((function(t){e.selectConditionOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadDictSetting:function(){var e=this;u["a"].queryDictsSetting({}).then((function(t){e.gender=t.gender,e.nationality=t.nationality,e.marry=t.marry,e.registerType=t.registerType,e.degree=t.degree,e.education=t.education,e.party=t.party,e.employeeStatus=t.employeeStatus,e.hireStyle=t.hireStyle,e.leaveStyle=t.leaveStyle,e.recruitmentCategory=t.recruitmentCategory,e.recruitmentCompany=t.recruitmentCompany,e.officialRank=t.officialRank,e.rank=t.rank,e.level=t.level,e.graduation=t.graduation,e.learnWay=t.learnWay,e.abroadType=t.abroadType,e.contractType=t.contractType,e.trainLevel=t.trainLevel,e.evaluateResult=t.evaluateResult,e.incentType=t.incentType,e.incentLevel=t.incentLevel,e.accidentType=t.accidentType,e.incomeType=t.incomeType,e.classLevel=t.classLevel,e.teacherType=t.teacherType,e.awardLevel=t.awardLevel})).catch((function(e){console.log(e)}))},entityColumnChange:function(e){this.listQuery.queryCondition.Keywords="",this.selectConditionValues=[],console.log(e),"3"===e.type||"4"===e.type?(this.selectTextBoxVisible=!1,this.selectComboBoxVisible=!0,this.selectDateBoxVisible=!1,this.selectDeptTreeVisible=!1,"3"===e.type?this.selectConditionValues=this.yesNo:(console.log(e.value),"Employee.EnumGender"===e.value?this.selectConditionValues=this.gender:"Employee.NationalityId"===e.value?this.selectConditionValues=this.nationality:"Employee.MarryId"===e.value?this.selectConditionValues=this.marry:"Employee.RegisterTypeId"===e.value?this.selectConditionValues=this.registerType:"EmployeeHR.DegreeId"===e.value||"EmployeeEducation.DegreeId"===e.value?this.selectConditionValues=this.degree:"EmployeeHR.EducationId"===e.value||"EmployeeEducation.EduLevelId"===e.value?this.selectConditionValues=this.education:"EmployeeHR.PartyId"===e.value?this.selectConditionValues=this.party:"EmployeeHR.EmpStatusId"===e.value?this.selectConditionValues=this.employeeStatus:"EmployeeHR.HireStyleId"===e.value?this.selectConditionValues=this.hireStyle:"EmployeeHR.LeaveStyleId"===e.value?this.selectConditionValues=this.leaveStyle:"EmployeeHR.RecruitmentCategoryId"===e.value?this.selectConditionValues=this.recruitmentCategory:"EmployeeHR.RecruitmentCompanyId"===e.value?this.selectConditionValues=this.recruitmentCompany:"EmployeeHR.OfficialRankId"===e.value?this.selectConditionValues=this.officialRank:"EmployeeStation.RankId"===e.value?this.selectConditionValues=this.rank:"EmployeeCertify.LevelId"===e.value?this.selectConditionValues=this.level:"EmployeeEducation.GraduationId"===e.value?this.selectConditionValues=this.graduation:"EmployeeEducation.LearnWayId"===e.value?this.selectConditionValues=this.learnWay:"EmployeeAbroadInfo.TypeId"===e.value?this.selectConditionValues=this.abroadType:"EmployeeContract.TypeId"===e.value?this.selectConditionValues=this.contractType:"EmployeeTrain.LevelId"===e.value?this.selectConditionValues=this.trainLevel:"EmployeeAssessment.ResultId"===e.value?this.selectConditionValues=this.evaluateResult:"EmployeeIncentive.TypeId"===e.value?this.selectConditionValues=this.incentType:"EmployeeIncentive.LevelId"===e.value?this.selectConditionValues=this.incentLevel:"EmployeeAccident.TypeId"===e.value?this.selectConditionValues=this.accidentType:"EmployeeArticle.IncomeId"===e.value?this.selectConditionValues=this.incomeType:"EmployeeClass.LevelId"===e.value?this.selectConditionValues=this.classLevel:"EmployeeTeacher.TypeId"===e.value?this.selectConditionValues=this.teacherType:"EmployeeAward.LevelId"===e.value&&(this.selectConditionValues=this.awardLevel))):"5"===e.type?(this.selectTextBoxVisible=!1,this.selectComboBoxVisible=!1,this.selectDateBoxVisible=!1,this.selectDeptTreeVisible=!0):"0"===e.type||"1"===e.type?(this.selectTextBoxVisible=!0,this.selectComboBoxVisible=!1,this.selectDateBoxVisible=!1,this.selectDeptTreeVisible=!1):(this.selectTextBoxVisible=!1,this.selectComboBoxVisible=!1,this.selectDateBoxVisible=!0,this.selectDeptTreeVisible=!1)},resetCurrentNode:function(){this.currentNode=null,this.currentData=null,this.empListId="",this.total=1},treeNodeClick:function(e,t){this.currentNode=t,this.currentData=e,3===t.level?this.empListId=e.id:this.empListId=""},addData:function(){this.dialogAdvancedSettingAddVisible=!0},updateEmpId:function(e){this.empId=e},handleWatchEmpInfo:function(e){this.empDialogTitle="人事信息——"+(e.DisplayName?e.DisplayName:""),this.empId=e.Id,this.dialogAppInfoVisible=!0},handleClose:function(){this.dialogAppInfoVisible=!1,this.clearEmp()},clearEmp:function(){this.$refs["empInfo"].clear()},modifyData:function(){this.dialogAdvancedSettingUptVisible=!0,this.$refs["advancedSettingModify"]&&this.$refs["advancedSettingModify"].loadData()},settingAddClose:function(){this.dialogAdvancedSettingAddVisible=!1,this.clearAddSetting(),this.loadTree()},settingUptClose:function(){this.dialogAdvancedSettingUptVisible=!1,this.clearModifySetting(),this.loadTree()},clearAddSetting:function(){this.$refs["advancedSettingAdd"].resetData(),this.$refs["advancedSettingAdd"].clear()},clearModifySetting:function(){this.$refs["advancedSettingModify"].clear()},sortChange:function(){},glUpdate:function(){},clear:function(){this.tableHead=null,this.tableData=null},deleteData:function(){var e=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){u["a"].deleteEmployeeList(e.currentData).then((function(t){t.succeed?(e.loadTree(),e.$notice.message("删除成功","success")):t.succeed||e.$notice.message("删除失败，请联系管理员","info")})).catch((function(t){e.listLoading=!1,console.log(t)}))})).catch((function(t){e.listLoading=!1,t.succeed||e.$notice.message("取消删除","info")}))},queryList:function(){var e=this;if(null!==this.currentNode&&3===this.currentNode.level){if(this.listQuery.ID=this.currentData.id,this.listQuery.entityColumn){if(!this.listQuery.queryCondition.EnumOperation)return void this.$notice.message("操作符必须选择","info");this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.value,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.ConditionList=[this.listQuery.queryCondition]}else this.listQuery.ConditionList=[];u["a"].queryEmployeeList(this.listQuery).then((function(t){t.success?(e.tableHead=t.tableHead,e.tableData=t.tableData,e.dataColumns=t.dataColumns,console.log(t.dataColumns),e.total=t.recordCount,e.listQuery.pageIndex=t.pageIndex):e.$notice.message(t.message,"info")})).catch((function(e){console.log(e)}))}},exportExcel:function(){if(null!==this.currentNode&&3===this.currentNode.level){if(this.listQuery.ID=this.currentData.id,this.listQuery.entityColumn){if(!this.listQuery.queryCondition.EnumOperation)return void this.$notice.message("操作符必须选择","info");this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.value,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.ConditionList=[this.listQuery.queryCondition]}else this.listQuery.ConditionList=[];u["a"].getEmployeeListExcel(this.listQuery).then((function(e){var t=new Blob([e],{type:e.type}),i="员工高级查询.xlsx";if(window.navigator&&window.navigator.msSaveOrOpenBlob)window.navigator.msSaveOrOpenBlob(t,i);else{var a=document.createElement("a"),s=window.URL.createObjectURL(t);a.href=s,a.download=i,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(s)}}))}},loadButtonPermission:function(){var e=this;d["a"].getControlRightByCurrentUser().then((function(t){e.userPermission=t.data})).catch((function(e){console.log(e)}))},searchQueryList:function(){this.listQuery.pageIndex=1,this.queryList()},checkSee:function(){this.listQuery.entityColumn=null,this.listQuery.queryCondition.EnumOperation=null,this.listQuery.queryCondition.Keywords="",this.searchQueryList()}}},v=C,I=(i("1e53"),i("fb56"),Object(y["a"])(v,s,n,!1,null,"2c14d1c5",null));t["default"]=I.exports},"6b75":function(e,t,i){"use strict";function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,a=new Array(t);i<t;i++)a[i]=e[i];return a}i.d(t,"a",(function(){return a}))},"91a7":function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container "},[i("el-tabs",{attrs:{type:"card","before-leave":e.beforeLeaveTab},on:{"tab-click":e.tabClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[e.userPermission.hrBaseInfo&&e.userPermission.hrBaseInfo.isShow?i("el-tab-pane",{attrs:{label:"基本信息",name:"baseInfoTab"}},[e.baseInfoVisiable?i("baseInfo",{ref:"baseInfo",attrs:{"user-permission":e.userPermission.hrBaseInfo,"dept-id":e.deptId,"emp-id":e.empId},on:{updateEmpId:e.updateEmpId}}):e._e()],1):e._e(),e.userPermission.hrInfo&&e.userPermission.hrInfo.isShow?i("el-tab-pane",{attrs:{label:"人事信息",name:"hRInfoTab"}},[e.hRInfoVisiable?i("hRInfo",{ref:"hRInfo",attrs:{"user-permission":e.userPermission.hrInfo,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.party&&e.userPermission.party.isShow?i("el-tab-pane",{attrs:{label:"政治面貌",name:"partyTab"}},[e.partyVisiable?i("party",{ref:"party",attrs:{"user-permission":e.userPermission.party,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.station&&e.userPermission.station.isShow?i("el-tab-pane",{attrs:{label:"聘任职务",name:"stationTab"}},[e.stationVisiable?i("station",{ref:"station",attrs:{"user-permission":e.userPermission.station,"emp-id":e.empId,"contain-rank":e.containRank,estype:1}}):e._e()],1):e._e(),e.userPermission.qualification&&e.userPermission.qualification.isShow?i("el-tab-pane",{attrs:{label:"聘任职称",name:"qualificationTab"}},[e.qualificationVisiable?i("station",{ref:"qualification",attrs:{"user-permission":e.userPermission.qualification,"emp-id":e.empId,"not-contain-rank":e.notContainRank,estype:2}}):e._e()],1):e._e(),e.userPermission.certify&&e.userPermission.certify.isShow?i("el-tab-pane",{attrs:{label:"职称资格",name:"certifyTab"}},[e.certifyVisiable?i("certify",{ref:"certify",attrs:{"user-permission":e.userPermission.certify,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.wages&&e.userPermission.wages.isShow?i("el-tab-pane",{attrs:{label:"工资",name:"wagesTab"}},[e.wagesVisiable?i("wages",{ref:"wages",attrs:{"user-permission":e.userPermission.wages,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.socialsecurity&&e.userPermission.socialsecurity.isShow?i("el-tab-pane",{attrs:{label:"社保",name:"socialsecurityTab"}},[e.socialsecurityVisiable?i("socialsecurity",{ref:"socialsecurity",attrs:{"user-permission":e.userPermission.socialsecurity,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.certieducation&&e.userPermission.certieducation.isShow?i("el-tab-pane",{attrs:{label:"学习经历",name:"educationTab"}},[e.educationVisiable?i("education",{ref:"education",attrs:{"user-permission":e.userPermission.certieducation,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.work&&e.userPermission.work.isShow?i("el-tab-pane",{attrs:{label:"工作经历",name:"workTab"}},[e.workVisiable?i("work",{ref:"work",attrs:{"user-permission":e.userPermission.work,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.abroadInfo&&e.userPermission.abroadInfo.isShow?i("el-tab-pane",{attrs:{label:"出国",name:"abroadInfoTab"}},[e.abroadInfoVisiable?i("abroadInfo",{ref:"abroadInfo",attrs:{"user-permission":e.userPermission.abroadInfo,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.contract&&e.userPermission.contract.isShow?i("el-tab-pane",{attrs:{label:"合同",name:"contractTab"}},[e.contractVisiable?i("contract",{ref:"contract",attrs:{"user-permission":e.userPermission.contract,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.train&&e.userPermission.train.isShow?i("el-tab-pane",{attrs:{label:"培养计划",name:"trainTab"}},[e.trainVisiable?i("train",{ref:"train",attrs:{"user-permission":e.userPermission.train,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.assessment&&e.userPermission.assessment.isShow?i("el-tab-pane",{attrs:{label:"考核",name:"assessmentTab"}},[e.assessmentVisiable?i("assessment",{ref:"assessment",attrs:{"user-permission":e.userPermission.assessment,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.incentive&&e.userPermission.incentive.isShow?i("el-tab-pane",{attrs:{label:"医德档案",name:"incentiveTab"}},[e.incentiveVisiable?i("incentive",{ref:"incentive",attrs:{"user-permission":e.userPermission.incentive,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.health&&e.userPermission.health.isShow?i("el-tab-pane",{attrs:{label:"健康信息",name:"healthTab"}},[e.healthVisiable?i("health",{ref:"health",attrs:{"user-permission":e.userPermission.health,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.accident&&e.userPermission.accident.isShow?i("el-tab-pane",{attrs:{label:"医疗",name:"accidentTab"}},[e.accidentVisiable?i("accident",{ref:"accident",attrs:{"user-permission":e.userPermission.accident,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.teach&&e.userPermission.teach.isShow?i("el-tab-pane",{attrs:{label:"教学信息",name:"teachTab"}},[e.teachVisiable?i("teach",{ref:"teach",attrs:{"user-permission":e.userPermission.teach,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.tech&&e.userPermission.tech.isShow?i("el-tab-pane",{attrs:{label:"科研信息",name:"empArticleTab"}},[e.empArticleVisiable?i("empArticle",{ref:"empArticle",attrs:{"user-permission":e.userPermission.tech,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.relation&&e.userPermission.relation.isShow?i("el-tab-pane",{attrs:{label:"社会关系",name:"relationTab"}},[e.relationVisiable?i("relation",{ref:"relation",attrs:{"user-permission":e.userPermission.relation,"emp-id":e.empId}}):e._e()],1):e._e()],1)],1)},s=[],n=(i("b0c0"),i("4fad"),i("498a"),i("2195")),o=i("2cf0"),l=i("185f"),r=i("e061"),c=i("c2f3"),u=i("de23"),d=i("12ed"),m=i("d8b5"),h=i("b96e"),p=i("3220"),y=i("f98d"),b=i("a8a1"),f=i("e675"),g=i("a4b1"),C=i("ff52"),v=i("d336"),I=i("5f06"),V=i("c69e"),D=i("cbd9"),T=i("f9ac"),w={components:{baseInfo:n["a"],hRInfo:o["a"],party:l["a"],station:r["a"],certify:c["a"],wages:u["a"],socialsecurity:d["a"],education:m["a"],work:h["a"],abroadInfo:p["a"],contract:y["a"],train:b["a"],assessment:f["a"],incentive:g["a"],health:C["a"],accident:v["a"],teach:I["a"],relation:V["a"],empArticle:D["a"]},props:{deptId:{type:String,default:""},empId:{type:String,default:""},userPermissionProp:{type:Object,default:function(){return{}}}},data:function(){return{userPermission:this.userPermissionProp,departmentId:"",activeName:"baseInfoTab",containRank:["06"],notContainRank:["06"],baseInfoVisiable:!0,hRInfoVisiable:!1,partyVisiable:!1,stationVisiable:!1,qualificationVisiable:!1,certifyVisiable:!1,wagesVisiable:!1,socialsecurityVisiable:!1,educationVisiable:!1,workVisiable:!1,abroadInfoVisiable:!1,contractVisiable:!1,trainVisiable:!1,assessmentVisiable:!1,incentiveVisiable:!1,healthVisiable:!1,accidentVisiable:!1,teachVisiable:!1,empArticleVisiable:!1,relationVisiable:!1}},watch:{deptId:function(e){this.departmentId=e}},created:function(){this.loadUserPermission()},mounted:function(){},methods:{beforeLeaveTab:function(e,t){return!!this.empId||(this.$notice.message("请先新增基本信息。","info"),!1)},tabClick:function(e){switch(e.name){case"baseInfoTab":this.baseInfoVisiable=!0;break;case"hRInfoTab":this.hRInfoVisiable=this.empId&&""!==this.empId.trim();break;case"partyTab":this.partyVisiable=this.empId&&""!==this.empId.trim();break;case"stationTab":this.stationVisiable=this.empId&&""!==this.empId.trim();break;case"qualificationTab":this.qualificationVisiable=this.empId&&""!==this.empId.trim();break;case"wagesTab":this.wagesVisiable=this.empId&&""!==this.empId.trim();break;case"socialsecurityTab":this.socialsecurityVisiable=this.empId&&""!==this.empId.trim();break;case"educationTab":this.educationVisiable=this.empId&&""!==this.empId.trim();break;case"workTab":this.workVisiable=this.empId&&""!==this.empId.trim();break;case"abroadInfoTab":this.abroadInfoVisiable=this.empId&&""!==this.empId.trim();break;case"contractTab":this.contractVisiable=this.empId&&""!==this.empId.trim();break;case"trainTab":this.trainVisiable=this.empId&&""!==this.empId.trim();break;case"assessmentTab":this.assessmentVisiable=this.empId&&""!==this.empId.trim();break;case"incentiveTab":this.incentiveVisiable=this.empId&&""!==this.empId.trim();break;case"healthTab":this.healthVisiable=this.empId&&""!==this.empId.trim();break;case"accidentTab":this.accidentVisiable=this.empId&&""!==this.empId.trim();break;case"teachTab":this.teachVisiable=this.empId&&""!==this.empId.trim();break;case"empArticleTab":this.empArticleVisiable=this.empId&&""!==this.empId.trim();break;case"relationTab":this.relationVisiable=this.empId&&""!==this.empId.trim();break;case"certifyTab":this.certifyVisiable=this.empId&&""!==this.empId.trim();break;default:this.baseInfoVisiable=!0;break}},clear:function(){this.baseInfoVisiable&&this.$refs["baseInfo"].clear(),this.hRInfoVisiable&&this.$refs["hRInfo"].clear(),this.partyVisiable&&this.$refs["party"].clear(),this.stationVisiable&&this.$refs["station"].clear(),this.qualificationVisiable&&this.$refs["qualification"].clear(),this.certifyVisiable&&this.$refs["certify"].clear(),this.wagesVisiable&&this.$refs["wages"].clear(),this.socialsecurityVisiable&&this.$refs["socialsecurity"].clear(),this.educationVisiable&&this.$refs["education"].clear(),this.workVisiable&&this.$refs["work"].clear(),this.abroadInfoVisiable&&this.$refs["abroadInfo"].clear(),this.contractVisiable&&this.$refs["contract"].clear(),this.trainVisiable&&this.$refs["train"].clear(),this.assessmentVisiable&&this.$refs["assessment"].clear(),this.incentiveVisiable&&this.$refs["incentive"].clear(),this.healthVisiable&&this.$refs["health"].clear(),this.accidentVisiable&&this.$refs["accident"].clear(),this.teachVisiable&&this.$refs["teach"].clear(),this.empArticleVisiable&&this.$refs["empArticle"].clear(),this.relationVisiable&&this.$refs["relation"].clear()},updateEmpId:function(e){this.$emit("updateEmpId",e)},loadUserPermission:function(){var e=this;0===Object.entries(this.userPermission).length&&T["a"].getControlRightByCurrentUser().then((function(t){e.userPermission=t.data})).catch((function(e){console.log(e)}))}}},S=w,k=i("2877"),L=Object(k["a"])(S,a,s,!1,null,null,null);t["a"]=L.exports},"93fe":function(e,t,i){"use strict";var a=i("9c12"),s=i.n(a);s.a},"9c12":function(e,t,i){},b85c:function(e,t,i){"use strict";i.d(t,"a",(function(){return s}));i("a4d3"),i("e01a"),i("d28b"),i("d3b7"),i("3ca3"),i("ddb0");var a=i("06c5");function s(e,t){var i;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(i=Object(a["a"])(e))||t&&e&&"number"===typeof e.length){i&&(e=i);var s=0,n=function(){};return{s:n,n:function(){return s>=e.length?{done:!0}:{done:!1,value:e[s++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,r=!1;return{s:function(){i=e[Symbol.iterator]()},n:function(){var e=i.next();return l=e.done,e},e:function(e){r=!0,o=e},f:function(){try{l||null==i["return"]||i["return"]()}finally{if(r)throw o}}}}},dc52:function(e,t,i){},eabd:function(e,t,i){},fb56:function(e,t,i){"use strict";var a=i("dc52"),s=i.n(a);s.a}}]);