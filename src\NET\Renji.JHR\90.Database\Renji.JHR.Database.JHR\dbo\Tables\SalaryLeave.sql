﻿CREATE TABLE [dbo].[SalaryLeave]
(
	[ID] UNIQUEIDENTIFIER NOT NULL ,
    [EmployeeId]   UNIQUEIDENTIFIER NOT NULL,
    [SalaryId] UNIQUEIDENTIFIER NOT NULL,
    [Remark] NVARCHAR(300) NULL,
    [DiscountRatio] DECIMAL(18, 2) NULL,
    [EnumSalaryLeaveStatus] INT NOT NULL,
    [EnumSalaryLeaveType] INT NOT NULL,
    [EnumSalaryLeaveType_Calculate] INT NOT NULL,
    [ContinuousH2Days] DECIMAL(18, 2) NULL,
    [ContinuousH3Days] DECIMAL(18, 2) NULL,

    [H2] DECIMAL(18,2) NULL ,
    [H3] DECIMAL(18, 2) NULL ,
    [H4] DECIMAL(18, 2) NULL ,
    [H5] DECIMAL(18, 2) NULL ,
    [H6] DECIMAL(18, 2) NULL ,
    [H7] DECIMAL(18, 2) NULL ,
    [H8] DECIMAL(18, 2) NULL ,
    [H12] DECIMAL(18, 2) NULL ,

	[Deleted]		BIT NOT NULL,
    [Creator]		NVARCHAR(50)        NULL,
    [CreateTime]	DATETIME            NULL,
    [LastEditor]	NVARCHAR(50)        NULL,
    [LastEditTime]	DATETIME            NULL,
    CONSTRAINT [PK_SalaryLeave] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_SalaryLeave_Employee] FOREIGN KEY ([EmployeeId]) REFERENCES [dbo].[Employee] ([ID]),
    CONSTRAINT [FK_SalaryLeave_Salary] FOREIGN KEY ([SalaryId]) REFERENCES [dbo].[Salary] ([ID]),
)
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'病产假',
    @level0type = N'SCHEMA',
    @level0name = N'dbo', 
    @level1type = N'TABLE', 
    @level1name = N'SalaryLeave';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'备注',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'SalaryLeave',
    @level2type = N'COLUMN',
    @level2name = N'Remark';
GO



EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'请假类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'SalaryLeave',
    @level2type = N'COLUMN',
    @level2name = N'EnumSalaryLeaveType'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'折扣比例',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'SalaryLeave',
    @level2type = N'COLUMN',
    @level2name = N'DiscountRatio'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'病假',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'SalaryLeave',
    @level2type = N'COLUMN',
    @level2name = N'H2'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'事假',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'SalaryLeave',
    @level2type = N'COLUMN',
    @level2name = N'H3'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'产假',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'SalaryLeave',
    @level2type = N'COLUMN',
    @level2name = N'H4'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'哺乳假',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'SalaryLeave',
    @level2type = N'COLUMN',
    @level2name = N'H5'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'探亲假',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'SalaryLeave',
    @level2type = N'COLUMN',
    @level2name = N'H6'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'计生假',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'SalaryLeave',
    @level2type = N'COLUMN',
    @level2name = N'H7'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'婚丧假',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'SalaryLeave',
    @level2type = N'COLUMN',
    @level2name = N'H8'
GO

GO

GO

GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'连续病假天数',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'SalaryLeave',
    @level2type = N'COLUMN',
    @level2name = N'ContinuousH2Days'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'连续事假天数',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'SalaryLeave',
    @level2type = N'COLUMN',
    @level2name = N'ContinuousH3Days'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'公休',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'SalaryLeave',
    @level2type = N'COLUMN',
    @level2name = N'H12'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'状态',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'SalaryLeave',
    @level2type = N'COLUMN',
    @level2name = N'EnumSalaryLeaveStatus'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'计算请假类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'SalaryLeave',
    @level2type = N'COLUMN',
    @level2name = N'EnumSalaryLeaveType_Calculate'