﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Renji.JHR.Entities
{
    public enum PartyOperations
    {
        /// <summary>
        /// 无
        /// </summary>
        [Description("无")]
        None = 0,

        /// <summary>
        /// 任一
        /// </summary>
        [Description("任一")]
        Any = 90,

        /// <summary>
        /// 所有
        /// </summary>
        [Description("所有")]
        All = 100,
    }
}
