﻿﻿namespace Renji.JHR.Api.Models
{
    public partial class RetiredEmployeeReissueQuery
    {
        /// <summary>
        /// 薪资类型
        /// </summary>
        [MapFromProperty(typeof(RetiredEmployeeReissue), RetiredEmployeeReissue.Foreigns.Employee, Entities.Employee.Inverses.EmployeeSalary, EmployeeSalary.Columns.EnumSalaryType)]
        public SalaryType? EnumSalaryType { get; set; }

        /// <summary>
        /// 规培
        /// </summary>
        [MapFromProperty(typeof(RetiredEmployeeReissue), RetiredEmployeeReissue.Foreigns.Employee, Entities.Employee.Inverses.EmployeeSalary, EmployeeSalary.Columns.SupplementaryHousing)]
        public bool? SupplementaryHousing{ get; set; }

        /// <summary>
        /// 唯一码
        /// </summary>
        [MapFromProperty(typeof(RetiredEmployeeReissue), RetiredEmployeeReissue.Foreigns.Employee, Employee.Columns.Uid)]
        public string? Uid { get; set; }

        /// <summary>
        /// 员工编号
        /// </summary>
        [MapFromProperty(typeof(RetiredEmployeeReissue), RetiredEmployeeReissue.Foreigns.Employee, Employee.Columns.EmpCode)]
        public string? EmpCode { get; set; }

        /// <summary>
        /// 员工姓名
        /// </summary>
        [MapFromProperty(typeof(RetiredEmployeeReissue), RetiredEmployeeReissue.Foreigns.Employee, Employee.Columns.DisplayName)]
        public string? DisplayName { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        [MapFromProperty(typeof(RetiredEmployeeReissue), RetiredEmployeeReissue.Foreigns.Employee, Employee.Foreigns.Department, Department.Columns.Name)]
        public string? DeptName { get; set; }

        /// <summary>
        /// 职别
        /// </summary>
        [MapFromProperty(typeof(RetiredEmployeeReissue), RetiredEmployeeReissue.Foreigns.Employee, Employee.Inverses.EmployeeHR, EmployeeHR.Foreigns.OfficialRank, Dict.Columns.Name)]
        public string? OfficialRankName { get; set; }

        /// <summary>
        /// 在职方式
        /// </summary>
        [MapFromProperty(typeof(RetiredEmployeeReissue), RetiredEmployeeReissue.Foreigns.Employee, Employee.Inverses.EmployeeHR, EmployeeHR.Foreigns.HireStyle, Dict.Columns.Name)]
        public string? HireStyleName { get; set; }

        /// <summary>
        /// 学历
        /// </summary>
        [MapFromProperty(typeof(RetiredEmployeeReissue), RetiredEmployeeReissue.Foreigns.Employee, Employee.Inverses.EmployeeHR, EmployeeHR.Foreigns.Education, Dict.Columns.Name)]
        public string? EducationName { get; set; }

        /// <summary>
        /// 职称
        /// </summary>
        [MapFromProperty(typeof(RetiredEmployeeReissue), RetiredEmployeeReissue.Foreigns.Employee, Employee.Columns.Qualification)]
        public string? Qualification { get; set; }
    }
}
