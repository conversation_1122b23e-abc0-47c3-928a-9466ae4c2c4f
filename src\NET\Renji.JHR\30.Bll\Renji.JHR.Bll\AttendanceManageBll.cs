﻿using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.EntityFrameworkCore.Internal;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.EntityFrameworkCore;
using System.Linq;
using System.Transactions;
using System.Data;
using System.Reflection;
using Microsoft.Data.SqlClient;
using Shinsoft.Core.Json;
using System.Globalization;
using NPOI.HSSF.Record;
using Org.BouncyCastle.Crypto;
using System.Reflection.Metadata;
using System.Runtime.InteropServices;
using System.Xml.Linq;

namespace Renji.JHR.Bll
{
    public class AttendanceManageBll : BaseBll
    {
        #region Constructs

        public AttendanceManageBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public AttendanceManageBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public AttendanceManageBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public AttendanceManageBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        #region MiddleNightShiftFee

        /// <summary>
        /// 暂存|提交|确认|退回中夜班费
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="handle"></param>
        /// <returns></returns>
        public BizResult<AttMonthShiftRecord> HandleAttMonthShiftRecord(AttMonthShiftRecord entity, AttMonthShiftRecord_Handle handle)
        {
            var result = new BizResult<AttMonthShiftRecord>();

            var record = this.GetEntity<AttMonthShiftRecord>(e => e.DeptId == entity.DeptId && e.RecordMonth == entity.RecordMonth);
            bool isNewRecord = false;

            if (record == null)
            {
                record = new AttMonthShiftRecord();
                record.DeptId = entity.DeptId;
                record.RecordMonth = entity.RecordMonth;

                record.ID = CombGuid.NewGuid();
                record.DocumentMaker = this.OperatorUser?.DisplayName;
                record.DocumentMakeTime = SysDateTime.Now;
                isNewRecord = true;
            }

            switch (handle)
            {
                case AttMonthShiftRecord_Handle.Save:
                    record.EnumStatus = AttMonthShiftRecordStatus.UnCommitted;
                    break;

                case AttMonthShiftRecord_Handle.Submit:
                    if (isNewRecord)
                    {
                        result.Error("请先暂存中夜班费数据");
                    }
                    record.EnumStatus = AttMonthShiftRecordStatus.WaitHrConfirm;
                    record.RejectReason = "";
                    break;

                case AttMonthShiftRecord_Handle.Confirm:
                    record.EnumStatus = AttMonthShiftRecordStatus.Confirmed;
                    record.Approver = this.OperatorUser?.DisplayName;
                    record.ApproveTime = SysDateTime.Now;
                    record.RejectReason = "";
                    break;

                case AttMonthShiftRecord_Handle.Reject:
                    record.EnumStatus = AttMonthShiftRecordStatus.UnCommitted;
                    record.RejectReason = entity.RejectReason;
                    break;

                default:
                    break;
            }
            if (result.Succeed)
            {
                if (isNewRecord)
                {
                    result.Data = this.Add(record, false);

                    foreach (var detail in entity.AttMonthShiftRecordDetail)
                    {
                        if (detail.ID.IsEmpty())
                        {
                            detail.ID = CombGuid.NewGuid();
                            detail.RecordId = record.ID;
                            detail.Updator = this.OperatorUser?.DisplayName;
                            detail.UpdateTime = SysDateTime.Now;
                            this.Add(detail, false);
                        }
                        else
                        {
                            detail.Updator = this.OperatorUser?.DisplayName;
                            detail.UpdateTime = SysDateTime.Now;
                            this.Update(detail, false);
                        }
                    }
                }
                else
                {
                    result.Data = this.Update(record, false);

                    foreach (var detail in entity.AttMonthShiftRecordDetail)
                    {
                        var dbDetail = record.AttMonthShiftRecordDetail.Where(c => c.EmployeeId == detail.EmployeeId).FirstOrDefault();

                        if (dbDetail != null)
                        {
                            detail.ID = dbDetail.ID;
                            detail.RecordId = record.ID;
                            detail.Updator = this.OperatorUser?.DisplayName;
                            detail.UpdateTime = SysDateTime.Now;
                            this.Update(detail, false);
                        }
                        else
                        {
                            detail.ID = CombGuid.NewGuid();
                            detail.RecordId = record.ID;
                            detail.Updator = this.OperatorUser?.DisplayName;
                            detail.UpdateTime = SysDateTime.Now;
                            this.Add(detail, false);
                        }
                    }
                }

                this.SaveChanges();
            }

            return result;
        }

        /// <summary>
        /// 批量确认中夜班费
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="handle"></param>
        /// <returns></returns>
        public BizResult<AttMonthShiftRecord> BatchConfirmAttMonthShiftRecord(string? recordMonth)
        {
            var result = new BizResult<AttMonthShiftRecord>();

            var rs = this.GetEntities<AttMonthShiftRecord>(predicate: s => s.RecordMonth == recordMonth && s.EnumStatus == AttMonthShiftRecordStatus.WaitHrConfirm);
            if (rs != null && rs.Count > 0)
            {
                foreach (var record in rs)
                {
                    record.EnumStatus = AttMonthShiftRecordStatus.Confirmed;
                    record.Approver = this.OperatorUser?.DisplayName;
                    record.ApproveTime = SysDateTime.Now;
                    record.RejectReason = "";

                    result.Data = this.Update(record, false);
                }

                this.SaveChanges();
            }

            return result;
        }

        /// <summary>
        /// 修改中夜班费明细
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<AttMonthShiftRecordDetail> UpdateAttMonthShiftRecordDetail(AttMonthShiftRecordDetail entity, string? recordMonth)
        {
            var result = new BizResult<AttMonthShiftRecord>();
            var result2 = new BizResult<AttMonthShiftRecordDetail>();

            var recordId = entity.RecordId;
            var record = this.Get<AttMonthShiftRecord>(recordId);
            var isadd = record == null;

            DateTime recordMonthDate = DateTime.ParseExact(recordMonth + "-01", "yyyy-MM-dd", null);

            List<Guid> recordIds = new List<Guid>();
            recordIds.AddRange(new List<Guid>() { entity.EmployeeId ?? Guid.Empty });

            if (isadd)
            {
                record = new AttMonthShiftRecord();
                record.EnumStatus = AttMonthShiftRecordStatus.Confirmed;
                record.Approver = this.OperatorUser?.DisplayName;
                record.ApproveTime = SysDateTime.Now;
                record.RejectReason = "";
                record.ID = CombGuid.NewGuid();
                record.RecordMonth = recordMonth;
                var emp = this.Get<Employee>(entity.EmployeeId);
                record.DeptId = emp?.DeptId;
                result.Data = this.Add(record, false);
            }
            else
            {
                record = record.Value();
                record.EnumStatus = AttMonthShiftRecordStatus.Confirmed;
                record.Approver = this.OperatorUser?.DisplayName;
                record.ApproveTime = SysDateTime.Now;
                record.RejectReason = "";
                result.Data = this.Update(record, false);
            }

            if (result.Succeed)
            {
                entity.Updator = this.OperatorUser?.DisplayName;
                entity.UpdateTime = SysDateTime.Now;
                if (isadd)
                {
                    entity.RecordId = record.ID;
                    entity.ID = CombGuid.NewGuid();
                    result2.Data = this.Add(entity, false);
                }
                else
                {
                    AddAttMonthShiftRecordDetailSalaryChange(entity);
                    result2.Data = this.Update(entity, false);
                }

                this.SaveChanges();
            }

            return result2;
        }

        public bool AddAttMonthShiftRecordDetailSalaryChange(AttMonthShiftRecordDetail newEntity)
        {
            var dbEntity = this.Get<AttMonthShiftRecordDetail>(newEntity.ID);

            if (dbEntity != null && dbEntity.AttMonthShiftRecord != null)
            {
                var entity = new AttMonthShiftRecordDetailSalaryChange()
                {
                    ID = CombGuid.NewGuid(),
                    MonthShiftRecordId = newEntity.ID,
                    IsCalculate = false,
                    Month = Convert.ToDateTime(dbEntity.AttMonthShiftRecord.RecordMonth),

                    // 中班
                    OldZB = dbEntity.ZB,
                    NewZB = newEntity.ZB,
                    DifferenceZB = (newEntity.ZB ?? 0) - (dbEntity.ZB ?? 0),

                    // 夜班
                    OldYB = dbEntity.YB,
                    NewYB = newEntity.YB,
                    DifferenceYB = (newEntity.YB ?? 0) - (dbEntity.YB ?? 0),

                    // 24小时班
                    OldB24 = dbEntity.B24,
                    NewB24 = newEntity.B24,
                    DifferenceB24 = (newEntity.B24 ?? 0) - (dbEntity.B24 ?? 0),

                    // 急诊中班
                    OldJZZB = dbEntity.JZZB,
                    NewJZZB = newEntity.JZZB,
                    DifferenceJZZB = (newEntity.JZZB ?? 0) - (dbEntity.JZZB ?? 0),

                    // 急诊夜班
                    OldJZYB = dbEntity.JZYB,
                    NewJZYB = newEntity.JZYB,
                    DifferenceJZYB = (newEntity.JZYB ?? 0) - (dbEntity.JZYB ?? 0),

                    // 急诊24小时
                    OldJZ24 = dbEntity.JZ24,
                    NewJZ24 = newEntity.JZ24,
                    DifferenceJZ24 = (newEntity.JZ24 ?? 0) - (dbEntity.JZ24 ?? 0),

                    // 后勤中班
                    OldHQZB = dbEntity.HQZB,
                    NewHQZB = newEntity.HQZB,
                    DifferenceHQZB = (newEntity.HQZB ?? 0) - (dbEntity.HQZB ?? 0),

                    // 后勤夜班
                    OldHQYB = dbEntity.HQYB,
                    NewHQYB = newEntity.HQYB,
                    DifferenceHQYB = (newEntity.HQYB ?? 0) - (dbEntity.HQYB ?? 0),

                    // 后勤12小时
                    OldHQ12 = dbEntity.HQ12,
                    NewHQ12 = newEntity.HQ12,
                    DifferenceHQ12 = (newEntity.HQ12 ?? 0) - (dbEntity.HQ12 ?? 0),

                    // 后勤24小时
                    OldHQ24 = dbEntity.HQ24,
                    NewHQ24 = newEntity.HQ24,
                    DifferenceHQ24 = (newEntity.HQ24 ?? 0) - (dbEntity.HQ24 ?? 0),

                    // 护理A挡中班
                    OldHLAZB = dbEntity.HLAZB,
                    NewHLAZB = newEntity.HLAZB,
                    DifferenceHLAZB = (newEntity.HLAZB ?? 0) - (dbEntity.HLAZB ?? 0),

                    // 护理A挡夜班
                    OldHLAYB = dbEntity.HLAYB,
                    NewHLAYB = newEntity.HLAYB,
                    DifferenceHLAYB = (newEntity.HLAYB ?? 0) - (dbEntity.HLAYB ?? 0),

                    // 护理A档24小时
                    OldHLA24 = dbEntity.HLA24,
                    NewHLA24 = newEntity.HLA24,
                    DifferenceHLA24 = (newEntity.HLA24 ?? 0) - (dbEntity.HLA24 ?? 0),

                    // 其他值班1
                    OldQT1 = dbEntity.QT1,
                    NewQT1 = newEntity.QT1,
                    DifferenceQT1 = (newEntity.QT1 ?? 0) - (dbEntity.QT1 ?? 0),

                    // 其他值班2
                    OldQT2 = dbEntity.QT2,
                    NewQT2 = newEntity.QT2,
                    DifferenceQT2 = (newEntity.QT2 ?? 0) - (dbEntity.QT2 ?? 0)
                };
                this.Add(entity);
            }
            return true;
        }

        #endregion MiddleNightShiftFee

        #region HolidayOT

        /// <summary>
        /// 暂存|提交|确认|退回节日加班
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="handle"></param>
        /// <returns></returns>
        public BizResult<AttHolidayOTRecord> HandleAttHolidayOTRecord(AttHolidayOTRecord entity, AttHolidayOTRecord_Handle handle)
        {
            var result = new BizResult<AttHolidayOTRecord>();
            var record = this.GetEntity<AttHolidayOTRecord>(e => e.DeptId == entity.DeptId && e.RecordDate == entity.RecordDate);
            bool isaddnew = false;
            if (record == null)
            {
                record = new AttHolidayOTRecord();
                record.DeptId = entity.DeptId;
                record.RecordDate = entity.RecordDate;

                record.ID = CombGuid.NewGuid();
                record.DocumentMaker = this.OperatorUser?.DisplayName;
                record.DocumentMakeTime = SysDateTime.Now;
                isaddnew = true;
            }

            switch (handle)
            {
                case AttHolidayOTRecord_Handle.Save:
                    record.EnumStatus = AttHolidayOTRecordStatus.UnCommitted;
                    break;

                case AttHolidayOTRecord_Handle.Submit:
                    if (isaddnew)
                    {
                        result.Error("请先暂存节日加班费数据");
                    }
                    record.EnumStatus = AttHolidayOTRecordStatus.DocMakerCommit;
                    record.RejectReason = "";
                    break;

                case AttHolidayOTRecord_Handle.Confirm:
                    record.EnumStatus = AttHolidayOTRecordStatus.Confirmed;
                    record.Approver = this.OperatorUser?.DisplayName;
                    record.ApproveTime = SysDateTime.Now;
                    record.RejectReason = "";
                    break;

                case AttHolidayOTRecord_Handle.Reject:
                    record.EnumStatus = AttHolidayOTRecordStatus.UnCommitted;
                    break;

                default:
                    break;
            }

            if (result.Succeed)
            {
                if (isaddnew)
                {
                    result.Data = this.Add(record, false);

                    foreach (var detail in entity.AttHolidayOTRecordDetail)
                    {
                        if (detail.ID.IsEmpty())
                        {
                            detail.ID = CombGuid.NewGuid();
                            detail.RecordId = record.ID;
                            detail.OTDate = entity.RecordDate;
                            detail.UpdateTime = SysDateTime.Now;
                            detail.Updator = this.OperatorUser?.DisplayName;
                            this.Add(detail, false);
                        }
                        else
                        {
                            detail.UpdateTime = SysDateTime.Now;
                            detail.Updator = this.OperatorUser?.DisplayName;
                            detail.OTDate = entity.RecordDate;
                            this.Update(detail, false);
                        }
                    }
                }
                else
                {
                    result.Data = this.Update(record, false);

                    foreach (var detail in entity.AttHolidayOTRecordDetail)
                    {
                        var dbDetail = record.AttHolidayOTRecordDetail.Where(c => c.EmployeeId == detail.EmployeeId).FirstOrDefault();

                        if (dbDetail != null)
                        {
                            detail.ID = dbDetail.ID;
                            detail.RecordId = record.ID;
                            detail.OTDate = entity.RecordDate;
                            detail.Updator = this.OperatorUser?.DisplayName;
                            detail.UpdateTime = SysDateTime.Now;
                            this.Update(detail, false);
                        }
                        else
                        {
                            detail.ID = CombGuid.NewGuid();
                            detail.RecordId = record.ID;
                            detail.OTDate = entity.RecordDate;
                            detail.Updator = this.OperatorUser?.DisplayName;
                            detail.UpdateTime = SysDateTime.Now;
                            this.Add(detail, false);
                        }
                    }
                }

                this.SaveChanges();
            }

            return result;
        }

        /// <summary>
        /// 批量确认节日加班
        /// </summary>
        /// <param name="recordMonth"></param>
        /// <returns></returns>
        public BizResult<AttHolidayOTRecord> BatchConfirmAttHolidayOTRecord(DateTime? recordDate)
        {
            var result = new BizResult<AttHolidayOTRecord>();

            var rs = this.GetEntities<AttHolidayOTRecord>(predicate: s => s.RecordDate == recordDate && s.EnumStatus == AttHolidayOTRecordStatus.DocMakerCommit);
            if (rs != null && rs.Count > 0)
            {
                foreach (var record in rs)
                {
                    record.EnumStatus = AttHolidayOTRecordStatus.Confirmed;
                    record.Approver = this.OperatorUser?.DisplayName;
                    record.ApproveTime = SysDateTime.Now;
                    record.RejectReason = "";

                    result.Data = this.Update(record, false);
                }

                this.SaveChanges();
            }

            return result;
        }

        /// <summary>
        /// 修改节日加班明细
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult UpdateAttHolidayOTRecordDetail(ref AttHolidayOTRecordDetail entity)
        {
            var result = new BizResult();

            var recordId = entity.RecordId;
            var record = this.Get<AttHolidayOTRecord>(recordId);

            var isadd = record == null;

            if (isadd)
            {
                record = new AttHolidayOTRecord();
                record.EnumStatus = AttHolidayOTRecordStatus.Confirmed;
                record.Approver = this.OperatorUser?.DisplayName;
                record.ApproveTime = SysDateTime.Now;
                record.RejectReason = "";
                record.ID = CombGuid.NewGuid();
                record.RecordDate = entity.OTDate;
                var emp = this.Get<Employee>(entity.EmployeeId);
                record.DeptId = emp?.DeptId;
                result.Data = this.Add(record, false);
            }
            else
            {
                record = record.Value();

                record.EnumStatus = AttHolidayOTRecordStatus.Confirmed;
                record.Approver = this.OperatorUser?.DisplayName;
                record.ApproveTime = SysDateTime.Now;
                record.RejectReason = "";
                result.Data = this.Update(record, false);
            }

            List<Guid> recordIds = new List<Guid>();
            recordIds.AddRange(new List<Guid>() { entity.EmployeeId ?? Guid.Empty });

            if (result.Succeed)
            {
                entity.Updator = this.OperatorUser?.DisplayName;
                entity.UpdateTime = SysDateTime.Now;
                if (isadd)
                {
                    entity.RecordId = record.ID;
                    entity.ID = CombGuid.NewGuid();
                    result.Data = this.Add(entity);
                }
                else
                {
                    result.Data = this.Update(entity);
                }

                this.SaveChanges();
            }
            return result;
        }

        #endregion HolidayOT

        #region AttDayOff

        /// <summary>
        /// 考勤员暂存考勤数据
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<AttDayOffRecord> UnCommittedAttDayOffRecord(AttDayOffRecord entity)
        {
            var result = CheckHandleAttDayOffRecord(entity);
            var record = this.GetEntity<AttDayOffRecord>(e => e.DeptId == entity.DeptId && e.RecordMonth == entity.RecordMonth);

            if (result.Succeed && record != null && record.EnumStatus == AttDayOffRecordStatus.Committed)
            {
                result.Error("状态错误，无法暂存，请刷新页面");
            }

            if (result.Succeed)
            {
                if (record == null)
                {
                    record = new AttDayOffRecord()
                    {
                        ID = CombGuid.NewGuid(),
                        DeptId = entity.DeptId,
                        RecordMonth = entity.RecordMonth,
                        EnumStatus = AttDayOffRecordStatus.UnCommitted,
                        EnumProphylacticStatus = ProphylacticStatus.None,
                        Remark = entity.Remark,
                    };

                    result.Data = this.Add(record, false);

                    foreach (var detail in entity.AttDayOffRecordFilling)
                    {
                        detail.RecordId = record.ID;
                        detail.RecordMonth = entity.RecordMonth!.As<DateTime>();
                        detail.EnumType = AttDayOffRecordFillingType.Check;   //   重要字段
                        detail.Updator = this.OperatorUser?.DisplayName;
                        detail.UpdateTime = SysDateTime.Now;
                        if (detail.ID.IsEmpty())
                        {
                            detail.ID = CombGuid.NewGuid();
                            this.Add(detail, false);
                        }
                        else
                        {
                            this.Update(detail, false);
                        }
                    }
                }
                else
                {
                    if (result.Succeed)
                    {
                        record.EnumStatus = AttDayOffRecordStatus.UnCommitted;
                        record.Remark = entity.Remark;
                        result.Data = this.Update(record, false);

                        var attDayOffRecordFillings = this.GetEntities<AttDayOffRecordFilling>(p => p.RecordId == record.ID && p.EnumType == AttDayOffRecordFillingType.Check);

                        foreach (var detail in entity.AttDayOffRecordFilling)
                        {
                            var dbDetail = attDayOffRecordFillings.Where(c => c.EmployeeId == detail.EmployeeId).FirstOrDefault();

                            detail.RecordId = record.ID;
                            detail.RecordMonth = entity.RecordMonth!.As<DateTime>();
                            detail.EnumType = AttDayOffRecordFillingType.Check;   //   重要字段
                            detail.Updator = this.OperatorUser?.DisplayName;
                            detail.UpdateTime = SysDateTime.Now;
                            if (dbDetail != null)
                            {
                                detail.ID = dbDetail.ID;
                                this.Update(detail, false);
                            }
                            else
                            {
                                detail.ID = CombGuid.NewGuid();
                                this.Add(detail, false);
                            }
                        }
                    }
                }

                this.SaveChanges();
            }

            return result;
        }

        /// <summary>
        /// 考勤员提交考勤数据
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<AttDayOffRecord> SubmitAttDayOffRecord(AttDayOffRecord entity)
        {
            var result = CheckHandleAttDayOffRecord(entity);
            var record = this.GetEntity<AttDayOffRecord>(e => e.DeptId == entity.DeptId && e.RecordMonth == entity.RecordMonth);

            if (result.Succeed)
            {
                if (record == null)
                {
                    result.Error("请通知考勤员先暂存考勤数据");
                }
                else if (record.EnumStatus != AttDayOffRecordStatus.UnCommitted)
                {
                    result.Error("状态错误，无法暂存，请刷新页面");
                }
            }

            if (result.Succeed)
            {
                record = record.Value();
                // 主表
                record.EnumStatus = AttDayOffRecordStatus.Committed;
                record.Remark = entity.Remark;
                result.Data = this.Update(record, false);

                // 中间表
                var attDayOffRecordFillings = this.GetEntities<AttDayOffRecordFilling>(p => p.RecordId == record.ID && p.EnumType == AttDayOffRecordFillingType.Check);
                foreach (var detail in entity.AttDayOffRecordFilling)
                {
                    var dbDetail = attDayOffRecordFillings.Where(c => c.EmployeeId == detail.EmployeeId).FirstOrDefault();

                    detail.RecordId = record.ID;
                    detail.RecordMonth = entity.RecordMonth!.As<DateTime>();
                    detail.EnumType = AttDayOffRecordFillingType.Check;   //   重要字段
                    detail.Updator = this.OperatorUser?.DisplayName;
                    detail.UpdateTime = SysDateTime.Now;
                    if (dbDetail != null)
                    {
                        detail.ID = dbDetail.ID;
                        this.Update(detail, false);
                    }
                    else
                    {
                        detail.ID = CombGuid.NewGuid();
                        this.Add(detail, false);
                    }
                }

                // 正式明细表处理
                //查询正式明细数据
                var attDayOffRecordDetails = this.GetEntities<AttDayOffRecordDetail>(p => p.RecordMonth == entity.RecordMonth && p.AttDayOffRecord != null && p.AttDayOffRecord.DeptId == entity.DeptId);

                foreach (var item in entity.AttDayOffRecordFilling)
                {
                    var attDayOffRecordDetail = attDayOffRecordDetails.FirstOrDefault(p => p.EmployeeId == item.EmployeeId);
                    if (attDayOffRecordDetail == null)
                    {
                        var detail = new AttDayOffRecordDetail()
                        {
                            ID = CombGuid.NewGuid(),
                            RecordId = item.RecordId,
                            EmployeeId = item.EmployeeId,
                            RecordMonth = entity.RecordMonth,
                            Updator = this.OperatorUser?.DisplayName,
                            UpdateTime = DateTime.Now,
                            H1 = item.H1,
                            H2 = item.H2,
                            H3 = item.H3,
                            H4 = item.H4,
                            H5 = item.H5,
                            H6 = item.H6,
                            H7 = item.H7,
                            H8 = item.H8,
                            H9 = item.H9,
                            H10 = item.H10,
                            H11 = item.H11,
                            H12 = item.H12,
                            HistoryH12 = item.HistoryH12,
                            EnumStatus = AttDayOffRecordDetailStatus.NoApproval,
                        };

                        this.Add(detail, false);
                    }
                    else
                    {
                        // 如果一致，则无需审批，如果出现不一致，则改为待审批,如果位none  则为废数据，重新启用
                        attDayOffRecordDetail.H1 = item.H1;
                        attDayOffRecordDetail.H12 = item.H12;
                        attDayOffRecordDetail.HistoryH12 = item.HistoryH12;
                        attDayOffRecordDetail.Updator = this.OperatorUser?.DisplayName;
                        attDayOffRecordDetail.UpdateTime = DateTime.Now;
                        if (attDayOffRecordDetail.EnumStatus == AttDayOffRecordDetailStatus.None)
                        {
                            attDayOffRecordDetail.H2 = item.H2;
                            attDayOffRecordDetail.H3 = item.H3;
                            attDayOffRecordDetail.H4 = item.H4;
                            attDayOffRecordDetail.H5 = item.H5;
                            attDayOffRecordDetail.H6 = item.H6;
                            attDayOffRecordDetail.H7 = item.H7;
                            attDayOffRecordDetail.H8 = item.H8;
                            attDayOffRecordDetail.H9 = item.H9;
                            attDayOffRecordDetail.H10 = item.H10;
                            attDayOffRecordDetail.H11 = item.H11;
                            attDayOffRecordDetail.EnumStatus = AttDayOffRecordDetailStatus.NoApproval;
                        }
                        else
                        {
                            // 防保科先提交，attDayOffRecordDetail 为防保科数据；考勤员再提交，item考勤员数据
                            // 比较 如果总请假天数相同，以防保科为准；如果总请假天数不同，以总请假天数多的为准
                            decimal dF = attDayOffRecordDetail.H2.GetValueOrDefault() + attDayOffRecordDetail.H3.GetValueOrDefault() +
                                attDayOffRecordDetail.H4.GetValueOrDefault() + attDayOffRecordDetail.H5.GetValueOrDefault() +
                                attDayOffRecordDetail.H6.GetValueOrDefault() + attDayOffRecordDetail.H7.GetValueOrDefault() +
                                attDayOffRecordDetail.H8.GetValueOrDefault() + attDayOffRecordDetail.H9.GetValueOrDefault() +
                                attDayOffRecordDetail.H10.GetValueOrDefault() + attDayOffRecordDetail.H11.GetValueOrDefault();

                            decimal dK = item.H2.GetValueOrDefault() + item.H3.GetValueOrDefault() +
                                item.H4.GetValueOrDefault() + item.H5.GetValueOrDefault() +
                                item.H6.GetValueOrDefault() + item.H7.GetValueOrDefault() +
                                item.H8.GetValueOrDefault() + item.H9.GetValueOrDefault() +
                                item.H10.GetValueOrDefault() + item.H11.GetValueOrDefault() +
                                item.H12.GetValueOrDefault();

                            if (dK > dF)
                            {
                                attDayOffRecordDetail.H2 = item.H2;
                                attDayOffRecordDetail.H3 = item.H3;
                                attDayOffRecordDetail.H4 = item.H4;
                                attDayOffRecordDetail.H5 = item.H5;
                                attDayOffRecordDetail.H6 = item.H6;
                                attDayOffRecordDetail.H7 = item.H7;
                                attDayOffRecordDetail.H8 = item.H8;
                                attDayOffRecordDetail.H9 = item.H9;
                                attDayOffRecordDetail.H10 = item.H10;
                                attDayOffRecordDetail.H11 = item.H11;
                            }
                            attDayOffRecordDetail.EnumStatus = AttDayOffRecordDetailStatus.Approved;

                            //// 防保科先提交，attDayOffRecordDetail 为防保科数据；考勤员再提交，item考勤员数据
                            //// 比较 如果请假天数相同，审批通过，不相同，等待人事审批
                            //if (attDayOffRecordDetail.H2.GetValueOrDefault() != item.H2.GetValueOrDefault() || attDayOffRecordDetail.H3.GetValueOrDefault() != item.H3.GetValueOrDefault() ||
                            //    attDayOffRecordDetail.H4.GetValueOrDefault() != item.H4.GetValueOrDefault() || attDayOffRecordDetail.H5.GetValueOrDefault() != item.H5.GetValueOrDefault() ||
                            //    attDayOffRecordDetail.H6.GetValueOrDefault() != item.H6.GetValueOrDefault() || attDayOffRecordDetail.H7.GetValueOrDefault() != item.H7.GetValueOrDefault() ||
                            //    attDayOffRecordDetail.H8.GetValueOrDefault() != item.H8.GetValueOrDefault() || attDayOffRecordDetail.H9.GetValueOrDefault() != item.H9.GetValueOrDefault() ||
                            //    attDayOffRecordDetail.H10.GetValueOrDefault() != item.H10.GetValueOrDefault() || attDayOffRecordDetail.H11.GetValueOrDefault() != item.H11.GetValueOrDefault())
                            //{
                            //    // 由于考勤员需要显示，所有待审批的Detail表数据改为考勤员数据
                            //    attDayOffRecordDetail.H2 = item.H2;
                            //    attDayOffRecordDetail.H3 = item.H3;
                            //    attDayOffRecordDetail.H4 = item.H4;
                            //    attDayOffRecordDetail.H5 = item.H5;
                            //    attDayOffRecordDetail.H6 = item.H6;
                            //    attDayOffRecordDetail.H7 = item.H7;
                            //    attDayOffRecordDetail.H8 = item.H8;
                            //    attDayOffRecordDetail.H9 = item.H9;
                            //    attDayOffRecordDetail.H10 = item.H10;
                            //    attDayOffRecordDetail.H11 = item.H11;
                            //    attDayOffRecordDetail.EnumStatus = AttDayOffRecordDetailStatus.Pending;
                            //}
                            //else
                            //{
                            //    attDayOffRecordDetail.EnumStatus = AttDayOffRecordDetailStatus.Approved;
                            //}
                        }

                        this.Update(attDayOffRecordDetail, false);
                    }
                }

                this.SaveChanges();
            }
            return result;
        }

        /// <summary>
        /// 人事退回考勤员数据
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<AttDayOffRecord> RejectAttDayOffRecord(AttDayOffRecord entity)
        {
            var result = new BizResult<AttDayOffRecord>();

            var record = this.Get<AttDayOffRecord>(entity.ID);
            if (record == null || record.EnumStatus != AttDayOffRecordStatus.Committed)
            {
                result.Error("状态错误，无法退回，请刷新页面");
            }
            else
            {
                if (entity.RecordMonth!.As<DateTime>() < SysDateTime.Now.Date.AddDays(1 - SysDateTime.Now.Date.Day).AddMonths(-1))
                {
                    result.Error("上月之前的考勤数据不允许退回");
                }

                if (result.Succeed)
                {
                    // 查询正式明细数据
                    var attDayOffRecordDetails = this.GetEntities<AttDayOffRecordDetail>(p => p.RecordId == record.ID);
                    var pendingEmployeeIds = attDayOffRecordDetails.Where(p => p.EnumStatus == AttDayOffRecordDetailStatus.Pending || p.EnumStatus == AttDayOffRecordDetailStatus.Approved).Select(p => p.EmployeeId).Distinct().ToList();

                    // 查询审批中明细数据对应的防保科数据
                    var prophylacticFillings = new List<AttDayOffRecordFilling>();
                    if (pendingEmployeeIds.Any())
                    {
                        prophylacticFillings = this.GetEntities<AttDayOffRecordFilling>(p => p.EnumType == AttDayOffRecordFillingType.Prophylactic
                                && pendingEmployeeIds.Contains(p.EmployeeId) && p.RecordId == record.ID);
                    }

                    // 主表 修改
                    record.EnumStatus = AttDayOffRecordStatus.UnCommitted;
                    result.Data = this.Update(record, false);

                    foreach (var attDayOffRecordDetail in attDayOffRecordDetails)
                    {
                        attDayOffRecordDetail.Updator = this.OperatorUser?.DisplayName;
                        attDayOffRecordDetail.UpdateTime = SysDateTime.Now;

                        if (attDayOffRecordDetail.EnumStatus == AttDayOffRecordDetailStatus.Pending || attDayOffRecordDetail.EnumStatus == AttDayOffRecordDetailStatus.Approved)
                        {
                            // 审批中的明细数据，回退为防保科数据
                            var prophylacticFilling = prophylacticFillings.FirstOrDefault(p => p.EmployeeId == attDayOffRecordDetail.EmployeeId);
                            if (prophylacticFilling != null)
                            {
                                attDayOffRecordDetail.H2 = prophylacticFilling.H2;
                                attDayOffRecordDetail.H3 = prophylacticFilling.H3;
                                attDayOffRecordDetail.H4 = prophylacticFilling.H4;
                                attDayOffRecordDetail.H5 = prophylacticFilling.H5;
                                attDayOffRecordDetail.H6 = prophylacticFilling.H6;
                                attDayOffRecordDetail.H7 = prophylacticFilling.H7;
                                attDayOffRecordDetail.H8 = prophylacticFilling.H8;
                                attDayOffRecordDetail.H9 = prophylacticFilling.H9;
                                attDayOffRecordDetail.H10 = prophylacticFilling.H10;
                                attDayOffRecordDetail.H11 = prophylacticFilling.H11;
                                attDayOffRecordDetail.H12 = prophylacticFilling.H12;
                                attDayOffRecordDetail.HistoryH12 = prophylacticFilling.HistoryH12;
                                attDayOffRecordDetail.Updator = prophylacticFilling.Updator;
                                attDayOffRecordDetail.EnumStatus = AttDayOffRecordDetailStatus.NoApproval;
                            }
                            else
                            {
                                attDayOffRecordDetail.EnumStatus = AttDayOffRecordDetailStatus.None;
                            }
                        }
                        else if (attDayOffRecordDetail.EnumStatus == AttDayOffRecordDetailStatus.NoApproval)
                        {
                            // 无需审批，说明只有考勤员提交了数据。  改为无效数据
                            attDayOffRecordDetail.EnumStatus = AttDayOffRecordDetailStatus.None;
                        }
                        else if (attDayOffRecordDetail.EnumStatus == AttDayOffRecordDetailStatus.Approved)
                        {
                            // 已审批的，说明数据一致，保留数据，改为无需审批
                            attDayOffRecordDetail.EnumStatus = AttDayOffRecordDetailStatus.NoApproval;
                        }

                        this.Update<AttDayOffRecordDetail>(attDayOffRecordDetail, false);
                    }

                    if (result.Succeed)
                    {
                        this.SaveChanges();
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 人事审批考勤数据
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult ApproveAttDayOffRecord(List<AttDayOffRecordDetail> entitys)
        {
            var result = new BizResult();
            if (entitys == null || !entitys.Any())
            {
                result.Error("没有审批数据");
            }
            else
            {
                var ids = entitys.Select(p => p.ID).ToList();
                var attDayOffRecordDetails = this.GetEntities<AttDayOffRecordDetail>(p => ids.Contains(p.ID));
                if (attDayOffRecordDetails.Any(p => p.EnumStatus != AttDayOffRecordDetailStatus.Pending) || attDayOffRecordDetails.Count() != entitys.Count())
                {
                    result.Error("部分数据状态已经被改变，请刷新页面，重新读取数据");
                }

                //if (result.Succeed && entitys.Any(p => p.H2.GetValueOrDefault() + p.H3.GetValueOrDefault() + p.H4.GetValueOrDefault() + p.H5.GetValueOrDefault() + p.H6.GetValueOrDefault() + p.H7.GetValueOrDefault() + p.H8.GetValueOrDefault() + p.H9.GetValueOrDefault() + p.H10.GetValueOrDefault() + p.H11.GetValueOrDefault() > p.RecordMonth.As<DateTime>().AddMonths(1).AddDays(-1).Day))
                //{
                //    result.Error("请假天数不能超过月份总天数,请检查数据");
                //}

                if (result.Succeed)
                {
                    foreach (var attDayOffRecordDetail in attDayOffRecordDetails)
                    {
                        var entity = entitys.FirstOrDefault(p => p.ID == attDayOffRecordDetail.ID);
                        if (entity != null)
                        {
                            attDayOffRecordDetail.H1 = entity.H1;
                            attDayOffRecordDetail.H2 = entity.H2;
                            attDayOffRecordDetail.H3 = entity.H3;
                            attDayOffRecordDetail.H4 = entity.H4;
                            attDayOffRecordDetail.H5 = entity.H5;
                            attDayOffRecordDetail.H6 = entity.H6;
                            attDayOffRecordDetail.H7 = entity.H7;
                            attDayOffRecordDetail.H8 = entity.H8;
                            attDayOffRecordDetail.H9 = entity.H9;
                            attDayOffRecordDetail.H10 = entity.H10;
                            attDayOffRecordDetail.H11 = entity.H11;
                            attDayOffRecordDetail.H12 = entity.H12;
                            attDayOffRecordDetail.HistoryH12 = entity.HistoryH12;
                            attDayOffRecordDetail.EnumStatus = AttDayOffRecordDetailStatus.Approved;

                            attDayOffRecordDetail.Updator = this.OperatorUser?.DisplayName;
                            attDayOffRecordDetail.UpdateTime = SysDateTime.Now;

                            this.Update(attDayOffRecordDetail, false);
                        }
                    }

                    this.SaveChanges();
                }
            }

            return result;
        }

        private BizResult<AttDayOffRecord> CheckHandleAttDayOffRecord(AttDayOffRecord entity)
        {
            var result = new BizResult<AttDayOffRecord>();

            if (entity.DeptId.IsEmpty())
            {
                result.Error("部门不能为空");
            }

            if (entity.AttDayOffRecordFilling.Any(p => p.H1 < 0))
            {
                result.Error("本月卫贴标准必须大于等于0");
            }

            if (entity.AttDayOffRecordFilling.Any(p => p.H2 < 0))
            {
                result.Error("病假必须大于等于0");
            }

            if (entity.AttDayOffRecordFilling.Any(p => p.H3 < 0))
            {
                result.Error("事假必须大于等于0");
            }

            if (entity.AttDayOffRecordFilling.Any(p => p.H4 < 0))
            {
                result.Error("产假必须大于等于0");
            }

            if (entity.AttDayOffRecordFilling.Any(p => p.H5 < 0))
            {
                result.Error("哺乳假必须大于等于0");
            }

            if (entity.AttDayOffRecordFilling.Any(p => p.H6 < 0))
            {
                result.Error("探亲假必须大于等于0");
            }

            if (entity.AttDayOffRecordFilling.Any(p => p.H7 < 0))
            {
                result.Error("计生假必须大于等于0");
            }

            if (entity.AttDayOffRecordFilling.Any(p => p.H8 < 0))
            {
                result.Error("婚丧假必须大于等于0");
            }

            if (entity.AttDayOffRecordFilling.Any(p => p.H9 < 0))
            {
                result.Error("脱产读研必须大于等于0");
            }

            if (entity.AttDayOffRecordFilling.Any(p => p.H10 < 0))
            {
                result.Error("因公出国必须大于等于0");
            }

            if (entity.AttDayOffRecordFilling.Any(p => p.H11 < 0))
            {
                result.Error("因私出国必须大于等于0");
            }

            if (entity.AttDayOffRecordFilling.Any(p => (!p.HistoryH12.HasValue && p.H12.HasValue) || (p.H12 > p.HistoryH12)))
            {
                result.Error("公休不能大于剩余公休");
            }

            if (entity.RecordMonth.IsEmpty())
            {
                result.Error("月份不能为空");
            }
            else if (!DateTime.TryParseExact(entity.RecordMonth, "yyyyMM", CultureInfo.CurrentCulture, DateTimeStyles.None, out DateTime datetime)
                 && !DateTime.TryParseExact(entity.RecordMonth + "01", "yyyyMM", CultureInfo.CurrentCulture, DateTimeStyles.None, out datetime)
                 && !DateTime.TryParseExact(entity.RecordMonth + "-01", "yyyyMM", CultureInfo.CurrentCulture, DateTimeStyles.None, out datetime)
                 && !DateTime.TryParseExact(entity.RecordMonth + "/01", "yyyyMM", CultureInfo.CurrentCulture, DateTimeStyles.None, out datetime)
                                    && !DateTime.TryParse(entity.RecordMonth, out datetime))
            {
                result.Error("月份不正确");
            }
            else if (result.Succeed)
            {
                //var day = entity.RecordMonth.As<DateTime>().AddDays(1 - entity.RecordMonth.As<DateTime>().Day).AddMonths(1).AddDays(-1).Day;
                //if (result.Succeed && entity.AttDayOffRecordFilling.Any(p => p.H2.GetValueOrDefault() + p.H3.GetValueOrDefault() + p.H4.GetValueOrDefault() + p.H5.GetValueOrDefault() + p.H6.GetValueOrDefault() + p.H7.GetValueOrDefault() + p.H8.GetValueOrDefault() + p.H9.GetValueOrDefault() + p.H10.GetValueOrDefault() + p.H11.GetValueOrDefault() > day))
                //{
                //    result.Error("请假天数不能超过月份总天数,请检查数据");
                //}

                if (entity.RecordMonth!.As<DateTime>().AddDays(1 - entity.RecordMonth!.As<DateTime>().Day) < SysDateTime.Now.Date.AddDays(1 - SysDateTime.Now.Date.Day).AddMonths(-1))
                {
                    result.Error("只允许暂存提交上月及以后的考勤数据");
                }
            }

            return result;
        }

        /// <summary>
        /// 修改考勤数据明细明细
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult UpdateAttDayOffRecordDetail(ref AttDayOffRecordDetail entity)
        {
            var result = new BizResult<AttDayOffRecordDetail>();

            var emp = this.Get<Employee>(entity.EmployeeId);
            var recordMonth = entity.RecordMonth?.As<DateTime>();
            var recordMonthStr = entity.RecordMonth;
            var deptId = emp?.DeptId;

            var record = this.GetEntity<AttDayOffRecord>(e => e.DeptId == deptId && e.RecordMonth == recordMonthStr);

            var employeeId = entity.EmployeeId;
            var attDayOffRecordDetail = record == null ? null : this.GetEntity<AttDayOffRecordDetail>(p => p.RecordId == record.ID && p.EmployeeId == employeeId);

            if ((!entity.HistoryH12.HasValue && entity.H12.HasValue) || (entity.H12 > entity.HistoryH12))
            {
                result.Error($"公休不能大于剩余公休");
            }

            // 人事老师要求去掉这个限制
            //if (record != null && record.EnumStatus != AttDayOffRecordStatus.Committed && record.EnumProphylacticStatus != ProphylacticStatus.Submitted && recordMonth.Date == DateTime.Now.Date.AddDays(1 - DateTime.Now.Date.Day).AddMonths(-1))
            //{
            //    result.Error($"上月的考勤员考勤数据还没有申报，请申报即可");
            //}
            //else if (recordMonth >= DateTime.Now.Date.AddDays(1 - DateTime.Now.Date.Day))
            if (recordMonth >= DateTime.Now.Date.AddDays(1 - DateTime.Now.Date.Day))
            {
                result.Error($"不能修改当前月和未来月的考勤数据");
            }
            else if (attDayOffRecordDetail != null && attDayOffRecordDetail.EnumStatus == AttDayOffRecordDetailStatus.Pending)
            {
                result.Error($"当前员工有待审批考勤数据，请到人事审批菜单进行审批");
            }
            //else if (entity.H2.GetValueOrDefault() + entity.H3.GetValueOrDefault() + entity.H4.GetValueOrDefault() + entity.H5.GetValueOrDefault() + entity.H6.GetValueOrDefault() + entity.H7.GetValueOrDefault() + entity.H8.GetValueOrDefault() + entity.H9.GetValueOrDefault() + entity.H10.GetValueOrDefault() + entity.H11.GetValueOrDefault() > recordMonth.AddMonths(1).AddDays(-1).Day)
            //{
            //    result.Error("请假天数不能超过月份总天数,请检查数据");
            //}
            else
            {
                if (record == null)
                {
                    record = new AttDayOffRecord();
                    record.ID = CombGuid.NewGuid();
                    record.DeptId = emp?.DeptId;
                    record.RecordMonth = entity.RecordMonth;
                    record.EnumStatus = AttDayOffRecordStatus.Committed;
                    record.EnumProphylacticStatus = ProphylacticStatus.None;
                    this.Add(record, false);

                    entity.ID = CombGuid.NewGuid();
                    entity.RecordMonth = entity.RecordMonth;
                    entity.RecordId = record.ID;
                    entity.Updator = this.OperatorUser?.DisplayName;
                    entity.UpdateTime = SysDateTime.Now;
                    entity.EnumStatus = AttDayOffRecordDetailStatus.NoApproval;
                    result.Data = this.Add(entity, false);

                    this.SaveChanges();
                }
                else
                {
                    record.EnumStatus = AttDayOffRecordStatus.Committed;
                    this.Update(record, false);

                    if (attDayOffRecordDetail != null)
                    {
                        attDayOffRecordDetail.H1 = entity.H1;
                        attDayOffRecordDetail.H2 = entity.H2;
                        attDayOffRecordDetail.H3 = entity.H3;
                        attDayOffRecordDetail.H4 = entity.H4;
                        attDayOffRecordDetail.H5 = entity.H5;
                        attDayOffRecordDetail.H6 = entity.H6;
                        attDayOffRecordDetail.H7 = entity.H7;
                        attDayOffRecordDetail.H8 = entity.H8;
                        attDayOffRecordDetail.H9 = entity.H9;
                        attDayOffRecordDetail.H10 = entity.H10;
                        attDayOffRecordDetail.H11 = entity.H11;
                        attDayOffRecordDetail.H12 = entity.H12;
                        attDayOffRecordDetail.HistoryH12 = entity.HistoryH12;

                        attDayOffRecordDetail.Updator = this.OperatorUser?.DisplayName;
                        attDayOffRecordDetail.UpdateTime = SysDateTime.Now;

                        if (attDayOffRecordDetail.EnumStatus == AttDayOffRecordDetailStatus.Pending)
                        {
                            attDayOffRecordDetail.EnumStatus = AttDayOffRecordDetailStatus.Approved;
                        }
                        else if (attDayOffRecordDetail.EnumStatus == AttDayOffRecordDetailStatus.None)
                        {
                            attDayOffRecordDetail.EnumStatus = AttDayOffRecordDetailStatus.NoApproval;
                        }

                        this.Update(ref attDayOffRecordDetail);
                        result.Data = attDayOffRecordDetail;
                    }
                    else
                    {
                        entity.ID = CombGuid.NewGuid();
                        entity.RecordMonth = entity.RecordMonth;
                        entity.RecordId = record.ID;
                        entity.Updator = this.OperatorUser?.DisplayName;
                        entity.UpdateTime = SysDateTime.Now;
                        entity.EnumStatus = AttDayOffRecordDetailStatus.NoApproval;
                        result.Data = this.Add(entity);
                    }
                }
            }

            return result;
        }

        public bool AddAttendanceHealthAllowanceSalaryChange(AttDayOffRecordDetail newEntity, AttDayOffRecordDetail dbEntity)
        {
            if (dbEntity != null && newEntity != null)
            {
                var entity = new AttendanceHealthAllowanceSalaryChange()
                {
                    ID = CombGuid.NewGuid(),
                    RecordDetailId = newEntity.ID,
                    IsCalculate = false,
                    Month = Convert.ToDateTime(newEntity.RecordMonth),
                    OldH1 = dbEntity?.H1,
                    NewH1 = newEntity.H1,
                    DifferenceH1 = newEntity.H1 - dbEntity?.H1,
                };
                this.Add(entity);
            }
            return true;
        }

        /// <summary>
        /// 考勤变更历史记录
        /// </summary>
        /// <param name="newEntity"></param>
        /// <param name="dbEntity"></param>
        /// <returns></returns>
        public bool AddAttDayOffRecordDetailSalaryChange(AttDayOffRecordDetail newEntity, AttDayOffRecordDetail dbEntity)
        {
            if (dbEntity != null && newEntity.EmployeeId.HasValue)
            {
                var entity = new AttDayOffRecordDetailSalaryChange()
                {
                    ID = CombGuid.NewGuid(),
                    RecordDetailId = newEntity.ID,
                    IsCalculate = false,
                    Month = Convert.ToDateTime(newEntity.RecordMonth),
                    OldH1 = dbEntity.H1,
                    NewH1 = newEntity.H1,
                    OldH2 = dbEntity.H2,
                    NewH2 = newEntity.H2,
                    OldH3 = dbEntity.H3,
                    NewH3 = newEntity.H3,
                    OldH4 = dbEntity.H4,
                    NewH4 = newEntity.H4,
                    OldH5 = dbEntity.H5,
                    NewH5 = newEntity.H5,
                    OldH6 = dbEntity.H6,
                    NewH6 = newEntity.H6,
                    OldH7 = dbEntity.H7,
                    NewH7 = newEntity.H7,
                    OldH8 = dbEntity.H8,
                    NewH8 = newEntity.H8
                };

                this.Add(entity, false);

                this.SaveChanges();
            }
            return true;
        }

        public DataTable SearchAttDayOffRecordDetail2(
           Guid? empListId, string? startMonth, string? endMonth, bool isNotNullHoliday, bool isNotNullSubsidy, string status, string condition)
        {
            string Proc_Name = (Config.IsDM ? "\"JHR\".usp_QueryAttDayOffRecordInfo" : "usp_QueryAttDayOffRecordInfo");

            var p1 = this.CreateParameter((Config.IsDM ? "EmpListID" : "@EmpListID"), empListId);
            var p2 = this.CreateParameter((Config.IsDM ? "startmonth" : "@startmonth"), startMonth);
            var p3 = this.CreateParameter((Config.IsDM ? "endmonth" : "@endmonth"), endMonth);
            var p4 = this.CreateParameter((Config.IsDM ? "isNotNullHoliday" : "@isNotNullHoliday"), isNotNullHoliday);
            var p5 = this.CreateParameter((Config.IsDM ? "isNotNullSubsidy" : "@isNotNullSubsidy"), isNotNullSubsidy);
            var p6 = this.CreateParameter((Config.IsDM ? "Status" : "@Status"), status);
            var p7 = this.CreateParameter((Config.IsDM ? "Condition" : "@Condition"), condition);
            var param = new[] { p1, p2, p3, p4, p5, p6, p7 };
            DataSet ds = this.ExecuteDataSet(CommandType.StoredProcedure, Proc_Name, param);

            return ds.Tables[0];
        }

        public DataTable SearchAttDayOffRecordDetail3(
            Guid? empListId, string? startMonth, string? endMonth, bool isNotNullHoliday, bool isNotNullSubsidy, string status, string condition)
        {
            string Proc_Name = (Config.IsDM ? "\"JHR\".usp_QueryAttDayOffRecordInfoEx" : "usp_QueryAttDayOffRecordInfoEx");
            var p1 = this.CreateParameter((Config.IsDM ? "EmpListID" : "@EmpListID"), empListId);
            var p2 = this.CreateParameter((Config.IsDM ? "startmonth1" : "@startmonth1"), startMonth);
            var p3 = this.CreateParameter((Config.IsDM ? "endmonth1" : "@endmonth1"), endMonth);
            var p4 = this.CreateParameter((Config.IsDM ? "isNotNullHoliday" : "@isNotNullHoliday"), isNotNullHoliday);
            var p5 = this.CreateParameter((Config.IsDM ? "isNotNullSubsidy" : "@isNotNullSubsidy"), isNotNullSubsidy);
            var p6 = this.CreateParameter((Config.IsDM ? "Status" : "@Status"), status);
            var p7 = this.CreateParameter((Config.IsDM ? "Condition" : "@Condition"), condition);
            var param = new[] { p1, p2, p3, p4, p5, p6, p7 };

            DataSet ds = this.ExecuteDataSet(CommandType.StoredProcedure, Proc_Name, param);

            return ds.Tables[0];
        }

        #endregion AttDayOff

        #region AttMonthWatch

        /// <summary>
        /// 保存一值班二值班
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<AttMonthWatchRecord> UpdateAttMonthShiftRecord(AttMonthWatchRecord entity)
        {
            var result = new BizResult<AttMonthWatchRecord>();

            var dbEntity = this.Get<AttMonthWatchRecord>(entity.ID);

            entity.Updator = this.OperatorUser?.DisplayName;
            entity.UpdateTime = SysDateTime.Now;

            DateTime recordMonthDate = DateTime.ParseExact(entity.RecordMonth + "-01", "yyyy-MM-dd", null);

            List<Guid> recordIds = new List<Guid>();
            recordIds.AddRange(new List<Guid>() { entity.EmployeeId ?? Guid.Empty });

            if (dbEntity == null || entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
                result.Data = this.Add(entity);
            }
            else
            {
                AddAttMonthWatchRecordSalaryChange(entity, dbEntity);
                result.Data = this.Update(entity);
            }
            return result;
        }

        public bool AddAttMonthWatchRecordSalaryChange(AttMonthWatchRecord attMonthWatchRecord, AttMonthWatchRecord dbEntity)
        {
            if (dbEntity != null && attMonthWatchRecord.EmployeeId.HasValue)
            {
                var entity = new AttMonthWatchRecordSalaryChange()
                {
                    ID = CombGuid.NewGuid(),
                    MonthRecordId = attMonthWatchRecord.ID,
                    IsCalculate = false,
                    Month = Convert.ToDateTime(attMonthWatchRecord.RecordMonth),
                    OldYZB = dbEntity.YZB,
                    NewYZB = attMonthWatchRecord.YZB,
                    DifferenceYZB = attMonthWatchRecord.YZB - dbEntity.YZB,
                    NewEZB = dbEntity.EZB,
                    OldEZB = dbEntity.EZB,
                    DifferenceEZB = attMonthWatchRecord.EZB - dbEntity.EZB
                };
                this.Add(entity);
            }
            return true;
        }

        #endregion AttMonthWatch

        /// <summary>
        /// 根据部门获取员工
        /// </summary>
        /// <param name="deptid"></param>
        /// <returns></returns>
        public List<Employee> GetEmployees(Guid deptId)
        {
            var entities = this.BizRepo.GetEntities<Employee>(p => p.DeptId == deptId && !p.Deleted);
            return entities;
        }

        #region 防保科考勤申报

        public BizResult<AttDayOffRecordProphylacticCase> AddAttDayOffRecordProphylactic(AttDayOffRecordProphylacticCase entityCase, List<AttDayOffRecordProphylacticDetail> details)
        {
            var result = CheckedAttDayOffRecordProphylactic(entityCase, details);
            var employeeId = entityCase.EmployeeId;
            if (result.Succeed)
            {
                var months = details.Select(p => p.RecordMonth).ToList();
                var exists = this.GetEntities<AttDayOffRecordProphylacticDetail>(p => p.EmployeeId == employeeId && months.Contains(p.RecordMonth));

                foreach (var detail in details)
                {
                    var existMonths = exists.Where(p => p.RecordMonth == detail.RecordMonth);

                    if (existMonths.Any())
                    {
                        var oldCount = existMonths.Sum(p => p.H2.GetValueOrDefault() + p.H3.GetValueOrDefault() + p.H4.GetValueOrDefault() + p.H5.GetValueOrDefault() + p.H6.GetValueOrDefault() + p.H7.GetValueOrDefault() + p.H8.GetValueOrDefault() + p.H9.GetValueOrDefault() + p.H10.GetValueOrDefault() + p.H11.GetValueOrDefault());
                        var count = detail.H2.GetValueOrDefault() + detail.H3.GetValueOrDefault() + detail.H4.GetValueOrDefault() + detail.H5.GetValueOrDefault() + detail.H6.GetValueOrDefault() + detail.H7.GetValueOrDefault() + detail.H8.GetValueOrDefault() + detail.H9.GetValueOrDefault() + detail.H10.GetValueOrDefault() + detail.H11.GetValueOrDefault();
                        var day = detail.RecordMonth.AddMonths(1).AddDays(-1).Day;

                        if (oldCount + count > day)
                        {
                            result.Error($"当前员工{detail.RecordMonth.ToString("yyyy-MM")}汇总休假天数超过月份天数。");
                        }
                    }
                }
            }

            if (result.Succeed)
            {
                var employee = this.Get<Employee>(employeeId);

                Guid attDayOffRecordProphylacticCaseId = CombGuid.NewGuid();
                Guid deptId = (employee != null && employee.DeptId.HasValue) ? employee.DeptId.Value : Guid.Empty;

                entityCase.ID = attDayOffRecordProphylacticCaseId;
                this.Add(entityCase, false);

                foreach (var detail in details)
                {
                    Guid? attDayOffRecordId = null;
                    Guid? attDayOffRecordProphylacticId = null;
                    Guid? attDayOffRecordFillingId = null;
                    var month = detail.RecordMonth.ToString("yyyy-MM");

                    var attDayOffRecord = this.GetEntity<AttDayOffRecord>(p => p.DeptId == deptId && p.RecordMonth == month);
                    if (attDayOffRecord == null)
                    {
                        attDayOffRecordId = CombGuid.NewGuid();

                        attDayOffRecord = new AttDayOffRecord()
                        {
                            ID = attDayOffRecordId.Value,
                            DeptId = deptId,
                            RecordMonth = month,
                            EnumStatus = AttDayOffRecordStatus.None,
                            EnumProphylacticStatus = ProphylacticStatus.Pending,
                        };

                        this.Add(attDayOffRecord, false);
                    }
                    else
                    {
                        attDayOffRecordId = attDayOffRecord.ID;
                        if (attDayOffRecord.EnumProphylacticStatus != ProphylacticStatus.Submitted)
                        {
                            attDayOffRecord.EnumProphylacticStatus = ProphylacticStatus.Pending;
                        }

                        this.Update(attDayOffRecord, false);
                    }

                    var attDayOffRecordProphylactic = this.GetEntity<AttDayOffRecordProphylactic>(p => p.RecordMonth == detail.RecordMonth);
                    if (attDayOffRecordProphylactic == null)
                    {
                        attDayOffRecordProphylacticId = CombGuid.NewGuid();

                        attDayOffRecordProphylactic = new AttDayOffRecordProphylactic()
                        {
                            ID = attDayOffRecordProphylacticId.Value,
                            RecordMonth = detail.RecordMonth,
                            EnumStatus = AttDayOffRecordProphylacticStatus.Pending
                        };

                        this.Add(attDayOffRecordProphylactic, false);
                    }
                    else
                    {
                        attDayOffRecordProphylacticId = attDayOffRecordProphylactic.ID;
                    }

                    // 如果已提交，则不往中间表丢数据
                    if (attDayOffRecordProphylactic.EnumStatus == AttDayOffRecordProphylacticStatus.Pending)
                    {
                        var attDayOffRecordFilling = this.GetEntity<AttDayOffRecordFilling>(p => p.RecordId == attDayOffRecordId
                                            && p.EmployeeId == employeeId && p.EnumType == AttDayOffRecordFillingType.Prophylactic);
                        if (attDayOffRecordFilling != null)
                        {
                            attDayOffRecordFillingId = attDayOffRecordFilling.ID;

                            attDayOffRecordFilling.Updator = this.OperatorUser?.DisplayName;
                            attDayOffRecordFilling.UpdateTime = DateTime.Now;
                            attDayOffRecordFilling.H2 = attDayOffRecordFilling.H2.GetValueOrDefault() + detail.H2.GetValueOrDefault();
                            attDayOffRecordFilling.H3 = attDayOffRecordFilling.H3.GetValueOrDefault() + detail.H3.GetValueOrDefault();
                            attDayOffRecordFilling.H4 = attDayOffRecordFilling.H4.GetValueOrDefault() + detail.H4.GetValueOrDefault();
                            attDayOffRecordFilling.H5 = attDayOffRecordFilling.H5.GetValueOrDefault() + detail.H5.GetValueOrDefault();
                            attDayOffRecordFilling.H6 = attDayOffRecordFilling.H6.GetValueOrDefault() + detail.H6.GetValueOrDefault();
                            attDayOffRecordFilling.H7 = attDayOffRecordFilling.H7.GetValueOrDefault() + detail.H7.GetValueOrDefault();
                            attDayOffRecordFilling.H8 = attDayOffRecordFilling.H8.GetValueOrDefault() + detail.H8.GetValueOrDefault();
                            attDayOffRecordFilling.H9 = attDayOffRecordFilling.H9.GetValueOrDefault() + detail.H9.GetValueOrDefault();
                            attDayOffRecordFilling.H10 = attDayOffRecordFilling.H10.GetValueOrDefault() + detail.H10.GetValueOrDefault();
                            attDayOffRecordFilling.H11 = attDayOffRecordFilling.H11.GetValueOrDefault() + detail.H11.GetValueOrDefault();

                            this.Update(attDayOffRecordFilling, false);
                        }
                        else
                        {
                            attDayOffRecordFillingId = CombGuid.NewGuid();

                            attDayOffRecordFilling = new AttDayOffRecordFilling()
                            {
                                ID = attDayOffRecordFillingId.Value,
                                RecordId = attDayOffRecordId.Value,
                                EmployeeId = employeeId,
                                RecordMonth = detail.RecordMonth,
                                EnumType = AttDayOffRecordFillingType.Prophylactic,   //   重要字段
                                Updator = this.OperatorUser?.DisplayName,
                                UpdateTime = DateTime.Now,
                                H2 = detail.H2,
                                H3 = detail.H3,
                                H4 = detail.H4,
                                H5 = detail.H5,
                                H6 = detail.H6,
                                H7 = detail.H7,
                                H8 = detail.H8,
                                H9 = detail.H9,
                                H10 = detail.H10,
                                H11 = detail.H11,
                            };

                            // 把上月津贴带入本月津贴，提交时用
                            var preMonth = detail.RecordMonth.AddMonths(-1).ToString("yyyy-MM");
                            var preAttDayOffRecordDetail = this.GetEntity<AttDayOffRecordDetail>(p => p.AttDayOffRecord != null && p.AttDayOffRecord.RecordMonth == preMonth && p.EmployeeId == employeeId
                                                                                && p.EnumStatus != AttDayOffRecordDetailStatus.None && p.EnumStatus != AttDayOffRecordDetailStatus.Pending);
                            if (preAttDayOffRecordDetail != null)
                            {
                                attDayOffRecordFilling.H1 = preAttDayOffRecordDetail.H1;
                            }

                            this.Add(attDayOffRecordFilling, false);
                        }
                    }

                    // 保存业务表数据
                    if (detail.ID.IsEmpty())
                    {
                        detail.ID = CombGuid.NewGuid();
                    }
                    detail.RecordId = attDayOffRecordId.Value;
                    detail.AttDayOffRecordProphylacticId = attDayOffRecordProphylacticId.Value;
                    detail.AttDayOffRecordProphylacticCaseId = attDayOffRecordProphylacticCaseId;
                    detail.AttDayOffRecordFillingId = attDayOffRecordFillingId;
                    detail.EmployeeId = employeeId;
                    detail.Updator = this.OperatorUser?.DisplayName;
                    detail.UpdateTime = DateTime.Now;
                    detail.EnumStatus = AttDayOffRecordProphylacticDetailStatus.Pending;

                    this.Add(detail, false);
                }

                this.SaveChanges();

                result.Data = entityCase;
            }

            return result;
        }

        #region 编辑数据检验

        private BizResult<AttDayOffRecordProphylacticCase> CheckedAttDayOffRecordProphylactic(AttDayOffRecordProphylacticCase entity, List<AttDayOffRecordProphylacticDetail> details, Guid? currentGuid = null)
        {
            var result = new BizResult<AttDayOffRecordProphylacticCase>();

            List<AttDayOffRecordProphylacticCase> cases = new List<AttDayOffRecordProphylacticCase>();

            if (entity.EmployeeId.IsEmpty())
            {
                result.Error("请选择员工");
            }
            else
            {
                var employee = this.Get<Employee>(entity.EmployeeId);

                if (employee == null)
                {
                    result.Error("员工不存在");
                }
                else if (!employee.DeptId.HasValue)
                {
                    result.Error("请先设置当前员工的部门");
                }

                cases = this.GetEntities<AttDayOffRecordProphylacticCase>(x => x.EmployeeId == entity.EmployeeId);
            }

            if (entity.EnumLeaveType == LeaveType.None)
            {
                result.Error("请假类别必选");
            }

            if (entity.DiagnostiOpinion.IsEmpty())
            {
                result.Error("诊断意见必填");
            }

            if (entity.VisitingHospital.IsEmpty())
            {
                result.Error("就诊医院必填");
            }

            if (entity.EnumHolidayType == HolidayType.None)
            {
                result.Error("假期类型必选");
            }
            else if (entity.EnumHolidayType == HolidayType.Other && entity.HolidayRemark.IsEmpty())
            {
                result.Error("选择其他假期类型时，必须添加假期类型备注");
            }

            if (entity.LeaveStartDate.IsEmpty() || entity.LeaveEndDate.IsEmpty())
            {
                result.Error("休假开始、结束时间必填");
            }
            else if (entity.LeaveStartDate > entity.LeaveEndDate)
            {
                result.Error("休假开始时间不能大于结束时间");
            }
            else
            {
                if (cases.Count > 0)
                {
                    if (currentGuid.HasValue)
                    {
                        if (cases.Any(x => x.ID != currentGuid.Value && ((x.LeaveStartDate <= entity.LeaveStartDate && x.LeaveEndDate >= entity.LeaveStartDate) || (x.LeaveStartDate <= entity.LeaveEndDate && x.LeaveEndDate >= entity.LeaveEndDate))))
                        {
                            result.Error("休假开始、结束时间有冲突，请更改时间");
                        }
                    }
                    else
                    {
                        if (cases.Any(x => (x.LeaveStartDate <= entity.LeaveStartDate && x.LeaveEndDate >= entity.LeaveStartDate) || (x.LeaveStartDate <= entity.LeaveEndDate && x.LeaveEndDate >= entity.LeaveEndDate)))
                        {
                            result.Error("休假开始、结束时间有冲突，请更改时间");
                        }
                    }
                }
            }

            if (entity.IssuingTime.IsEmpty())
            {
                result.Error("开具时间必填");
            }

            if (details == null || !details.Any())
            {
                result.Error("没有休假详情");
            }
            else
            {
                if (details.Any(p => p.RecordMonth.IsEmpty()))
                {
                    result.Error("明细中有月份为空的数据，请刷新页面");
                }

                if (details.Any(p => p.H2 < 0))
                {
                    result.Error("病假必须大于等于0");
                }

                if (details.Any(p => p.H3 < 0))
                {
                    result.Error("事假必须大于等于0");
                }

                if (details.Any(p => p.H4 < 0))
                {
                    result.Error("产假必须大于等于0");
                }

                if (details.Any(p => p.H5 < 0))
                {
                    result.Error("哺乳假必须大于等于0");
                }

                if (details.Any(p => p.H6 < 0))
                {
                    result.Error("探亲假必须大于等于0");
                }

                if (details.Any(p => p.H7 < 0))
                {
                    result.Error("计生假必须大于等于0");
                }

                if (details.Any(p => p.H8 < 0))
                {
                    result.Error("婚丧假必须大于等于0");
                }

                if (details.Any(p => p.H9 < 0))
                {
                    result.Error("脱产读研必须大于等于0");
                }

                if (details.Any(p => p.H10 < 0))
                {
                    result.Error("因公出国必须大于等于0");
                }

                if (details.Any(p => p.H11 < 0))
                {
                    result.Error("因私出国必须大于等于0");
                }

                if (details.Any(p =>
                    (!p.H2.HasValue || p.H2.Value == 0) &&
                    (!p.H3.HasValue || p.H3.Value == 0) &&
                    (!p.H4.HasValue || p.H4.Value == 0) &&
                    (!p.H5.HasValue || p.H5.Value == 0) &&
                    (!p.H6.HasValue || p.H6.Value == 0) &&
                    (!p.H7.HasValue || p.H7.Value == 0) &&
                    (!p.H8.HasValue || p.H8.Value == 0) &&
                    (!p.H9.HasValue || p.H9.Value == 0) &&
                    (!p.H10.HasValue || p.H10.Value == 0) &&
                    (!p.H11.HasValue || p.H11.Value == 0)
                ))
                {
                    result.Error("明细中有休假天数为空的月份，请补充后再提交");
                }

                if (result.Succeed)
                {
                    foreach (var detail in details)
                    {
                        if ((detail.H2.GetValueOrDefault() + detail.H3.GetValueOrDefault() + detail.H4.GetValueOrDefault() + detail.H5.GetValueOrDefault() + detail.H6.GetValueOrDefault() + detail.H7.GetValueOrDefault() + detail.H8.GetValueOrDefault() + detail.H9.GetValueOrDefault() + detail.H10.GetValueOrDefault() + detail.H11.GetValueOrDefault()) > detail.RecordMonth.AddMonths(1).AddDays(-1).Day)
                        {
                            result.Error($"{detail.RecordMonth.ToString("yyyy-MM")}请假天数不能超过月份总天数");
                        }
                    }
                }
            }

            return result;
        }

        #endregion 编辑数据检验

        public BizResult<AttDayOffRecordProphylacticCase> UpdateAttDayOffRecordProphylactic(AttDayOffRecordProphylacticCase entityCase, List<AttDayOffRecordProphylacticDetail> details)
        {
            var result = new BizResult<AttDayOffRecordProphylacticCase>();
            var dbEntity = this.Get<AttDayOffRecordProphylacticCase>(entityCase.ID);
            if (dbEntity == null)
            {
                result.Error("防保科考勤申报数据不存在");
            }
            else
            {
                result = CheckedAttDayOffRecordProphylactic(entityCase, details, entityCase.ID);
                var employeeId = entityCase.EmployeeId;

                if (result.Succeed)
                {
                    var months = details.Select(p => p.RecordMonth).ToList();
                    var detailIds = details.Select(p => p.ID).ToList();
                    var exists = this.GetEntities<AttDayOffRecordProphylacticDetail>(p => p.EmployeeId == employeeId && months.Contains(p.RecordMonth) && !detailIds.Contains(p.ID));

                    foreach (var detail in details)
                    {
                        var existMonths = exists.Where(p => p.RecordMonth == detail.RecordMonth);

                        if (existMonths.Any())
                        {
                            var oldCount = existMonths.Sum(p => p.H2.GetValueOrDefault() + p.H3.GetValueOrDefault() + p.H4.GetValueOrDefault() + p.H5.GetValueOrDefault() + p.H6.GetValueOrDefault() + p.H7.GetValueOrDefault() + p.H8.GetValueOrDefault() + p.H9.GetValueOrDefault() + p.H10.GetValueOrDefault() + p.H11.GetValueOrDefault());
                            var count = detail.H2.GetValueOrDefault() + detail.H3.GetValueOrDefault() + detail.H4.GetValueOrDefault() + detail.H5.GetValueOrDefault() + detail.H6.GetValueOrDefault() + detail.H7.GetValueOrDefault() + detail.H8.GetValueOrDefault() + detail.H9.GetValueOrDefault() + detail.H10.GetValueOrDefault() + detail.H11.GetValueOrDefault();
                            var day = detail.RecordMonth.AddMonths(1).AddDays(-1).Day;

                            if (oldCount + count > day)
                            {
                                result.Error($"当前员工{detail.RecordMonth.ToString("yyyy-MM")}汇总休假天数超过月份天数。");
                            }
                        }
                    }
                }

                if (result.Succeed)
                {
                    var employee = this.Get<Employee>(employeeId);

                    Guid attDayOffRecordProphylacticCaseId = entityCase.ID;
                    Guid deptId = (employee != null && employee.DeptId.HasValue) ? employee.DeptId.Value : Guid.Empty;

                    entityCase.RemoveChangedColumn(AttDayOffRecordProphylacticCase.Columns.EmployeeId);
                    this.Update(entityCase, false);

                    foreach (var detail in details)
                    {
                        Guid? attDayOffRecordId = null;
                        Guid? attDayOffRecordProphylacticId = null;
                        Guid? attDayOffRecordFillingId = null;
                        var month = detail.RecordMonth.ToString("yyyy-MM");

                        var attDayOffRecord = this.GetEntity<AttDayOffRecord>(p => p.DeptId == deptId && p.RecordMonth == month);
                        if (attDayOffRecord == null)
                        {
                            attDayOffRecordId = CombGuid.NewGuid();

                            attDayOffRecord = new AttDayOffRecord()
                            {
                                ID = attDayOffRecordId.Value,
                                DeptId = deptId,
                                RecordMonth = month,
                                EnumStatus = AttDayOffRecordStatus.None,
                                EnumProphylacticStatus = ProphylacticStatus.Pending,
                            };

                            this.Add(attDayOffRecord, false);
                        }
                        else
                        {
                            attDayOffRecordId = attDayOffRecord.ID;
                            if (attDayOffRecord.EnumProphylacticStatus != ProphylacticStatus.Submitted)
                            {
                                attDayOffRecord.EnumProphylacticStatus = ProphylacticStatus.Pending;
                            }

                            this.Update(attDayOffRecord, false);
                        }

                        var attDayOffRecordProphylactic = this.GetEntity<AttDayOffRecordProphylactic>(p => p.RecordMonth == detail.RecordMonth);
                        if (attDayOffRecordProphylactic == null)
                        {
                            attDayOffRecordProphylacticId = CombGuid.NewGuid();

                            attDayOffRecordProphylactic = new AttDayOffRecordProphylactic()
                            {
                                ID = attDayOffRecordProphylacticId.Value,
                                RecordMonth = detail.RecordMonth,
                                EnumStatus = AttDayOffRecordProphylacticStatus.Pending
                            };

                            this.Add(attDayOffRecordProphylactic, false);
                        }
                        else
                        {
                            attDayOffRecordProphylacticId = attDayOffRecordProphylactic.ID;
                        }

                        // 保存业务表数据
                        if (detail.ID.IsEmpty())
                        {
                            // 如果已提交，则不往中间表丢数据
                            if (attDayOffRecordProphylactic.EnumStatus == AttDayOffRecordProphylacticStatus.Pending)
                            {
                                var attDayOffRecordFilling = this.GetEntity<AttDayOffRecordFilling>(p => p.RecordId == attDayOffRecordId
                                                    && p.EmployeeId == employeeId && p.EnumType == AttDayOffRecordFillingType.Prophylactic);
                                if (attDayOffRecordFilling != null)
                                {
                                    attDayOffRecordFillingId = attDayOffRecordFilling.ID;

                                    attDayOffRecordFilling.Updator = this.OperatorUser?.DisplayName;
                                    attDayOffRecordFilling.UpdateTime = DateTime.Now;
                                    attDayOffRecordFilling.H2 = attDayOffRecordFilling.H2.GetValueOrDefault() + detail.H2.GetValueOrDefault();
                                    attDayOffRecordFilling.H3 = attDayOffRecordFilling.H3.GetValueOrDefault() + detail.H3.GetValueOrDefault();
                                    attDayOffRecordFilling.H4 = attDayOffRecordFilling.H4.GetValueOrDefault() + detail.H4.GetValueOrDefault();
                                    attDayOffRecordFilling.H5 = attDayOffRecordFilling.H5.GetValueOrDefault() + detail.H5.GetValueOrDefault();
                                    attDayOffRecordFilling.H6 = attDayOffRecordFilling.H6.GetValueOrDefault() + detail.H6.GetValueOrDefault();
                                    attDayOffRecordFilling.H7 = attDayOffRecordFilling.H7.GetValueOrDefault() + detail.H7.GetValueOrDefault();
                                    attDayOffRecordFilling.H8 = attDayOffRecordFilling.H8.GetValueOrDefault() + detail.H8.GetValueOrDefault();
                                    attDayOffRecordFilling.H9 = attDayOffRecordFilling.H9.GetValueOrDefault() + detail.H9.GetValueOrDefault();
                                    attDayOffRecordFilling.H10 = attDayOffRecordFilling.H10.GetValueOrDefault() + detail.H10.GetValueOrDefault();
                                    attDayOffRecordFilling.H11 = attDayOffRecordFilling.H11.GetValueOrDefault() + detail.H11.GetValueOrDefault();

                                    this.Update(attDayOffRecordFilling, false);
                                }
                                else
                                {
                                    attDayOffRecordFillingId = CombGuid.NewGuid();

                                    attDayOffRecordFilling = new AttDayOffRecordFilling()
                                    {
                                        ID = attDayOffRecordFillingId.Value,
                                        RecordId = attDayOffRecordId.Value,
                                        EmployeeId = employeeId,
                                        RecordMonth = detail.RecordMonth,
                                        EnumType = AttDayOffRecordFillingType.Prophylactic,   //   重要字段
                                        Updator = this.OperatorUser?.DisplayName,
                                        UpdateTime = DateTime.Now,
                                        H2 = detail.H2,
                                        H3 = detail.H3,
                                        H4 = detail.H4,
                                        H5 = detail.H5,
                                        H6 = detail.H6,
                                        H7 = detail.H7,
                                        H8 = detail.H8,
                                        H9 = detail.H9,
                                        H10 = detail.H10,
                                        H11 = detail.H11,
                                    };

                                    // 把上月津贴带入本月津贴，提交时用
                                    var preMonth = detail.RecordMonth.AddMonths(-1).ToString("yyyy-MM");
                                    var preAttDayOffRecordDetail = this.GetEntity<AttDayOffRecordDetail>(p => p.AttDayOffRecord != null && p.AttDayOffRecord.RecordMonth == preMonth && p.EmployeeId == employeeId
                                                                                        && p.EnumStatus != AttDayOffRecordDetailStatus.None && p.EnumStatus != AttDayOffRecordDetailStatus.Pending);
                                    if (preAttDayOffRecordDetail != null)
                                    {
                                        attDayOffRecordFilling.H1 = preAttDayOffRecordDetail.H1;
                                    }

                                    this.Add(attDayOffRecordFilling, false);
                                }
                            }

                            detail.ID = CombGuid.NewGuid();
                            detail.RecordId = attDayOffRecordId.Value;
                            detail.AttDayOffRecordProphylacticId = attDayOffRecordProphylacticId.Value;
                            detail.AttDayOffRecordProphylacticCaseId = attDayOffRecordProphylacticCaseId;
                            detail.AttDayOffRecordFillingId = attDayOffRecordFillingId;
                            detail.EmployeeId = employeeId;
                            detail.Updator = this.OperatorUser?.DisplayName;
                            detail.UpdateTime = DateTime.Now;
                            detail.EnumStatus = AttDayOffRecordProphylacticDetailStatus.Pending;

                            this.Add(detail, false);
                        }
                        else
                        {
                            var attDayOffRecordFilling = this.Get<AttDayOffRecordFilling>(detail.AttDayOffRecordFillingId);
                            var dbDetail = this.GetEntity<AttDayOffRecordProphylacticDetail>(p => p.ID == detail.ID);
                            if (dbDetail?.EnumStatus == AttDayOffRecordProphylacticDetailStatus.Pending && attDayOffRecordFilling != null
                                    && attDayOffRecordProphylactic.EnumStatus == AttDayOffRecordProphylacticStatus.Pending)
                            {
                                attDayOffRecordFilling.Updator = this.OperatorUser?.DisplayName;
                                attDayOffRecordFilling.UpdateTime = DateTime.Now;
                                attDayOffRecordFilling.H2 = attDayOffRecordFilling.H2.GetValueOrDefault() - dbDetail.H2.GetValueOrDefault() + detail.H2.GetValueOrDefault();
                                attDayOffRecordFilling.H3 = attDayOffRecordFilling.H3.GetValueOrDefault() - dbDetail.H3.GetValueOrDefault() + detail.H3.GetValueOrDefault();
                                attDayOffRecordFilling.H4 = attDayOffRecordFilling.H4.GetValueOrDefault() - dbDetail.H4.GetValueOrDefault() + detail.H4.GetValueOrDefault();
                                attDayOffRecordFilling.H5 = attDayOffRecordFilling.H5.GetValueOrDefault() - dbDetail.H5.GetValueOrDefault() + detail.H5.GetValueOrDefault();
                                attDayOffRecordFilling.H6 = attDayOffRecordFilling.H6.GetValueOrDefault() - dbDetail.H6.GetValueOrDefault() + detail.H6.GetValueOrDefault();
                                attDayOffRecordFilling.H7 = attDayOffRecordFilling.H7.GetValueOrDefault() - dbDetail.H7.GetValueOrDefault() + detail.H7.GetValueOrDefault();
                                attDayOffRecordFilling.H8 = attDayOffRecordFilling.H8.GetValueOrDefault() - dbDetail.H8.GetValueOrDefault() + detail.H8.GetValueOrDefault();
                                attDayOffRecordFilling.H9 = attDayOffRecordFilling.H9.GetValueOrDefault() - dbDetail.H9.GetValueOrDefault() + detail.H9.GetValueOrDefault();
                                attDayOffRecordFilling.H10 = attDayOffRecordFilling.H10.GetValueOrDefault() - dbDetail.H10.GetValueOrDefault() + detail.H10.GetValueOrDefault();
                                attDayOffRecordFilling.H11 = attDayOffRecordFilling.H11.GetValueOrDefault() - dbDetail.H11.GetValueOrDefault() + detail.H11.GetValueOrDefault();

                                this.Update(attDayOffRecordFilling, false);
                            }

                            detail.RemoveChangedColumn(AttDayOffRecordProphylacticDetail.Columns.RecordId);
                            detail.RemoveChangedColumn(AttDayOffRecordProphylacticDetail.Columns.AttDayOffRecordFillingId);
                            detail.RemoveChangedColumn(AttDayOffRecordProphylacticDetail.Columns.EmployeeId);
                            detail.RemoveChangedColumn(AttDayOffRecordProphylacticDetail.Columns.RecordMonth);
                            detail.RemoveChangedColumn(AttDayOffRecordProphylacticDetail.Columns.EnumStatus);

                            detail.Updator = this.OperatorUser?.DisplayName;
                            detail.UpdateTime = DateTime.Now;
                            this.Update(detail, false);
                        }
                    }

                    this.SaveChanges();

                    result.Data = entityCase;
                }
            }

            return result;
        }

        public BizResult DeleteAttDayOffRecordProphylacticDetail(Guid id)
        {
            var result = new BizResult();
            var dbEntity = this.Get<AttDayOffRecordProphylacticDetail>(id);

            if (dbEntity == null)
            {
                result.Error("当前防保科考勤申报数据不存在");
            }
            else
            {
                if (dbEntity.EnumStatus == AttDayOffRecordProphylacticDetailStatus.Submitted)
                {
                    result.Error("当前为已提交数据，不允许删除");
                }

                if (result.Succeed)
                {
                    var entityCase = this.GetEntity<AttDayOffRecordProphylacticCase>(x => x.ID == dbEntity.AttDayOffRecordProphylacticCaseId);
                    if (entityCase != null)
                    {
                        var entitysDetail = this.GetEntities<AttDayOffRecordProphylacticDetail>(x => entityCase.ID == x.AttDayOffRecordProphylacticCaseId && x.ID != dbEntity.ID);
                        if (entitysDetail == null || entitysDetail.Count == 0)
                        {
                            this.Delete(entityCase);
                        }
                    }

                    var attDayOffRecordProphylactic = this.Get<AttDayOffRecordProphylactic>(dbEntity.AttDayOffRecordProphylacticId);

                    var attDayOffRecordFilling = this.Get<AttDayOffRecordFilling>(dbEntity.AttDayOffRecordFillingId);
                    if (attDayOffRecordFilling != null && attDayOffRecordProphylactic?.EnumStatus == AttDayOffRecordProphylacticStatus.Pending)
                    {
                        attDayOffRecordFilling.Updator = this.OperatorUser?.DisplayName;
                        attDayOffRecordFilling.UpdateTime = SysDateTime.Now;
                        attDayOffRecordFilling.H2 = attDayOffRecordFilling.H2.GetValueOrDefault() - dbEntity.H2.GetValueOrDefault();
                        attDayOffRecordFilling.H3 = attDayOffRecordFilling.H3.GetValueOrDefault() - dbEntity.H3.GetValueOrDefault();
                        attDayOffRecordFilling.H4 = attDayOffRecordFilling.H4.GetValueOrDefault() - dbEntity.H4.GetValueOrDefault();
                        attDayOffRecordFilling.H5 = attDayOffRecordFilling.H5.GetValueOrDefault() - dbEntity.H5.GetValueOrDefault();
                        attDayOffRecordFilling.H6 = attDayOffRecordFilling.H6.GetValueOrDefault() - dbEntity.H6.GetValueOrDefault();
                        attDayOffRecordFilling.H7 = attDayOffRecordFilling.H7.GetValueOrDefault() - dbEntity.H7.GetValueOrDefault();
                        attDayOffRecordFilling.H8 = attDayOffRecordFilling.H8.GetValueOrDefault() - dbEntity.H8.GetValueOrDefault();
                        attDayOffRecordFilling.H9 = attDayOffRecordFilling.H9.GetValueOrDefault() - dbEntity.H9.GetValueOrDefault();
                        attDayOffRecordFilling.H10 = attDayOffRecordFilling.H10.GetValueOrDefault() - dbEntity.H10.GetValueOrDefault();
                        attDayOffRecordFilling.H11 = attDayOffRecordFilling.H11.GetValueOrDefault() - dbEntity.H11.GetValueOrDefault();

                        this.Update(attDayOffRecordFilling, false);
                    }

                    this.Delete(dbEntity);
                }
            }

            return result;
        }

        public BizResult<AttDayOffRecordProphylactic> SubjectAttDayOffRecordProphylactic(AttDayOffRecordProphylactic entity)
        {
            var result = new BizResult<AttDayOffRecordProphylactic>();
            var dbEntity = this.Get<AttDayOffRecordProphylactic>(entity.ID);
            if (dbEntity == null)
            {
                result.Error("请先新增防保科考勤申报数据");
            }
            else
            {
                if (dbEntity.EnumStatus == AttDayOffRecordProphylacticStatus.Submitted)
                {
                    result.Error("当前月份数据已被提交，请刷新页面");
                }

                if (dbEntity.RecordMonth.Date != SysDateTime.Now.Date.AddDays(1 - SysDateTime.Now.Date.Day).AddMonths(-1))
                {
                    result.Error("只能提交上月的防保科考勤申报数据");
                }

                var details = this.GetEntities<AttDayOffRecordProphylacticDetail>(p => p.AttDayOffRecordProphylacticId == entity.ID);

                if (details == null || !details.Any())
                {
                    result.Error("请先新增防保科考勤申报数据");
                }

                var errorDetails = details!.Where(p =>
                    (!p.H2.HasValue || p.H2.Value == 0) &&
                    (!p.H3.HasValue || p.H3.Value == 0) &&
                    (!p.H4.HasValue || p.H4.Value == 0) &&
                    (!p.H5.HasValue || p.H5.Value == 0) &&
                    (!p.H6.HasValue || p.H6.Value == 0) &&
                    (!p.H7.HasValue || p.H7.Value == 0) &&
                    (!p.H8.HasValue || p.H8.Value == 0) &&
                    (!p.H9.HasValue || p.H9.Value == 0) &&
                    (!p.H10.HasValue || p.H10.Value == 0) &&
                    (!p.H11.HasValue || p.H11.Value == 0)
                ).ToList();

                if (errorDetails.Any())
                {
                    foreach (var item in errorDetails)
                    {
                        result.Error($"({item.Employee.EmpCode}){item.Employee.DisplayName} 请假天数缺失，请补充后再提交");
                    }
                }

                if (result.Succeed)
                {
                    var time = SysDateTime.Now;

                    var strMonth = entity.RecordMonth.ToString("yyyy-MM");

                    // 查询防保科填报汇总数据
                    var attProphylacticFillings = this.GetEntities<AttDayOffRecordFilling>("AttDayOffRecord", p => p.RecordMonth == dbEntity.RecordMonth && p.EnumType == AttDayOffRecordFillingType.Prophylactic);

                    //查询正式明细数据
                    var attDayOffRecordDetails = this.GetEntities<AttDayOffRecordDetail>(p => p.RecordMonth == strMonth);

                    // 查询所有主表
                    var attDayOffRecords = attProphylacticFillings.Select(p => p.AttDayOffRecord).Distinct().ToList();

                    foreach (var item in details!)
                    {
                        item.UpdateTime = time;

                        item.EnumStatus = AttDayOffRecordProphylacticDetailStatus.Submitted;

                        this.Update(item, false);
                    }

                    foreach (var item in attDayOffRecords)
                    {
                        item.EnumProphylacticStatus = ProphylacticStatus.Submitted;

                        this.Update(item, false);
                    }

                    foreach (var item in attProphylacticFillings)
                    {
                        var attDayOffRecordDetail = attDayOffRecordDetails.FirstOrDefault(p => p.EmployeeId == item.EmployeeId);
                        if (attDayOffRecordDetail == null)
                        {
                            var detail = new AttDayOffRecordDetail()
                            {
                                ID = CombGuid.NewGuid(),
                                RecordId = item.RecordId,
                                EmployeeId = item.EmployeeId,
                                RecordMonth = strMonth,
                                Updator = this.OperatorUser?.DisplayName,
                                UpdateTime = time,
                                H1 = item.H1,
                                H2 = item.H2,
                                H3 = item.H3,
                                H4 = item.H4,
                                H5 = item.H5,
                                H6 = item.H6,
                                H7 = item.H7,
                                H8 = item.H8,
                                H9 = item.H9,
                                H10 = item.H10,
                                H11 = item.H11,
                                H12 = item.H12,
                                HistoryH12 = item.HistoryH12,
                                EnumStatus = AttDayOffRecordDetailStatus.NoApproval,
                            };

                            this.Add(detail, false);
                        }
                        else
                        {
                            // 如果一致，则无需审批，如果出现不一致，则改为待审批,如果位none  则为废数据，重新启用
                            if (attDayOffRecordDetail.EnumStatus == AttDayOffRecordDetailStatus.None)
                            {
                                attDayOffRecordDetail.H1 = item.H1;
                                attDayOffRecordDetail.H2 = item.H2;
                                attDayOffRecordDetail.H3 = item.H3;
                                attDayOffRecordDetail.H4 = item.H4;
                                attDayOffRecordDetail.H5 = item.H5;
                                attDayOffRecordDetail.H6 = item.H6;
                                attDayOffRecordDetail.H7 = item.H7;
                                attDayOffRecordDetail.H8 = item.H8;
                                attDayOffRecordDetail.H9 = item.H9;
                                attDayOffRecordDetail.H10 = item.H10;
                                attDayOffRecordDetail.H11 = item.H11;
                                attDayOffRecordDetail.H12 = item.H12;
                                attDayOffRecordDetail.HistoryH12 = item.HistoryH12;
                                attDayOffRecordDetail.EnumStatus = AttDayOffRecordDetailStatus.NoApproval;

                                attDayOffRecordDetail.Updator = this.OperatorUser?.DisplayName;
                                attDayOffRecordDetail.UpdateTime = time;
                            }
                            else
                            {
                                attDayOffRecordDetail.Updator = this.OperatorUser?.DisplayName;
                                attDayOffRecordDetail.UpdateTime = time;
                                // 考勤员先提交，attDayOffRecordDetail 考勤员为数据；防保科再提交，item防保科数据
                                // 比较 如果总请假天数相同，以防保科为准；如果总请假天数不同，以总请假天数多的为准
                                decimal dK = attDayOffRecordDetail.H2.GetValueOrDefault() + attDayOffRecordDetail.H3.GetValueOrDefault() +
                                    attDayOffRecordDetail.H4.GetValueOrDefault() + attDayOffRecordDetail.H5.GetValueOrDefault() +
                                    attDayOffRecordDetail.H6.GetValueOrDefault() + attDayOffRecordDetail.H7.GetValueOrDefault() +
                                    attDayOffRecordDetail.H8.GetValueOrDefault() + attDayOffRecordDetail.H9.GetValueOrDefault() +
                                    attDayOffRecordDetail.H10.GetValueOrDefault() + attDayOffRecordDetail.H11.GetValueOrDefault();

                                decimal dF = item.H2.GetValueOrDefault() + item.H3.GetValueOrDefault() +
                                    item.H4.GetValueOrDefault() + item.H5.GetValueOrDefault() +
                                    item.H6.GetValueOrDefault() + item.H7.GetValueOrDefault() +
                                    item.H8.GetValueOrDefault() + item.H9.GetValueOrDefault() +
                                    item.H10.GetValueOrDefault() + item.H11.GetValueOrDefault();

                                if (dF >= dK)
                                {
                                    attDayOffRecordDetail.H2 = item.H2;
                                    attDayOffRecordDetail.H3 = item.H3;
                                    attDayOffRecordDetail.H4 = item.H4;
                                    attDayOffRecordDetail.H5 = item.H5;
                                    attDayOffRecordDetail.H6 = item.H6;
                                    attDayOffRecordDetail.H7 = item.H7;
                                    attDayOffRecordDetail.H8 = item.H8;
                                    attDayOffRecordDetail.H9 = item.H9;
                                    attDayOffRecordDetail.H10 = item.H10;
                                    attDayOffRecordDetail.H11 = item.H11;
                                    attDayOffRecordDetail.H12 = item.H12;
                                    attDayOffRecordDetail.HistoryH12 = item.HistoryH12;
                                }
                                attDayOffRecordDetail.EnumStatus = AttDayOffRecordDetailStatus.Approved;

                                //// 考勤员先提交，attDayOffRecordDetail 考勤员为数据；防保科再提交，item防保科数据
                                //// 比较 如果请假天数相同，审批通过，不相同，等待人事审批
                                //if (attDayOffRecordDetail.H2.GetValueOrDefault() != item.H2.GetValueOrDefault() || attDayOffRecordDetail.H3.GetValueOrDefault() != item.H3.GetValueOrDefault() ||
                                //attDayOffRecordDetail.H4.GetValueOrDefault() != item.H4.GetValueOrDefault() || attDayOffRecordDetail.H5.GetValueOrDefault() != item.H5.GetValueOrDefault() ||
                                //attDayOffRecordDetail.H6.GetValueOrDefault() != item.H6.GetValueOrDefault() || attDayOffRecordDetail.H7.GetValueOrDefault() != item.H7.GetValueOrDefault() ||
                                //attDayOffRecordDetail.H8.GetValueOrDefault() != item.H8.GetValueOrDefault() || attDayOffRecordDetail.H9.GetValueOrDefault() != item.H9.GetValueOrDefault() ||
                                //attDayOffRecordDetail.H10.GetValueOrDefault() != item.H10.GetValueOrDefault() || attDayOffRecordDetail.H11.GetValueOrDefault() != item.H11.GetValueOrDefault())
                                //{
                                //    attDayOffRecordDetail.EnumStatus = AttDayOffRecordDetailStatus.Pending;
                                //}
                                //else
                                //{
                                //    attDayOffRecordDetail.EnumStatus = AttDayOffRecordDetailStatus.Approved;
                                //}
                            }

                            this.Update(attDayOffRecordDetail, false);
                        }
                    }

                    dbEntity.SubmitUserId = this.OperatorUser?.ID;
                    dbEntity.SubmitTime = time;
                    dbEntity.EnumStatus = AttDayOffRecordProphylacticStatus.Submitted;
                    this.Update(dbEntity, false);

                    this.SaveChanges();
                }
            }

            return result;
        }

        #endregion 防保科考勤申报
    }
}