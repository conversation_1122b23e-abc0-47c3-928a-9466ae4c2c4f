﻿using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Renji.JHR.Entities
{
    public partial class Permission : IParent<Permission>, IOrder
    {
        #region IParent

        IComparable IParent.ID => this.ID;

        IComparable? IParent.ParentId => this.ParentId;

        #endregion IParent

        #region IOrder

        IComparable IOrder.Order => this.Sequence;

        #endregion IOrder
    }
}