(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3251fe36"],{"06c5":function(t,e,a){"use strict";a.d(e,"a",(function(){return r}));a("a630"),a("fb6a"),a("b0c0"),a("d3b7"),a("25f0"),a("3ca3");var n=a("6b75");function r(t,e){if(t){if("string"===typeof t)return Object(n["a"])(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?Object(n["a"])(t,e):void 0}}},3835:function(t,e,a){"use strict";function n(t){if(Array.isArray(t))return t}a.d(e,"a",(function(){return l}));a("a4d3"),a("e01a"),a("d28b"),a("d3b7"),a("3ca3"),a("ddb0");function r(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var a=[],n=!0,r=!1,o=void 0;try{for(var i,l=t[Symbol.iterator]();!(n=(i=l.next()).done);n=!0)if(a.push(i.value),e&&a.length===e)break}catch(c){r=!0,o=c}finally{try{n||null==l["return"]||l["return"]()}finally{if(r)throw o}}return a}}var o=a("06c5");function i(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(t,e){return n(t)||r(t,e)||Object(o["a"])(t,e)||i()}},"6b75":function(t,e,a){"use strict";function n(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,n=new Array(e);a<e;a++)n[a]=t[a];return n}a.d(e,"a",(function(){return n}))},7869:function(t,e,a){},a859:function(t,e,a){"use strict";var n=a("7869"),r=a.n(n);r.a},a8f9:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("layout3",{scopedSlots:t._u([{key:"aside",fn:function(){return[a("c-tree",{attrs:{options:t.treeData,props:t.treeProps,"expanded-keys":t.treeExpandedKeys},on:{nodeClick:t.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.headModel}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"input_class",attrs:{label:"月份"}},[a("el-date-picker",{attrs:{type:"month",placeholder:"请选择申领月份","value-format":"yyyy-MM"},on:{change:t.dateChange},model:{value:t.recordMonth,callback:function(e){t.recordMonth=e},expression:"recordMonth"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"本月天数："}},[a("span",[t._v(t._s(t.monthTotalDay))])])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"备注"}},[0==t.headModel.enumStatus||-1==t.headModel.enumStatus?a("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:t.headModel.remark,callback:function(e){t.$set(t.headModel,"remark",e)},expression:"headModel.remark"}}):a("span",[t._v(t._s(t.headModel.remark))])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"状态"}},[t._v(" "+t._s(t.headModel.enumStatusDesc)+" ")])],1)],1),a("el-row",[0==t.headModel.enumStatus||-1==t.headModel.enumStatus?a("el-col",{attrs:{span:24}},[a("el-button",{attrs:{type:"primary"},on:{click:t.saveRecord}},[t._v("暂存")])],1):t._e()],1),a("el-row",[2==t.headModel.enumStatus?a("el-col",{staticClass:"diffColor",attrs:{span:24}},[t._v(" 唯一码、工号橙色员工为防保科与考勤员提交数据不一致，正在等待人事确认。 ")]):t._e()],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:t.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"},on:{"sort-change":t.sortChange}},[a("el-table-column",{attrs:{prop:"Uid",label:"唯一码",align:"center",width:"85",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",{class:n.attDayOffRecordDetailStatus&&1==n.attDayOffRecordDetailStatus?"diffColor":""},[t._v(t._s(n.empUid))])]}}])}),a("el-table-column",{attrs:{prop:"EmpCode",label:"工号",align:"center",width:"80",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",{class:n.attDayOffRecordDetailStatus&&1==n.attDayOffRecordDetailStatus?"diffColor":""},[t._v(t._s(n.empCode))])]}}])}),a("el-table-column",{attrs:{prop:"DisplayName",label:"姓名",align:"center",width:"80",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.empName))])]}}])}),a("el-table-column",{attrs:{prop:"date",label:"在职方式",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.hireStyleName))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"应发公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(0===n.generalHoliday?"":n.generalHoliday))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"剩余公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[0==t.headModel.enumStatus||-1==t.headModel.enumStatus?a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return n.historyH12=Math.abs(n.historyH12)}},model:{value:n.historyH12,callback:function(e){t.$set(n,"historyH12",t._n(e))},expression:"row.historyH12"}}):a("span",[t._v(t._s(0===n.historyH12?"":n.historyH12))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[0==t.headModel.enumStatus||-1==t.headModel.enumStatus?a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return n.h12=Math.abs(n.h12)}},model:{value:n.h12,callback:function(e){t.$set(n,"h12",t._n(e))},expression:"row.h12"}}):a("span",[t._v(t._s(n.h12))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"上月卫贴标准",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.preMonthH1))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"本月卫贴标准",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[0==t.headModel.enumStatus||-1==t.headModel.enumStatus?a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return n.h1=Math.abs(n.h1)}},model:{value:n.h1,callback:function(e){t.$set(n,"h1",t._n(e))},expression:"row.h1"}}):a("span",[t._v(t._s(n.h1))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"病假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[0==t.headModel.enumStatus||-1==t.headModel.enumStatus?a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return n.h2=Math.abs(n.h2)}},model:{value:n.h2,callback:function(e){t.$set(n,"h2",t._n(e))},expression:"row.h2"}}):n.attDayOffRecordDetailStatus&&1==n.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(n.h2)),a("br")]),n.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(n.prophylacticFilling.h2))]):t._e()]):a("span",[t._v(t._s(n.h2))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"事假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[0==t.headModel.enumStatus||-1==t.headModel.enumStatus?a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return n.h3=Math.abs(n.h3)}},model:{value:n.h3,callback:function(e){t.$set(n,"h3",t._n(e))},expression:"row.h3"}}):n.attDayOffRecordDetailStatus&&1==n.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(n.h3)),a("br")]),n.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(n.prophylacticFilling.h3))]):t._e()]):a("span",[t._v(t._s(n.h3))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"产假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[0==t.headModel.enumStatus||-1==t.headModel.enumStatus?a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return n.h4=Math.abs(n.h4)}},model:{value:n.h4,callback:function(e){t.$set(n,"h4",t._n(e))},expression:"row.h4"}}):n.attDayOffRecordDetailStatus&&1==n.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(n.h4)),a("br")]),n.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(n.prophylacticFilling.h4))]):t._e()]):a("span",[t._v(t._s(n.h4))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"哺乳假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[0==t.headModel.enumStatus||-1==t.headModel.enumStatus?a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return n.h5=Math.abs(n.h5)}},model:{value:n.h5,callback:function(e){t.$set(n,"h5",t._n(e))},expression:"row.h5"}}):n.attDayOffRecordDetailStatus&&1==n.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(n.h5)),a("br")]),n.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(n.prophylacticFilling.h5))]):t._e()]):a("span",[t._v(t._s(n.h5))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"探亲假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[0==t.headModel.enumStatus||-1==t.headModel.enumStatus?a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return n.h6=Math.abs(n.h6)}},model:{value:n.h6,callback:function(e){t.$set(n,"h6",t._n(e))},expression:"row.h6"}}):n.attDayOffRecordDetailStatus&&1==n.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(n.h6)),a("br")]),n.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(n.prophylacticFilling.h6))]):t._e()]):a("span",[t._v(t._s(n.h6))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"计生假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[0==t.headModel.enumStatus||-1==t.headModel.enumStatus?a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return n.h7=Math.abs(n.h7)}},model:{value:n.h7,callback:function(e){t.$set(n,"h7",t._n(e))},expression:"row.h7"}}):n.attDayOffRecordDetailStatus&&1==n.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(n.h7)),a("br")]),n.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(n.prophylacticFilling.h7))]):t._e()]):a("span",[t._v(t._s(n.h7))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"婚丧假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[0==t.headModel.enumStatus||-1==t.headModel.enumStatus?a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return n.h8=Math.abs(n.h8)}},model:{value:n.h8,callback:function(e){t.$set(n,"h8",t._n(e))},expression:"row.h8"}}):n.attDayOffRecordDetailStatus&&1==n.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(n.h8)),a("br")]),n.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(n.prophylacticFilling.h8))]):t._e()]):a("span",[t._v(t._s(n.h8))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"脱产读研",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[0==t.headModel.enumStatus||-1==t.headModel.enumStatus?a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return n.h9=Math.abs(n.h9)}},model:{value:n.h9,callback:function(e){t.$set(n,"h9",t._n(e))},expression:"row.h9"}}):n.attDayOffRecordDetailStatus&&1==n.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(n.h9)),a("br")]),n.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(n.prophylacticFilling.h9))]):t._e()]):a("span",[t._v(t._s(n.h9))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因公出国",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[0==t.headModel.enumStatus||-1==t.headModel.enumStatus?a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return n.h10=Math.abs(n.h10)}},model:{value:n.h10,callback:function(e){t.$set(n,"h10",t._n(e))},expression:"row.h10"}}):n.attDayOffRecordDetailStatus&&1==n.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(n.h10)),a("br")]),n.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(n.prophylacticFilling.h10))]):t._e()]):a("span",[t._v(t._s(n.h10))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因私出国",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[0==t.headModel.enumStatus||-1==t.headModel.enumStatus?a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return n.h11=Math.abs(n.h11)}},model:{value:n.h11,callback:function(e){t.$set(n,"h11",t._n(e))},expression:"row.h11"}}):n.attDayOffRecordDetailStatus&&1==n.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(n.h11)),a("br")]),n.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(n.prophylacticFilling.h11))]):t._e()]):a("span",[t._v(t._s(n.h11))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"修改人",align:"right","header-align":"center","min-width":"140px"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[n.attDayOffRecordDetailStatus&&1==n.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(n.updator)),a("br")]),n.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(n.prophylacticFilling.updator))]):t._e()]):a("span",[t._v(t._s(n.updator))])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[20,50,100],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.paginationChanged}})]},proxy:!0}])})],1)},r=[],o=(a("99af"),a("d81d"),a("fb6a"),a("a9e3"),a("d3b7"),a("ac1f"),a("25f0"),a("4d90"),a("1276"),a("3835")),i=a("d368"),l=a("cbd2"),c={components:{},data:function(){return{total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,recordMonth:this.getNowTime(),monthTotalDay:0,headModel:{remark:"",statue:""},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeLoading:!1,currentNode:null,tableData:[],allData:[],treeExpandedKeys:[],zcSucceed:!1}},created:function(){this.loadTree(),this.getMonthTotalDay()},methods:{getNowTime:function(){var t=new Date,e=t.getFullYear(),a=t.getMonth();a=a.toString().padStart(2,"0"),"00"===a&&(e-=1,a="12");var n="".concat(e,"-").concat(a);return n},getMonthTotalDay:function(){if(this.recordMonth){var t=this.recordMonth.split("-")[0],e=this.recordMonth.split("-")[1],a=new Date(t,e,0);this.monthTotalDay=a.getDate()}},loadTree:function(){var t=this;this.treeLoading=!0,i["a"].queryDeptByUser({}).then((function(e){t.treeData=e.data,t.treeExpandedKeys.push(e.data[0].id)})).catch((function(t){console.log(t)})).finally((function(){t.treeLoading=!1})),this.resetCurrentNode()},resetCurrentNode:function(){this.currentNode=null},treeNodeClick:function(t){this.currentNode=t,this.listQuery.pageIndex=1,this.getAttDayOffRecord()},dateChange:function(){this.listQuery.pageIndex=1,this.getMonthTotalDay(),this.getAttDayOffRecord()},sortChange:function(t,e,a){this.listQuery.pageIndex=1;var n="";"descending"===t.order?n="desc":"ascending"===t.order&&(n="asc"),this.listQuery.order=n?t.prop+" "+n:"",this.getAttDayOffRecord()},getAttDayOffRecord:function(){var t=this;if(this.currentNode)if(this.recordMonth){if(this.recordMonth&&this.currentNode){var e={RecordMonth:this.recordMonth,DeptId:this.currentNode.id};l["a"].getAttDayOffRecord(e).then((function(e){e.succeed?(t.headModel=e.data,t.queryCheckRecordFilling(e.data.id)):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))}}else this.$notice.message("请选择月份","warning");else this.$notice.message("请选择部门","warning")},queryCheckRecordFilling:function(t){var e=this,a={RecordId:t,DeptId:this.currentNode.id,RecordMonth:this.recordMonth,Order:this.listQuery.order};l["a"].queryCheckRecordFilling(a).then((function(t){e.listLoading=!1,t.succeed?(e.allData=t.data.datas,e.total=t.data.recordCount,e.getTableData()):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},paginationChanged:function(){this.getTableData()},getTableData:function(){var t=this.listQuery.pageSize,e=this.listQuery.pageIndex;this.tableData=this.allData.slice((e-1)*t,e*t)},saveRecord:function(){for(var t=0;t<this.allData.length;t++){var e=this.allData[t],a=(e.h2||0)+(e.h3||0)+(e.h4||0)+(e.h5||0)+(e.h6||0)+(e.h7||0)+(e.h8||0)+(e.h9||0)+(e.h10||0)+(e.h11||0),n=this.recordMonth+"-01",r=n.split("-").map(Number),i=Object(o["a"])(r,2),l=i[0],c=i[1],s=new Date(l,c,0),u=s.getDate();if(a>u)return this.$message.error("(".concat(e.empCode,")").concat(e.empName,"的月份数据超过当月最多天数，请检查。")),void(this.zcSucceed=!1);this.zcSucceed=!0}this.zcSucceed&&this.save()},save:function(){var t=this;this.currentNode?this.recordMonth?this.recordMonth&&this.currentNode&&(this.headModel.fillings=this.allData,l["a"].saveAttDayOffRecord(this.headModel).then((function(e){e.succeed?(t.getAttDayOffRecord(),t.$notice.message("操作成功","success")):t.$notice.resultTip(e)})).catch((function(e){t.getAttDayOffRecord(),console.log(e)}))):this.$notice.message("请选择月份","warning"):this.$notice.message("请选择部门","warning")}}},s=c,u=(a("a859"),a("2877")),d=Object(u["a"])(s,n,r,!1,null,null,null);e["default"]=d.exports},cbd2:function(t,e,a){"use strict";var n=a("cfe3"),r="AttendanceManage",o=new n["a"](r);e["a"]={getAttMonthShiftRecord:function(t){return o.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return o.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return o.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return o.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return o.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return o.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return o.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return o.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return o.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return o.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return o.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return o.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return o.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return o.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return o.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return o.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return o.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return o.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return o.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return o.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return o.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return o.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return o.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return o.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return o.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return o.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return o.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return o.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return o.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return o.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return o.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return o.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return o.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return o.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return o.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return o.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return o.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return o.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return o.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return o.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return o.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return o.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return o.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return o.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return o.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return o.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return o.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return o.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return o.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return o.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return o.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return o.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return o.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return o.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return o.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return o.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return o.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return o.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return o.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return o.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return o.get("GetSameDeptEmployeeWithHealthAllowance",t)}}}}]);