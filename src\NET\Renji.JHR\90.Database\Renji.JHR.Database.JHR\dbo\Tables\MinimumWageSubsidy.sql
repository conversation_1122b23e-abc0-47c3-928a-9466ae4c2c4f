CREATE TABLE [dbo].[MinimumWageSubsidy](
    [ID] UNIQUEIDENTIFIER NOT NULL,
    [EmployeeId] UNIQUEIDENTIFIER NOT NULL,
    [SalaryId] UNIQUEIDENTIFIER NOT NULL,
    [SubsidyAmount] decimal(18, 4) NULL,    
    [Remark] NVARCHAR(300) NULL,
	[Deleted]		BIT                 NOT NULL,
    [Creator]		NVARCHAR(50)        NULL, 
    [CreateTime]	DATETIME            NULL,
    [LastEditor]	NVARCHAR(50)        NULL,
    [LastEditTime]	DATETIME            NULL,
    CONSTRAINT [PK_MinimumWageSubsidy] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_MinimumWageSubsidy_Employee] FOREIGN KEY ([EmployeeId]) REFERENCES [dbo].[Employee] ([ID]),
    CONSTRAINT [FK_MinimumWageSubsidy_Salary] FOREIGN KEY ([SalaryId]) REFERENCES [dbo].[Salary] ([ID]),
)
GO
-- 添加字段描述
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最低工资补助表',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'MinimumWageSubsidy';
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'MinimumWageSubsidy',
    @level2type = N'COLUMN',
    @level2name = N'ID';
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'员工ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'MinimumWageSubsidy',
    @level2type = N'COLUMN',
    @level2name = N'EmployeeId';
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'薪资ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'MinimumWageSubsidy',
    @level2type = N'COLUMN',
    @level2name = N'SalaryId';
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'补助金额',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'MinimumWageSubsidy',
    @level2type = N'COLUMN',
    @level2name = N'SubsidyAmount';
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'备注',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'MinimumWageSubsidy',
    @level2type = N'COLUMN',
    @level2name = N'Remark';
GO