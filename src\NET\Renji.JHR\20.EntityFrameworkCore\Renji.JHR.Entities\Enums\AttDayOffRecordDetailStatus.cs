﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Renji.JHR.Entities
{
    /// <summary>
    /// 状态
    /// </summary>
    public enum AttDayOffRecordDetailStatus
    {
        None = 0,

        /// <summary>
        /// 待审批
        /// </summary>
        [Description("待审批")]
        Pending = 1,

        /// <summary>
        /// 已审批   两方数据一致或人事科审批确认
        /// </summary>
        [Description("已审批")]
        Approved = 10,

        /// <summary>
        /// 无需审批，  只有一方提交
        /// </summary>
        [Description("无需审批")]
        NoApproval = 20,
    }
}
