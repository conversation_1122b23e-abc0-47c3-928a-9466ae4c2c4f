(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0989fe00"],{"1b77":function(t,e,r){"use strict";var o=r("7b35"),a=r.n(o);a.a},"7b35":function(t,e,r){},cbd2:function(t,e,r){"use strict";var o=r("cfe3"),a="AttendanceManage",n=new o["a"](a);e["a"]={getAttMonthShiftRecord:function(t){return n.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return n.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return n.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return n.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return n.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return n.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return n.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return n.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return n.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return n.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return n.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return n.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return n.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return n.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return n.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return n.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return n.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return n.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return n.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return n.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return n.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return n.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return n.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return n.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return n.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return n.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return n.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return n.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return n.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return n.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return n.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return n.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return n.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return n.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return n.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return n.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return n.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return n.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return n.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return n.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return n.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return n.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return n.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return n.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return n.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return n.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return n.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return n.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return n.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return n.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return n.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return n.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return n.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return n.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return n.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return n.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return n.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return n.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return n.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return n.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return n.get("GetSameDeptEmployeeWithHealthAllowance",t)}}},fb43:function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"app-container"},[r("layout3",{scopedSlots:t._u([{key:"aside",fn:function(){return[r("div",{staticClass:"block"},[r("el-form",{attrs:{inline:!0,size:"small"}},[r("el-row",[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"节日加班日期"}},[r("el-date-picker",{staticClass:"input_headerDate",attrs:{type:"date",placeholder:"请选择","value-format":"yyyy-MM-dd"},on:{change:t.dateChange},model:{value:t.recordDate,callback:function(e){t.recordDate=e},expression:"recordDate"}})],1)],1)],1)],1)],1),r("c-tree",{attrs:{options:t.treeData,props:t.treeProps,"expanded-keys":t.treeExpandedKeys},on:{nodeClick:t.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[r("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.headModel}},[r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"制表人"}},[r("span",[t._v(" "+t._s(t.headModel.documentMaker)+" ")])])],1),1==t.headModel.enumStatus?r("el-col",{attrs:{span:12}},[r("el-button",{attrs:{type:"primary"},on:{click:t.saveRecord}},[t._v("暂存")])],1):t._e()],1)],1),r("div",{staticClass:"tip_div"},[r("span",{staticClass:"tip_content"},[t._v(t._s(t.headModel.tip))])]),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",attrs:{data:t.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"},on:{"sort-change":t.sortChange}},[r("el-table-column",{attrs:{prop:"EmpCode",label:"工号",align:"center",width:"130",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[r("span",[t._v(t._s(o.empCode))])]}}])}),r("el-table-column",{attrs:{prop:"DisplayName",label:"姓名",align:"center",width:"160",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[r("span",[t._v(t._s(o.empName))])]}}])}),r("el-table-column",{attrs:{prop:"date",label:"在职方式",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[r("span",[t._v(t._s(o.hireStyleName))])]}}])}),r("el-table-column",{attrs:{prop:"name",label:"加班类型",align:"center",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[1==t.headModel.enumStatus?r("el-select",{attrs:{placeholder:"请选择"},model:{value:o.enumOverTimeType,callback:function(e){t.$set(o,"enumOverTimeType",e)},expression:"row.enumOverTimeType"}},t._l(t.overTimeType,(function(t){return r("el-option",{key:t.value,attrs:{label:t.desc,value:t.value}})})),1):r("span",[t._v(t._s(o.enumOverTimeTypeDesc))])]}}])}),r("el-table-column",{attrs:{prop:"name",label:"人事修改人",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[r("span",[t._v(t._s(o.updator))])]}}])})],1),r("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[20,50,100],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.paginationChanged}})]},proxy:!0}])})],1)},a=[],n=(r("99af"),r("fb6a"),r("d3b7"),r("25f0"),r("4d90"),r("d368")),i=r("cbd2"),c=r("f9ac"),l={components:{},data:function(){return{total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,recordDate:this.getNowTime(),headModel:{},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},currentNode:null,tableData:[],allData:[],treeExpandedKeys:[],overTimeType:[]}},created:function(){this.loadTree(),this.loadOverTimeType()},methods:{getNowTime:function(){var t=new Date,e=t.getFullYear(),r=t.getMonth(),o=t.getDate();r+=1,r=r.toString().padStart(2,"0"),o=o.toString().padStart(2,"0");var a="".concat(e,"-").concat(r,"-").concat(o);return a},loadTree:function(){var t=this;this.treeLoading=!0,n["a"].queryDeptByUser({}).then((function(e){t.treeData=e.data,t.treeExpandedKeys.push(e.data[0].id)})).catch((function(t){console.log(t)})).finally((function(){t.treeLoading=!1})),this.resetCurrentNode()},loadOverTimeType:function(){var t=this;c["a"].getEnumInfos({enumType:"OverTimeType"}).then((function(e){t.overTimeType=e.data.datas})).catch((function(t){console.log(t)}))},resetCurrentNode:function(){this.currentNode=null},treeNodeClick:function(t){this.currentNode=t,this.listQuery.pageIndex=1,this.getAttHolidayOTRecord()},dateChange:function(){this.listQuery.pageIndex=1,this.getAttHolidayOTRecord()},sortChange:function(t,e,r){this.listQuery.pageIndex=1;var o="";"descending"===t.order?o="desc":"ascending"===t.order&&(o="asc"),this.listQuery.order=o?t.prop+" "+o:"",this.getAttHolidayOTRecord()},getAttHolidayOTRecord:function(){var t=this;if(this.currentNode&&this.recordDate){var e={RecordDate:this.recordDate,DeptId:this.currentNode.id};i["a"].getAttHolidayOTRecord(e).then((function(e){e.succeed?(t.headModel=e.data,t.queryAttHolidayOTRecordDetail(e.data.id)):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))}else this.$notice.message("请选择部门和节日加班日期。","info")},queryAttHolidayOTRecordDetail:function(t){var e=this,r={RecordId:t,DeptId:this.currentNode.id,RecordMonth:this.recordDate,Order:this.listQuery.order};i["a"].queryAttHolidayOTRecordDetail(r).then((function(t){e.listLoading=!1,t.succeed?(e.allData=t.data.datas,e.total=t.data.recordCount,e.getTableData()):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},paginationChanged:function(){this.getTableData()},getTableData:function(){var t=this.listQuery.pageSize,e=this.listQuery.pageIndex;this.tableData=this.allData.slice((e-1)*t,e*t)},saveRecord:function(){var t=this;this.recordDate&&this.currentNode&&(this.headModel.details=this.allData,i["a"].saveAttHolidayOTRecord(this.headModel).then((function(e){e.succeed?(t.getAttHolidayOTRecord(),t.$notice.message("操作成功","success")):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))),this.currentNode?this.recordDate||this.$notice.message("请选择加班日期","warning"):this.$notice.message("请选择部门","warning")}}},d=l,u=(r("1b77"),r("2877")),f=Object(u["a"])(d,o,a,!1,null,null,null);e["default"]=f.exports}}]);