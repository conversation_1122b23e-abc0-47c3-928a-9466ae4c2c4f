﻿using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.Extensions.Configuration;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.Configuration;
using Shinsoft.Core.EntityFrameworkCore;
using static Renji.JHR.Common.Utility.SM4Utils;

namespace Renji.JHR.Common.Configration
{
    public abstract class Config : BaseConfig
    {
        /// <summary>
        /// 标题
        /// </summary>
        public static string Title = Config.Debug
            ? $"【测试版】{AppSetting.GetMemberValue()}"
            : AppSetting.GetMemberValue();

        public static string SysRedis => AppSetting.GetMemberValue();

        /// <summary>
        /// 创建用户时生成的默认密码
        /// </summary>
        public static string DefaultPwd = AppSetting.GetMemberValue("Renji123");

        /// <summary>
        /// 应用令牌默认长度
        /// </summary>
        public static int AppTokenLength = AppSetting.GetMemberValue(8);

        /// <summary>
        /// 应用登录默认过期分钟
        /// </summary>
        public static int AppLoginExpireMins = AppSetting.GetMemberValue(5);

        /// <summary>
        /// 合同到期提醒时间
        /// </summary>
        public static int ContractsExpire = AppSetting.GetMemberValue(30);

        /// <summary>
        /// 合同续签页（领导）
        /// </summary>
        public static string LeaderFileName = AppSetting.GetMemberValue("合同续签页（负责人）.pdf");

        /// <summary>
        /// 合同续签页（员工）
        /// </summary>
        public static string EmpFileName = AppSetting.GetMemberValue("合同续签页（员工）.pdf");

        /// <summary>
        /// 邮箱后缀
        /// </summary>
        public static string RenjiEmail = AppSetting.GetMemberValue("@Renji.com");

        public static bool IsDM => GetConnectionSetting("BizDbContext")?.DbProvider == DbProvider.DM;

        public static class WebApi
        {
            /// <summary>
            /// Key
            /// </summary>
            public static string Key => AppSetting.GetValue("WebApi:Key");

            /// <summary>
            /// Domain
            /// </summary>
            public static int Domain => AppSetting.GetMemberValue(0, "WebApi:Domain");

            /// <summary>
            /// 接口检验时间
            /// </summary>
            public static int ChkMin => AppSetting.GetMemberValue(10, "WebApi:ChkMin");
        }

        public static class SM4
        {
            /// <summary>
            /// 邮箱后缀
            /// </summary>
            public static string Key => AppSetting.GetValue("SM4:Key");

            /// <summary>
            /// 邮箱后缀
            /// </summary>
            public static string Iv => AppSetting.GetValue("SM4:Iv");

            /// <summary>
            /// HexString
            /// </summary>
            public static bool HexString => AppSetting.GetMemberValue(true, "SM4:HexString");

            /// <summary>
            /// 邮箱后缀
            /// </summary>
            public static Sm4CryptoEnum CryptoMode => AppSetting.GetValue<Sm4CryptoEnum>("SM4:CryptoMode");
        }


        /// <summary>
        /// 不推送平台人员
        /// </summary>
        public static string NotUploadEmployeeUids = AppSetting.GetMemberValue("", "NotUploadEmployeeUids");


        public static class Salary
        {

            /// <summary>
            /// 十三薪
            /// </summary>
            public static string ThirteenSalary => AppSetting.GetValue("SalaryType:ThirteenSalary");

            /// <summary>
            /// 退休库
            /// </summary>
            public static string Retirement => AppSetting.GetValue("SalaryType:Retirement");

            /// <summary>
            /// 报道日期距当前月
            /// </summary>
            public static int HireDateMonthDifference = AppSetting.GetMemberValue(23);
        }


        /// <summary>
        /// 字典项
        /// </summary>
        public static class DictCode
        {
            /// <summary>
            /// 离职方式
            /// </summary>
            public static string HireStyleCode => AppSetting.GetValue("DictCode:HireStyleCode");

            /// <summary>
            /// 离职方式
            /// </summary>
            public static string LeaveStyleCode => AppSetting.GetValue("DictCode:LeaveStyleCode");

            /// <summary>
            /// 已故
            /// </summary>
            public static string LeaveStyleCode_Deceased => AppSetting.GetValue("DictCode:LeaveStyleCode_Deceased");

            /// <summary>
            /// 退休
            /// </summary>
            public static string LeaveStyleCode_Retire => AppSetting.GetValue("DictCode:LeaveStyleCode_Retire");

            /// <summary>
            /// 辞职
            /// </summary>
            public static string LeaveStyleCode_Resign => AppSetting.GetValue("DictCode:LeaveStyleCode_Resign");

            /// <summary>
            /// 培训离站
            /// </summary>
            public static string LeaveStyleCode_Outbound => AppSetting.GetValue("DictCode:LeaveStyleCode_Outbound");

            /// <summary>
            /// 年龄段
            /// </summary>
            public static string AgeRange => AppSetting.GetValue("DictCode:AgeRange");

            /// <summary>
            /// 待遇类别
            /// </summary>
            public static string TreatmentCategory => AppSetting.GetValue("DictCode:TreatmentCategory");
            
        }

        /// <summary>
        /// Domain
        /// </summary>
        public static int InterfaceTimeout => AppSetting.GetMemberValue(60000, "InterfaceTimeout");
    }
}