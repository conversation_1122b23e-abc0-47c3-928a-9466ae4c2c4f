﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

using Shinsoft.Core;

using System;
using System.Collections.Generic;
using System.Data;
using System.IO;

namespace Renji.JHR.Common.Utility
{
#pragma warning disable CS8600 // 将 null 字面量或可能为 null 的值转换为非 null 类型。
#pragma warning disable CS8601 // 引用类型赋值可能为 null。
#pragma warning disable CS8602 // 解引用可能出现空引用。
#pragma warning disable CS8603 // 可能返回 null 引用。
#pragma warning disable CS8604 // 引用类型参数可能为 null。

    public static class ExcelHelper
    {
        public enum ExcelType
        {
            xlsx = 0,
            xls = 1,
        }

        //private string fileName = null; //文件名
        //private IWorkbook workbook = null;
        //private FileStream fs = null;
        //private bool disposed;

        //public ExcelHelper(string fileName)
        //{
        //    this.fileName = fileName;
        //    disposed = false;
        //}

        /// <summary>
        /// 将DataTable数据导入到excel中
        /// </summary>
        /// <param name="stream"></param>
        /// <param name="data">要导入的数据</param>
        /// <param name="isColumnWritten">DataTable的列名是否要导入</param>
        /// <param name="sheetName">要导入的excel的sheet的名称</param>
        /// <param name="excelType"></param>
        /// <returns>导入数据行数(包含列名那一行)</returns>
        public static int DataTableToExcel(Stream stream, DataTable data, string sheetName, bool isColumnWritten, ExcelType excelType = ExcelType.xlsx)
        {
            int i = 0;
            int j = 0;
            int count = 0;
            IWorkbook workbook = null;
            ISheet sheet = null;

            // fs = new FileStream(fileName, FileMode.OpenOrCreate, FileAccess.ReadWrite);-
            if (excelType == ExcelType.xlsx) // 2007版本
                workbook = new XSSFWorkbook();
            else if (excelType == ExcelType.xls) // 2003版本
                workbook = new HSSFWorkbook();

            try
            {
                if (workbook != null)
                {
                    sheet = workbook.CreateSheet(sheetName);
                }
                else
                {
                    return -1;
                }

                if (isColumnWritten == true) //写入DataTable的列名
                {
                    IRow row = sheet.CreateRow(0);
                    for (j = 0; j < data.Columns.Count; ++j)
                    {
                        row.CreateCell(j).SetCellValue(data.Columns[j].ColumnName);
                    }
                    count = 1;
                }
                else
                {
                    count = 0;
                }

                for (i = 0; i < data.Rows.Count; ++i)
                {
                    IRow row = sheet.CreateRow(count);
                    for (j = 0; j < data.Columns.Count; ++j)
                    {
                        row.CreateCell(j).SetCellValue(data.Rows[i][j].ToString());
                    }
                    ++count;
                }
                workbook.Write(stream); //写入到excel
                return count;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception: " + ex.Message);
                return -1;
            }
        }

        /// <summary>
        /// 将excel中的数据导入到DataTable中
        /// </summary>
        /// <param name="stream"></param>
        /// <param name="sheetName">excel工作薄sheet的名称</param>
        /// <param name="isFirstRowColumn">第一行是否是DataTable的列名</param>
        /// <param name="data"></param>
        /// <param name="excelType"></param>
        /// <returns>返回的DataTable</returns>
        public static DataTable ExcelToDataTable(Stream stream, string? sheetName, bool isFirstRowColumn, DataTable? data = null, ExcelType excelType = ExcelType.xlsx)
        {
            IWorkbook workbook = null;
            ISheet sheet = null;
            IFormulaEvaluator evalor = null;
            data ??= new DataTable();
            int startRow = 0;
            try
            {
                // fs = new FileStream(fileName, FileMode.Open, FileAccess.Read);
                if (excelType == ExcelType.xlsx) // 2007版本
                {
                    workbook = new XSSFWorkbook(stream);
                    // IFormulaEvaluator, IWorkbookEvaluatorProvider
                    evalor = new XSSFFormulaEvaluator(workbook);
                }
                else if (excelType == ExcelType.xls) // 2003版本
                {
                    workbook = new HSSFWorkbook(stream);
                    // IFormulaEvaluator, IWorkbookEvaluatorProvider
                    evalor = new HSSFFormulaEvaluator(workbook);
                }

                if (sheetName != null)
                {
                    sheet = workbook.GetSheet(sheetName);
                    if (sheet == null) //如果没有找到指定的sheetName对应的sheet，则尝试获取第一个sheet
                    {
                        sheet = workbook.GetSheetAt(0);
                    }
                }
                else
                {
                    sheet = workbook.GetSheetAt(0);
                }
                if (sheet != null)
                {
                    //datatable 列 对应 sheet 列
                    Dictionary<int, int> col = new Dictionary<int, int>();
                    IRow firstRow = sheet.GetRow(0);
                    int cellCount = firstRow.LastCellNum; //一行最后一个cell的编号 即总的列数

                    if (isFirstRowColumn)
                    {
                        if (data.Columns.Count == 0)
                        {
                            int datatableindex = 0;
                            for (int i = firstRow.FirstCellNum; i < cellCount; ++i)
                            {
                                ICell cell = firstRow.GetCell(i);
                                if (cell != null)
                                {
                                    string cellValue = cell.StringCellValue;
                                    if (cellValue != null)
                                    {
                                        DataColumn column = new DataColumn(cellValue);
                                        data.Columns.Add(column);
                                        col.Add(datatableindex, i);
                                        datatableindex++;
                                    }
                                }
                            }
                        }
                        else
                        {
                            // int datatableindex = 0;
                            for (int i = firstRow.FirstCellNum; i < cellCount; ++i)
                            {
                                ICell cell = firstRow.GetCell(i);
                                if (cell != null)
                                {
                                    string cellValue = cell.StringCellValue;
                                    if (cellValue != null)
                                    {
                                        cellValue = cellValue.Trim();
                                        for (int j = 0; j < data.Columns.Count; j++)
                                        {
                                            var datatableColumn = data.Columns[j];
                                            if (string.Compare(datatableColumn.ColumnName, cellValue, StringComparison.OrdinalIgnoreCase) == 0 ||
                                                string.Compare(datatableColumn.Caption, cellValue, StringComparison.OrdinalIgnoreCase) == 0)
                                            {
                                                col.Add(j, i);
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        startRow = sheet.FirstRowNum + 1;
                    }
                    else
                    {
                        startRow = sheet.FirstRowNum;
                    }

                    int rowCount = sheet.LastRowNum;
                    for (int i = startRow; i <= rowCount; ++i)
                    {
                        IRow row = sheet.GetRow(i);
                        if (row == null) continue; //没有数据的行默认是null　　　　　　　

                        DataRow dataRow = data.NewRow();
                        //for (int j = row.FirstCellNum; j < cellCount; ++j)
                        //{
                        //    if (row.GetCell(j) != null) //同理，没有数据的单元格都默认是null
                        //        dataRow[j] = row.GetCell(j).ToString();
                        //}
                        bool isnotnullrow = false;
                        foreach (var item in col)
                        {
                            var cellv = row.GetCell(item.Value);
                            if (cellv != null)
                            {
                                //?数据格式问题 / 单元格公式
                                var value = GetCellValue(cellv, evalor);
                                if (value != null && !string.IsNullOrEmpty(value.ToString()))
                                    isnotnullrow = true;

                                dataRow[item.Key] = value;
                                //cellv.ToString();
                            }
                        }
                        if (isnotnullrow)
                            data.Rows.Add(dataRow);
                    }
                }

                return data;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception: " + ex.Message);
            }

            return null;
        }

        public static object GetCellValue(ICell cell, IFormulaEvaluator evalor)
        {
            object result = null;
            switch (cell.CellType)
            {
                case CellType.Numeric:
                    if (DateUtil.IsCellDateFormatted(cell))
                    {
                        result = cell.DateCellValue.ToString("yyyy-MM-dd");
                    }
                    else
                    {
                        result = cell.NumericCellValue;
                    }
                    break;

                case CellType.String:
                    result = cell.StringCellValue.Trim();
                    break;

                case CellType.Boolean:
                    result = cell.BooleanCellValue;
                    break;

                case CellType.Error:
                    //result= HSSFErrorConstants.GetText(cell.ErrorCellValue);
                    break;

                case CellType.Formula:
                    //result = GetCellValue(cell.CachedFormulaResultType);
                    //针对公式列 进行动态计算;注意：公式暂时只支持 数值 字符串类型
                    var formulaValue = evalor.Evaluate(cell);
                    if (formulaValue.CellType == CellType.Numeric)
                    {
                        result = formulaValue.NumberValue;
                    }
                    else if (formulaValue.CellType == CellType.String)
                    {
                        result = formulaValue.StringValue;
                    }
                    break;

                default:
                    result = cell.ToString().Trim();
                    break;
            }
            return result;
        }

        public static DataSet ExcelToDataSet(Stream file, bool onesheet = true)
        {
            var ds = new DataSet();
            var hssfworkbook = WorkbookFactory.Create(file);
            int a = hssfworkbook.NumberOfSheets;
            if (onesheet)
            {
                a = 1;
            }
            for (int j = 0; j < a; j++)
            {
                bool ishide = hssfworkbook.IsSheetHidden(j);
                if (ishide)
                {
                    continue;
                }
                var dt = new DataTable();
                var sheet = hssfworkbook.GetSheetAt(j);
                var rows = sheet.GetRowEnumerator();
                dt.TableName = sheet.SheetName;
                bool first = true;
                while (rows.MoveNext())
                {
                    var row = (XSSFRow)rows.Current;
                    if (row.FirstCellNum != 0) { continue; }
                    else if (row.GetCell(0).CellType == CellType.Blank)
                    {
                        continue;
                    }
                    if (first)
                    {
                        for (var i = 0; i < row.LastCellNum; i++)
                        {
                            if (first)
                            {
                                var cell = row.GetCell(i);
                                string column = "Column" + i;
                                if (cell != null && !cell.StringCellValue.IsEmpty())
                                {
                                    column = cell.StringCellValue;
                                }
                                dt.Columns.Add(column);
                                continue;
                            }
                        }
                    }
                    else
                    {
                        var dr = dt.NewRow();
                        int lastcell = row.LastCellNum;
                        if (dt.Columns.Count < row.LastCellNum) lastcell = dt.Columns.Count;
                        for (var i = 0; i < lastcell; i++)
                        {
                            var cell = row.GetCell(i);
                            if (cell == null)
                            {
                                dr[i] = null;
                            }
                            else
                            {
                                switch (cell.CellType)
                                {
                                    case CellType.Blank:
                                        dr[i] = "";
                                        break;

                                    case CellType.Boolean:
                                        dr[i] = cell.BooleanCellValue;
                                        break;

                                    case CellType.Numeric:
                                        if (DateUtil.IsCellDateFormatted(cell))
                                            dr[i] = cell.DateCellValue;
                                        else
                                            dr[i] = cell.ToString();
                                        break;

                                    case CellType.String:
                                        dr[i] = cell.StringCellValue;
                                        break;

                                    case CellType.Error:
                                        dr[i] = cell.ErrorCellValue;
                                        break;

                                    case CellType.Formula:
                                        try
                                        {
                                            dr[i] = cell.NumericCellValue;
                                        }
                                        catch
                                        {
                                            dr[i] = cell.StringCellValue;
                                        }
                                        break;

                                    default:
                                        dr[i] = "=" + cell.CellFormula;
                                        break;
                                }
                            }
                        }
                        dt.Rows.Add(dr);
                    }
                    first = false;
                }

                ds.Tables.Add(dt);
            }

            file.Position = 0;

            return ds;
        }

        //public void Dispose()
        //{
        //    Dispose(true);
        //    GC.SuppressFinalize(this);
        //}

        //protected virtual void Dispose(bool disposing)
        //{
        //    if (!this.disposed)
        //    {
        //        if (disposing)
        //        {
        //            if (fs != null)
        //                fs.Close();
        //        }

        //        fs = null;
        //        disposed = true;
        //    }
        //}
    }

#pragma warning restore CS8600 // 将 null 字面量或可能为 null 的值转换为非 null 类型。
#pragma warning restore CS8601 // 引用类型赋值可能为 null。
#pragma warning restore CS8602 // 解引用可能出现空引用。
#pragma warning restore CS8603 // 可能返回 null 引用。
#pragma warning restore CS8604 // 引用类型参数可能为 null。
}