﻿using Renji.JHR.Entities;
using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.Text;

namespace Renji.JHR.Bll
{
    public class SysBll : BaseBll
    {
        #region Constructs

        public SysBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public SysBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public SysBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public SysBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        #region Login

        public User? GetUserByUsername(string username)
        {
            var user = this.GetEntity<User>(p => p.Username == username);

            if (user != null)
            {
                user = this.InitUser(user);
            }

            return user;
        }

        public User? GetUserByIdentityKey(IIdentityKey identityKey)
        {
            var id = identityKey.UserId.As<Guid>();

            var user = !id.IsEmpty()
                ? this.Get<User>(id)
                : null;

            if (user != null)
            {
                user = this.InitUser(user);
            }

            return user;
        }

        protected User InitUser(User user)
        {
            User clone = user.Clone();

            //初始化权限等信息
            var id = user.ID;
            var roles = this.GetEntities<Role>(p => p.RoleMember
              .Any(p1 => (p1.EnumType == RoleMemberType.User && p1.UserId == id))
            );

            var roleIds = roles.Select(p => p.ID).ToList();

            var rolePermessions = this.GetEntities<RolePermission>(p => roleIds.Contains(p.RoleId));
            var permessionIds = rolePermessions.Select(p => p.PermissionId).ToList();

            clone.Permissions = this.SysCache.Permissions
                .Where(p => permessionIds.Contains(p.ID))
                .Select(p => p.Code)
                .ToList();

            return clone;
        }

        #endregion Login
    }
}