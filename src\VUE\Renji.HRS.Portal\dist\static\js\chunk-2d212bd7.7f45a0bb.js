(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d212bd7"],{aa67:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("layout3",{scopedSlots:e._u([{key:"aside",fn:function(){return[a("el-checkbox",{on:{change:e.selectedChange},model:{value:e.listQuery.IsContainSubDept,callback:function(t){e.$set(e.listQuery,"IsContainSubDept",t)},expression:"listQuery.IsContainSubDept"}},[e._v("包含下级部门")]),a("c-tree",{directives:[{name:"loading",rawName:"v-loading",value:e.treeLoading,expression:"treeLoading"}],attrs:{options:e.treeData,props:e.tree<PERSON><PERSON>,"expanded-keys":e.treeExpandedKeys},on:{nodeClick:e.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[a("el-card",[a("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[a("el-form-item",[a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"请选择字段"},model:{value:e.listQuery.entityColumn,callback:function(t){e.$set(e.listQuery,"entityColumn",t)},expression:"listQuery.entityColumn"}},e._l(e.dataColumns,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e}})})),1)],1),a("el-form-item",[a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"请选择条件"},model:{value:e.listQuery.queryCondition.EnumOperation,callback:function(t){e.$set(e.listQuery.queryCondition,"EnumOperation",t)},expression:"listQuery.queryCondition.EnumOperation"}},e._l(e.selectConditionOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),a("el-form-item",[a("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",placeholder:"请输入关键字"},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}})],1),a("el-form-item",{attrs:{label:"在岗状态"}},[a("el-select",{staticStyle:{width:"120px"},attrs:{clearable:""},model:{value:e.listQuery.status,callback:function(t){e.$set(e.listQuery,"status",t)},expression:"listQuery.status"}},e._l(e.empStatusOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:e.addDialog}},[e._v("添加")]),a("el-button",{attrs:{type:"primary"},on:{click:e.downloadexceltemplate}},[e._v("模板下载")]),a("el-upload",{staticStyle:{float:"right","margin-left":"10px"},attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{slot:"trigger",icon:"el-icon-upload",type:"primary"},slot:"trigger"},[e._v("导入")])],1)],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{label:"姓名",sortable:"custom",prop:"Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.name))])]}}])}),a("el-table-column",{attrs:{label:"身份证号",sortable:"custom",prop:"IDCard"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.idCard))])]}}])}),a("el-table-column",{attrs:{label:"联系电话",sortable:"custom",prop:"Phone"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.phone))])]}}])}),a("el-table-column",{attrs:{label:"科室",sortable:"custom",prop:"Department.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.deptName))])]}}])}),a("el-table-column",{attrs:{label:"进院日期",sortable:"custom",prop:"EnterDateTime"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.enterDateTime?new Date(o.enterDateTime).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"离院日期",sortable:"custom",prop:"LeaveDateTime"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.leaveDateTime?new Date(o.leaveDateTime).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"人员类型",sortable:"custom",prop:"OtherEmpType.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.otherEmpTypeName))])]}}])}),a("el-table-column",{attrs:{label:"在岗状态",sortable:"custom",prop:"EmpStatus.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.empStateName))])]}}])}),a("el-table-column",{attrs:{label:"是否纳入核酸","min-width":"90px",sortable:"custom",prop:"IsNucleicAcid"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.isNucleicAcid?"是":"否"))])]}}])}),a("el-table-column",{attrs:{fixed:"right",label:"操作",align:"left","header-align":"center",width:"180","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("el-button",{staticStyle:{"margin-left":"5px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.updateDialog(o)}}},[e._v(" 编辑 ")]),a("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(t){return e.deleteRecord(o)}}},[e._v(" 删除 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})],1)]},proxy:!0}])}),a("el-dialog",{attrs:{top:"5vh",title:"添加",visible:e.addDialogVisible,width:"50%"},on:{"update:visible":function(t){e.addDialogVisible=t},close:e.closeAddDialog}},[a("el-form",{ref:"ref_addForm",attrs:{rules:e.rules,model:e.addForm,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"姓名",prop:"name"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.addForm.name,callback:function(t){e.$set(e.addForm,"name",t)},expression:"addForm.name"}})],1),a("el-form-item",{attrs:{label:"身份证号",prop:"idCard"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.addForm.idCard,callback:function(t){e.$set(e.addForm,"idCard",t)},expression:"addForm.idCard"}})],1),a("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.addForm.phone,callback:function(t){e.$set(e.addForm,"phone",t)},expression:"addForm.phone"}})],1),a("el-form-item",{attrs:{label:"科室",prop:"departmentID"}},[a("el-cascader",{staticStyle:{width:"100%"},attrs:{clearable:"",options:e.departmentOptions,props:{expandTrigger:"hover",value:"id",label:"name",checkStrictly:!0},disabled:!0,placeholder:"请选择部门"},model:{value:e.addForm.departmentID,callback:function(t){e.$set(e.addForm,"departmentID",t)},expression:"addForm.departmentID"}})],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"进院日期",prop:"enterDateTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择进院日期"},model:{value:e.addForm.enterDateTime,callback:function(t){e.$set(e.addForm,"enterDateTime",t)},expression:"addForm.enterDateTime"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"离院日期",prop:"leaveDateTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择离院日期"},model:{value:e.addForm.leaveDateTime,callback:function(t){e.$set(e.addForm,"leaveDateTime",t)},expression:"addForm.leaveDateTime"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"人员类型",prop:"otherEmpTypeId"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.addForm.otherEmpTypeId,callback:function(t){e.$set(e.addForm,"otherEmpTypeId",t)},expression:"addForm.otherEmpTypeId"}},e._l(e.otheremptype,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"在岗状态",prop:"empStatusId"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.addForm.empStatusId,callback:function(t){e.$set(e.addForm,"empStatusId",t)},expression:"addForm.empStatusId"}},e._l(e.empStatusOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"是否纳入核酸",prop:"isNucleicAcid"}},[a("el-checkbox",{model:{value:e.addForm.isNucleicAcid,callback:function(t){e.$set(e.addForm,"isNucleicAcid",t)},expression:"addForm.isNucleicAcid"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.addDialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submitAddForm}},[e._v("保 存")])],1)],1),a("el-dialog",{attrs:{top:"5vh",title:"更新",visible:e.updateDialogVisible,width:"50%"},on:{"update:visible":function(t){e.updateDialogVisible=t},close:e.closeupdateDialog}},[a("el-form",{ref:"ref_updateForm",attrs:{rules:e.rules,model:e.updateForm,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"姓名",prop:"name"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.updateForm.name,callback:function(t){e.$set(e.updateForm,"name",t)},expression:"updateForm.name"}})],1),a("el-form-item",{attrs:{label:"身份证号",prop:"idCard"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.updateForm.idCard,callback:function(t){e.$set(e.updateForm,"idCard",t)},expression:"updateForm.idCard"}})],1),a("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.updateForm.phone,callback:function(t){e.$set(e.updateForm,"phone",t)},expression:"updateForm.phone"}})],1),a("el-form-item",{attrs:{label:"科室",prop:"departmentID"}},[a("el-cascader",{staticStyle:{width:"100%"},attrs:{clearable:"",options:e.departmentOptions,props:{expandTrigger:"hover",value:"id",label:"name",checkStrictly:!0},disabled:!0,placeholder:"请选择部门"},model:{value:e.updateForm.departmentID,callback:function(t){e.$set(e.updateForm,"departmentID",t)},expression:"updateForm.departmentID"}})],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"进院日期",prop:"enterDateTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择进院日期"},model:{value:e.updateForm.enterDateTime,callback:function(t){e.$set(e.updateForm,"enterDateTime",t)},expression:"updateForm.enterDateTime"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"离院日期",prop:"leaveDateTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择离院日期"},model:{value:e.updateForm.leaveDateTime,callback:function(t){e.$set(e.updateForm,"leaveDateTime",t)},expression:"updateForm.leaveDateTime"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"人员类型",prop:"otherEmpTypeId"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.updateForm.otherEmpTypeId,callback:function(t){e.$set(e.updateForm,"otherEmpTypeId",t)},expression:"updateForm.otherEmpTypeId"}},e._l(e.otheremptype,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"在岗状态",prop:"empStatusId"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.updateForm.empStatusId,callback:function(t){e.$set(e.updateForm,"empStatusId",t)},expression:"updateForm.empStatusId"}},e._l(e.empStatusOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"是否纳入核酸",prop:"isNucleicAcid"}},[a("el-checkbox",{model:{value:e.updateForm.isNucleicAcid,callback:function(t){e.$set(e.updateForm,"isNucleicAcid",t)},expression:"updateForm.isNucleicAcid"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.updateDialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submitUpdateForm}},[e._v("保 存")])],1)],1)],1)},i=[],l=(a("d3b7"),a("ac1f"),a("841c"),a("e44c")),r=a("f9ac"),n=a("d368"),s={components:{},data:function(){var e=this,t=function(t,a,o){if(!a)return o();setTimeout((function(){var t=e.addForm.leaveDateTime||e.updateForm.leaveDateTime;if(t){var i=new Date(t),l=new Date(a);l<=i?o():o(new Error("进院日期不得晚于离院日期"))}else o()}),100)},a=function(t,a,o){if(!a)return o();setTimeout((function(){var t=e.addForm.enterDateTime||e.updateForm.enterDateTime;if(t){var i=new Date(t),l=new Date(a);l>=i?o():o(new Error("离院日期不得早于起聘进院。"))}else o()}),100)},o=function(e,t,a){var o=/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;if(!t)return a();setTimeout((function(){o.test(t)?a():a(new Error("请输入正确的身份证号"))}),100)};return{treeData:[],treeExpandedKeys:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},addForm:{departmentID:null,enterDateTime:"",leaveDateTime:"",empStatusId:null,name:"",idCard:"",phone:"",otherEmpTypeId:null,isNucleicAcid:!1},updateForm:{},rules:{name:[{required:!0,message:"[姓名]是必填项！",trigger:"blur"},{max:100,message:"[姓名]超长！",trigger:"blur"}],idCard:[{max:18,message:"[身份证号]超长！",trigger:"blur"},{trigger:"blur",validator:o}],phone:[{max:11,message:"[联系电话]超长！",trigger:"blur"}],enterDateTime:[{validator:t,trigger:"blur"}],leaveDateTime:[{validator:a,trigger:"blur"}],otherEmpTypeId:[{required:!0,message:"[人员类型]是必选项！",trigger:["blur"]}]},addDialogVisible:!1,updateDialogVisible:!1,pageList:[],listQuery:{queryCondition:{},total:1,pageIndex:1,pageSize:10},listLoading:!1,dataColumns:[{value:"1",label:"姓名",type:"System.String",columnName:"Name"},{value:"2",label:"身份证号",type:"System.String",columnName:"IDCard"},{value:"3",label:"联系电话",type:"System.String",columnName:"Phone"}],selectConditionOptions:[],empStatusOptions:[],otheremptype:[],isShowDynamicQueryTable:!1,treeLoading:!1,currentNode:{},departmentOptions:[]}},created:function(){this.getPageList(),this.loadConditions(),this.loadTree(),this.loadEmployeeStatus(),this.loadOtherEmpType(),this.loadDepartment()},methods:{loadOtherEmpType:function(){var e=this;l["a"].queryOtherEmpTypes().then((function(t){e.otheremptype=t.data.datas})).catch((function(e){console.log(e)}))},loadDepartment:function(){var e=this;n["a"].QueryOrganization({}).then((function(t){e.departmentOptions=t.data})).catch((function(e){console.log(e)}))},loadTree:function(){var e=this;this.treeLoading=!0,n["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data,e.treeData&&e.treeData.length>0&&e.treeExpandedKeys.push(e.treeData[0].id)})).catch((function(e){console.log(e)})).finally((function(){e.treeLoading=!1})),this.resetCurrentNode()},resetCurrentNode:function(){this.currentNode=null},treeNodeClick:function(e){this.currentNode=e,this.listQuery.deptId=e.id,this.listQuery.pageIndex=1,this.isShowDynamicQueryTable=!1,this.getPageList()},loadEmployeeStatus:function(){var e=this;l["a"].queryEmployeeStatus().then((function(t){e.empStatusOptions=t.data.datas})).catch((function(e){console.log(e)}))},sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.listQuery.order="",this.search()},search:function(){this.listQuery.pageIndex=1,this.getPageList()},loadConditions:function(){var e=this;r["a"].getEnumInfos({enumType:"Operations"}).then((function(t){e.selectConditionOptions=t.data.datas})).catch((function(e){console.log(e)}))},getPageList:function(){var e=this;this.listLoading=!0,this.listQuery.entityColumn&&""!==this.listQuery.entityColumn&&this.listQuery.queryCondition.EnumOperation?(this.listQuery.queryCondition.Keywords||(this.listQuery.queryCondition.Keywords=""),this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.columnName,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.ConditionList=[this.listQuery.queryCondition]):(this.listQuery.ConditionList=[],this.listQuery.queryCondition={}),l["a"].queryOtherEmployeeInfo(this.listQuery).then((function(t){t.succeed?(e.pageList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1}))},addDialog:function(){this.listQuery.deptId?(this.addForm.departmentID=this.listQuery.deptId,this.addDialogVisible=!0):this.$notice.message("请先选择部门。","info")},closeAddDialog:function(){this.$refs["ref_addForm"].resetFields(),this.$refs["ref_addForm"].clearValidate()},updateDialog:function(e){this.updateDialogVisible=!0,console.log(e),this.updateForm=Object.assign({},e)},closeupdateDialog:function(){this.$refs["ref_updateForm"].resetFields(),this.$refs["ref_updateForm"].clearValidate()},submitAddForm:function(){var e=this;this.$refs["ref_addForm"].validate((function(t){t&&l["a"].saveOtherEmployeeInfo(e.addForm).then((function(t){t.succeed?(e.addDialogVisible=!1,e.search(),e.$notice.message("创建成功","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))}))},submitUpdateForm:function(){var e=this;this.$refs["ref_updateForm"].validate((function(t){t&&l["a"].saveOtherEmployeeInfo(e.updateForm).then((function(t){t.succeed?(e.updateDialogVisible=!1,e.search(),e.$notice.message("更新成功","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))}))},deleteRecord:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteOtherEmployeeInfo({id:e.id}).then((function(e){e.succeed?(t.search(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){console.log(e)}))})).catch((function(e){e.succeed||t.$notice.message("取消删除","info")}))},selectedChange:function(){this.isShowDynamicQueryTable||(this.listQuery.pageIndex=1,this.getPageList())},downloadexceltemplate:function(){l["a"].downlodaImportExcelTemplate({type:"importotheremp"}).then((function(e){var t=a("19de"),o="OtherEmployeeInfoTemplate.xlsx";e.data?t(e.data,o):t(e,o)})).catch((function(e){}))},importExcel:function(e){var t=this,a=e.file;l["a"].importExcel(a,{type:"importotheremp"}).then((function(e){if(e.succeed){var a=e.data;t.$message.success(a),t.search()}})).catch((function(e){}))}}},d=s,u=a("2877"),c=Object(u["a"])(d,o,i,!1,null,"d52574a2",null);t["default"]=c.exports}}]);