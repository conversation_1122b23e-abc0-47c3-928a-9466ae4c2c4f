﻿using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Renji.JHR.Entities
{
    [Flags]
    public enum PositionFlag
    {
        None = 0,

        /// <summary>
        /// 系统
        /// </summary>
        [Description("系统")]
        Sys = 1,

        /// <summary>
        /// 部门职位
        /// </summary>
        [Description("部门职位")]
        [EnumGroup("Edit")]
        Department = 2,

        /// <summary>
        /// 功能型
        /// </summary>
        [Description("功能型")]
        [EnumGroup("Edit")]
        Function = 4,

        /// <summary>
        /// 职务型
        /// </summary>
        [Description("职务型")]
        [EnumGroup("Edit")]
        Title = 8,
    }
}
