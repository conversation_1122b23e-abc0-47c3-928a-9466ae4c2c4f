(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7ca0120a"],{"35e2":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container "},[i("layout1",{scopedSlots:e._u([{key:"header",fn:function(){return[e.userPermission.isShowGeneralHoliday?i("el-button",{attrs:{type:"primary",icon:"el-icon-refresh"},on:{click:e.updateGeneralHoliday}},[e._v("公休年初更新")]):e._e(),e.userPermission.isShowAdd?i("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:e.addEmp}},[e._v("添加")]):e._e(),e.userPermission.isShowQuery?i("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.querySetting}},[e._v("查询设置")]):e._e(),e.userPermission.isShowDelete?i("el-button",{attrs:{type:"primary",icon:"el-icon-delete"},on:{click:e.deleteData}},[e._v("删除")]):e._e()]},proxy:!0},{key:"aside",fn:function(){return[i("el-checkbox",{model:{value:e.listQuery.IsContainSubDept,callback:function(t){e.$set(e.listQuery,"IsContainSubDept",t)},expression:"listQuery.IsContainSubDept"}},[e._v("包含下级部门")]),i("c-tree",{directives:[{name:"loading",rawName:"v-loading",value:e.treeLoading,expression:"treeLoading"}],attrs:{options:e.treeData,props:e.treeProps,"expanded-keys":e.treeExpandedKeys},on:{nodeClick:e.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[i("el-card",[i("div",{staticClass:"filter-container"},[i("el-form",{attrs:{inline:!0,model:e.listQuery}},[i("el-form-item",{attrs:{label:"唯一码"}},[i("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",placeholder:"唯一码"},model:{value:e.listQuery.empUid,callback:function(t){e.$set(e.listQuery,"empUid",t)},expression:"listQuery.empUid"}})],1),i("el-form-item",{attrs:{label:"员工状态"}},[i("el-select",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{"value-key":"id",clearable:"",placeholder:"请选择"},model:{value:e.selectedStatus,callback:function(t){e.selectedStatus=t},expression:"selectedStatus"}},e._l(e.empStatusOptions,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e}})})),1)],1),e.listQuery.EnumStatus&&1==e.listQuery.EnumStatus?i("el-form-item",{attrs:{label:"在职方式"}},[i("el-select",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.listQuery.HireStyleId,callback:function(t){e.$set(e.listQuery,"HireStyleId",t)},expression:"listQuery.HireStyleId"}},e._l(e.empHireStyleOptions,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1):e.listQuery.EnumStatus&&2==e.listQuery.EnumStatus?i("el-form-item",{attrs:{label:"离职方式"}},[i("el-select",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.listQuery.LeaveStyleId,callback:function(t){e.$set(e.listQuery,"LeaveStyleId",t)},expression:"listQuery.LeaveStyleId"}},e._l(e.empLeaveStyleOptions,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1):e._e(),i("el-form-item",{attrs:{label:"职别"}},[i("el-select",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.listQuery.OfficialRankId,callback:function(t){e.$set(e.listQuery,"OfficialRankId",t)},expression:"listQuery.OfficialRankId"}},e._l(e.empOfficialRankOptions,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1)],1)],1),e.isShowDynamicQueryTable?e._e():i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageEmpList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"row-click":e.rowChange}},[i("el-table-column",{attrs:{fixed:"",label:"唯一码",sortable:"custom",prop:"Uid","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",{staticClass:"link-type",on:{click:function(t){return e.handleWatchEmpInfo(a)}}},[e._v(e._s(a.uid))])]}}],null,!1,2374015891)}),i("el-table-column",{attrs:{label:"工号","min-width":"80px",sortable:"custom",prop:"EmpCode"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.empCode))])]}}],null,!1,2960556977)}),i("el-table-column",{attrs:{label:"姓名","min-width":"80px",sortable:"custom",prop:"DisplayName"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.displayName))])]}}],null,!1,3748713369)}),i("el-table-column",{attrs:{label:"部门","min-width":"80px",sortable:"custom",prop:"Department.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.departmentName))])]}}],null,!1,3021220391)}),i("el-table-column",{attrs:{label:"岗位","min-width":"80px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.majorStation?a.majorStation.positionName:""))])]}}],null,!1,1022113493)}),i("el-table-column",{attrs:{label:"职别","min-width":"80px",sortable:"custom",prop:"EmployeeHR.OfficialRank.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.officialRank))])]}}],null,!1,2643951635)}),i("el-table-column",{attrs:{label:"员工状态","min-width":"120px",sortable:"custom",prop:"EnumStatus"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.empStatusName))])]}}],null,!1,3296281327)}),i("el-table-column",{attrs:{label:"在职方式","min-width":"120px",sortable:"custom",prop:"EmployeeHR.HireStyle.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.hireStyle))])]}}],null,!1,1488854117)}),i("el-table-column",{attrs:{label:"离职方式","min-width":"120px",sortable:"custom",prop:"EmployeeHR.LeaveStyle.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.leaveStyle))])]}}],null,!1,93469992)})],1),e.isShowDynamicQueryTable?e._e():i("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getEmpList}}),e.isShowDynamicQueryTable?i("el-table",{staticStyle:{width:"100%"},attrs:{data:e.pageEmpInfoList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChangeForEmpInfo,"row-click":e.rowChange}},[i("el-table-column",{attrs:{fixed:"",label:"唯一码",sortable:"custom",prop:"Uid","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",{staticClass:"link-type",on:{click:function(t){return e.handleWatchEmpInfo(a)}}},[e._v(e._s(a.uid))])]}}],null,!1,2374015891)}),i("el-table-column",{attrs:{label:"工号","min-width":"80px",sortable:"custom",prop:"EmpCode"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.empCode))])]}}],null,!1,2960556977)}),i("el-table-column",{attrs:{label:"姓名","min-width":"80px",sortable:"custom",prop:"DisplayName"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.displayName))])]}}],null,!1,3748713369)}),i("el-table-column",{attrs:{label:"部门","min-width":"80px",sortable:"custom",prop:"DepartmentName"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.departmentName))])]}}],null,!1,3021220391)}),i("el-table-column",{attrs:{label:"岗位","min-width":"80px",sortable:"custom",prop:"PositionName"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.positionName))])]}}],null,!1,619372506)}),i("el-table-column",{attrs:{label:"职别","min-width":"80px",sortable:"custom",prop:"OfficialRankName"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.officialRankName))])]}}],null,!1,2473209300)}),i("el-table-column",{attrs:{label:"员工状态","min-width":"120px",sortable:"custom",prop:"EmpStatusName"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.empStatusName))])]}}],null,!1,3296281327)}),i("el-table-column",{attrs:{label:"在职方式","min-width":"120px",sortable:"custom",prop:"HireStyleName"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.hireStyleName))])]}}],null,!1,3892048706)}),i("el-table-column",{attrs:{label:"离职方式","min-width":"120px",sortable:"custom",prop:"LeaveStyleName"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.leaveStyleName))])]}}],null,!1,2688899183)})],1):e._e(),e.isShowDynamicQueryTable?i("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQueryEmpInfo.total>0,expression:"listQueryEmpInfo.total > 0"}],attrs:{total:e.listQueryEmpInfo.total,"page-sizes":[10,20,50],page:e.listQueryEmpInfo.pageIndex,limit:e.listQueryEmpInfo.pageSize},on:{"update:page":function(t){return e.$set(e.listQueryEmpInfo,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQueryEmpInfo,"pageSize",t)},pagination:e.queryEmployeeByConditions}}):e._e()],1)]},proxy:!0}])}),e.dialogAppInfoVisible?i("el-dialog",{staticClass:"empManager",attrs:{title:e.empDialogTitle,visible:e.dialogAppInfoVisible,width:"90%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogAppInfoVisible=t}}},[i("empInfo",{ref:"empInfo",attrs:{"dept-id":e.listQuery.deptId,"emp-id":e.empId,"user-permission-prop":e.userPermission},on:{updateEmpId:e.updateEmpId}})],1):e._e(),e.dialogQuerySettingVisible?i("el-dialog",{staticClass:"empManager",attrs:{title:"查询设置",visible:e.dialogQuerySettingVisible,width:"90%","before-close":e.hideQuerySettingDialog},on:{"update:visible":function(t){e.dialogQuerySettingVisible=t}}},[i("querySetting",{ref:"querySetting",on:{search:e.quickSearch,CloseDialog:e.hideQuerySettingDialog}})],1):e._e()],1)},s=[],n=(i("d3b7"),i("d368")),o=i("e44c"),l=i("91a7"),r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("el-tabs",{attrs:{type:"card"},on:{"tab-click":e.tabClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[i("el-tab-pane",{attrs:{label:"查询设置",name:"querySetting"}},[i("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"100px"}},[i("el-row",{staticStyle:{"padding-top":"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"逻辑关系",prop:"enumLogicRelationships"}},[i("el-select",{staticStyle:{width:"500px"},attrs:{"value-key":"value",clearable:"",placeholder:"请选择逻辑关系"},on:{change:e.relationChange},model:{value:e.tempData.tempFormModel.enumLogicRelationships,callback:function(t){e.$set(e.tempData.tempFormModel,"enumLogicRelationships",t)},expression:"tempData.tempFormModel.enumLogicRelationships"}},e._l(e.logicRelationshipOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.desc,value:e}})})),1)],1)],1),i("el-col",{attrs:{span:2}}),i("el-col",{attrs:{span:10}},[i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.quickSearch()}}},[e._v("快捷查询")])],1)],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"字段",prop:"selectedColumn"}},[i("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{"value-key":"id",clearable:"",placeholder:"请选择字段"},on:{change:e.columnChange},model:{value:e.tempData.tempFormModel.selectedColumn,callback:function(t){e.$set(e.tempData.tempFormModel,"selectedColumn",t)},expression:"tempData.tempFormModel.selectedColumn"}},e._l(e.empColumnOptions,(function(e){return i("el-option",{key:e.id,attrs:{label:e.displayName,value:e}})})),1)],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"运算符",prop:"selectedCondition"}},[i("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{"value-key":"value",clearable:"",placeholder:"请选择运算符"},on:{change:e.conditionChange},model:{value:e.tempData.tempFormModel.selectedCondition,callback:function(t){e.$set(e.tempData.tempFormModel,"selectedCondition",t)},expression:"tempData.tempFormModel.selectedCondition"}},e._l(e.selectConditionOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.desc,value:e}})})),1)],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"关键字",prop:"keywords"}},[i("el-input",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请输入关键字"},model:{value:e.tempData.tempFormModel.keywords,callback:function(t){e.$set(e.tempData.tempFormModel,"keywords",t)},expression:"tempData.tempFormModel.keywords"}})],1)],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:24}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"dataListTable",staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}}},[i("el-table-column",{attrs:{type:"selection",width:"55"}}),i("el-table-column",{attrs:{label:"关系","min-width":"80px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.displayRelationship))])]}}])}),i("el-table-column",{attrs:{label:"运算符","min-width":"300px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.children?e._e():i("span",[e._v(e._s(a.displayName)+e._s(a.displayOperation)+e._s(a.keywords))]),a.children?i("span",[e._v(e._s(a.description))]):e._e()]}}])}),i("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteData(a)}}},[e._v(" 删除 ")])]}}])})],1)],1)],1),i("el-row",{staticStyle:{padding:"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:24,align:"center"}},[i("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.addData(e.tempData.tempFormModel)}}},[e._v("添加")]),i("el-button",{attrs:{type:"primary",icon:"el-icon-connection"},on:{click:e.showCombineData}},[e._v("组合")]),i("el-button",{attrs:{type:"primary",icon:"el-icon-check"},on:{click:function(t){return e.search()}}},[e._v("确定")]),i("el-button",{attrs:{type:"warning",icon:"el-icon-refresh-left"},on:{click:function(t){return e.cancle()}}},[e._v("取消")]),i("el-button",{attrs:{type:"primary",icon:"el-icon-document"},on:{click:function(t){return e.showSaveDialog()}}},[e._v("保存")])],1)],1)],1),e.dialogCombineVisible?i("el-dialog",{attrs:{title:"组合",visible:e.dialogCombineVisible,"append-to-body":""},on:{"update:visible":function(t){e.dialogCombineVisible=t}}},[i("el-row",{staticStyle:{padding:"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:24,align:"center"}},[i("el-radio",{attrs:{label:10},model:{value:e.relationshipForCombine,callback:function(t){e.relationshipForCombine=t},expression:"relationshipForCombine"}},[e._v("AND组合")]),i("el-radio",{attrs:{label:20},model:{value:e.relationshipForCombine,callback:function(t){e.relationshipForCombine=t},expression:"relationshipForCombine"}},[e._v("OR组合")])],1)],1),i("el-row",{staticStyle:{padding:"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:24,align:"center"}},[i("el-button",{attrs:{type:"primary",icon:"el-icon-check"},on:{click:e.combineData}},[e._v("保存")]),i("el-button",{attrs:{type:"warning",icon:"el-icon-circle-close"},on:{click:function(t){return e.colseCombine()}}},[e._v("关闭")])],1)],1)],1):e._e(),e.dialogSaveVisible?i("el-dialog",{attrs:{title:"保存查询",visible:e.dialogSaveVisible,"append-to-body":""},on:{"update:visible":function(t){e.dialogSaveVisible=t}}},[i("el-form",{ref:"dataFormForSave",attrs:{rules:e.rulesForSave,model:e.tempData.tempFormModelForSave,"label-position":"right","label-width":"100px"}},[i("el-row",{staticStyle:{"padding-top":"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"名称",prop:"name"}},[i("el-input",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请输入名称"},model:{value:e.tempData.tempFormModelForSave.name,callback:function(t){e.$set(e.tempData.tempFormModelForSave,"name",t)},expression:"tempData.tempFormModelForSave.name"}})],1)],1)],1),i("el-row",{staticStyle:{padding:"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:24,align:"center"}},[i("el-button",{attrs:{type:"primary",icon:"el-icon-check"},on:{click:e.save}},[e._v("保存")]),i("el-button",{attrs:{type:"warning",icon:"el-icon-circle-close"},on:{click:e.cancleSave}},[e._v("取消")])],1)],1)],1)],1):e._e()],1),i("el-tab-pane",{attrs:{label:"已存查询",name:"savedQuery"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageSettingList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}}},[i("el-table-column",{attrs:{label:"名称","min-width":"200px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",{staticClass:"link-type",on:{click:function(t){return e.handleQuickSearch(a)}}},[e._v(e._s(a.name))])]}}])}),i("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteSetting(a)}}},[e._v(" 删除 ")])]}}])})],1),i("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQueryForSetting.total>0,expression:"listQueryForSetting.total > 0"}],attrs:{total:e.listQueryForSetting.total,"page-sizes":[10,20,50],page:e.listQueryForSetting.pageIndex,limit:e.listQueryForSetting.pageSize},on:{"update:page":function(t){return e.$set(e.listQueryForSetting,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQueryForSetting,"pageSize",t)},pagination:e.getSettingList}})],1)],1)],1)},c=[],u=(i("a4d3"),i("e01a"),i("4de4"),i("c975"),i("a434"),i("b0c0"),i("f9ac")),d={name:"",components:{},data:function(){return{activeName:"querySetting",dataList:[],listQuery:{conditionList:[]},listLoading:!1,rules:{keywords:[{max:100,message:"关键字不允许超过100个字符",trigger:"blur"}]},tempData:{tempFormModel:{keywords:""},tempFormModelForSave:{}},logicRelationshipOptions:[],empColumnOptions:[],selectConditionOptions:[],dialogCombineVisible:!1,relationshipForCombine:10,rulesForSave:{name:[{required:!0,message:"请输入名称",trigger:"blur"},{max:100,message:"名称不允许超过100个字符",trigger:"blur"}]},dialogSaveVisible:!1,pageSettingList:[],listQueryForSetting:{total:1,pageIndex:1,pageSize:10}}},created:function(){this.loadRelationships(),this.loadColumns(),this.loadConditions()},methods:{tabClick:function(e){"savedQuery"===e.name&&(this.listQueryForSetting.pageIndex=1,this.reloadSettingList())},loadRelationships:function(){var e=this;u["a"].getEnumInfos({enumType:"LogicRelationships"}).then((function(t){e.logicRelationshipOptions=t.data.datas,e.logicRelationshipOptions.length>0&&(e.tempData.tempFormModel.enumLogicRelationships=e.logicRelationshipOptions[0])})).catch((function(e){console.log(e)}))},loadColumns:function(){var e=this;o["a"].querySettingColumns().then((function(t){e.empColumnOptions=t.data.datas,e.empColumnOptions.length>0&&(e.tempData.tempFormModel.selectedColumn=e.empColumnOptions[0])})).catch((function(e){console.log(e)}))},loadConditions:function(){var e=this;u["a"].getEnumInfos({enumType:"Operations"}).then((function(t){e.selectConditionOptions=t.data.datas,e.selectConditionOptions.length>0&&(e.tempData.tempFormModel.selectedCondition=e.selectConditionOptions[e.selectConditionOptions.length-1])})).catch((function(e){console.log(e)}))},columnChange:function(e){this.$forceUpdate()},conditionChange:function(e){this.$forceUpdate()},relationChange:function(e){this.$forceUpdate()},quickSearch:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var i=e.tempData.tempFormModel,a={enumLogicRelationships:i.enumLogicRelationships.value,displayRelationship:i.enumLogicRelationships.desc,entityColumnName:i.selectedColumn.entityColumnName,displayName:i.selectedColumn.displayName,entityColumnType:i.selectedColumn.entityColumnType,enumOperations:i.selectedCondition.value,displayOperation:i.selectedCondition.desc,keywords:e.tempData.tempFormModel.keywords};e.listQuery.conditionList=e.dataList,e.listQuery.conditionList.push(a),e.listQuery.employeeQuerySettingId&&delete e.listQuery.employeeQuerySettingId,e.$emit("search",e.listQuery)}}))},search:function(){this.listQuery.conditionList=this.dataList,this.listQuery.conditionList.length>0&&(this.listQuery.employeeQuerySettingId&&delete this.listQuery.employeeQuerySettingId,this.$emit("search",this.listQuery))},handleQuickSearch:function(e){this.listQuery.conditionList&&delete this.listQuery.conditionList,this.listQuery.employeeQuerySettingId=e.id,this.$emit("search",this.listQuery)},clear:function(){this.$refs["dataForm"].resetFields(),this.tempData.tempFormModel={},this.dataList=[]},addData:function(e){var t={enumLogicRelationships:e.enumLogicRelationships.value,displayRelationship:e.enumLogicRelationships.desc,entityColumnName:e.selectedColumn.entityColumnName,displayName:e.selectedColumn.displayName,entityColumnType:e.selectedColumn.entityColumnType,enumOperations:e.selectedCondition.value,displayOperation:e.selectedCondition.desc,keywords:e.keywords};t.description=t.displayName+" "+t.displayOperation+" "+t.keywords,this.dataList.push(t)},deleteData:function(e){this.dataList.splice(this.dataList.indexOf(e),1)},showCombineData:function(){this.dialogCombineVisible=!0},colseCombine:function(){this.dialogCombineVisible=!1,this.relationshipForCombine=10},combineData:function(){var e=this,t=this.$refs.dataListTable.selection;if(!t||t.length<2)this.$notice.message("所选记录数量不足，无需组合。","info");else{for(var i={enumLogicRelationships:t[0].enumLogicRelationships,displayRelationship:t[0].displayRelationship,description:""},a=[],s=0;s<t.length;s++){var n=JSON.parse(JSON.stringify(t[s]));n.enumLogicRelationships=this.relationshipForCombine,n.displayRelationship=this.logicRelationshipOptions.filter((function(t){return t.value===e.relationshipForCombine}))[0].desc,a.push(n),i.description=0===s?n.description:i.description+" "+n.displayRelationship+" "+n.description,this.dataList.splice(this.dataList.indexOf(t[s]),1)}i.children=a,this.dataList.push(i),this.colseCombine()}},showSaveDialog:function(){this.dialogSaveVisible=!0},save:function(){var e=this;if(this.addData(this.tempData.tempFormModel),!this.dataList||this.dataList&&0===this.dataList)this.$notice.message("列表中无数据，无法保存。","info");else{var t={name:this.tempData.tempFormModelForSave.name,employeeQuerySettingItem:this.dataList};this.$refs["dataFormForSave"].validate((function(i){i&&o["a"].addEmployeeQuerySetting(t).then((function(t){t.succeed?(e.$notice.message("保存成功","success"),e.activeName="savedQuery",e.reloadSettingList(),e.clearSaveForm(),e.colseCombine(),e.dialogSaveVisible=!1):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("保存失败。","error")}))}))}},cancle:function(){this.$emit("CloseDialog")},cancleSave:function(){this.dialogSaveVisible=!1,this.clearSaveForm()},clearSaveForm:function(){this.$refs["dataFormForSave"].resetFields(),this.tempData.tempFormModelForSave={}},getSettingList:function(){var e=this;o["a"].queryEmployeeQuerySetting(this.listQueryForSetting).then((function(t){t.succeed?(e.pageSettingList=t.data.datas,e.listQueryForSetting.total=t.data.recordCount,e.listQueryForSetting.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))},reloadSettingList:function(){this.listQueryForSetting.pageIndex=1,this.getSettingList()},deleteSetting:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){o["a"].deleteEmployeeQuerySetting(e).then((function(e){e.succeed?(t.getSettingList(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))}}},p=d,m=(i("5591"),i("2877")),h=Object(m["a"])(p,r,c,!1,null,"a0937b1a",null),f=h.exports,b={components:{empInfo:l["a"],querySetting:f},data:function(){return{treeData:[],treeExpandedKeys:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},empDialogTitle:"人事信息",selected:"",currentNode:{},includeDownDept:!1,empStatusOptions:[],empOfficialRankOptions:[],empHireStyleOptions:[],empLeaveStyleOptions:[],empList:[],pageEmpList:[],total:1,listQuery:{pageIndex:1,pageSize:10},listLoading:!1,treeLoading:!1,dialogAppInfoVisible:!1,activeName:"tab1",empId:"",selectedStatus:{code:"1",name:"在职"},dialogQuerySettingVisible:!1,isShowDynamicQueryTable:!1,pageEmpInfoList:[],listQueryEmpInfo:{total:1,pageIndex:1,pageSize:10},userPermission:{},rowData:{}}},created:function(){this.loadButtonPermission(),this.loadTree(),this.loadEmployeeStatus(),this.loadEmployeeOfficialRank(),this.loadEmployeeHireStyle(),this.loadEmployeeLeaveStyle()},methods:{loadEmployeeStatus:function(){var e=this;o["a"].queryEmployeeStatus().then((function(t){e.empStatusOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadEmployeeOfficialRank:function(){var e=this;o["a"].queryOfficialRank().then((function(t){e.empOfficialRankOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadEmployeeHireStyle:function(){var e=this;o["a"].queryHireStyle().then((function(t){e.empHireStyleOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadEmployeeLeaveStyle:function(){var e=this;o["a"].queryLeaveStyle().then((function(t){e.empLeaveStyleOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadTree:function(){var e=this;this.treeLoading=!0,n["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data,e.treeData&&e.treeData.length>0&&e.treeExpandedKeys.push(e.treeData[0].id)})).catch((function(e){console.log(e)})).finally((function(){e.treeLoading=!1})),this.resetCurrentNode()},resetCurrentNode:function(){this.currentNode=null},treeNodeClick:function(e){this.currentNode=e,this.listQuery.deptId=e.id,this.listQuery.pageIndex=1,this.isShowDynamicQueryTable=!1,this.getEmpList()},statusSelectedChange:function(e){this.listQuery.EnumStatus=e.code,this.selectedChange()},search:function(){this.getEmpList()},selectedChange:function(){this.isShowDynamicQueryTable||(this.listQuery.pageIndex=1,this.getEmpList())},getEmpList:function(){var e=this;this.listQuery.deptId&&(this.listLoading=!0,this.selectedStatus&&this.selectedStatus.id?this.listQuery.empStatusId=this.selectedStatus.id:this.listQuery.empStatusId=null,this.listQuery.EnumStatus||(this.listQuery.LeaveStyleId=null,this.listQuery.HireStyleId=null),this.listQuery.EnumStatus&&"1"===this.listQuery.EnumStatus&&(this.listQuery.LeaveStyleId=null),this.listQuery.EnumStatus&&"2"===this.listQuery.EnumStatus&&(this.listQuery.HireStyleId=null),o["a"].queryEmployee(this.listQuery).then((function(t){t.succeed?(e.pageEmpList=t.data.datas,e.listQuery.total=t.data.recordCount,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1})))},sortChange:function(e,t,i){this.listQuery.pageIndex=1;var a="";"descending"===e.order&&(a="desc"),"ascending"===e.order&&(a="asc"),this.listQuery.order=e.prop+" "+a,this.getEmpList()},rowChange:function(e,t,i){this.rowData=e},addEmp:function(){this.listQuery.deptId?(this.empId="",this.empDialogTitle="人事信息",this.dialogAppInfoVisible=!0):this.$notice.message("请先选择部门。","info")},clear:function(){this.$refs["empInfo"].clear()},handleClose:function(){this.dialogAppInfoVisible=!1,this.clear()},tabClick:function(e,t){console.log(e,t)},handleWatchEmpInfo:function(e){var t=this.$router.resolve({path:"/empInfoBlank",query:{deptId:e.deptId,empId:e.id,userPermission:JSON.stringify(this.userPermission)}});window.open(t.href,"_blank","400,400")},updateCompanyAge:function(){var e=this;this.$confirm("确定工龄更新吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){o["a"].updateCompanyAge().then((function(t){t.succeed?e.$notice.message("工龄更新成功","success"):t.succeed||e.$notice.message("工龄更新失败，请联系管理员","info")})).catch((function(t){e.listLoading=!1,console.log(t)}))})).catch((function(t){e.listLoading=!1,t.succeed||e.$notice.message("取消工龄更新","info")}))},updateGeneralHoliday:function(){var e=this;this.$confirm("确定公休更新吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){o["a"].calculateGeneralHoliday().then((function(t){t.succeed?e.$notice.message("公休更新成功","success"):t.succeed||e.$notice.message("公休更新失败，请联系管理员","info")})).catch((function(t){e.listLoading=!1,console.log(t)}))})).catch((function(t){e.listLoading=!1,t.succeed||e.$notice.message("取消公休更新","info")}))},deleteData:function(){"{}"!==JSON.stringify(this.rowData)?this.deleteEmp(this.rowData):this.$notice.message("请先选择记录","info")},deleteEmp:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){o["a"].deleteEmployee(e).then((function(e){e.succeed?(t.rowData={},t.isShowDynamicQueryTable?t.queryEmployeeByConditions():t.getEmpList(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))},updateEmpId:function(e){this.empId=e},querySetting:function(){this.dialogQuerySettingVisible=!0},quickSearch:function(e){this.hideQuerySettingDialog(),e.conditionList&&(this.listQueryEmpInfo.conditionList=e.conditionList),e.employeeQuerySettingId&&(this.listQueryEmpInfo.employeeQuerySettingId=e.employeeQuerySettingId),this.listQueryEmpInfo.pageIndex=1,this.isShowDynamicQueryTable=!0,this.currentNode=null,this.listQuery.deptId&&delete this.listQuery.deptId,this.queryEmployeeByConditions()},queryEmployeeByConditions:function(){var e=this;o["a"].queryEmployeeByConditions(this.listQueryEmpInfo).then((function(t){t.succeed?(e.pageEmpInfoList=t.data.datas,e.listQueryEmpInfo.total=t.data.recordCount,e.listQueryEmpInfo.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))},sortChangeForEmpInfo:function(e,t,i){console.log(e),console.log(t),console.log(i),this.listQueryEmpInfo.pageIndex=1;var a="";"descending"===e.order&&(a="desc"),"ascending"===e.order&&(a="asc"),this.listQueryEmpInfo.order=e.prop+" "+a,this.queryEmployeeByConditions()},hideQuerySettingDialog:function(){this.dialogQuerySettingVisible=!1,this.$refs["querySetting"].clear()},loadButtonPermission:function(){var e=this;u["a"].getControlRightByCurrentUser().then((function(t){e.userPermission=t.data})).catch((function(e){console.log(e)}))}}},y=b,g=(i("5991"),i("e443"),Object(m["a"])(y,a,s,!1,null,"df6cc876",null));t["default"]=g.exports},5591:function(e,t,i){"use strict";var a=i("79db"),s=i.n(a);s.a},5991:function(e,t,i){"use strict";var a=i("ed97"),s=i.n(a);s.a},"79db":function(e,t,i){},"91a7":function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container "},[i("el-tabs",{attrs:{type:"card","before-leave":e.beforeLeaveTab},on:{"tab-click":e.tabClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[e.userPermission.hrBaseInfo&&e.userPermission.hrBaseInfo.isShow?i("el-tab-pane",{attrs:{label:"基本信息",name:"baseInfoTab"}},[e.baseInfoVisiable?i("baseInfo",{ref:"baseInfo",attrs:{"user-permission":e.userPermission.hrBaseInfo,"dept-id":e.deptId,"emp-id":e.empId},on:{updateEmpId:e.updateEmpId}}):e._e()],1):e._e(),e.userPermission.hrInfo&&e.userPermission.hrInfo.isShow?i("el-tab-pane",{attrs:{label:"人事信息",name:"hRInfoTab"}},[e.hRInfoVisiable?i("hRInfo",{ref:"hRInfo",attrs:{"user-permission":e.userPermission.hrInfo,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.party&&e.userPermission.party.isShow?i("el-tab-pane",{attrs:{label:"政治面貌",name:"partyTab"}},[e.partyVisiable?i("party",{ref:"party",attrs:{"user-permission":e.userPermission.party,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.station&&e.userPermission.station.isShow?i("el-tab-pane",{attrs:{label:"聘任职务",name:"stationTab"}},[e.stationVisiable?i("station",{ref:"station",attrs:{"user-permission":e.userPermission.station,"emp-id":e.empId,"contain-rank":e.containRank,estype:1}}):e._e()],1):e._e(),e.userPermission.qualification&&e.userPermission.qualification.isShow?i("el-tab-pane",{attrs:{label:"聘任职称",name:"qualificationTab"}},[e.qualificationVisiable?i("station",{ref:"qualification",attrs:{"user-permission":e.userPermission.qualification,"emp-id":e.empId,"not-contain-rank":e.notContainRank,estype:2}}):e._e()],1):e._e(),e.userPermission.certify&&e.userPermission.certify.isShow?i("el-tab-pane",{attrs:{label:"职称资格",name:"certifyTab"}},[e.certifyVisiable?i("certify",{ref:"certify",attrs:{"user-permission":e.userPermission.certify,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.wages&&e.userPermission.wages.isShow?i("el-tab-pane",{attrs:{label:"工资",name:"wagesTab"}},[e.wagesVisiable?i("wages",{ref:"wages",attrs:{"user-permission":e.userPermission.wages,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.socialsecurity&&e.userPermission.socialsecurity.isShow?i("el-tab-pane",{attrs:{label:"社保",name:"socialsecurityTab"}},[e.socialsecurityVisiable?i("socialsecurity",{ref:"socialsecurity",attrs:{"user-permission":e.userPermission.socialsecurity,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.certieducation&&e.userPermission.certieducation.isShow?i("el-tab-pane",{attrs:{label:"学习经历",name:"educationTab"}},[e.educationVisiable?i("education",{ref:"education",attrs:{"user-permission":e.userPermission.certieducation,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.work&&e.userPermission.work.isShow?i("el-tab-pane",{attrs:{label:"工作经历",name:"workTab"}},[e.workVisiable?i("work",{ref:"work",attrs:{"user-permission":e.userPermission.work,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.abroadInfo&&e.userPermission.abroadInfo.isShow?i("el-tab-pane",{attrs:{label:"出国",name:"abroadInfoTab"}},[e.abroadInfoVisiable?i("abroadInfo",{ref:"abroadInfo",attrs:{"user-permission":e.userPermission.abroadInfo,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.contract&&e.userPermission.contract.isShow?i("el-tab-pane",{attrs:{label:"合同",name:"contractTab"}},[e.contractVisiable?i("contract",{ref:"contract",attrs:{"user-permission":e.userPermission.contract,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.train&&e.userPermission.train.isShow?i("el-tab-pane",{attrs:{label:"培养计划",name:"trainTab"}},[e.trainVisiable?i("train",{ref:"train",attrs:{"user-permission":e.userPermission.train,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.assessment&&e.userPermission.assessment.isShow?i("el-tab-pane",{attrs:{label:"考核",name:"assessmentTab"}},[e.assessmentVisiable?i("assessment",{ref:"assessment",attrs:{"user-permission":e.userPermission.assessment,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.incentive&&e.userPermission.incentive.isShow?i("el-tab-pane",{attrs:{label:"医德档案",name:"incentiveTab"}},[e.incentiveVisiable?i("incentive",{ref:"incentive",attrs:{"user-permission":e.userPermission.incentive,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.health&&e.userPermission.health.isShow?i("el-tab-pane",{attrs:{label:"健康信息",name:"healthTab"}},[e.healthVisiable?i("health",{ref:"health",attrs:{"user-permission":e.userPermission.health,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.accident&&e.userPermission.accident.isShow?i("el-tab-pane",{attrs:{label:"医疗",name:"accidentTab"}},[e.accidentVisiable?i("accident",{ref:"accident",attrs:{"user-permission":e.userPermission.accident,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.teach&&e.userPermission.teach.isShow?i("el-tab-pane",{attrs:{label:"教学信息",name:"teachTab"}},[e.teachVisiable?i("teach",{ref:"teach",attrs:{"user-permission":e.userPermission.teach,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.tech&&e.userPermission.tech.isShow?i("el-tab-pane",{attrs:{label:"科研信息",name:"empArticleTab"}},[e.empArticleVisiable?i("empArticle",{ref:"empArticle",attrs:{"user-permission":e.userPermission.tech,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.relation&&e.userPermission.relation.isShow?i("el-tab-pane",{attrs:{label:"社会关系",name:"relationTab"}},[e.relationVisiable?i("relation",{ref:"relation",attrs:{"user-permission":e.userPermission.relation,"emp-id":e.empId}}):e._e()],1):e._e()],1)],1)},s=[],n=(i("b0c0"),i("4fad"),i("498a"),i("2195")),o=i("2cf0"),l=i("185f"),r=i("e061"),c=i("c2f3"),u=i("de23"),d=i("12ed"),p=i("d8b5"),m=i("b96e"),h=i("3220"),f=i("f98d"),b=i("a8a1"),y=i("e675"),g=i("a4b1"),v=i("ff52"),S=i("d336"),I=i("5f06"),w=i("c69e"),k=i("cbd9"),_=i("f9ac"),C={components:{baseInfo:n["a"],hRInfo:o["a"],party:l["a"],station:r["a"],certify:c["a"],wages:u["a"],socialsecurity:d["a"],education:p["a"],work:m["a"],abroadInfo:h["a"],contract:f["a"],train:b["a"],assessment:y["a"],incentive:g["a"],health:v["a"],accident:S["a"],teach:I["a"],relation:w["a"],empArticle:k["a"]},props:{deptId:{type:String,default:""},empId:{type:String,default:""},userPermissionProp:{type:Object,default:function(){return{}}}},data:function(){return{userPermission:this.userPermissionProp,departmentId:"",activeName:"baseInfoTab",containRank:["06"],notContainRank:["06"],baseInfoVisiable:!0,hRInfoVisiable:!1,partyVisiable:!1,stationVisiable:!1,qualificationVisiable:!1,certifyVisiable:!1,wagesVisiable:!1,socialsecurityVisiable:!1,educationVisiable:!1,workVisiable:!1,abroadInfoVisiable:!1,contractVisiable:!1,trainVisiable:!1,assessmentVisiable:!1,incentiveVisiable:!1,healthVisiable:!1,accidentVisiable:!1,teachVisiable:!1,empArticleVisiable:!1,relationVisiable:!1}},watch:{deptId:function(e){this.departmentId=e}},created:function(){this.loadUserPermission()},mounted:function(){},methods:{beforeLeaveTab:function(e,t){return!!this.empId||(this.$notice.message("请先新增基本信息。","info"),!1)},tabClick:function(e){switch(e.name){case"baseInfoTab":this.baseInfoVisiable=!0;break;case"hRInfoTab":this.hRInfoVisiable=this.empId&&""!==this.empId.trim();break;case"partyTab":this.partyVisiable=this.empId&&""!==this.empId.trim();break;case"stationTab":this.stationVisiable=this.empId&&""!==this.empId.trim();break;case"qualificationTab":this.qualificationVisiable=this.empId&&""!==this.empId.trim();break;case"wagesTab":this.wagesVisiable=this.empId&&""!==this.empId.trim();break;case"socialsecurityTab":this.socialsecurityVisiable=this.empId&&""!==this.empId.trim();break;case"educationTab":this.educationVisiable=this.empId&&""!==this.empId.trim();break;case"workTab":this.workVisiable=this.empId&&""!==this.empId.trim();break;case"abroadInfoTab":this.abroadInfoVisiable=this.empId&&""!==this.empId.trim();break;case"contractTab":this.contractVisiable=this.empId&&""!==this.empId.trim();break;case"trainTab":this.trainVisiable=this.empId&&""!==this.empId.trim();break;case"assessmentTab":this.assessmentVisiable=this.empId&&""!==this.empId.trim();break;case"incentiveTab":this.incentiveVisiable=this.empId&&""!==this.empId.trim();break;case"healthTab":this.healthVisiable=this.empId&&""!==this.empId.trim();break;case"accidentTab":this.accidentVisiable=this.empId&&""!==this.empId.trim();break;case"teachTab":this.teachVisiable=this.empId&&""!==this.empId.trim();break;case"empArticleTab":this.empArticleVisiable=this.empId&&""!==this.empId.trim();break;case"relationTab":this.relationVisiable=this.empId&&""!==this.empId.trim();break;case"certifyTab":this.certifyVisiable=this.empId&&""!==this.empId.trim();break;default:this.baseInfoVisiable=!0;break}},clear:function(){this.baseInfoVisiable&&this.$refs["baseInfo"].clear(),this.hRInfoVisiable&&this.$refs["hRInfo"].clear(),this.partyVisiable&&this.$refs["party"].clear(),this.stationVisiable&&this.$refs["station"].clear(),this.qualificationVisiable&&this.$refs["qualification"].clear(),this.certifyVisiable&&this.$refs["certify"].clear(),this.wagesVisiable&&this.$refs["wages"].clear(),this.socialsecurityVisiable&&this.$refs["socialsecurity"].clear(),this.educationVisiable&&this.$refs["education"].clear(),this.workVisiable&&this.$refs["work"].clear(),this.abroadInfoVisiable&&this.$refs["abroadInfo"].clear(),this.contractVisiable&&this.$refs["contract"].clear(),this.trainVisiable&&this.$refs["train"].clear(),this.assessmentVisiable&&this.$refs["assessment"].clear(),this.incentiveVisiable&&this.$refs["incentive"].clear(),this.healthVisiable&&this.$refs["health"].clear(),this.accidentVisiable&&this.$refs["accident"].clear(),this.teachVisiable&&this.$refs["teach"].clear(),this.empArticleVisiable&&this.$refs["empArticle"].clear(),this.relationVisiable&&this.$refs["relation"].clear()},updateEmpId:function(e){this.$emit("updateEmpId",e)},loadUserPermission:function(){var e=this;0===Object.entries(this.userPermission).length&&_["a"].getControlRightByCurrentUser().then((function(t){e.userPermission=t.data})).catch((function(e){console.log(e)}))}}},Q=C,x=i("2877"),V=Object(x["a"])(Q,a,s,!1,null,null,null);t["a"]=V.exports},dd94:function(e,t,i){},e443:function(e,t,i){"use strict";var a=i("dd94"),s=i.n(a);s.a},ed97:function(e,t,i){}}]);