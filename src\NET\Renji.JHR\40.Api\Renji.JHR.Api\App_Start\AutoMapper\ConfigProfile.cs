﻿using AutoMapper;
using Renji.JHR.Api.Models;
using Renji.JHR.Entities;
using Shinsoft.Core.AutoMapper;
using Shinsoft.Core.AutoMapper.TypeConverters;
using System.Collections.Generic;

namespace Renji.JHR.Api
{
    public class ConfigProfile : Profile
    {
        public ConfigProfile()
        {
            this.CreateMaps<IModel>();

            this.CreateMap<List<SysSetting>, Dictionary<string, string?>>()
                .ConvertUsing<KeyValueToDictTypeConverter<SysSetting>>();

            this.CreateMap<Dictionary<string, string>, List<SysSetting>>()
                .ConvertUsing<DictToKeyValueTypeConverter<SysSetting>>();
        }
    }
}