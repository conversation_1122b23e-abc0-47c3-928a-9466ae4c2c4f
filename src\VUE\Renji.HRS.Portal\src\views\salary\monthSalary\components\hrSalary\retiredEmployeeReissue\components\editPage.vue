<template>
  <div>
    <el-dialog :title="title" :visible="showDialog" width="90%" :top="'10vh'" :close-on-press-escape="false" :close-on-click-modal="false" @close="closeDialog">
      <el-form ref="dataForm" :rules="rules" :model="dataModel" label-width="100px">
        <el-card style="margin-top: 3px;">
          <div slot="header">
            <span>基础信息</span>
          </div>
          <el-row>
            <el-col :span="6">
              <el-form-item label="员工姓名" label-width="120px">
                <div>
                  <span>
                    {{ dataModel.employeeModel.empName }}
                  </span>
                  <el-button v-if="!isEdit" style="margin-left: 15px;" type="primary" title="选择员工" @click="selectEmployeeDialog">选 择</el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="唯一码" label-width="120px">
                {{ dataModel.employeeModel.empUid }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="工号" label-width="120px">
                {{ dataModel.employeeModel.empCode }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="性别" label-width="120px">
                {{ dataModel.employeeModel.genderDesc }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="工龄" label-width="120px">
                {{ dataModel.employeeModel.societyAge }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        <el-card style="margin-top: 3px;">
          <div slot="header">
            <span>退休库信息</span>
          </div>
          <el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item label="金额" prop="backPay" label-width="120px">
                  <el-input-number v-model="dataModel.backPay" :min="0" :precision="2" :controls="false" placeholder="金额" style="width: 60%" maxlength="5" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="备注" label-width="120px">
                <el-input v-model="dataModel.remark" type="textarea" :rows="3" maxlength="300" placeholder="备注" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" :loading="btnSaveLoading" @click="saveDialog">保 存</el-button>
      </div>
    </el-dialog>

    <selectUserComponent ref="selectEmployee" @selectRow="setEmployee" />

  </div>
</template>

<script>
import salaryApi from '@/api/salary'
import selectUserComponent from '@/views/salary/employeeSalary/components/selectUser'

export default {
  components: {
    selectUserComponent
  },
  props: {
    salaryId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showDialog: false,
      title: '',
      rules: {
        backPay: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          { type: 'number', min: 0, message: '金额必须大于等于0', trigger: 'blur' }
        ]
      },
      btnSaveLoading: false,
      isEdit: false,
      dataModel: {
        employeeModel: {}
      }
    }
  },
  methods: {
    initDialog(row) {
      if (!row) {
        this.title = '新增退休库'
        this.isEdit = false
      } else {
        this.title = '编辑退休库'
        this.isEdit = true
        this.getData(row.id)
      }
      this.dataModel.salaryId = this.salaryId
      this.showDialog = true
    },
    getData(id) {
      salaryApi.getRetiredEmployeeReissue({ id: id }).then(res => {
        if (res.succeed) {
          this.dataModel = res.data
          this.setEmployee(res.data.employee)
        }
      }).catch(error => {
        console.error('获取数据失败:', error)
        this.$message({ message: '获取数据失败，请重试', type: 'error' })
      })
    },
    selectEmployeeDialog() {
      this.$refs.selectEmployee.loadTree()
      this.$refs.selectEmployee.showEmployee = true
    },
    saveDialog() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          // 验证是否选择了员工
          if (!this.dataModel.employeeId) {
            this.$message({ message: '请选择员工', type: 'warning' })
            return
          }
          this.btnSaveLoading = true
          if (!this.isEdit) {
            salaryApi.addRetiredEmployeeReissue(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '添加成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(error => {
              console.error('添加失败:', error)
              this.$message({ message: '添加失败，请重试', type: 'error' })
              this.btnSaveLoading = false
            })
          } else {
            salaryApi.updateRetiredEmployeeReissue(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '修改成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(error => {
              console.error('修改失败:', error)
              this.$message({ message: '修改失败，请重试', type: 'error' })
              this.btnSaveLoading = false
            })
          }
        }
      })
    },
    closeDialog() {
      this.dataModel = {
        employeeModel: {}
      }
      this.showDialog = false
      this.$refs.dataForm.resetFields()
    },
    setEmployee(emp) {
      this.dataModel.employeeId = emp.id
      this.dataModel.salaryId = this.salaryId
      this.$set(this.dataModel, 'employeeModel',
        {
          employeeId: emp.id,
          empUid: emp.uid,
          empCode: emp.empCode,
          empName: emp.displayName,
          genderDesc: emp.enumGenderDesc,
          empDept: emp.deptName,
          hospitalAreaNameText: emp.hospitalAreaNameText,
          identityNumber: emp.identityNumber,
          hireStyleName: emp.employeeHR.hireStyleName,
          leaveStyleName: emp.employeeHR.leaveStyleName,
          deadDate: emp.employeeHR.deadDate,
          societyAge: emp.employeeHR.societyAge
        })
    }
  }
}
</script>
