﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Renji.JHR.Entities
{
    /// <summary>
    /// 节日加班记录状态
    /// </summary>
    public enum AttHolidayOTRecordStatus
    {
        /// <summary>
        /// 未知
        /// </summary>
        [Description("未知")]
        UnKnown = 0,
        /// <summary>
        /// 申领表未提交
        /// </summary>
        [Description("申领表未提交")]
        UnCommitted = 1,
        /// <summary>
        /// 录入员提交
        /// </summary>
        [Description("已提交")]
        DocMakerCommit = 2, 
        /// <summary>
        /// 人事部门已确认
        /// </summary>
        [Description("人事部门已确认")]
        Confirmed = 3
    }
}
