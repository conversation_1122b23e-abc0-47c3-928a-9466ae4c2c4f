﻿CREATE TABLE [dbo].[RetireYear]
(
    [ID]                   UNIQUEIDENTIFIER NOT NULL,
    [EnumRetireEmpType]          INT              NOT  NULL ,
    [BeginDate]            DATETIME        NOT NULL,
    [EndDate]              DATETIME        NOT NULL,
    [WorkYear]          INT              NOT  NULL,
    [<PERSON><PERSON><PERSON>h]          INT              NOT  NULL,
    [Deleted]      BIT              NOT NULL,
    [Creator]      NVARCHAR (50)    NULL,
    [CreateTime]   DATETIME         NULL,
    [LastEditor]   NVARCHAR (50)    NULL,
    [LastEditTime] DATETIME         NULL,
    CONSTRAINT [PK_RetireYear] PRIMARY KEY CLUSTERED ([ID] ASC),
)

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'退休类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RetireYear',
    @level2type = N'COLUMN',
    @level2name = 'EnumRetireEmpType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'起始日期',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RetireYear',
    @level2type = N'COLUMN',
    @level2name = N'BeginDate'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'结束日期',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RetireYear',
    @level2type = N'COLUMN',
    @level2name = N'EndDate'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'工作年数',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RetireYear',
    @level2type = N'COLUMN',
    @level2name = 'WorkYear'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'工作月数',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RetireYear',
    @level2type = N'COLUMN',
    @level2name = 'WorkMonth'