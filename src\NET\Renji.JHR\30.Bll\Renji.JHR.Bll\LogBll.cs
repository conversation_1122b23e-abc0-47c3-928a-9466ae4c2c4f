﻿using Renji.JHR.Dal;
using Shinsoft.Core;
using Shinsoft.Core.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Text;

namespace Renji.JHR.Bll
{
    public class LogBll : BaseLogBll
    {
        #region Constructs

        public LogBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public LogBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public LogBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public LogBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs
    }
}