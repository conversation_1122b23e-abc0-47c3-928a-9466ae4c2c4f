﻿using Shinsoft.Core;
using Shinsoft.Core.AutoMapper;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Xml.Serialization;

namespace Renji.JHR.Entities
{
    public partial class Department : IParentUid<Department>, IOrder
    {
        #region IParent

        IComparable IParent.ID => this.ID;

        IComparable? IParent.ParentId => this.ParentId;

        #endregion IParent

        #region IOrder

        IComparable IOrder.Order => this.Code;

        #endregion IOrder

        /// <summary>
        /// 删除时是否同时删除子部门和关联的员工
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public bool ConfirmToDelete { get; set; }

        /// <summary>
        /// 上级科室编码
        /// </summary>
        [Description("上级科室编码")]
        [NotMapped]
        public string? ParentCode => this.Parent?.Code;

        /// <summary>
        /// 上级科室名称
        /// </summary>
        [Description("上级科室名称")]
        [NotMapped]
        public string? ParentName => this.Parent?.Name;

        /// <summary>
        /// 虚拟组织
        /// </summary>
        [NotMapped]
        public string VirtualDept => this.IsVirtual.HasValue ? (this.IsVirtual.Value ? "是" : "否") : "否";

        /// <summary>
        /// 组织结构类型名称
        /// </summary>
        [NotMapped]
        public string? DeptTypeName => this.DeptType?.Name;

        /// <summary>
        /// 操作源对象
        /// </summary>
        [Description("操作源对象")]
        [NotMapped, JsonIgnore, XmlIgnore]
        public string OriginDept { get; set; }

        /// <summary>
        /// 目标上级
        /// </summary>
        [Description("目标上级")]
        [NotMapped, JsonIgnore, XmlIgnore]
        public string ToParentDept { get; set; }

        /// <summary>
        /// 合并到目标
        /// </summary>
        [Description("合并到目标")]
        [NotMapped, JsonIgnore, XmlIgnore]
        public string MergeToDept { get; set; }
    }
}