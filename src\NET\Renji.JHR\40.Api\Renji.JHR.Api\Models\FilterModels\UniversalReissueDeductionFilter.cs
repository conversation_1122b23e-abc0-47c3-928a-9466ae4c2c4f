﻿namespace Renji.JHR.Api.Models
{
    public partial class SalaryExtendedDetailsFilter
    {
        /// <summary>
        /// 月薪ID
        /// </summary>
        [DynamicQueryColumn(typeof(SalaryExtendedDetails), SalaryExtendedDetails.Foreigns.Salary, Salary.Columns.ID, Operation = Operation.Equal)]
        public Guid? SalaryId { get; set; }

        /// <summary>
        /// 员工Id
        /// </summary>
        [DynamicQueryColumn(typeof(SalaryExtendedDetails), SalaryExtendedDetails.Foreigns.Employee, Employee.Columns.ID, Operation = Operation.Equal)]
        public Guid? EmployeeId { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        [DynamicQueryColumn(typeof(SalaryExtendedDetails), SalaryExtendedDetails.Foreigns.Employee, Employee.Columns.EmpCode, Operation = Operation.StringContains)]
        public string? EmpCode { get; set; }

        /// <summary>
        /// 唯一码
        /// </summary>
        [DynamicQueryColumn(typeof(SalaryExtendedDetails), SalaryExtendedDetails.Foreigns.Employee, Employee.Columns.Uid, Operation = Operation.Equal)]
        public int? Uid { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [DynamicQueryColumn(typeof(SalaryExtendedDetails), SalaryExtendedDetails.Foreigns.Employee, Employee.Columns.DisplayName, Operation = Operation.StringContains)]
        public string? DisplayName { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        [DynamicQueryColumn(typeof(SalaryExtendedDetails), SalaryExtendedDetails.Foreigns.Employee, Employee.Foreigns.Department, Department.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? DeptName { get; set; }

        /// <summary>
        /// 职别
        /// </summary>
        [DynamicQueryColumn(typeof(SalaryExtendedDetails), SalaryExtendedDetails.Foreigns.Employee, Employee.Inverses.EmployeeHR, EmployeeHR.Foreigns.OfficialRank, Dict.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? OfficialRankName { get; set; }

        /// <summary>
        /// 模块
        /// </summary>
        [DynamicQueryColumn(typeof(SalaryExtendedDetails), SalaryExtendedDetails.Foreigns.SalaryExtendedColumn, SalaryExtendedColumn.Columns.EnumGroupType, Operation = Operation.Equal)]
        public GroupType? GroupTypeName { get; set; }

        /// <summary>
        /// 类型查询
        /// </summary>
        [DynamicQueryColumn(typeof(SalaryExtendedDetails), SalaryExtendedDetails.Columns.EnumPaymentType, Operation = Operation.Equal)]
        public PaymentType? EnumPaymentType { get; set; }
    }
}
