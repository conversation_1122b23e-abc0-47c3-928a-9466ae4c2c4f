﻿<#@ template debug="true" hostspecific="true" language="C#" #>
<#@ assembly name="System.Core" #>
<#@ assembly name="EnvDTE" #>
<#@ import namespace="System.IO" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Text" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ include file="../../EFCore.CodeGenerator/Config/File.SqlServer.ttinclude"#>
<#@ include file="../../EFCore.CodeGenerator/DbHelper/SqlServer.ttinclude"#>
<#@ output extension=".cs" #>
<#
	this.GenerationEnvironment.Clear();

	using(var dbHelper = new DbHelper(connectionString, database))
	{
		var tables = dbHelper.GetTables(entityPrefix, entitySchemas, entityTables, entityViews, entityViewPrefixes);

#>
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码是从模板生成的.
//
//     手动更改此文件可能会导致应用程序出现意外行为。
//     如果重新生成代码，将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Runtime.Serialization;
using System.Text.Json.Serialization;
using System.Xml.Serialization;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Shinsoft.Core;
using Shinsoft.Core.EntityFrameworkCore;
using Column = Shinsoft.Core.EntityFrameworkCore.ColumnAttribute;
<#
		foreach(var enumNamespace in enumNamespaces)
		{
#>
using <#=enumNamespace#>;
<#
		}
#>

#pragma warning disable CS8669

namespace <#=entityNamespace#>
{
<#
		foreach(var table in tables)
		{
			var entityName = table.SysTypeName;
			var columns = table.Columns;
			var foreigns = columns.Where(p=>p.IsForeignKey && p.ForeignName != null).ToList();
			var inverses = tables
                    .SelectMany(p => p.Columns)
                    .Where(p => p.IsForeignKey
                                && p.ForeignTableSchema == table.Schema
								&& p.ForeignTableName == table.Name
                                && p.InverseName != null)
                    .ToList();

			var tableAttr = string.IsNullOrEmpty(table.Schema) || table.Schema == "dbo"
				? $"[Table(\"{table.Name}\")]"
				:$"[Table(\"{table.Name}\", Schema = \"{table.Schema}\")]";

			var inheritStr = new StringBuilder();

			if(table.IsView)
			{
				inheritStr.Append(", IView");
			}
			else
			{
				inheritStr.Append(", ITable");

				if(table.IsDeleteable)
				{
					inheritStr.Append(", IDeleteable");
				}
			}

			if(table.IsOperateInfo)
			{
				inheritStr.Append(", IOperateInfo");
			}

			if(table.IsPrimaryKey)
			{
				inheritStr.AppendFormat(", IPrimaryKey<{0}>", table.PrimaryKeySysTypeName);
			}

			if(!string.IsNullOrEmpty(companyIdTypeName) && columns.Any(p=>p.Name == "CompanyId" && p.SysTypeName == companyIdTypeName))
			{
				if(!string.IsNullOrEmpty(companyTypeName) && foreigns.Any(p=>p.ForeignName == "Company" && p.ForeignTableName == companyTypeName ))
				{
					inheritStr.AppendFormat(", ICompanyEntity<{0}, {1}>", companyIdTypeName, companyTypeName);
				}
				else
				{
					inheritStr.AppendFormat(", ICompanyEntity<{0}>", companyIdTypeName);
				}
			}
#>

	#region <#=entityName#>

<#
			if(!string.IsNullOrEmpty(table.Comment))
			{
#>
    /// <summary>
    /// <#=table.Comment#>
    /// </summary>
    [Description("<#=table.Comment#>")]
<#
			}
#>
	<#=tableAttr#>
	public partial class <#=entityName#> : Entity<#=inheritStr.ToString()#>
    {

        #region Const

        public static partial class Columns
        {

<#
			foreach(var column in columns)
			{
				if(!string.IsNullOrEmpty(column.Comment))
				{
#>
			/// <summary>
			/// <#=column.Comment#>
			/// </summary>
<#
				}
#>
            public const string <#=column.Name#> = "<#=column.Name#>";

<#
			}
#>
        }
<#
			if(foreigns.Any())
			{
#>

        public static partial class Foreigns
        {

<#
				foreach(var foreign in foreigns)
				{
					if(!string.IsNullOrEmpty(foreign.ForeignComment))
					{
#>
			/// <summary>
			/// <#=foreign.ForeignComment#>
			/// </summary>
<#
					}
#>
            public const string <#=foreign.ForeignName#> = "<#=foreign.ForeignName#>";

<#
				}
#>
        }
<#
			}

			if(inverses.Any())
			{
#>

        public static partial class Inverses
        {

<#
				foreach(var inverse in inverses)
				{
					if(!string.IsNullOrEmpty(inverse.InverseComment))
					{
#>
			/// <summary>
			/// <#=inverse.InverseComment#>
			/// </summary>
<#
					}
#>
            public const string <#=inverse.InverseName#> = "<#=inverse.InverseName#>";

<#
				}
#>
        }
<#
			}
#>

        #endregion Const

        #region Construct

        public <#=entityName#>()
        {

        }

        public <#=entityName#>(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties
<#
			foreach(var column in columns)
			{
				var hasComment = !string.IsNullOrEmpty(column.Comment);
				var decimalPrecision = column.NumPrecision.HasValue && column.NumScale.HasValue
					? $", NumPrecision = {column.NumPrecision}, NumScale = {column.NumScale}"
					: column.NumPrecision.HasValue
						? $", NumPrecision = {column.NumPrecision}"
						: "";
				var stringMaxLength = column.MaxLength > 0
					? $", MaxLength = {column.MaxLength}"
					: "";
				var dbTypeName = string.IsNullOrEmpty(column.DbTypeName)
					? string.Empty
					: $", TypeName = \"{column.DbTypeName}\"";
				var defaultValue = column.SysTypeName == "string" && !column.IsNullable
					? " = string.Empty"	
					: "";
				var setValue =  column.SysTypeName == "string" && !column.IsNullable
					? "value ?? string.Empty"	
					: "value";
#>

        #region <#=column.Name#><#=hasComment ? $" {column.Comment}" : ""#>

<#
				if(column.IsPrimaryKey)
				{
					if(hasComment)
					{
#>
		/// <summary>
        /// <#=column.Comment#>
        /// </summary>
        [Description("<#=column.Comment#>")]
<#
					}
#>
        [Key]
        [Column(Columns.<#=column.Name#>, DbType = DbType.<#=column.DbType.ToString()#>, IsPrimaryKey = true, IsForeignKey = <#=column.IsPrimaryKey.ToString().ToLower()#>, IsNullable = <#=column.IsNullable.ToString().ToLower()#><#=decimalPrecision#><#=stringMaxLength#><#=dbTypeName#>)]
<#
					if(column.IsIdentity)
					{
#>
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
<#
					}
#>
        public virtual <#=column.SysTypeName#> <#=column.Name#> { get; set; }
<#
				}
				else
				{
					if(column.IsIdentity)
					{
						if(hasComment)
						{
#>
		/// <summary>
        /// <#=column.Comment#>
        /// </summary>
        [Description("<#=column.Comment#>")]
<#
						}
#>
        [Column(Columns.<#=column.Name#>, DbType = DbType.<#=column.DbType.ToString()#>, IsPrimaryKey = false, IsForeignKey = <#=column.IsPrimaryKey.ToString().ToLower()#>, IsNullable = <#=column.IsNullable.ToString().ToLower()#><#=decimalPrecision#><#=stringMaxLength#><#=dbTypeName#>)]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public virtual <#=column.SysTypeName#> <#=column.Name#> { get; set; }
<#
					}
					else
					{
#>
        [NotMapped, XmlIgnore, JsonIgnore]
        private <#=column.SysTypeName#> _<#=column.Name#><#=defaultValue#>;

<#
						if(hasComment)
						{
#>
		/// <summary>
        /// <#=column.Comment#>
        /// </summary>
        [Description("<#=column.Comment#>")]
<#
						}
#>
        [Column(Columns.<#=column.Name#>, DbType = DbType.<#=column.DbType.ToString()#>, IsPrimaryKey = false, IsForeignKey = <#=column.IsPrimaryKey.ToString().ToLower()#>, IsNullable = <#=column.IsNullable.ToString().ToLower()#><#=decimalPrecision#><#=stringMaxLength#><#=dbTypeName#>)]
		public virtual <#=column.SysTypeName#> <#=column.Name#>
        {
            get => _<#=column.Name#>;
            set
            {
                _<#=column.Name#> = <#=setValue#>;
                this.SetChangedColumn(_<#=column.Name#>);
            }
        }
<#
					}

					if(column.IsEnum)
					{
						if(hasComment)
						{
#>

		/// <summary>
        /// <#=column.Comment#>
        /// </summary>
        [Description("<#=column.Comment#>")]
<#
						}
#>
		[NotMapped]
		public virtual string <#=column.Name#>Desc => this.<#=column.Name#>.GetDesc();
<#
					}
				}
#>

        #endregion <#=column.Name#>
<#
			}
#>

        #endregion Properties
<#

			if(foreigns.Any())
			{
#>

        #region Foreign
<#
				foreach(var foreign in foreigns)
				{
					var hasComment = !string.IsNullOrEmpty(foreign.ForeignComment);
#>

		#region <#=foreign.ForeignName#><#=hasComment ? $" {foreign.ForeignComment}" : ""#>

        [NotMapped, XmlIgnore, JsonIgnore]
        private <#=foreign.ForeignSysTypeName#>? _<#=foreign.ForeignName#>;

<#
					if(hasComment)
					{
#>
		/// <summary>
        /// <#=foreign.ForeignComment#>
        /// </summary>
<#
					}
#>
        [ForeignKey(Columns.<#=foreign.Name#>)]
        public virtual <#=foreign.ForeignSysTypeName#><#=foreign.IsNullable ? "?" : ""#> <#=foreign.ForeignName#>
        {

            get => this.LazyLoad(ref _<#=foreign.ForeignName#>);
            set => _<#=foreign.ForeignName#> = value;
        }


        #endregion <#=foreign.ForeignName#>
<#
				}
#>

        #endregion Foreign
<#
			}
	
			if(inverses.Any())
			{
#>

		#region Inverse
<#

				foreach(var inverse in inverses)
				{
					var hasComment = !string.IsNullOrEmpty(inverse.InverseComment);
#>

        #region <#=inverse.InverseName#><#=hasComment ? $" {inverse.InverseComment}" : ""#>

        [NotMapped, XmlIgnore, JsonIgnore]
        private <#=inverse.InverseSysTypeName#>? _<#=inverse.InverseName#>;

<#
					if(hasComment)
					{
#>
		/// <summary>
        /// <#=inverse.InverseComment#>
        /// </summary>
<#
					}
#>
		[InverseProperty("<#=inverse.ForeignName#>")]
        public virtual <#=inverse.InverseSysTypeName#><#=inverse.IsPrimaryKey ? "?" : ""#> <#=inverse.InverseName#>
        {
            get => this.LazyLoad(ref _<#=inverse.InverseName#>);
            set => _<#=inverse.InverseName#> = value;
        }

        #endregion <#=inverse.InverseName#>
<#
				}
#>

		#endregion Inverse
<#
			}
#>
    }

	#endregion <#=entityName#>

<#
		}
#>
}

#pragma warning restore CS8669
<#
	}
#>
