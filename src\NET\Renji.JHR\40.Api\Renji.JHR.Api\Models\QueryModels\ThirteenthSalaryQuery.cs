using Renji.JHR.Entities;
using Shinsoft.Core.AutoMapper;
using System;

namespace Renji.JHR.Api.Models
{
    /// <summary>
    /// 十三薪查询结果模型
    /// </summary>
    public partial class ThirteenthSalaryQuery
    {
        /// <summary>
        /// 唯一码
        /// </summary>
        [MapFromProperty(typeof(ThirteenthSalary), ThirteenthSalary.Foreigns.Employee, Employee.Columns.Uid)]
        public int Uid { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        [MapFromProperty(typeof(ThirteenthSalary), ThirteenthSalary.Foreigns.Employee, Employee.Columns.EmpCode)]
        public string? EmpCode { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [MapFromProperty(typeof(ThirteenthSalary), ThirteenthSalary.Foreigns.Employee, Employee.Columns.DisplayName)]
        public string? DisplayName { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        [MapFromProperty(typeof(ThirteenthSalary), ThirteenthSalary.Foreigns.Employee, Employee.Foreigns.Department, Department.Columns.Name)]
        public string? DeptName { get; set; }

        /// <summary>
        /// 薪资月份
        /// </summary>
        [MapFromProperty(typeof(ThirteenthSalary), ThirteenthSalary.Foreigns.Salary, Salary.Columns.Month)]
        public DateTime? SalaryMonth { get; set; }

        /// <summary>
        /// 薪资状态
        /// </summary>
        [MapFromProperty(typeof(ThirteenthSalary), ThirteenthSalary.Foreigns.Salary, Salary.Columns.EnumStatus)]
        public SalaryStatus? SalaryStatus { get; set; }

        /// <summary>
        /// 薪资状态描述
        /// </summary>
        public string SalaryStatusDesc => SalaryStatus?.GetDesc() ?? "";
    }
}
