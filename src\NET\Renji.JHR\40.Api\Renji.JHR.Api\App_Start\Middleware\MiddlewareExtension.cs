﻿using Microsoft.AspNetCore.Builder;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Renji.JHR.Api
{
    public static class MiddlewareExtension
    {
        public static IApplicationBuilder UseRedirectionHanlderMiddlerware(this IApplicationBuilder applicationBuilder)
        {
            return applicationBuilder.UseMiddleware<RedirectionHandlerMiddleware>();
        }

        public static IApplicationBuilder UserIPRestrictionModuleMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<IPRestrictionModuleMiddleware>();
        }
    }
}
