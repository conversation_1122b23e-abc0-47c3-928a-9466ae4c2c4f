﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Shinsoft.Core;

namespace Renji.JHR.Common
{
    public static class DataTableHelper
    {
        /// <summary>
        /// 检查列头是否符合格式要求
        /// </summary>
        /// <param name="uploadData">从Excel中读取的表</param>
        /// <param name="columnsMapping">Excel导入时字段映射，取Key值</param>
        public static List<string> CheckColumns(DataTable uploadData, Dictionary<string, string> columnsMapping)
        {
            var notColumns = columnsMapping
                .Where(p => !uploadData.Columns.Contains(p.Key))
                .Select(s => s.Key).ToList();

            return notColumns;
        }

        /// <summary>
        /// 将Excel中读取到的表转换为与数据库表结构一致
        /// </summary>
        /// <param name="dataTable"></param>
        /// <param name="mappingColumns">Excel与数据库表列映射</param>
        /// <param name="dataBaseOrderedColumn">数据库列顺序</param>
        /// <returns></returns>
        public static DataTable TransferToDataBaseDataTable(DataTable dataTable, Dictionary<string, string> mappingColumns, Dictionary<string, Type> dataBaseOrderedColumn)
        {
            int rowIndex = 0, columnIndex = 0;

            DataTable dataBaseDataTable = new DataTable();
            foreach (var item in dataBaseOrderedColumn)
                dataBaseDataTable.Columns.Add(new DataColumn(item.Key, item.Value));

            Dictionary<int, string> columns = new Dictionary<int, string>();

            for (int i = 0; i < dataTable.Columns.Count; i++)
            {
                var columnName = dataTable.Columns[i].ColumnName;
                if (mappingColumns.ContainsKeyIgnoreCase(columnName))
                {
                    var value = mappingColumns.GetValueIgnoreCase(columnName);
                    if (value != null)
                    {
                        columns.Add(i, value);
                    }
                }
            }

            //拷贝数据
            for (int i = 0; i < dataTable.Rows.Count; i++)
            {
                rowIndex = i;
                var newRow = dataBaseDataTable.NewRow();
                var originalRow = dataTable.Rows[i];
                foreach (var item in columns)
                {
                    columnIndex = item.Key;
                    newRow[item.Value] = originalRow[item.Key];
                }
                dataBaseDataTable.Rows.Add(newRow);
            }
            return dataBaseDataTable;
        }

        /// <summary>
        /// 删除空行
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="excludeColumnNames"></param>
        public static void RemoveEmptyRow(DataTable dt, List<string> excludeColumnNames)
        {
            List<DataRow> removeList = new List<DataRow>();
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                bool isNull = true;
                for (int j = 0; j < dt.Columns.Count; j++)
                {
                    if (excludeColumnNames.Contains(dt.Columns[j].ColumnName))
                        continue;

                    if (!string.IsNullOrEmpty(dt.Rows[i][j].ToString()?.Trim()))
                    {
                        isNull = false;
                    }
                }
                if (isNull)
                {
                    removeList.Add(dt.Rows[i]);
                }
            }
            for (int i = removeList.Count - 1; i >= 0; i--)
            {
                dt.Rows.Remove(removeList[i]);
            }
        }

        public static List<T> ConvertDataTableToList<T>(this DataTable dt, Dictionary<string, string> dictionary, Func<T, PropertyInfo, object?, bool>? setProperty = null)
            where T : new()
        {
            var list = new List<T>();

            foreach (DataRow dr in dt.Rows)
            {
                T entity = new T();
                var properties = entity.GetType().GetProperties();

                foreach (DataColumn dc in dt.Columns)
                {
                    PropertyInfo? property;
                    if (dictionary != null)
                    {
                        //ContainsKey 修改为ContainsKeyIgnoreCase   验证时否存在 不区分大小写
                        if (!dictionary.ContainsKeyIgnoreCase(dc.ColumnName))
                            continue;

                        property = properties.SingleOrDefault(s => s.Name == dictionary.GetValueIgnoreCase(dc.ColumnName));
                    }
                    else
                    {
                        property = properties.SingleOrDefault(s => s.Name == dc.ColumnName);
                    }
                    if (property != null && !property.CanWrite)
                        continue;

                    var value = dr[dc.ColumnName];
                    if (value != DBNull.Value && value.ToString() != "")
                    {
                        if (property != null)
                        {
                            var set = false;
                            if (setProperty != null)
                            {
                                set = setProperty(entity, property, value);
                            }
                            if (!set)
                            {
                                if (property.PropertyType == typeof(DateTimeOffset))
                                {
                                    DateTime dto = Convert.ToDateTime(value);
                                    property.SetValue(entity, dto, null);
                                }
                                else if (property.PropertyType == typeof(DateTime?) || property.PropertyType == typeof(DateTime))
                                {
                                    DateTime dto = Convert.ToDateTime(value);
                                    property.SetValue(entity, dto, null);
                                }
                                else if (property.PropertyType == typeof(int?))
                                {
                                    var dto = value.AsString().As<int?>();
                                    property.SetValue(entity, dto, null);
                                }
                                else
                                {
                                    var obj = Convert.ChangeType(value, property.PropertyType);
                                    property.SetValue(entity, obj, null);
                                }
                            }
                        }
                    }
                }

                list.Add(entity);
            }
            return list;
        }

        public static List<T> TableToListModel<T>(DataTable dt) where T : new()
        {
            // 定义集合
            List<T> ts = new List<T>();

            // 获得此模型的类型
            Type type = typeof(T);
            string tempName = "";

            foreach (DataRow dr in dt.Rows)
            {
                T t = new T();
                // 获得此模型的公共属性
                PropertyInfo[] propertys = t.GetType().GetProperties();
                foreach (PropertyInfo pi in propertys)
                {
                    tempName = pi.Name;  // 检查DataTable是否包含此列

                    if (dt.Columns.Contains(tempName))
                    {
                        // 判断此属性是否有Setter
                        if (!pi.CanWrite) continue;

                        object value = dr[tempName];
                        if (value != DBNull.Value)
                            pi.SetValue(t, value, null);
                    }
                }
                ts.Add(t);
            }
            return ts;
        }
    }
}
