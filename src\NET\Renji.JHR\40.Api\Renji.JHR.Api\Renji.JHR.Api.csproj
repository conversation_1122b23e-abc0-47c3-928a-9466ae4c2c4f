﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
	<NoWarn>1591;</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
	<NoWarn>1591;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
	<Using Include="Renji.JHR.Bll" />
	<Using Include="Renji.JHR.Common" />
	<Using Include="Renji.JHR.Common.Configration" />
	<Using Include="Renji.JHR.Entities" />
	<Using Include="Renji.JHR.Api.Models" />
	<Using Include="Shinsoft.Core" />
	<Using Include="Shinsoft.Core.AutoMapper" />
	<Using Include="Shinsoft.Core.DynamicQuery" />
	<Using Include="Shinsoft.Core.Mvc" />
  </ItemGroup>
	

  <ItemGroup>
	<None Remove="Renji.JHR.Api.csproj.vspscc" />
	<None Remove="Renji.JHR.Api.xml" />
  </ItemGroup>

  <ItemGroup>
	<Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
  </ItemGroup>

  <ItemGroup>
	<Reference Include="Shinsoft.Core">
	  <HintPath>..\..\00.Reference\net6.0\Shinsoft.Core.dll</HintPath>
	</Reference>
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="AutoMapper" Version="13.0.1" />
	<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.28" />
	<PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.28" />
	<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.28" />
	<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
	<PackageReference Include="NLog.Database" Version="5.0.0" />
	<PackageReference Include="NLog.Web.AspNetCore" Version="5.4.0" />
	<PackageReference Include="Quartz" Version="3.4.0" />
	<PackageReference Include="SkiaSharp" Version="2.88.9" />
	<PackageReference Include="SkiaSharp.NativeAssets.Linux.NoDependencies" Version="2.88.9" />
	<PackageReference Include="StackExchange.Redis" Version="2.7.33" />
	<PackageReference Include="Swashbuckle.AspNetCore" Version="6.3.1" />
	<PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="7.0.3" />	
  </ItemGroup>

  <ItemGroup>
	<ProjectReference Include="..\..\10.Common\Renji.JHR.Common\Renji.JHR.Common.csproj" />
	<ProjectReference Include="..\..\20.EntityFrameworkCore\Renji.JHR.Entities\Renji.JHR.Entities.csproj" />
	<ProjectReference Include="..\..\30.Bll\Renji.JHR.Bll\Renji.JHR.Bll.csproj" />
  </ItemGroup>

  <ItemGroup>
	<Compile Update="Models\Biz.Model.cs">
	  <DependentUpon>Biz.Model.tt</DependentUpon>
	  <DesignTime>True</DesignTime>
	  <AutoGen>True</AutoGen>
	</Compile>
	<Compile Update="Models\File.Model.cs">
	  <DesignTime>True</DesignTime>
	  <AutoGen>True</AutoGen>
	  <DependentUpon>File.Model.tt</DependentUpon>
	</Compile>
	<Compile Update="Models\Log.Model.cs">
	  <DesignTime>True</DesignTime>
	  <AutoGen>True</AutoGen>
	  <DependentUpon>Log.Model.tt</DependentUpon>
	</Compile>
  </ItemGroup>

  <ItemGroup>
	<None Update="Excel_Template\AssistanceForeignTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\AssistanceYunnanTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\AttendanceHealthAllowanceTemplate.xlsx">
	  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\EmployeeOverseasReissueDetailTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\EmployeeSalaryBaseTemplate.xlsx">
	  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\EmployeeSalaryCorrectionMonthTemplate.xlsx">
	  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\GeneralHospitalAdminDutyAllowanceTemplate.xlsx">
	  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\MiddleNightShiftAllowanceTemplate.xlsx">
	  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\NonMonthlySalaryLeaveTemplate.xlsx">
	  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\OvertimeAllowanceTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\PostdoctoralHousingAllowanceTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\SalaryLeaveTemplate.xlsx">
	  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\ShiftAllowanceTemplate.xlsx">
	  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\SickMaternityLeaveTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\SalaryExtendedDetailsTemplate.xlsx">
	  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\SporadicSalaryLeaveTemplate.xlsx">
	  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\RetiredEmployeeReissueTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\UpdateCompanyAgeSalaryScale.xlsx">
	  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</None>
	<None Update="Models\Biz.Model.tt">
	  <LastGenOutput>Biz.Model.cs</LastGenOutput>
	  <Generator>TextTemplatingFileGenerator</Generator>
	</None>
	<None Update="Models\File.Model.tt">
	  <Generator>TextTemplatingFileGenerator</Generator>
	  <LastGenOutput>File.Model.cs</LastGenOutput>
	</None>
	<None Update="Models\Log.Model.tt">
	  <Generator>TextTemplatingFileGenerator</Generator>
	  <LastGenOutput>Log.Model.cs</LastGenOutput>
	</None>
  </ItemGroup>


  <ItemGroup>
	<Content Update="NOTICE.TXT">
	  <CopyToOutputDirectory>Never</CopyToOutputDirectory>
	</Content>
  </ItemGroup>

  <ItemGroup>
	<None Update="Excel_Template\EmployeeSocialInsuranceTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\OtherEmployeeInfoTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\SalaryScaleTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\SeniorityTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\EmployeeCertifyTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\EmployeeContractTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\EmployeeEducationTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\EmployeeHRTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\EmployeeStationTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\EmployeeTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
	<None Update="Excel_Template\EmployeeWorkTemplate.xlsx">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
  </ItemGroup>

  <ProjectExtensions><VisualStudio><UserProperties /></VisualStudio></ProjectExtensions>

</Project>
