﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NPOI.SS.Formula.Functions;
using Renji.JHR.Api.Models;
using Renji.JHR.Bll;
using Renji.JHR.Bll.Caching;
using Renji.JHR.Common.Utility;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.Mvc;
using Shinsoft.Core.NLog;
using SkiaSharp;

namespace Renji.JHR.Api.Controllers
{
    [ApiExplorerSettings(GroupName = "")]
    public class HomeController : BaseApiController<HomeBll>
    {
        /*
        [AllowAnonymous]
        [HttpGet]
        public SysSettingModel? GetSetting([FromQuery] Guid id)
        {
            var bll = (IRepo)this.Repo;

            var dbEntity = bll.Get<SysSetting>(id);

            return dbEntity?.Map<SysSettingModel>();
        }

        [AllowAnonymous]
        [HttpPost]
        public SysSettingModel SaveSetting([FromBody] SysSettingModel model)
        {
            var bll = (IRepo)this.Repo;

            var entity = model.Map<SysSetting>();

            var id = entity.ID;

            var dbEntity = id.IsEmpty()
                ? null
                : bll.Get<SysSetting>(id);

            if (dbEntity == null)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }
                entity = bll.Add(entity);
            }
            else
            {
                entity = bll.Update(entity);
            }

            return entity.Map<SysSettingModel>();
        }

        */

        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [JwtIgnore]
        [HttpPost]
        [LogApi(ApiType.Login)]
        public BizResult<IdentityUser> UserLogin([FromBody] LoginRequest request)
        {
            var result = new BizResult<IdentityUser>();

            if (request.VerificationKey.IsEmpty() || request.VerificationCode.AsTrim().IsEmpty())
            {
                result.Error("验证码不正确");
            }
            else
            {
                var code = this.SysCache.GetVerificationCode(request.VerificationKey);

                if (code.ToLower() != request.VerificationCode.AsTrim().ToLower())
                {
                    result.Error("验证码不正确");
                }
            }

            if (result.Succeed)
            {
                result = this.UserProvider.UserLogin(request.LoginName, request.Password, request.VerificationKey, request.VerificationCode);
            }

            this.SysCache.RemoveVerificationCode(request.VerificationKey);

            return result;
        }

        /// <summary>
        /// 登出
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [JwtIgnore]
        [LogApi(ApiType.Logout)]
        public BizResult UserLogout()
        {
            this.UserProvider.UserLogout(this.CurrentUser);

            return this.BizResult();
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取当前用户信息")]
        public BizResult<IdentityUser> GetCurrentUser()
        {
            var result = new BizResult<IdentityUser>
            {
                Data = this.UserProvider.GetIdentityUser(this.User.GetIdentityJson())
            };
            return result;
        }

        /// <summary>
        /// 更新当前用户登录密码
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新当前用户登录密码", Mask = ApiMask.Input)]
        public BizResult UpdateUserPwd(EditPwdRequest request)
        {
            var entity = new User
            {
                ID = this.CurrentUser!.ID,
                OldPwdText = SM4Utils.DecryptCBC(new SM4Config(request.OldPwdText)),
                NewPwdText = SM4Utils.DecryptCBC(new SM4Config(request.NewPwdText))
            };

            var result = this.Repo.UpdateUserPwd(entity);

            return result;
        }

        /// <summary>
        /// 获取登录验证码
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [JwtIgnore]
        [HttpPost]
        [LogApi(ApiType.Login)]
        public BizResult<byte[]> GetVerificationCode([FromBody] VerificationCodeModel model)
        {
            var code = this.SysCache.GetVerificationCode(model.VerificationKey);

            byte[] imageBytes = CreateCaptchaImage(code);

            return new BizResult<byte[]>(imageBytes);
        }

        private byte[] CreateCaptchaImage(string code)
        {
            int width = code.Length * 18;
            int height = 40;
            SKBitmap bitmap = new SKBitmap(width, height);
            using SKCanvas canvas = new(bitmap);
            //填充背景颜色为白色
            canvas.Clear(SKColors.White);

            Random random = new Random();
            int minLength = 5;
            int maxLength = 20;

            // 添加噪声线，增加验证难度
            for (int i = 0; i < 20; i++)
            {
                float x1 = random.Next(bitmap.Width);
                float x2 = random.Next(minLength, maxLength);
                float y1 = random.Next(bitmap.Height);
                float y2 = random.Next(minLength, maxLength);

                SKPaint linePaint = new SKPaint
                {
                    Color = SKColors.DarkGray,
                    StrokeWidth = 2
                };
                canvas.DrawLine(x1, y1, x2, y2, linePaint);
            }

            // 绘制到画布
            canvas.DrawBitmap(bitmap, 5, 10);

            SKPaint textPaint = new SKPaint()
            {
                TextSize = 20,
                Color = SKColors.Black,
                IsAntialias = true
            };

            textPaint.Typeface = SKTypeface.FromFamilyName("Arial");

            //绘制文本
            canvas.DrawText(code, 5, 25, textPaint);

            using var img = SKImage.FromBitmap(bitmap);
            using SKData p = img.Encode(SKEncodedImageFormat.Png, 100);

            return p.ToArray();

        }





    }
}