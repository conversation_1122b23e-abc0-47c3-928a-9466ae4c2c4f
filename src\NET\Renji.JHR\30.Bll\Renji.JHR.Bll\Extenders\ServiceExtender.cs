﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Renji.JHR.Bll.Caching;
using Renji.JHR.Common.Configration;
using Renji.JHR.Dal;
using Shinsoft.Core.EntityFrameworkCore;

namespace Renji.JHR.Bll
{
    public static class ServiceExtender
    {
        public static IServiceCollection AddBllServics(this IServiceCollection services)
        {
            services.AddDbContextPool<BizDbContext>();

            services.AddDbContextPool<FileDbContext>();

            services.AddDbContextPool<LogDbContext>();

            services.AddDbContextPool<MailDbContext>();

            services.AddRepositories<IRepo>();

            services.AddSingleton<SysCache>();

            return services;
        }
    }
}