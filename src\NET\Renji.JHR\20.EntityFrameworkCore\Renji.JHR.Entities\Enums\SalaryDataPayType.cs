﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Renji.JHR.Entities
{
    public enum SalaryDataPayType
    {
        None = 0,

        /// <summary>
        /// 月薪
        /// </summary>
        /// <summary>
        [Description("月薪")]
        MonthlySalary = 1,

        /// <summary>
        /// 年薪
        /// </summary>
        [Description("年薪")]
        Finance = 2,

        /// <summary>
        /// 返聘
        /// </summary>
        [Description("返聘")]
        RehireAfterRetirement = 3,

        /// <summary>
        /// 年薪和月薪
        /// </summary>
        [Description("年薪和月薪")]
        MonthlySalaryAndFinance = 4
    }
}
