﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;

namespace Renji.JHR.Entities
{
    public static class PersonExtender
    {
        private static PropertyInfo[]? _personProps = null;

        public static void CopyFrom(this IPerson dest, IPerson src)
        {
            if (dest != null && src != null)
            {
                if (_personProps == null)
                {
                    _personProps = typeof(IPerson).GetProperties();
                }

                foreach (var prop in _personProps)
                {
                    var value = prop.GetValue(src);
                    prop.SetValue(dest, value);
                }
            }
        }
    }
}