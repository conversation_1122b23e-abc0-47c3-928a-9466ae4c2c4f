<template>
  <div>
    <el-dialog :title="title" :visible="showDialog" width="90%" :top="'10vh'" :close-on-press-escape="false" :close-on-click-modal="false" @close="closeDialog">
      <el-form ref="dataForm" :rules="rules" :model="dataModel" label-width="100px" class="el-dialogform">
        <el-card>
          <el-row>
          <el-col :span="6">
            <el-form-item label="员工姓名" label-width="120px">
              <div>
                <span>
                  {{ dataModel.employeeModel.empName }}
                </span>
                <el-button v-if="!isEdit" style="margin-left: 15px;" type="primary" title="选择员工" @click="selectEmployeeDialog">选 择</el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="唯一码" label-width="120px">
              {{ dataModel.employeeModel.empUid }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="工号" label-width="120px">
              {{ dataModel.employeeModel.empCode }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="姓名" label-width="120px">
              {{ dataModel.employeeModel.empName }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="部门" label-width="120px">
              {{ dataModel.employeeModel.empDept }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="职别" label-width="120px">
              {{ dataModel.employeeModel.officialRankName }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="性别" label-width="120px">
              {{ dataModel.employeeModel.genderDesc }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="在职方式" label-width="120px">
              {{ dataModel.employeeModel.hireStyleName }}
            </el-form-item>
          </el-col>
        </el-row>
        </el-card>
        <el-card style="margin-top: 3px;">
          <div slot="header">
            <span>行政聘任数据</span>
          </div>
          <el-row>
            <el-col :span="8">
              <el-row>
                <el-form-item label="补发/补扣类型" label-width="120px" prop="enumPaymentType">
                  <el-radio-group v-model="dataModel.enumPaymentType">
                    <el-radio :label="2">补扣</el-radio>
                    <el-radio :label="1">补发</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item :label="monthRangeLabel" label-width="120px">
                  <el-date-picker
                    v-model="dataModel.monthRange"
                    style="width: 60%;"
                    type="monthrange"
                    range-separator="至"
                    start-placeholder="开始月份"
                    end-placeholder="结束月份"
                    :picker-options="pickerOptions"
                    format="yyyy 年 MM 月"
                    value-format="yyyy-MM-dd"
                    @change="monthRangeChange"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item :label="monthCountLabel" label-width="120px" prop="backPayDeductionMonth">
                  <el-input-number v-model="dataModel.backPayDeductionMonth"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="差额" label-width="120px">
                  {{ dataModel.difference | formatMoney2 }}
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item :label="calculatedAmountLabel" prop="backPayDeduction" label-width="120px">
                  {{ dataModel.backPayDeduction | formatMoney2 }}
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item :label="actualAmountLabel" prop="actualBackPayDeduction" label-width="120px">
                  <el-input-number v-model="dataModel.actualBackPayDeduction"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
                </el-form-item>
              </el-row>
            </el-col>
            <el-col :span="16">
              <el-form label-width="120px" style="width: 90%; margin: 0 auto;">
                <div class="table-row">
                  <span class="table-header">类型</span>
                  <span class="table-header">原数据</span>
                  <span class="table-header">新数据</span>
                </div>
                <div class="table-row">
                  <span class="table-data">岗位等级</span>
                  <span class="table-data">
                    <el-cascader v-model="dataModel.oldStationId" :show-all-levels="false" style="width: 60%" :options="stationTrees" :props="{ expandTrigger: 'hover', value: 'id', label: 'name',emitPath:false}" @change="oldStationTreeChange"></el-cascader>
                  </span>
                  <span class="table-data">
                    <el-cascader v-model="dataModel.newStationId" :show-all-levels="false" style="width: 60%" :options="stationTrees" :props="{ expandTrigger: 'hover', value: 'id', label: 'name',emitPath:false}" @change="stationTreeChange"></el-cascader>
                  </span>
                </div>
                <div class="table-row">
                  <span class="table-data">岗位工资</span>
                  <span class="table-data">
                    <el-input-number v-model="dataModel.oldStationWage"  :min="0" :precision="2" :controls="false" style="width: 60%" maxlength="5" />
                  </span>
                  <span class="table-data">
                    <el-input-number v-model="dataModel.newStationWage"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
                  </span>
                </div>
                <div class="table-row">
                  <span class="table-data">岗位津贴</span>
                  <span class="table-data">
                    <el-input-number v-model="dataModel.oldStationAllowance"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
                  </span>
                  <span class="table-data">
                    <el-input-number v-model="dataModel.newStationAllowance"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
                  </span>
                </div>
                <div class="table-row">
                  <span class="table-data">电话费等级</span>
                  <span class="table-data">
                    <el-select v-model="dataModel.oldTelephoneFeeId" placeholder="电话费" style="width: 60%" @change="oldTelephoneFeeChange">
                      <el-option v-for="item in telephoneFees" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </span>
                  <span class="table-data">
                    <el-select v-model="dataModel.newTelephoneFeeId" placeholder="电话费" style="width: 60%" @change="telephoneFeeChange">
                      <el-option v-for="item in telephoneFees" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </span>
                </div>
                <div class="table-row">
                  <span class="table-data">电话费</span>
                  <span class="table-data">
                    <el-input-number v-model="dataModel.oldTelephoneFeeAllowance"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
                  </span>
                  <span class="table-data">
                    <el-input-number v-model="dataModel.newTelephoneFeeAllowance"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
                  </span>
                </div>
                <div class="table-row">
                  <span class="table-data">公车津贴等级</span>
                  <span class="table-data">
                    <el-select v-model="dataModel.oldCarSubsidyId" placeholder="公车津贴" style="width: 60%" @change="oldCarSubsidyChange">
                      <el-option v-for="item in carSubsidys" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </span>
                  <span class="table-data">
                    <el-select v-model="dataModel.newCarSubsidyId" placeholder="公车津贴" style="width: 60%" @change="carSubsidyChange">
                      <el-option v-for="item in carSubsidys" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </span>
                </div>
                <div class="table-row">
                  <span class="table-data">公车补贴</span>
                  <span class="table-data">
                    <el-input-number v-model="dataModel.oldCarSubsidyAllowance"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
                  </span>
                  <span class="table-data">
                    <el-input-number v-model="dataModel.newCarSubsidyAllowance"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
                  </span>
                </div>
              </el-form>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="备注" label-width="120px">
                <el-input v-model="dataModel.remark" type="textarea" :rows="3" maxlength="300" placeholder="备注" />
              </el-form-item>
            </el-col>
          </el-row>

        </el-card></el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="calculate">计 算</el-button>
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" :loading="btnSaveLoading" @click="saveDialog">保 存</el-button>
      </div>
    </el-dialog>

    <selectUserComponent ref="selectEmployee" @selectRow="setEmployee" />

  </div>
</template>
<script>
import salaryApi from '@/api/salary'
import sysManageApi from '@/api/sysManage'
import selectUserComponent from '@/views/salary/employeeSalary/components/selectUser'

export default {
  components: {
    selectUserComponent
  },
  data() {
    var validateMoney = (rule, value, callback) => {
      if (!(/^(\d|[1-9]\d+)(\.\d{1,2})?$/).test(value)) {
        callback(new Error('请输入正确格式数字,小数不超过2位'))
      } else {
        callback()
      }
    }
    return {
      salaryId: '',
      showDialog: false,
      title: '',
      btnSaveLoading: false,
      isEdit: false,
      stationTrees: [],
      telephoneFees: [],
      carSubsidys: [],
      stationAllowances: [],
      societyAge: 0,
      salaryMonth: '',
      dataModel: {
        employeeModel: {},
        newTelephoneFeeId: '',
        newCarSubsidyId: '',
        newStationId: ''
      },
      rules: {
        backPayDeductionMonth: [
          { required: true, message: '补发/补扣月份数必填', trigger: 'blur' },
          { validator: validateMoney, trigger: 'blur' }
        ],
        newStationId: [
          { required: true, message: '请选择新岗位等级', trigger: 'change' }
        ],
        newTelephoneFeeId: [
          { required: true, message: '请选择新电话费等级', trigger: 'change' }
        ],
        newCarSubsidyId: [
          { required: true, message: '请选择新公车补贴等级', trigger: 'change' }
        ]
      },
      pickerOptions: {
        disabledDate: time => {
          const val = new Date(this.salaryMonth)
          return time.getTime() > val
          }
        }
    }
  },
  computed: {
    paymentTypeText() {
      return this.dataModel.enumPaymentType === 1 ? '补发' : '补扣'
    },
    // 动态起始年月标签
    monthRangeLabel() {
      return `${this.paymentTypeText}月份`
    },
    // 动态月份数标签
    monthCountLabel() {
      return `${this.paymentTypeText}月份数`
    },
    // 动态计算金额标签
    calculatedAmountLabel() {
      return `计算${this.paymentTypeText}`
    },
    // 动态实际金额标签
    actualAmountLabel() {
      return `实际${this.paymentTypeText}`
    }
  },
  methods: {
    initDialog(row) {
      if (!row) {
        this.title = '新增行政聘任'
        this.isEdit = false
      } else {
        this.title = '编辑行政聘任'
        this.isEdit = true
        this.getData(row.id)
      }
      this.dataModel.salaryId = this.salaryId
      this.$set(this.dataModel, 'enumPaymentType', 1) // 默认选择补扣
      this.showDialog = true
      this.queryStationTree()
      this.queryTelephoneFeeSelector()
      this.queryCarSubsidySelector()
      this.queryStationAllowance()
      this.getMaxDaysOfMonth(this.salaryId)
    },
    getMaxDaysOfMonth(salaryId) {
      salaryApi.getSalary({ id: salaryId }).then(res => {
        if (res.succeed) {
          const salaryData = res.data
          var recordMonth = salaryData.month
          var year = recordMonth.split('-')[0]
          var month = recordMonth.split('-')[1]
          var d = new Date(year, month, 0)
          // 月份需要减1，因为js的月份是从0开始，设置为当月的第1天
          var m = new Date(year, (month - 1), 1)
          this.salaryMonth = m
          this.maxDaysOfMonth = d.getDate()
        }
      }).catch(res => {
      })
    },
    queryStationTree() {
      sysManageApi.queryStationTree()
        .then((result) => {
          this.stationTrees = result.data
        })
        .catch((error) => {
          console.log(error)
        })
    },
    queryTelephoneFeeSelector() {
      sysManageApi.queryTelephoneFeeSelector()
        .then((result) => {
          this.telephoneFees = result.data
        })
        .catch((error) => {
          console.log(error)
        })
    },
    queryCarSubsidySelector() {
      sysManageApi.queryCarSubsidySelector()
        .then((result) => {
          this.carSubsidys = result.data
        })
        .catch((error) => {
          console.log(error)
        })
    },
    queryStationAllowance() {
      sysManageApi.queryStationAllowance()
        .then((result) => {
          this.stationAllowances = result.data
        })
        .catch((error) => {
          console.log(error)
        })
    },
    getData(id) {
      salaryApi.getAdministrativeAppointment({ id: id }).then(res => {
        if (res.succeed) {
          this.dataModel = res.data
          this.setEmployeeModel(res.data.employee)
        }
      }).catch(res => {
      })
    },
    saveDialog() {
            if (this.dataModel.actualBackPayDeduction === undefined || this.dataModel.actualBackPayDeduction === 0) {
         this.$message.error('实际金额必须大于等于0')
         return
      }
      if (parseFloat(this.dataModel.backPayDeduction) !== parseFloat(this.dataModel.actualBackPayDeduction)) {
        if (this.dataModel.remark === '' || this.dataModel.remark === undefined) {
           this.$message.error('计算金额与实际金额不一致，请备注原因后再提交')
           return
        }
      }
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.btnSaveLoading = true
          this.$delete(this.dataModel, 'station')
          if (this.dataModel.backPayMonth === 0) {
            this.changeBackPayMonth()
          }
          if (!this.isEdit) {
            salaryApi.addAdministrativeAppointment(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '添加成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          } else {
            salaryApi.updateAdministrativeAppointment(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '修改成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          }
        }
      })
    },
    closeDialog() {
      this.dataModel = { employeeModel: {}, newTelephoneFeeId: '', newCarSubsidyId: '', newStationId: '' }
      this.showDialog = false
      this.$refs.dataForm.resetFields()
    },
    selectEmployeeDialog() {
      this.$refs.selectEmployee.loadTree()
      // 是否管理津贴
      this.$refs.selectEmployee.listQuery.isManagementAllowance = true
      this.$refs.selectEmployee.showEmployee = true
    },
    setEmployee(emp) {
      this.dataModel = { employeeModel: {}, newTelephoneFeeId: '', newCarSubsidyId: '', newStationId: '' }
      this.$set(this.dataModel, 'enumPaymentType', 1)
      this.calculateAdministrativeAppointment(emp)
    },
    setEmployeeModel(emp) {
      this.societyAge = emp.employeeHR.societyAge
      this.$set(this.dataModel, 'employeeModel',
        {
          employeeId: emp.id,
          empUid: emp.uid,
          empCode: emp.empCode,
          empName: emp.displayName,
          genderDesc: emp.enumGenderDesc,
          empDept: emp.deptName,
          hospitalAreaNameText: emp.hospitalAreaNameText,
          identityNumber: emp.identityNumber,
          hireStyleName: emp.employeeHR.hireStyleName,
          leaveStyleName: emp.employeeHR.leaveStyleName,
          deadDate: emp.employeeHR.deadDate,
          effHireDate: emp.employeeHR.effHireDate,
          officialRankName: emp.employeeHR.officialRankName
        })
    },
    // 设置初始月份
    setInitialMonth() {
      // 获取当前薪资月的前一个月作为起始月份
      const currentMonth = new Date(this.salaryMonth)
      const prevMonth = new Date(currentMonth)
      prevMonth.setMonth(prevMonth.getMonth() - 1)

      // 设置月份范围为前一个月到当前月
      this.$set(this.dataModel, 'monthRange', [prevMonth, this.salaryMonth])
      this.dataModel.startDate = prevMonth
      this.dataModel.endDate = this.salaryMonth

      // 计算月份差值
      const getMonths = (date) => date.getFullYear() * 12 + date.getMonth()
      const monthDiff = getMonths(new Date(this.salaryMonth)) - getMonths(new Date(prevMonth))
      this.$set(this.dataModel, 'backPayDeductionMonth', monthDiff)
    },
    calculateAdministrativeAppointment(emp) {
      this.dataModel.employeeId = emp.id
      this.dataModel.salaryId = this.salaryId
      salaryApi.calculateAdministrativeAppointment(this.dataModel).then(res => {
        if (res.succeed) {
          this.dataModel = res.data
          this.setInitialMonth()
          this.setEmployeeModel(emp)
        }
      }).catch(res => {
        console.log(res)
      })
    },
    // 计算差额
    calculateDifference() {
      // 岗位工资
      const newStationWage = parseFloat(this.dataModel.newStationWage) || 0
      const oldStationWage = parseFloat(this.dataModel.oldStationWage) || 0
      // 岗位津贴
      const oldStationAllowance = parseFloat(this.dataModel.oldStationAllowance) || 0
      const newStationAllowance = parseFloat(this.dataModel.newStationAllowance) || 0
      // 电话费
      const oldTelephoneFeeAllowance = parseFloat(this.dataModel.oldTelephoneFeeAllowance) || 0
      const newTelephoneFeeAllowance = parseFloat(this.dataModel.newTelephoneFeeAllowance) || 0
      // 公车补贴
      const oldCarSubsidyAllowance = parseFloat(this.dataModel.oldCarSubsidyAllowance) || 0
      const newCarSubsidyAllowance = parseFloat(this.dataModel.newCarSubsidyAllowance) || 0

      const difference = Math.abs(newStationWage + newStationAllowance + newTelephoneFeeAllowance + newCarSubsidyAllowance -
      oldStationWage - oldStationAllowance - oldTelephoneFeeAllowance - oldCarSubsidyAllowance).toFixed(2)

      this.$set(this.dataModel, 'difference', difference)
    },
    // 补发/补扣月份数
    changeBackPayMonth() {
      const backPayDeductionMonth = parseFloat(this.dataModel.backPayDeductionMonth) || 0
      const difference = parseFloat(this.dataModel.difference) || 0

      const value = Math.abs(backPayDeductionMonth * difference).toFixed(2)
      this.$set(this.dataModel, 'backPayDeduction', value)
      this.$set(this.dataModel, 'actualBackPayDeduction', value)
    },
    oldStationTreeChange() {
      this.stationTrees.forEach(parent => {
        if (parent.children != null) {
          var stationWage = parent.children.find(a => a.id === this.dataModel.oldStationId)
          if (stationWage) {
            this.dataModel.oldStationWage = stationWage.wage
          }
        }
      })

      var stationAllowance = this.stationAllowances.find(a => a.stationId === this.dataModel.oldStationId && a.workAge == this.societyAge)
      if (stationAllowance) {
        this.dataModel.oldStationAllowance = stationAllowance.allowance
      }
    },
    stationTreeChange() {
      this.stationTrees.forEach(parent => {
        if (parent.children != null) {
          var stationWage = parent.children.find(a => a.id === this.dataModel.newStationId)
          if (stationWage) {
            this.dataModel.newStationWage = stationWage.wage
          }
        }
      })
      var stationAllowance = this.stationAllowances.find(a => a.stationId === this.dataModel.newStationId && a.workAge == this.societyAge)
      if (stationAllowance) {
        this.dataModel.newStationAllowance = stationAllowance.allowance
      }
    },
    oldTelephoneFeeChange() {
      this.telephoneFees.forEach(e => {
        if (e.id === this.dataModel.oldTelephoneFeeId) {
          this.dataModel.oldTelephoneFeeAllowance = e.allowance
        }
      })
    },
    telephoneFeeChange() {
      this.telephoneFees.forEach(e => {
        if (e.id === this.dataModel.newTelephoneFeeId) {
          this.dataModel.newTelephoneFeeAllowance = e.allowance
        }
      })
    },
    oldCarSubsidyChange() {
      this.carSubsidys.forEach(e => {
        if (e.id === this.dataModel.oldCarSubsidyId) {
          this.dataModel.oldCarSubsidyAllowance = e.allowance
        }
      })
    },
    carSubsidyChange() {
      this.carSubsidys.forEach(e => {
        if (e.id === this.dataModel.newCarSubsidyId) {
          this.dataModel.newCarSubsidyAllowance = e.allowance
        }
      })
    },
    monthRangeChange(date) {
      if (date) {
          const [start, end] = this.dataModel.monthRange
          this.dataModel.startDate = start
          this.dataModel.endDate = end

          const getMonths = (date) => date.getFullYear() * 12 + date.getMonth()
          this.$set(this.dataModel, 'backPayDeductionMonth', getMonths(new Date(end)) - getMonths(new Date(start)))
      }
    },
    calculate() {
      // 先计算差额
      this.calculateDifference()
      // 再计算补发/补扣金额
      this.changeBackPayMonth()
    }
  }
}
</script>

<style>
</style>
