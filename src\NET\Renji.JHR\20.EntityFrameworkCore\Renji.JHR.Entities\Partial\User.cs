﻿using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;
using System.Text.Json.Serialization;
using System.Xml.Serialization;

namespace Renji.JHR.Entities
{
    public partial class User : IUser, IPerson
    {
        #region IUser

        [NotMapped, XmlIgnore, JsonIgnore]
        public string UniqueName => this.Username;

        [NotMapped, XmlIgnore, JsonIgnore]
        public string UserId => this.ID.AsString();

        [NotMapped, XmlIgnore, JsonIgnore]
        public string IdentityId => string.Empty;

        [NotMapped, XmlIgnore, JsonIgnore]
        public string IdentityDisplayName => string.Empty;

        [NotMapped, XmlIgnore, JsonIgnore]
        public string AgentId => string.Empty;

        [NotMapped, XmlIgnore, JsonIgnore]
        public string AgentDisplayName => string.Empty;

        [NotMapped, XmlIgnore, JsonIgnore]
        string IIdentityKey.RoleId => string.Empty;

        [NotMapped, XmlIgnore, JsonIgnore]
        string IUser.RoleName => string.Empty;

        [NotMapped, XmlIgnore, JsonIgnore]
        public IIdentityKey IdentityKey => new IdentityKey
        {
            UserId = this.UserId,
            IdentityId = this.IdentityId,
            AgentId = this.AgentId,
            RoleId = string.Empty
        };

        #endregion IUser

        [NotMapped, JsonIgnore, XmlIgnore]
        public string OldPwdText { get; set; }

        [NotMapped, JsonIgnore, XmlIgnore]
        public string NewPwdText { get; set; }

        [NotMapped, JsonIgnore, XmlIgnore]
        public List<string> Permissions { get; set; }
    }
}