<template>
  <div class="app-container">
    <layout4>
      <template #main>
        <el-form v-if="!employeeId" ref="ref_searchFrom" :inline="true" :model="listQuery">
          <el-form-item>
            <el-input v-model="listQuery.uid" clearable placeholder="唯一码" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.empCode" clearable placeholder="工号" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.empName" clearable placeholder="姓名" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.deptName" clearable placeholder="部门" />
          </el-form-item>
          <el-form-item>
            <el-select v-model="listQuery.enumCalculationType" clearable placeholder="请选择标准">
              <el-option v-for="item in calculationTypeOptions" :key="item.value" :label="item.desc" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="listQuery.enumThirteenthSalaryEmployeeStatus" clearable placeholder="请选择人员范围">
              <el-option v-for="item in employeeStatusOptions" :key="item.value" :label="item.desc" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
            <el-button v-if="salaryData.enumStatus == 1" type="primary" icon="el-icon-upload" @click="autoImport">自动导入</el-button>
            <el-button v-if="salaryData.enumStatus == 1" type="primary" icon="el-icon-plus" @click="showDialog()">新增</el-button>
          </el-form-item>
        </el-form>
        <el-table ref="tableList" v-loading="listLoading" :data="pageList" border stripe fit highlight-current-row style="width: 100%;" :header-cell-style="{ background: '#F5F7FA', color: '#606266' }" @sort-change="sortChange">
          <el-table-column label="部门" prop="Employee.Department.Name" sortable="custom">
            <template slot-scope="{ row }">
              <span>{{ row.deptName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="标准" sortable="custom" prop="EnumThirteenthSalaryCalculationType">
            <template slot-scope="{ row }">
              <span>{{ row.enumThirteenthSalaryCalculationTypeDesc }}</span>
            </template>
          </el-table-column>
          <el-table-column label="人员范围" sortable="custom" prop="EnumThirteenthSalaryEmployeeStatus">
            <template slot-scope="{ row }">
              <span>{{ row.enumThirteenthSalaryEmployeeStatusDesc }}</span>
            </template>
          </el-table-column>
          <el-table-column label="计算金额" sortable="custom" prop="CalculateAmount">
            <template slot-scope="{ row }">
              <span>{{ row.calculateAmount ? row.calculateAmount.toFixed(2) : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实际金额" sortable="custom" prop="ActualAmount">
            <template slot-scope="{ row }">
              <span>{{ row.actualAmount ? row.actualAmount.toFixed(2) : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="计算说明" min-width="200px" sortable="custom" prop="CalculateRemark">
            <template slot-scope="{ row }">
              <span>{{ row.calculateRemark }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="150px" sortable="custom" prop="Remark">
            <template slot-scope="{ row }">
              <span>{{ row.remark }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" header-align="center" width="150" class-name="small-padding fixed-width">
            <template slot-scope="{ row }">
              <el-button v-if="salaryData.enumStatus == 1" type="primary" style="margin-left:5px !important; padding-left: 5px !important;" size="mini" @click="showDialog(row)">
                编辑
              </el-button>
              <el-button v-if="salaryData.enumStatus == 1" style="padding-left: 5px !important;" size="mini" type="danger" @click="deleteThirteenthSalary(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
          <employeeTableColumns />
        </el-table>
        <c-pagination v-show="listQuery.total > 0" :total="listQuery.total" :page-sizes="[10, 20, 50]" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getPageList" />
      </template>
    </layout4>

    <editDialog ref="editDialog" @refreshData="getPageList" />
  </div>
</template>

<script>
import salaryApi from '@/api/salary'
import sysManageApi from '@/api/sysManage'
import editDialog from './components/editPage'
import employeeTableColumns from '@/views/salary/monthSalary/components/hrSalary/employeeTableColumns'

export default {
  components: {
    editDialog,
    employeeTableColumns
  },
  props: {
    employeeId: {
      type: String,
      default: '',
      required: false
    }
  },
  data() {
    return {
      salaryId: '',
      pageList: [],
      listQuery: {
        total: 1,
        pageIndex: 1,
        pageSize: 10,
        employeeId: ''
      },
      listLoading: false,
      calculationTypeOptions: [],
      paymentStatusOptions: [],
      employeeStatusOptions: [],
      salaryData: {}
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.salaryId = this.$route.query.salaryId
      if (this.salaryId) {
        this.getSalary()
        if (this.employeeId) {
          this.listQuery.employeeId = this.employeeId
        }
      } else {
        this.$notice.message('系统错误，请刷新页面重试或联系管理员', 'error')
      }
      this.loadCalculationTypeOptions()
      this.loadEmployeeStatusOptions()
      this.getPageList()
    },
    getSalary() {
      salaryApi.getSalary({ id: this.salaryId }).then(result => {
        if (result.succeed) {
          this.salaryData = result.data
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    sortChange(c) {
      if (c.order === 'ascending') {
        this.listQuery.order = c.prop + ' ' + 'asc'
      } else if (c.order === 'descending') {
        this.listQuery.order = c.prop + ' ' + 'desc'
      } else {
        this.$delete(this.listQuery, 'order')
      }
      this.search()
    },
    search() {
      this.listQuery.pageIndex = 1
      this.getPageList()
    },
    getPageList() {
      this.listLoading = true
      this.listQuery.salaryId = this.salaryId
      salaryApi.queryThirteenthSalary(this.listQuery).then(result => {
        if (result.succeed) {
          this.pageList = result.data.datas
          this.listQuery.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      }).finally(() => {
        this.listLoading = false
      })
    },
    autoImport() {
      this.autoImportThirteenthSalary()
    },
    autoImportThirteenthSalary() {
      this.$confirm('确定导入当月十三月工资数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        salaryApi.autoImportThirteenthSalary({ salaryId: this.salaryId }).then(result => {
          if (result.succeed) {
            this.getPageList()
            this.$notice.message('导入成功', 'success')
          } else {
            if (!result.succeed) {
              this.$notice.message('导入失败，请联系管理员', 'info')
            }
          }
        })
          .catch(error => {
            this.listLoading = false
            console.log(error)
          })
      })
    },
    loadCalculationTypeOptions() {
      sysManageApi.getEnumInfos({ enumType: 'ThirteenthSalaryCalculationType' }).then(result => {
        this.calculationTypeOptions = result.data.datas
      }).catch(error => {
        console.log(error)
      })
    },
    loadEmployeeStatusOptions() {
      sysManageApi.getEnumInfos({ enumType: 'ThirteenthSalaryEmployeeStatus' }).then(result => {
        this.employeeStatusOptions = result.data.datas
      }).catch(error => {
        console.log(error)
      })
    },
    deleteThirteenthSalary(data) {
      this.$confirm('确定删除这条记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        salaryApi.deleteThirteenthSalary(data).then(result => {
          if (result.succeed) {
            this.getPageList()
            this.$notice.message('删除成功', 'success')
          } else {
            if (!result.succeed) {
              this.$notice.message('删除失败，请联系管理员', 'info')
            }
          }
        })
          .catch(error => {
            this.listLoading = false
            console.log(error)
          })
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    showDialog(row) {
      this.$refs.editDialog.salaryId = this.salaryId
      this.$refs.editDialog.initDialog(row)
    }
  }
}
</script>

<style></style>
