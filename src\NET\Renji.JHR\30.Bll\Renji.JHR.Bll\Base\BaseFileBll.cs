﻿using Renji.JHR.Bll.Caching;
using Renji.JHR.Dal;

namespace Renji.JHR.Bll
{
    public abstract class BaseFileBll : Repository<FileDbContext>, IRepo
    {
        #region Constructs

        protected BaseFileBll(IUser? operatorUser = null)
            : base(operatorUser)
        {
        }

        protected BaseFileBll(string operatorUniqueName)
            : base(operatorUniqueName)
        {
        }

        protected BaseFileBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        protected BaseFileBll(IRepo bll)
            : base(bll.FileRepo)
        {
        }

        #endregion Constructs

        #region IRepo

        protected virtual IRepo BizRepo => this.SysBll;
        protected virtual IRepo FileRepo => this.FileBll;
        protected virtual IRepo LogRepo => this.LogBll;
        protected virtual IRepo MailRepo => this.MailBll;

        protected virtual SysBll SysBll => this.GetRepo<SysBll>();
        protected virtual FileBll FileBll => this.GetRepo<FileBll>();
        protected virtual LogBll LogBll => this.GetRepo<LogBll>();
        protected virtual MailBll MailBll => this.GetRepo<MailBll>();

        IRepo IRepo.BizRepo => this.BizRepo;
        IRepo IRepo.FileRepo => this.FileRepo;
        IRepo IRepo.LogRepo => this.LogRepo;
        IRepo IRepo.MailRepo => this.MailRepo;

        #endregion IRepo

        #region Operator User

        /// <summary>
        /// 操作用户（可能为空）
        /// </summary>
        public new User? OperatorUser => base.OperatorUser as User;

        /// <summary>
        /// 操作用户ID（可能为空）
        /// </summary>
        public virtual Guid? OperatorUserId => this.OperatorUser?.ID;

        /// <summary>

        #endregion Operator User

        #region Current User

        /// <summary>
        /// 当前用户（操作用户为空时报错）
        /// </summary>
        public new User CurrentUser => (User)base.CurrentUser;

        /// <summary>
        /// 当前用户ID（操作用户为空时报错）
        /// </summary>
        public virtual Guid CurrentUserId => this.CurrentUser.ID;

        #endregion Current User

        #region SysCache

        private SysCache? _sysCache = null;

        protected virtual SysCache SysCache => _sysCache ??= this.GetRequiredService<SysCache>();

        protected virtual SysCache GetSysCache()
        {
            var cache = this.GetRequiredService<SysCache>();

            if (this.ServiceScope != null)
            {
                cache.SetServiceScope(this.ServiceScope);
            }

            return cache;
        }

        public override void ClearServiceScope()
        {
            base.ClearServiceScope();

            if (_sysCache != null)
            {
                _sysCache.ClearServiceScope();
            }
        }

        #endregion SysCache
    }
}