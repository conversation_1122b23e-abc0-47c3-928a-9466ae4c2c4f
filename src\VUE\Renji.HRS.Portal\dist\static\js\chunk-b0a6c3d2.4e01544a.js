(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b0a6c3d2"],{"19f6":function(t,e,o){"use strict";var a=o("8168"),i=o.n(a);i.a},"1ccf":function(t,e,o){"use strict";o.r(e);var a=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"app-container"},[o("layout1",{scopedSlots:t._u([{key:"header",fn:function(){return[o("el-input",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"岗位名称"},model:{value:t.stationListQuery.stationName,callback:function(e){t.$set(t.stationListQuery,"stationName",e)},expression:"stationListQuery.stationName"}}),o("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.loadTree}},[t._v("查询")]),o("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:t.addStationDialog}},[t._v("添加岗位")]),o("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:t.allocatePositionDialog}},[t._v("分配职位")]),o("el-button",{attrs:{type:"primary",icon:"el-icon-edit"},on:{click:t.updateDialog}},[t._v("更新")]),o("el-button",{attrs:{type:"primary",icon:"el-icon-delete"},on:{click:t.deleteRecord}},[t._v("删除")])]},proxy:!0},{key:"aside",fn:function(){return[o("c-tree",{directives:[{name:"loading",rawName:"v-loading",value:t.fullscreenLoading,expression:"fullscreenLoading"}],ref:"tree",attrs:{"element-loading-text":"正在刷新",options:t.treeData,props:t.treeProps,"expanded-keys":t.treeExpandedKeys},on:{nodeClick:t.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[o("span")]},proxy:!0}])}),o("el-dialog",{attrs:{title:"添加岗位",visible:t.addStationDialogVisible,width:"60%"},on:{"update:visible":function(e){t.addStationDialogVisible=e}}},[o("el-form",{ref:"ref_addStationForm",attrs:{rules:t.rules_Station,model:t.addStationForm,"label-width":"140px"}},[o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"上级岗位"}},[t._v(" "+t._s(t.addStationForm.parentName)+" ")])],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"中文名",prop:"name"}},[o("el-input",{attrs:{placeholder:"中文名",maxlength:"50"},model:{value:t.addStationForm.name,callback:function(e){t.$set(t.addStationForm,"name",e)},expression:"addStationForm.name"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"英文名",prop:"eName"}},[o("el-input",{attrs:{placeholder:"英文名",maxlength:"50"},model:{value:t.addStationForm.eName,callback:function(e){t.$set(t.addStationForm,"eName",e)},expression:"addStationForm.eName"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"排序",prop:"number"}},[o("el-input",{attrs:{placeholder:"排序",maxlength:"9"},model:{value:t.addStationForm.number,callback:function(e){t.$set(t.addStationForm,"number",e)},expression:"addStationForm.number"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"岗位工资",prop:"wage"}},[o("el-input",{staticClass:"numrule",staticStyle:{width:"100%"},attrs:{type:"number",placeholder:"岗位工资",maxlength:"10"},on:{change:t.wageChange},model:{value:t.addStationForm.wage,callback:function(e){t.$set(t.addStationForm,"wage",e)},expression:"addStationForm.wage"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"是否护理"}},[o("el-checkbox",{model:{value:t.addStationForm.isNurse,callback:function(e){t.$set(t.addStationForm,"isNurse",e)},expression:"addStationForm.isNurse"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"不计算薪级"}},[o("el-checkbox",{model:{value:t.addStationForm.isCalSalaryScale,callback:function(e){t.$set(t.addStationForm,"isCalSalaryScale",e)},expression:"addStationForm.isCalSalaryScale"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"描述"}},[o("el-input",{attrs:{placeholder:"描述",maxlength:"50"},model:{value:t.addStationForm.description,callback:function(e){t.$set(t.addStationForm,"description",e)},expression:"addStationForm.description"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"备注"}},[o("el-input",{attrs:{placeholder:"中文描述",maxlength:"50"},model:{value:t.addStationForm.memo,callback:function(e){t.$set(t.addStationForm,"memo",e)},expression:"addStationForm.memo"}})],1)],1)],1),o("el-row",[o("el-col",[null===t.addStationForm.parentId?o("el-form-item",{attrs:{label:"是否是职务"}},[o("el-checkbox",{model:{value:t.addStationForm.isPost,callback:function(e){t.$set(t.addStationForm,"isPost",e)},expression:"addStationForm.isPost"}})],1):t._e()],1)],1),o("el-row",[o("el-col",[null===t.addStationForm.parentId?o("el-form-item",{attrs:{label:"是否是职称"}},[o("el-checkbox",{model:{value:t.addStationForm.isTitle,callback:function(e){t.$set(t.addStationForm,"isTitle",e)},expression:"addStationForm.isTitle"}})],1):t._e()],1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.addStationDialogVisible=!1}}},[t._v("取 消")]),o("el-button",{attrs:{type:"primary"},on:{click:t.submitAddStationForm}},[t._v("确 定")])],1)],1),o("el-dialog",{staticClass:"allocateDialog",attrs:{title:"分配职位",visible:t.allocatePositionDialogVisible,width:"30%"},on:{"update:visible":function(e){t.allocatePositionDialogVisible=e}}},[o("el-form",{ref:"ref_allocatePositionForm",attrs:{rules:t.rules_Position,model:t.allocatePositionForm,"label-width":"100px"}},[o("el-checkbox-group",{model:{value:t.allocatePositionForm.checkedPosition,callback:function(e){t.$set(t.allocatePositionForm,"checkedPosition",e)},expression:"allocatePositionForm.checkedPosition"}},t._l(t.positionOptions,(function(e){return o("el-row",{key:e.id},[o("el-col",[o("el-checkbox",{attrs:{label:e.id}},[t._v(t._s(e.name))])],1)],1)})),1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.allocatePositionDialogVisible=!1}}},[t._v("取 消")]),o("el-button",{attrs:{type:"primary"},on:{click:t.submitAllocatePositionForm}},[t._v("确 定")])],1)],1),o("el-dialog",{attrs:{title:"更新岗位信息",visible:t.updateStationDialogVisible,width:"60%"},on:{"update:visible":function(e){t.updateStationDialogVisible=e}}},[o("el-form",{ref:"ref_updateStationForm",attrs:{rules:t.rules_Station,model:t.updateStationForm,"label-width":"140px"}},[o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"上级岗位"}},[t._v(" "+t._s(t.updateStationForm.parentName)+" ")])],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"中文名",prop:"name"}},[o("el-input",{attrs:{placeholder:"中文名",maxlength:"50"},model:{value:t.updateStationForm.name,callback:function(e){t.$set(t.updateStationForm,"name",e)},expression:"updateStationForm.name"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"英文名",prop:"eName"}},[o("el-input",{attrs:{placeholder:"英文名",maxlength:"50"},model:{value:t.updateStationForm.eName,callback:function(e){t.$set(t.updateStationForm,"eName",e)},expression:"updateStationForm.eName"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"排序",prop:"number"}},[o("el-input",{attrs:{placeholder:"排序",maxlength:"9"},model:{value:t.updateStationForm.number,callback:function(e){t.$set(t.updateStationForm,"number",e)},expression:"updateStationForm.number"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"岗位工资",prop:"wage"}},[o("el-input",{staticClass:"numrule",staticStyle:{width:"100%"},attrs:{type:"number",placeholder:"岗位工资",maxlength:"10"},on:{change:t.wageChange2},model:{value:t.updateStationForm.wage,callback:function(e){t.$set(t.updateStationForm,"wage",e)},expression:"updateStationForm.wage"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"是否护理"}},[o("el-checkbox",{model:{value:t.updateStationForm.isNurse,callback:function(e){t.$set(t.updateStationForm,"isNurse",e)},expression:"updateStationForm.isNurse"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"不计算薪级"}},[o("el-checkbox",{model:{value:t.updateStationForm.isCalSalaryScale,callback:function(e){t.$set(t.updateStationForm,"isCalSalaryScale",e)},expression:"updateStationForm.isCalSalaryScale"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"描述"}},[o("el-input",{attrs:{placeholder:"描述",maxlength:"50"},model:{value:t.updateStationForm.description,callback:function(e){t.$set(t.updateStationForm,"description",e)},expression:"updateStationForm.description"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"备注"}},[o("el-input",{attrs:{placeholder:"备注",maxlength:"50"},model:{value:t.updateStationForm.memo,callback:function(e){t.$set(t.updateStationForm,"memo",e)},expression:"updateStationForm.memo"}})],1)],1)],1),o("el-row",[o("el-col",[null===t.updateStationForm.parentId?o("el-form-item",{attrs:{label:"是否是职务"}},[o("el-checkbox",{model:{value:t.updateStationForm.isPost,callback:function(e){t.$set(t.updateStationForm,"isPost",e)},expression:"updateStationForm.isPost"}})],1):t._e()],1)],1),o("el-row",[o("el-col",[null===t.updateStationForm.parentId?o("el-form-item",{attrs:{label:"是否是职称"}},[o("el-checkbox",{model:{value:t.updateStationForm.isTitle,callback:function(e){t.$set(t.updateStationForm,"isTitle",e)},expression:"updateStationForm.isTitle"}})],1):t._e()],1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.updateStationDialogVisible=!1}}},[t._v("取 消")]),o("el-button",{attrs:{type:"primary"},on:{click:t.submitUpdateStationForm}},[t._v("确 定")])],1)],1),o("el-dialog",{attrs:{title:"更新职位信息",visible:t.updatePositionDialogVisible,width:"60%"},on:{"update:visible":function(e){t.updatePositionDialogVisible=e}}},[o("el-form",{ref:"ref_updatePositionForm",attrs:{rules:t.rules_Position,model:t.updatePositionForm,"label-width":"100px"}},[o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"中文名"}},[o("el-input",{attrs:{placeholder:"中文名",maxlength:"50"},model:{value:t.updatePositionForm.name,callback:function(e){t.$set(t.updatePositionForm,"name",e)},expression:"updatePositionForm.name"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"ParentId"}})],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"序号"}},[o("el-input",{attrs:{placeholder:"序号",maxlength:"9"},model:{value:t.updatePositionForm.ordinal,callback:function(e){t.$set(t.updatePositionForm,"ordinal",e)},expression:"updatePositionForm.ordinal"}})],1)],1)],1),o("el-row",[o("el-col",[o("el-form-item",{attrs:{label:"中文描述"}},[o("el-input",{attrs:{placeholder:"中文描述",maxlength:"50"},model:{value:t.updatePositionForm.description,callback:function(e){t.$set(t.updatePositionForm,"description",e)},expression:"updatePositionForm.description"}})],1)],1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.updatePositionDialogVisible=!1}}},[t._v("取 消")]),o("el-button",{attrs:{type:"primary"},on:{click:t.submitUpdatePositionForm}},[t._v("确 定")])],1)],1)],1)},i=[],n=(o("b0c0"),o("a9e3"),o("b680"),o("ac1f"),o("5319"),o("d368")),l={data:function(){var t=function(t,e,o){if(!e)return o();var a=/^[0-9]*[1-9][0-9]*$/;setTimeout((function(){a.test(e)?o():o(new Error("请输入正确的排序"))}),100)};return{fullscreenLoading:!1,treeData:[{parentId:"-1",id:null,name:"岗位（职位）结构树",icon:"树",type:"top",children:[]}],positionOptions:[],options:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeExpandedKeys:["0"],currentNode:null,addStationDialogVisible:!1,allocatePositionDialogVisible:!1,updateStationDialogVisible:!1,updatePositionDialogVisible:!1,addStationForm:{},allocatePositionForm:{stationId:"",checkedPosition:[]},updateStationForm:{},updatePositionForm:{},rules_Station:{name:[{required:!0,message:"请输入中文名",trigger:"blur"},{max:50,message:"中文名不允许超过50个字符",trigger:"blur"}],eName:[{max:50,message:"拼音不允许超过50个字符",trigger:"blur"}],number:[{validator:t,trigger:"blur"}],wage:[{required:!0,trigger:"blur",message:"岗位工资必填"},{pattern:/(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/,trigger:"blur",message:"岗位工资必须大于零，且最多两位小数"}]},rules_Position:{name:[{max:50,message:"中文名不允许超过50个字符",trigger:"blur"}],ordinal:[{max:10,message:"序号不允许超过10个字符",trigger:"blur"}],description:[{max:50,message:"中文描述不允许超过50个字符",trigger:"blur"}]},stationListQuery:{}}},created:function(){this.loadTree(),this.loadPosition()},methods:{loadTree:function(){var t=this;this.fullscreenLoading=!0,n["a"].QueryPositionStationTree(this.stationListQuery).then((function(e){t.treeData[0]["children"]=e.data,t.treeExpandedKeys.push("null"),t.fullscreenLoading=!1})).catch((function(t){console.log(t)}))},loadPosition:function(){var t=this;n["a"].queryPosition({}).then((function(e){t.positionOptions=e.data.datas})).catch((function(t){console.log(t)}))},treeNodeClick:function(t){this.currentNode=t},wageChange:function(){var t=(this.addStationForm.wage+"").replace(/,/g,""),e=Number(t).toFixed(2);this.$set(this.addStationForm,"wage",Number(e))},wageChange2:function(){var t=(this.updateStationForm.wage+"").replace(/,/g,""),e=Number(t).toFixed(2);this.$set(this.updateStationForm,"wage",Number(e))},resetAddStationForm:function(){var t=this;this.addStationForm={name:"",eName:"",parentId:"",parentName:"",number:"",wage:"",description:"",memo:""},this.$nextTick((function(){t.$refs["ref_addStationForm"].clearValidate()}))},addStationDialog:function(){this.currentNode?"gw"===this.currentNode.type||"top"===this.currentNode.type?(this.resetAddStationForm(),this.addStationForm.parentName=this.currentNode.name,this.addStationForm.parentId=this.currentNode.id,this.addStationDialogVisible=!0):this.$message({showClose:!0,message:"职位节点不允许添加岗位",type:"warning"}):this.$message({showClose:!0,message:"请选择一个岗位或职位节点"})},allocatePositionDialog:function(){var t=this;this.currentNode?"top"!==this.currentNode.type?"gw"===this.currentNode.type?(this.resetAllocatePositionForm(),this.allocatePositionDialogVisible=!0,n["a"].GetStation({id:this.currentNode.id}).then((function(e){t.allocatePositionForm.checkedPosition=e.data.positionIds})).catch((function(e){-3!==e.type&&t.$notice.resultTip(e)}))):this.$message({showClose:!0,message:"职位节点不允许分配职位",type:"warning"}):this.$message({showClose:!0,message:"根节点无法分配职位"}):this.$message({showClose:!0,message:"请选择一个岗位或职位节点"})},updateDialog:function(){var t=this;this.currentNode?"top"!==this.currentNode.type?"gw"===this.currentNode.type?(this.resetUpdateStationForm(),this.updateStationDialogVisible=!0,n["a"].GetStation({id:this.currentNode.id}).then((function(e){t.updateStationForm=e.data})).catch((function(e){-3!==e.type&&t.$notice.resultTip(e)}))):(this.resetUpdatePositionForm(),this.updatePositionDialogVisible=!0,n["a"].getPosition({id:this.currentNode.id}).then((function(e){t.updatePositionForm=e.data})).catch((function(t){console.log(t)}))):this.$message({showClose:!0,message:"根节点无法更新"}):this.$message({showClose:!0,message:"请选择一个岗位或职位节点"})},resetAllocatePositionForm:function(){var t=this;this.allocatePositionForm={checkedPosition:[]},this.$nextTick((function(){t.$refs["ref_allocatePositionForm"].clearValidate()}))},resetUpdatePositionForm:function(){var t=this;this.updatePositionForm={},this.$nextTick((function(){t.$refs["ref_updatePositionForm"].clearValidate()}))},resetUpdateStationForm:function(){var t=this;this.updateStationForm={},this.$nextTick((function(){t.$refs["ref_updateStationForm"].clearValidate()}))},deleteRecord:function(){var t=this;this.currentNode?"top"!==this.currentNode.type?this.currentNode.children&&this.currentNode.children.length>0?this.$message({showClose:!0,message:"岗位下存在子岗位或职位时，此岗位不允许删除",type:"warning"}):this.$confirm("确认删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){"gw"===t.currentNode.type?n["a"].DeleteStation({id:t.currentNode.id,confirmToDelete:!1}).then((function(e){e.succeed?(t.loadTree(),t.$notice.message("删除成功","success")):t.$notice.resultTip(e)})).catch((function(t){console.log(t)})):n["a"].DeletePositionStation({stationId:t.currentNode.parentId,positionId:t.currentNode.id}).then((function(e){e.succeed?(t.loadTree(),t.$notice.message("删除成功","success")):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})})):this.$message({showClose:!0,message:"根节点无法删除"}):this.$message({showClose:!0,message:"请选择一个岗位或职位节点"})},submitAddStationForm:function(){var t=this;this.$refs["ref_addStationForm"].validate((function(e){if(e){if(t.addStationForm.number){var o=Number(t.addStationForm.number);t.$set(t.addStationForm,"number",Number(o))}n["a"].AddStation(t.addStationForm).then((function(e){e.succeed?(t.$notice.message("新增成功","success"),t.addStationDialogVisible=!1,t.loadTree()):-3!==e.type&&t.$notice.resultTip(e)})).catch((function(e){e.processed||t.$notice.message("新增失败。","error")}))}}))},submitAllocatePositionForm:function(){var t=this,e={stationId:this.currentNode.id,PositionIds:this.allocatePositionForm.checkedPosition};n["a"].AllocatePosition(e).then((function(e){e.succeed?(t.$notice.message("分配成功","success"),t.allocatePositionDialogVisible=!1,t.loadTree()):-3!==e.type&&t.$notice.resultTip(e)})).catch((function(e){e.processed||t.$notice.message("分配失败。","error")}))},submitUpdateStationForm:function(){var t=this;this.$refs["ref_updateStationForm"].validate((function(e){if(e){if(t.updateStationForm.number){var o=Number(t.updateStationForm.number);t.$set(t.updateStationForm,"number",Number(o))}if(t.updateStationForm.wage){var a=Number(t.updateStationForm.wage).toFixed(2);t.$set(t.updateStationForm,"wage",Number(a))}n["a"].UpdateStation(t.updateStationForm).then((function(e){e.succeed?(t.$notice.message("更新成功","success"),t.updateStationDialogVisible=!1,t.loadTree()):-3!==e.type&&t.$notice.resultTip(e)})).catch((function(e){e.processed||t.$notice.message("更新失败。","error")}))}}))},submitUpdatePositionForm:function(){var t=this;this.$refs["ref_updatePositionForm"].validate((function(e){e&&n["a"].updatePosition(t.updatePositionForm).then((function(e){e.succeed?(t.$notice.message("更新成功","success"),t.updatePositionDialogVisible=!1,t.loadTree()):-3!==e.type&&t.$notice.resultTip(e)})).catch((function(e){e.processed||t.$notice.message("更新失败。","error")}))}))}}},r=l,s=(o("19f6"),o("ff53"),o("9210"),o("2877")),c=Object(s["a"])(r,a,i,!1,null,"3f0dacca",null);e["default"]=c.exports},8168:function(t,e,o){},9210:function(t,e,o){"use strict";var a=o("e62f"),i=o.n(a);i.a},e62f:function(t,e,o){},ff50:function(t,e,o){},ff53:function(t,e,o){"use strict";var a=o("ff50"),i=o.n(a);i.a}}]);