﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
	<NoWarn>1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
	<None Remove="Renji.JHR.Dal.csproj.vspscc" />
	<None Remove="Renji.JHR.Dal.xml" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Renji.JHR.Entities" />
    <Using Include="Shinsoft.Core" />
  </ItemGroup>

  <ItemGroup>
	<Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
  </ItemGroup>

  <ItemGroup>
	<Reference Include="Shinsoft.Core">
	  <HintPath>..\..\00.Reference\net6.0\Shinsoft.Core.dll</HintPath>
	</Reference>
  </ItemGroup>

  <ItemGroup>
	<ProjectReference Include="..\..\10.Common\Renji.JHR.Common\Renji.JHR.Common.csproj" />
	<ProjectReference Include="..\Renji.JHR.Entities\Renji.JHR.Entities.csproj" />
  </ItemGroup>

  <ItemGroup>
	<None Update="DbContext\File.DbContext.tt">
	  <Generator>TextTemplatingFileGenerator</Generator>
	  <LastGenOutput>File.DbContext.cs</LastGenOutput>
	</None>
	<None Update="DbContext\Log.DbContext.tt">
	  <Generator>TextTemplatingFileGenerator</Generator>
	  <LastGenOutput>Log.DbContext.cs</LastGenOutput>
	</None>
	<None Update="DbContext\Biz.DbContext.tt">
	  <Generator>TextTemplatingFileGenerator</Generator>
	  <LastGenOutput>Biz.DbContext.cs</LastGenOutput>
	</None>
	<None Update="DbContext\Mail.DbContext.tt">
	  <Generator>TextTemplatingFileGenerator</Generator>
	  <LastGenOutput>Mail.DbContext.cs</LastGenOutput>
	</None>
  </ItemGroup>

  <ItemGroup>
	<Compile Update="DbContext\Biz.DbContext.cs">
	  <DesignTime>True</DesignTime>
	  <AutoGen>True</AutoGen>
	  <DependentUpon>Biz.DbContext.tt</DependentUpon>
	</Compile>
	<Compile Update="DbContext\File.DbContext.cs">
	  <DesignTime>True</DesignTime>
	  <AutoGen>True</AutoGen>
	  <DependentUpon>File.DbContext.tt</DependentUpon>
	</Compile>
	<Compile Update="DbContext\Log.DbContext.cs">
	  <DesignTime>True</DesignTime>
	  <AutoGen>True</AutoGen>
	  <DependentUpon>Log.DbContext.tt</DependentUpon>
	</Compile>
	<Compile Update="DbContext\Mail.DbContext.cs">
	  <DesignTime>True</DesignTime>
	  <AutoGen>True</AutoGen>
	  <DependentUpon>Mail.DbContext.tt</DependentUpon>
	</Compile>
  </ItemGroup>
</Project>
