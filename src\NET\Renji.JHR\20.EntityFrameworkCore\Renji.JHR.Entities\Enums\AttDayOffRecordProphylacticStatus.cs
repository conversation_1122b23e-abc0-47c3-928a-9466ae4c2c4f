﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Renji.JHR.Entities
{
    /// <summary>
    /// 防保科考勤申报 状态
    /// </summary>
    public enum AttDayOffRecordProphylacticStatus
    {
        None = 0,

        /// <summary>
        /// 待提交
        /// </summary>
        [Description("待提交")]
        Pending = 1,

        /// <summary>
        /// 已提交
        /// </summary>
        [Description("已提交")]
        Submitted = 10,
    }
}
