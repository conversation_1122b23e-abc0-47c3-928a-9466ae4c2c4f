<template>
  <div class="app-container ">
    <layout4>
      <template #main>
        <el-form ref="ref_searchFrom" :inline="true" :model="listQuery">
          <el-form-item>
            <el-input v-model="listQuery.uid" style="width:120px;" clearable placeholder="唯一码" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.empCode" style="width:120px;" clearable placeholder="工号" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.displayName" style="width:120px;" clearable placeholder="姓名" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="exportThirteenthFinanceData">导出十三月工资财务数据</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-user-solid" @click="employeeSalaryRecord">员工薪资状态记录</el-button>
          </el-form-item>
        </el-form>

        <el-table
          ref="tableList"
          v-loading="listLoading"
          class="my-table"
          :data="pageList"
          border
          stripe
          fit
          highlight-current-row
          style="width: 100%;"
          :header-cell-style="{background:'#F5F7FA',color:'#606266'}"
          @sort-change="sortChange"
        >
        
          <!-- 十三月工资相关字段 -->
          <el-table-column label="十三月工资应发" sortable="custom" :min-width="getStringWidth('十三月工资应发')" header-align="left" align="right" prop="ThirteenthMonthSalary">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'thirteenthMonthSalary')">{{ getDecimalValueOrDefault(row.thirteenthMonthSalary) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="十三月工资代扣税金" sortable="custom" :min-width="getStringWidth('十三月工资代扣税金')" header-align="left" align="right" prop="ThirteenthMonthTaxWithholding">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'thirteenthMonthTaxWithholding')">{{ getDecimalValueOrDefault(row.thirteenthMonthTaxWithholding) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="十三月工资实发" sortable="custom" :min-width="getStringWidth('十三月工资实发')" header-align="left" align="right" prop="ThirteenthMonthSalaryNet">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'thirteenthMonthSalaryNet')">{{ getDecimalValueOrDefault(row.thirteenthMonthSalaryNet) }}</span>
            </template>
          </el-table-column>

          <employeeTableColumns />
        </el-table>
        <c-pagination v-show="listQuery.total > 0" :total="listQuery.total" :page-sizes="[10, 20, 50]" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getPageList" />
      </template>
    </layout4>

    <!-- 员工薪资状态记录弹窗 -->
    <employeeSalaryRecordDialog ref="employeeSalaryRecordDialog" />
  </div>
</template>

<script>
import salaryApi from '@/api/salary'
import sysManageApi from '@/api/sysManage'
import editDialog from '../components/editPage'
import employeeSalaryRecordDialog from '../components/employeeSalaryRecord.vue'
import socialSecurityWithhold from '../components/socialSecurityWithhold.vue'
import employeeTableColumns from '@/views/salary/monthSalary/components/hrSalary/employeeTableColumns'

export default {
  components: {
    employeeTableColumns
  },
  props: {
    salaryId: {
      type: String,
      default: '',
      required: true
    }
  },
  data() {
    return {
      pageList: [],
      listQuery: {
        total: 1,
        pageIndex: 1,
        pageSize: 10,
        uid: '',
        empCode: '',
        displayName: '',
        keywords: '',
        entityColumn: null,
        queryCondition: {
          EnumOperation: null,
          Keywords: ''
        }
      },
      listLoading: false,
      dataColumns: [],
      selectConditionOptions: []
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    init() {
      this.getPageList()
    },
    getPageList () {
      this.pageList = []
      this.listLoading = true
      this.listQuery.salaryId = this.salaryId
      salaryApi.querySalaryDetail(this.listQuery).then(result => {
        if (result.succeed) {
          this.pageList = result.data.datas
          this.listQuery.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      }).finally(() => {
        this.listLoading = false
      })
    },
    search() {
      this.listQuery.pageIndex = 1
      this.getPageList()
    },
    sortChange(c) {
      if (c.order === 'ascending') {
        this.listQuery.order = c.prop + ' ' + 'asc'
      } else if (c.order === 'descending') {
        this.listQuery.order = c.prop + ' ' + 'desc'
      } else {
        this.listQuery.order = null
      }
      this.getPageList()
    },
    getStringWidth(str) {
      return Math.max(str.length * 12 + 40, 100) + 'px'
    },
    getDecimalValueOrDefault(value) {
      return value != null && value > 0 ? parseFloat(value).toFixed(2) : ''
    },
    getStyle(currentRow, previousRow, field) {
      // 样式处理逻辑，可以根据需要实现
      return {}
    },
    exportThirteenthFinanceData() {
      this.pageList = []
      this.listLoading = true
      salaryApi.exportThirteenthFinanceData(this.listQuery).then(res => {
        const fileDownload = require('js-file-download')
        var filename = '员工薪资' + this.$moment().format('YYYYMMDDHHmmss') + '.xlsx'
        if (res.data) {
          fileDownload(res.data, filename)
        } else {
          fileDownload(res, filename)
        }
        this.getPageList()
      })
    }
  }
}
</script>

<style scoped>
.my-table {
  font-size: 12px;
}
</style>
