﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Renji.JHR.Entities
{
    public enum FieldType
    {
        /// <summary>
        /// 字符串
        /// </summary>
        [Description("字符串")]
        String = 0,

        /// <summary>
        /// 数值int decimal
        /// </summary>
        [Description("数值")]
        Numeric = 1,

        /// <summary>
        /// 日期
        /// </summary>
        [Description("日期")]
        Date = 2,

        /// <summary>
        /// 布尔
        /// </summary>
        [Description("布尔")]
        Bool = 3,

        /// <summary>
        /// 字典
        /// </summary>
        [Description("字典")]
        Dict = 4,

        /// <summary>
        /// 部门
        /// </summary>
        [Description("部门")]
        Dept = 5,

        /// <summary>
        /// 工资标准类型
        /// </summary>
        [Description("工资标准类型")]
        OrgClass = 6,

        /// <summary>
        /// 工资组
        /// </summary>
        [Description("工资组")]
        CompGroup = 7,

        /// <summary>
        /// 岗位工资级别
        /// </summary>
        [Description("岗位工资级别")]
        OrgSalary = 8,

        /// <summary>
        /// 薪级工资级别
        /// </summary>
        [Description("薪级工资级别")]
        OrgSalaryLevel = 9,

        /// <summary>
        /// 职务工资级别
        /// </summary>
        [Description("职务工资级别")]
        OrgPositionSalary = 10,
    }
}
