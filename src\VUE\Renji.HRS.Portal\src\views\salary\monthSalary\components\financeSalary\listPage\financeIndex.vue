<template>
  <div class="app-container ">
    <layout4>
      <template #main>
        <el-form ref="ref_searchFrom" :inline="true" :model="listQuery">
          <el-form-item>
            <el-input v-model="listQuery.uid" style="width:120px;" clearable placeholder="唯一码" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.empCode" style="width:120px;" clearable placeholder="工号" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.displayName" style="width:120px;" clearable placeholder="姓名" />
          </el-form-item>
          <!--
          <el-form-item>
            <el-input v-model="listQuery.keywords" style="width:120px;" clearable placeholder="关键字" />
          </el-form-item>
          -->
          <el-form-item>
            <el-select v-model="listQuery.entityColumn" class="filter-item" filterable style="width:120px;" clearable placeholder="请选择字段">
              <el-option v-for="item in dataColumns" :key="item.value" :label="item.label" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="listQuery.queryCondition.EnumOperation" class="filter-item" style="width:120px;" clearable placeholder="请选择条件">
              <el-option v-for="item in selectConditionOptions" :key="item.value" :label="item.desc" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.queryCondition.Keywords" style="width:150px;" clearable placeholder="请输入关键字" />
          </el-form-item>
          <!--<el-form-item>
            <el-select v-model="listQuery.fourGoldDeficiencySign" class="filter-item" style="width:180px;" clearable placeholder="请选择四金不够扣标记">
              <el-option v-for="item in signDropdown" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>-->
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="exportData">导出财务数据</el-button>
          </el-form-item>
          <el-form-item>
            <el-upload action="" :http-request="importExcel" accept=".xlsx" :show-file-list="false">
              <el-button slot="trigger" icon="el-icon-upload" type="primary">导入</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-user-solid" @click="employeeSalaryRecord">员工薪资状态记录</el-button>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="exportHRData">导出人事数据</el-button>
          </el-form-item>
        </el-form>

        <el-table
          ref="tableList"
          v-loading="listLoading"
          class="my-table"
          :data="pageList"
          border
          stripe
          fit
          highlight-current-row
          style="width: 100%;"
          :header-cell-style="{background:'#F5F7FA',color:'#606266'}"
          @sort-change="sortChange"
        >
          <el-table-column label="上月数据" width="100px" align="center" fixed>
            <template slot-scope="{ row }">
              <input v-if="!row.id.includes('_lastMonth')" id="choose" class="choose" type="checkbox" @input="sarechLastMonthData(row)">
              <span v-if="row.id.includes('_lastMonth')">{{ lastMonthData.salaryMonth }}</span>
            </template>
          </el-table-column>
          <!-- 扩展列
          <el-table-column
            v-for="item in groupList"
            :key="item.id"
            :prop="item.id"
            :label="item.name"
          >
            <template v-slot="scope">
              {{ getReissueDeduction(scope.row.universalReissueDeduction, item.id) }}
            </template>
          </el-table-column>
          -->
          <el-table-column label="证件号" sortable="custom" :min-width="getStringWidth('证件号')" prop="Employee.IdentityNumber">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'identityNumber')">{{ row.identityNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="手机号" sortable="custom" :min-width="getStringWidth('手机号')" prop="Employee.Mobile">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'mobile')">{{ row.mobile }}</span>
            </template>
          </el-table-column>
          <el-table-column label="在职方式" sortable="custom" :min-width="getStringWidth('在职方式')" prop="Employee.EmployeeHR.HireStyle.Name">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'hireStyleName')">{{ row.hireStyleName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="受雇年份" sortable="custom" :min-width="getStringWidth('受雇年份')" prop="EmployYear">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'employYear')">{{ row.employYear }}</span>
            </template>
          </el-table-column>
          <el-table-column label="受雇月份" sortable="custom" :min-width="getStringWidth('受雇月份')" prop="EmployMonth">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'employMonth')">{{ row.employMonth }}</span>
            </template>
          </el-table-column>
          <el-table-column label="本年度受雇月数" sortable="custom" :min-width="getStringWidth('本年度受雇月数')" prop="EmployMonthNumber">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'employMonthNumber')">{{ row.employMonthNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注1" sortable="custom" :min-width="getStringWidth('备注1')" prop="Memo1">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'memo1')">{{ row.memo1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注2" sortable="custom" :min-width="getStringWidth('备注2')" prop="Memo2">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'memo2')">{{ row.memo2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注3" sortable="custom" :min-width="getStringWidth('备注3')" prop="Memo3">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'memo3')">{{ row.memo3 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="四金不够扣标记" sortable="custom" :min-width="getStringWidth('四金不够扣标记')" align="center" prop="FourGoldDeficiencySign">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'fourGoldDeficiencySign')">{{ row.fourGoldDeficiencySign ? "有" : "无" }}</span>
            </template>
          </el-table-column>
          <!--<el-table-column label="旧社保基数" sortable="custom" :min-width="getStringWidth('旧社保基数')" header-align="left" align="right" prop="OldSocialSecurityBase">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'oldSocialSecurityBase')">{{ getDecimalValueOrDefault(row.oldSocialSecurityBase) }}</span>
            </template>
          </el-table-column>-->
          <el-table-column label="社保基数" sortable="custom" :min-width="getStringWidth('社保基数')" header-align="left" align="right" prop="SocialSecurityBase">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'socialSecurityBase')">{{ getDecimalValueOrDefault(row.socialSecurityBase) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="公积金基数" sortable="custom" :min-width="getStringWidth('公积金基数')" header-align="left" align="right" prop="HousingFundBase">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'housingFundBase')">{{ parseFloat(row.housingFundBase).toFixed(0) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="岗位级别" sortable="custom" :min-width="getStringWidth('岗位级别')" align="center" prop="Station.Name">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'stationName')">{{ row.stationName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="薪级" sortable="custom" :min-width="getStringWidth('薪级')" align="center" prop="SalaryScale.scale">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'scale')">{{ row.scale }}</span>
            </template>
          </el-table-column>
          <el-table-column label="工龄" sortable="custom" :min-width="getStringWidth('工龄')" align="center" prop="SocietyAge">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'societyAge')">{{ row.societyAge }}</span>
            </template>
          </el-table-column>
          <el-table-column label="工龄段" sortable="custom" :min-width="getStringWidth('工龄段')" align="center" prop="SeniorityRangeId">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'seniorityRangeName')">{{ row.seniorityRangeName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补充公积金标记" sortable="custom" :min-width="getStringWidth('补充公积金标记')" align="center" prop="SupplementaryHousingFundFlag">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'supplementaryHousingFundFlag')">{{ row.supplementaryHousingFundFlag ? "有" : "无" }}</span>
            </template>
          </el-table-column>
          <el-table-column label="职员年金标记" sortable="custom" :min-width="getStringWidth('职员年金标记')" align="center" prop="EmployeePensionFlag">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'employeePensionFlag')">{{ row.employeePensionFlag ? "有" : "无" }}</span>
            </template>
          </el-table-column>
          <el-table-column label="病产假天数" sortable="custom" :min-width="getStringWidth('病产假天数')" align="center" prop="SickLeaveDays">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'sickLeaveDays')">{{ getDecimalValueOrDefault(row.sickLeaveDays) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="休假类型" sortable="custom" min-width="300px" align="center" prop="SalaryLeaveDesc">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'salaryLeaveDesc')">{{ row.salaryLeaveDesc }}</span>
            </template>
          </el-table-column>
          <el-table-column label="基本工资小计" sortable="custom" :min-width="getStringWidth('基本工资小计')" header-align="left" align="right" prop="BasicSalarySubtotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'basicSalarySubtotal')">{{ getDecimalValueOrDefault(row.basicSalarySubtotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="岗资基数" sortable="custom" :min-width="getStringWidth('岗资基数')" header-align="left" align="right" prop="StationWageBase">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'stationWageBase')">{{ getDecimalValueOrDefault(row.stationWageBase) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="岗资" sortable="custom" :min-width="getStringWidth('岗资')" header-align="left" align="right" prop="StationWage">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'stationWage')">{{ getDecimalValueOrDefault(row.stationWage) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="薪资基数" sortable="custom" :min-width="getStringWidth('薪资基数')" header-align="left" align="right" prop="SalaryBase">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'salaryBase')">{{ getDecimalValueOrDefault(row.salaryBase) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="薪资" sortable="custom" :min-width="getStringWidth('薪资')" header-align="left" align="right" prop="SalaryMoney">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'salaryMoney')">{{ getDecimalValueOrDefault(row.salaryMoney) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="基本工资其它加" sortable="custom" :min-width="getStringWidth('基本工资其它加')" header-align="left" align="right" prop="BasicSalaryOtherAdd">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'basicSalaryOtherAdd')">{{ getDecimalValueOrDefault(row.basicSalaryOtherAdd) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="津补贴小计" sortable="custom" :min-width="getStringWidth('津补贴小计')" header-align="left" align="right" prop="AllowanceSubtotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'allowanceSubtotal')">{{ getDecimalValueOrDefault(row.allowanceSubtotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="粮油补贴" sortable="custom" :min-width="getStringWidth('粮油补贴')" header-align="left" align="right" prop="GrainOilSubsidy">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'grainOilSubsidy')">{{ getDecimalValueOrDefault(row.grainOilSubsidy) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="上下班交通费基数" sortable="custom" :min-width="getStringWidth('上下班交通费基数')" header-align="left" align="right" prop="CommuteSubsidyBase">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'commuteSubsidyBase')">{{ getDecimalValueOrDefault(row.commuteSubsidyBase) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="上下班交通费" sortable="custom" :min-width="getStringWidth('上下班交通费')" header-align="left" align="right" prop="CommuteSubsidy">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'commuteSubsidy')">{{ getDecimalValueOrDefault(row.commuteSubsidy) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="护龄基数" sortable="custom" :min-width="getStringWidth('护龄基数')" header-align="left" align="right" prop="NursingBase">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'nursingBase')">{{ getDecimalValueOrDefault(row.nursingBase) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="护龄" sortable="custom" :min-width="getStringWidth('护龄')" align="center" prop="Nursing">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'nursing')">{{ getDecimalValueOrDefault(row.nursing) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="独子" sortable="custom" :min-width="getStringWidth('独子')" align="center" prop="OnlyChild">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'onlyChild')">{{ getDecimalValueOrDefault(row.onlyChild) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="独子补发" sortable="custom" :min-width="getStringWidth('独子补发')" header-align="left" align="right" prop="BackPayOnlyChild">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'backPayOnlyChild')">{{ getDecimalValueOrDefault(row.backPayOnlyChild) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="独子合计" sortable="custom" :min-width="getStringWidth('独子合计')" header-align="left" align="right" prop="OnlyChildTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'onlyChildTotal')">{{ getDecimalValueOrDefault(row.onlyChildTotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="援外津贴" sortable="custom" :min-width="getStringWidth('援外津贴')" header-align="left" align="right" prop="ForeignAidAllowance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'foreignAidAllowance')">{{ getDecimalValueOrDefault(row.foreignAidAllowance) }}</span>
            </template>
          </el-table-column>
          <!--<el-table-column label="援滇津贴" sortable="custom" :min-width="getStringWidth('援滇津贴')" header-align="left" align="right" prop="YunnanAidAllowance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'yunnanAidAllowance')">{{ getDecimalValueOrDefault(row.yunnanAidAllowance) }}</span>
            </template>
          </el-table-column>-->
          <el-table-column label="津补贴其它加" sortable="custom" :min-width="getStringWidth('津补贴其它加')" header-align="left" align="right" prop="AllowanceOtherAdd">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'allowanceOtherAdd')">{{ getDecimalValueOrDefault(row.allowanceOtherAdd) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="绩效小计" sortable="custom" :min-width="getStringWidth('绩效小计')" header-align="left" align="right" prop="PerformanceSubtotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'performanceSubtotal')">{{ getDecimalValueOrDefault(row.performanceSubtotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="岗位津贴基数" sortable="custom" :min-width="getStringWidth('岗位津贴基数')" header-align="left" align="right" prop="StationAllowanceBase">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'stationAllowanceBase')">{{ getDecimalValueOrDefault(row.stationAllowanceBase) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="岗位津贴" sortable="custom" :min-width="getStringWidth('岗位津贴')" header-align="left" align="right" prop="StationAllowance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'stationAllowance')">{{ getDecimalValueOrDefault(row.stationAllowance) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="工作量津贴" sortable="custom" :min-width="getStringWidth('工作量津贴')" header-align="left" align="right" prop="WorkloadAllowance1">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'workloadAllowance1')">{{ getDecimalValueOrDefault(row.workloadAllowance1) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="工作量津贴合计" sortable="custom" :min-width="getStringWidth('工作量津贴合计')" header-align="left" align="right" prop="WorkloadAllowance2Subtotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'workloadAllowance2Subtotal')">{{ getDecimalValueOrDefault(row.workloadAllowance2Subtotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="停车补贴" sortable="custom" :min-width="getStringWidth('停车补贴')" header-align="left" align="right" prop="ParkingSubsidy">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'parkingSubsidy')">{{ getDecimalValueOrDefault(row.parkingSubsidy) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="公派车贴补贴" sortable="custom" :min-width="getStringWidth('公派车贴补贴')" header-align="left" align="right" prop="OfficialCarAllowance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'officialCarAllowance')">{{ getDecimalValueOrDefault(row.officialCarAllowance) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="电话费" sortable="custom" :min-width="getStringWidth('电话费')" header-align="left" align="right" prop="TelephoneFee">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'telephoneFee')">{{ getDecimalValueOrDefault(row.telephoneFee) }}</span>
            </template>
          </el-table-column>
          <!--<el-table-column label="补发电话费" sortable="custom" :min-width="getStringWidth('补发电话费')" header-align="left" align="right" prop="BackPayTelephoneFee">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'backPayTelephoneFee')">{{ getDecimalValueOrDefault(row.backPayTelephoneFee) }}</span>
            </template>
          </el-table-column>-->
          <el-table-column label="电话费合计" sortable="custom" :min-width="getStringWidth('电话费合计')" header-align="left" align="right" prop="TelephoneFeeTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'telephoneFeeTotal')">{{ getDecimalValueOrDefault(row.telephoneFeeTotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="长病假职工最低工资补助" sortable="custom" :min-width="getStringWidth('长病假职工最低工资补助')" header-align="left" align="right" prop="LongSickLeaveMinimumWageSubsidy">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'longSickLeaveMinimumWageSubsidy')">{{ getDecimalValueOrDefault(row.longSickLeaveMinimumWageSubsidy) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="博士后房帖" sortable="custom" :min-width="getStringWidth('博士后房帖')" header-align="left" align="right" prop="PostdoctoralHousingAllowance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'postdoctoralHousingAllowance')">{{ getDecimalValueOrDefault(row.postdoctoralHousingAllowance) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="卫生津贴" sortable="custom" :min-width="getStringWidth('卫生津贴')" header-align="left" align="right" prop="HealthAllowance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'healthAllowance')">{{ getDecimalValueOrDefault(row.healthAllowance) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="零星补节假日加班绩效" sortable="custom" :min-width="getStringWidth('零星补节假日加班绩效')" header-align="left" align="right" prop="OccasionalHolidayOvertimePerformance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'occasionalHolidayOvertimePerformance')">{{ getDecimalValueOrDefault(row.occasionalHolidayOvertimePerformance) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="节假日加班绩效" sortable="custom" :min-width="getStringWidth('节假日加班绩效')" header-align="left" align="right" prop="HolidayOvertimePerformance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'holidayOvertimePerformance')">{{ getDecimalValueOrDefault(row.holidayOvertimePerformance) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="中夜班绩效" sortable="custom" :min-width="getStringWidth('中夜班绩效')" header-align="left" align="right" prop="MidnightShiftPerformance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'midnightShiftPerformance')">{{ getDecimalValueOrDefault(row.midnightShiftPerformance) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="行政值班绩效" sortable="custom" :min-width="getStringWidth('行政值班绩效')" header-align="left" align="right" prop="AdministrativeDutyPerformance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'administrativeDutyPerformance')">{{ getDecimalValueOrDefault(row.administrativeDutyPerformance) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="一二值班绩效" sortable="custom" :min-width="getStringWidth('一二值班绩效')" header-align="left" align="right" prop="FirstSecondDutyPerformance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'firstSecondDutyPerformance')">{{ getDecimalValueOrDefault(row.firstSecondDutyPerformance) }}</span>
            </template>
          </el-table-column>
          <!--<el-table-column label="急诊拖后值班绩效" sortable="custom" :min-width="getStringWidth('急诊拖后值班绩效')" header-align="left" align="right" prop="EmergencyDelayDutyPerformance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'emergencyDelayDutyPerformance')">{{ getDecimalValueOrDefault(row.emergencyDelayDutyPerformance) }}</span>
            </template>
          </el-table-column>-->
          <el-table-column label="其它加" sortable="custom" :min-width="getStringWidth('其它加')" header-align="left" align="right" prop="OtherAdd">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'otherAdd')">{{ getDecimalValueOrDefault(row.otherAdd) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="行政聘任补发" sortable="custom" :min-width="getStringWidth('行政聘任补发')" header-align="left" align="right" prop="WorkChangeReissue">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'workChangeReissue')">{{ getDecimalValueOrDefault(row.workChangeReissue) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="退还多扣养扣" sortable="custom" :min-width="getStringWidth('退还多扣养扣')" header-align="left" align="right" prop="ReturnOverDeductPension">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'returnOverDeductPension')">{{ getDecimalValueOrDefault(row.returnOverDeductPension) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="退还多扣医疗" sortable="custom" :min-width="getStringWidth('退还多扣医疗')" header-align="left" align="right" prop="ReturnOverDeductMedical">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'returnOverDeductMedical')">{{ getDecimalValueOrDefault(row.returnOverDeductMedical) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="退还多扣失业" sortable="custom" :min-width="getStringWidth('退还多扣失业')" header-align="left" align="right" prop="ReturnOverDeductUnemployment">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'returnOverDeductUnemployment')">{{ getDecimalValueOrDefault(row.returnOverDeductUnemployment) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="退还多扣公扣" sortable="custom" :min-width="getStringWidth('退还多扣公扣')" header-align="left" align="right" prop="ReturnOverDeductPublic">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'returnOverDeductPublic')">{{ getDecimalValueOrDefault(row.returnOverDeductPublic) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="退还多扣补充公积金" sortable="custom" :min-width="getStringWidth('退还多扣补充公积金')" header-align="left" align="right" prop="ReturnOverDeductSupplementaryHousingFund">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'returnOverDeductSupplementaryHousingFund')">{{ getDecimalValueOrDefault(row.returnOverDeductSupplementaryHousingFund) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="退还多扣年金" sortable="custom" :min-width="getStringWidth('退还多扣年金')" header-align="left" align="right" prop="ReturnOverDeductOccupationalAnnuity">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'returnOverDeductOccupationalAnnuity')">{{ getDecimalValueOrDefault(row.returnOverDeductOccupationalAnnuity) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="退还多扣出国人员扣款" sortable="custom" :min-width="getStringWidth('退还多扣出国人员扣款')" header-align="left" align="right" prop="ReturnOverDeductDeductionForForeigners">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'returnOverDeductDeductionForForeigners')">{{ getDecimalValueOrDefault(row.returnOverDeductDeductionForForeigners) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="退还多扣病假工资" sortable="custom" :min-width="getStringWidth('退还多扣病假工资')" header-align="left" align="right" prop="ReturnOverDeductSickLeaveSalary">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'returnOverDeductSickLeaveSalary')">{{ getDecimalValueOrDefault(row.returnOverDeductSickLeaveSalary) }}</span>
            </template>
          </el-table-column>
          <!--<el-table-column label="长病假、待退休绩效奖" sortable="custom" :min-width="getStringWidth('长病假、待退休绩效奖')" header-align="left" align="right" prop="LongSickLeaveRetirementPerformanceBonus">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'longSickLeaveRetirementPerformanceBonus')">{{ getDecimalValueOrDefault(row.longSickLeaveRetirementPerformanceBonus) }}</span>
            </template>
          </el-table-column>-->
          <el-table-column label="定级补发" sortable="custom" :min-width="getStringWidth('定级补发')" header-align="left" align="right" prop="BackPayGrading">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'backPayGrading')">{{ getDecimalValueOrDefault(row.backPayGrading) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="应发工资" sortable="custom" :min-width="getStringWidth('应发工资')" header-align="left" align="right" prop="GrossSalary">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'grossSalary')">{{ getDecimalValueOrDefault(row.grossSalary) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="应发工资（申报个税用）" sortable="custom" :min-width="getStringWidth('应发工资（申报个税用）')" header-align="left" align="right" prop="GrossSalaryForTaxDeclaration">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'grossSalaryForTaxDeclaration')">{{ getDecimalValueOrDefault(row.grossSalaryForTaxDeclaration) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="养扣" sortable="custom" :min-width="getStringWidth('养扣')" header-align="left" align="right" prop="PensionDeduction">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'pensionDeduction')">{{ getDecimalValueOrDefault(row.pensionDeduction) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补扣养扣" sortable="custom" :min-width="getStringWidth('补扣养扣')" header-align="left" align="right" prop="BackDeductionPension">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'backDeductionPension')">{{ getDecimalValueOrDefault(row.backDeductionPension) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="养扣合计" sortable="custom" :min-width="getStringWidth('养扣合计')" header-align="left" align="right" prop="PensionDeductionTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'pensionDeductionTotal')">{{ getDecimalValueOrDefault(row.pensionDeductionTotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="医疗保险" sortable="custom" :min-width="getStringWidth('医疗保险')" header-align="left" align="right" prop="MedicalInsurance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'medicalInsurance')">{{ getDecimalValueOrDefault(row.medicalInsurance) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补扣医疗保险" sortable="custom" :min-width="getStringWidth('补扣医疗保险')" header-align="left" align="right" prop="BackDeductionMedicalInsurance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'backDeductionMedicalInsurance')">{{ getDecimalValueOrDefault(row.backDeductionMedicalInsurance) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="医疗保险合计" sortable="custom" :min-width="getStringWidth('医疗保险合计')" header-align="left" align="right" prop="MedicalInsuranceTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'medicalInsuranceTotal')">{{ getDecimalValueOrDefault(row.medicalInsuranceTotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="失业保险" sortable="custom" :min-width="getStringWidth('失业保险')" header-align="left" align="right" prop="UnemploymentInsurance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'unemploymentInsurance')">{{ getDecimalValueOrDefault(row.unemploymentInsurance) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补扣失业保险" sortable="custom" :min-width="getStringWidth('补扣失业保险')" header-align="left" align="right" prop="BackDeductionUnemploymentInsurance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'backDeductionUnemploymentInsurance')">{{ getDecimalValueOrDefault(row.backDeductionUnemploymentInsurance) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="失业保险合计" sortable="custom" :min-width="getStringWidth('失业保险合计')" header-align="left" align="right" prop="UnemploymentInsuranceTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'unemploymentInsuranceTotal')">{{ getDecimalValueOrDefault(row.unemploymentInsuranceTotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="基本公积金" sortable="custom" :min-width="getStringWidth('基本公积金')" header-align="left" align="right" prop="BasicHousingFund">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'basicHousingFund')">{{ getDecimalValueOrDefault(row.basicHousingFund) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补扣基本公积金" sortable="custom" :min-width="getStringWidth('补扣基本公积金')" header-align="left" align="right" prop="BackDeductionBasicHousingFund">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'backDeductionBasicHousingFund')">{{ getDecimalValueOrDefault(row.backDeductionBasicHousingFund) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="基本公积金合计" sortable="custom" :min-width="getStringWidth('基本公积金合计')" header-align="left" align="right" prop="HousingFundTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'housingFundTotal')">{{ getDecimalValueOrDefault(row.housingFundTotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补充公积金" sortable="custom" :min-width="getStringWidth('补充公积金')" header-align="left" align="right" prop="SupplementaryHousingFund">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'supplementaryHousingFund')">{{ parseFloat(row.supplementaryHousingFund).toFixed(0) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补扣补充公积金" sortable="custom" :min-width="getStringWidth('补扣补充公积金')" header-align="left" align="right" prop="BackDeductionSupplementaryHousingFund">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'backDeductionSupplementaryHousingFund')">{{ getDecimalValueOrDefault(row.backDeductionSupplementaryHousingFund) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补充公积金合计" sortable="custom" :min-width="getStringWidth('补充公积金合计')" header-align="left" align="right" prop="SupplementaryHousingFundTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'supplementaryHousingFundTotal')">{{ getDecimalValueOrDefault(row.supplementaryHousingFundTotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="职业年金" sortable="custom" :min-width="getStringWidth('职业年金')" header-align="left" align="right" prop="OccupationalAnnuity">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'occupationalAnnuity')">{{ getDecimalValueOrDefault(row.occupationalAnnuity) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补扣职业年金" sortable="custom" :min-width="getStringWidth('补扣职业年金')" header-align="left" align="right" prop="BackDeductionOccupationalAnnuity">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'backDeductionOccupationalAnnuity')">{{ getDecimalValueOrDefault(row.backDeductionOccupationalAnnuity) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="职业年金合计" sortable="custom" :min-width="getStringWidth('职业年金合计')" header-align="left" align="right" prop="OccupationalAnnuityTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'occupationalAnnuityTotal')">{{ getDecimalValueOrDefault(row.occupationalAnnuityTotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="会费" sortable="custom" :min-width="getStringWidth('会费')" header-align="left" align="right" prop="MembershipFee">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'membershipFee')">{{ parseFloat(row.membershipFee).toFixed(1) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补扣会费" sortable="custom" :min-width="getStringWidth('补扣会费')" header-align="left" align="right" prop="BackDeductionMembershipFee">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'backDeductionMembershipFee')">{{ getDecimalValueOrDefault(row.backDeductionMembershipFee) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="会费合计" sortable="custom" :min-width="getStringWidth('会费合计')" header-align="left" align="right" prop="MembershipFeeTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'membershipFeeTotal')">{{ getDecimalValueOrDefault(row.membershipFeeTotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="房租" sortable="custom" :min-width="getStringWidth('房租')" header-align="left" align="right" prop="Rent">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'rent')">{{ getDecimalValueOrDefault(row.rent) }}</span>
            </template>
          </el-table-column>
          <!--<el-table-column label="病假工资" sortable="custom" :min-width="getStringWidth('病假工资')" header-align="left" align="right" prop="SickLeaveSalary">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'sickLeaveSalary')">{{ getDecimalValueOrDefault(row.sickLeaveSalary) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补扣病假" sortable="custom" :min-width="getStringWidth('补扣病假')" header-align="left" align="right" prop="BackDeductionSickLeave">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'backDeductionSickLeave')">{{ getDecimalValueOrDefault(row.backDeductionSickLeave) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="病假工资合计" sortable="custom" :min-width="getStringWidth('病假工资合计')" header-align="left" align="right" prop="SickLeaveSalaryTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'sickLeaveSalaryTotal')">{{ getDecimalValueOrDefault(row.sickLeaveSalaryTotal) }}</span>
            </template>
          </el-table-column>>-->
          <el-table-column label="代扣税金" sortable="custom" :min-width="getStringWidth('代扣税金')" header-align="left" align="right" prop="TaxWithholding">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'taxWithholding')">{{ getDecimalValueOrDefault(row.taxWithholding) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="其它扣" sortable="custom" :min-width="getStringWidth('其它扣')" header-align="left" align="right" prop="OtherDeduction">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'otherDeduction')">{{ getDecimalValueOrDefault(row.otherDeduction) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="长病假补扣" sortable="custom" :min-width="getStringWidth('长病假补扣')" header-align="left" align="right" prop="BackDeductionLongSickLeave">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'backDeductionLongSickLeave')">{{ getDecimalValueOrDefault(row.backDeductionLongSickLeave) }}</span>
            </template>
          </el-table-column>
          <!--<el-table-column label="综合险" sortable="custom" :min-width="getStringWidth('综合险')" header-align="left" align="right" prop="ComprehensiveInsurance">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'comprehensiveInsurance')">{{ getDecimalValueOrDefault(row.comprehensiveInsurance) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="户口挂靠费" sortable="custom" :min-width="getStringWidth('户口挂靠费')" header-align="left" align="right" prop="HukouHostingFee">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'hukouHostingFee')">{{ getDecimalValueOrDefault(row.hukouHostingFee) }}</span>
            </template>
          </el-table-column>-->
          <el-table-column label="扣出国人员扣款" sortable="custom" :min-width="getStringWidth('扣出国人员扣款')" header-align="left" align="right" prop="DeductionForForeigners">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'deductionForForeigners')">{{ getDecimalValueOrDefault(row.deductionForForeigners) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="扣款合计" sortable="custom" :min-width="getStringWidth('扣款合计')" header-align="left" align="right" prop="DeductionTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'deductionTotal')">{{ getDecimalValueOrDefault(row.deductionTotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实发工资" sortable="custom" :min-width="getStringWidth('实发工资')" header-align="left" align="right" prop="NetSalary">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'netSalary')">{{ getDecimalValueOrDefault(row.netSalary) }}</span>
            </template>
          </el-table-column>
          <!--<el-table-column label="房贴2016" sortable="custom" :min-width="getStringWidth('房贴2016')" header-align="left" align="right" prop="HousingAllowance2016">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'housingAllowance2016')">{{ getDecimalValueOrDefault(row.housingAllowance2016) }}</span>
            </template>
          </el-table-column>-->
          <el-table-column label="养扣基础数据" sortable="custom" :min-width="getStringWidth('养扣基础数据')" header-align="left" align="right" prop="BasicDataDeduction">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'basicDataDeduction')">{{ getDecimalValueOrDefault(row.basicDataDeduction) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="医疗保险基础数据" sortable="custom" :min-width="getStringWidth('医疗保险基础数据')" header-align="left" align="right" prop="MedicalInsuranceBasicData">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'medicalInsuranceBasicData')">{{ getDecimalValueOrDefault(row.medicalInsuranceBasicData) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="失业保险基础数据" sortable="custom" :min-width="getStringWidth('失业保险基础数据')" header-align="left" align="right" prop="UnemploymentInsuranceBasicData">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'unemploymentInsuranceBasicData')">{{ getDecimalValueOrDefault(row.unemploymentInsuranceBasicData) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="公积金基础数据" sortable="custom" :min-width="getStringWidth('公积金基础数据')" header-align="left" align="right" prop="HousingFundBasicData">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'housingFundBasicData')">{{ getDecimalValueOrDefault(row.housingFundBasicData) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补充公积金基础数据" sortable="custom" :min-width="getStringWidth('补充公积金基础数据')" header-align="left" align="right" prop="SupplementaryHousingFundBasicData">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'supplementaryHousingFundBasicData')">{{ getDecimalValueOrDefault(row.supplementaryHousingFundBasicData) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="年金基础数据" sortable="custom" :min-width="getStringWidth('年金基础数据')" header-align="left" align="right" prop="PensionBasicData">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'pensionBasicData')">{{ getDecimalValueOrDefault(row.pensionBasicData) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="养老（申报个税用）" sortable="custom" :min-width="getStringWidth('养老（申报个税用）')" header-align="left" align="right" prop="PensionTaxDeclaration">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'pensionTaxDeclaration')">{{ getDecimalValueOrDefault(row.pensionTaxDeclaration) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="医疗（申报个税用）" sortable="custom" :min-width="getStringWidth('医疗（申报个税用）')" header-align="left" align="right" prop="MedicalTaxDeclaration">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'medicalTaxDeclaration')">{{ getDecimalValueOrDefault(row.medicalTaxDeclaration) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="失业（申报个税用）" sortable="custom" :min-width="getStringWidth('失业（申报个税用）')" header-align="left" align="right" prop="UnemploymentTaxDeclaration">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'unemploymentTaxDeclaration')">{{ getDecimalValueOrDefault(row.unemploymentTaxDeclaration) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="公扣（申报个税用）" sortable="custom" :min-width="getStringWidth('公扣（申报个税用）')" header-align="left" align="right" prop="PublicTaxDeclaration">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'publicTaxDeclaration')">{{ getDecimalValueOrDefault(row.publicTaxDeclaration) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="年金（申报个税用）" sortable="custom" :min-width="getStringWidth('年金（申报个税用）')" header-align="left" align="right" prop="OccupationalAnnuityTaxDeclaration">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'occupationalAnnuityTaxDeclaration')">{{ getDecimalValueOrDefault(row.occupationalAnnuityTaxDeclaration) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="申报个税四金合计" sortable="custom" :min-width="getStringWidth('申报个税四金合计')" header-align="left" align="right" prop="TaxDeclarationFourGold">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'taxDeclarationFourGold')">{{ getDecimalValueOrDefault(row.taxDeclarationFourGold) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="基本工资合计" sortable="custom" :min-width="getStringWidth('基本工资合计')" header-align="left" align="right" prop="BasicSalaryTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'basicSalaryTotal')">{{ getDecimalValueOrDefault(row.basicSalaryTotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="津贴补贴合计" sortable="custom" :min-width="getStringWidth('津贴补贴合计')" header-align="left" align="right" prop="AllowanceTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'allowanceTotal')">{{ getDecimalValueOrDefault(row.allowanceTotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="对个人及家庭补助" sortable="custom" :min-width="getStringWidth('对个人及家庭补助')" header-align="left" align="right" prop="OnlyChildTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'onlyChildTotal')">{{ getDecimalValueOrDefault(row.onlyChildTotal) }}</span>
            </template>
          </el-table-column>
          <!--<el-table-column label="加班工资" sortable="custom" :min-width="getStringWidth('加班工资')" header-align="left" align="right" prop="OvertimePay">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'overtimePay')">{{ getDecimalValueOrDefault(row.overtimePay) }}</span>
            </template>
          </el-table-column>-->
          <el-table-column label="奖金合计" sortable="custom" :min-width="getStringWidth('奖金合计')" header-align="left" align="right" prop="BonusTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'bonusTotal')">{{ getDecimalValueOrDefault(row.bonusTotal) }}</span>
            </template>
          </el-table-column>
          <!--<el-table-column label="十三月工资（应发）" sortable="custom" :min-width="getStringWidth('十三月工资（应发）')" header-align="left" align="right" prop="ThirteenthMonthSalary">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'thirteenthMonthSalary')">{{ getDecimalValueOrDefault(row.thirteenthMonthSalary) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="计税工资2（12月计税工资与十三月工资应发之和）" sortable="custom" :min-width="getStringWidth('计税工资2（12月计税工资与十三月工资应发之和）')" header-align="left" align="right" prop="TaxableWage2">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'taxableWage2')">{{ getDecimalValueOrDefault(row.taxableWage2) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="代扣税金2（以计税工资2计算出的代扣税金）" sortable="custom" :min-width="getStringWidth('代扣税金2（以计税工资2计算出的代扣税金）')" header-align="left" align="right" prop="TaxWithholding2">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'taxWithholding2')">{{ getDecimalValueOrDefault(row.taxWithholding2) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="十三月工资代扣税金" sortable="custom" :min-width="getStringWidth('十三月工资代扣税金')" header-align="left" align="right" prop="ThirteenthMonthTaxWithholding">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'thirteenthMonthTaxWithholding')">{{ getDecimalValueOrDefault(row.thirteenthMonthTaxWithholding) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="十三月工资（实发）" sortable="custom" :min-width="getStringWidth('十三月工资（实发）')" header-align="left" align="right" prop="ThirteenthMonthSalaryNet">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'thirteenthMonthSalaryNet')">{{ getDecimalValueOrDefault(row.thirteenthMonthSalaryNet) }}</span>
            </template>
          </el-table-column>-->
          <el-table-column label="年终一次性奖励" sortable="custom" :min-width="getStringWidth('年终一次性奖励')" header-align="left" align="right" prop="YearEndBonus">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'yearEndBonus')">{{ getDecimalValueOrDefault(row.yearEndBonus) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="其他各项奖金" sortable="custom" :min-width="getStringWidth('其他各项奖金')" header-align="left" align="right" prop="OtherBonusesTotal">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'otherBonusesTotal')">{{ getDecimalValueOrDefault(row.otherBonusesTotal) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实发奖金" sortable="custom" :min-width="getStringWidth('实发奖金')" header-align="left" align="right" prop="ActualBonus">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'actualBonus')">{{ getDecimalValueOrDefault(row.actualBonus) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="税前奖金" sortable="custom" :min-width="getStringWidth('税前奖金')" header-align="left" align="right" prop="PreTaxBonus">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'preTaxBonus')">{{ getDecimalValueOrDefault(row.preTaxBonus) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="奖金扣税" sortable="custom" :min-width="getStringWidth('奖金扣税')" header-align="left" align="right" prop="BonusTax">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'bonusTax')">{{ getDecimalValueOrDefault(row.bonusTax) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="累计收入额" sortable="custom" :min-width="getStringWidth('累计收入额')" header-align="left" align="right" prop="AccumulatedIncome">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'accumulatedIncome')">{{ getDecimalValueOrDefault(row.accumulatedIncome) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="累计专项扣除" sortable="custom" :min-width="getStringWidth('累计专项扣除')" header-align="left" align="right" prop="AccumulatedDeduction">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'accumulatedDeduction')">{{ getDecimalValueOrDefault(row.accumulatedDeduction) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="累计专项附加扣除" sortable="custom" :min-width="getStringWidth('累计专项附加扣除')" header-align="left" align="right" prop="AccumulatedAdditionalDeduction">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'accumulatedAdditionalDeduction')">{{ getDecimalValueOrDefault(row.accumulatedAdditionalDeduction) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="累计应纳税所得额" sortable="custom" :min-width="getStringWidth('累计应纳税所得额')" header-align="left" align="right" prop="AccumulatedTaxableIncome">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'accumulatedTaxableIncome')">{{ getDecimalValueOrDefault(row.accumulatedTaxableIncome) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="累计已预扣税额" sortable="custom" :min-width="getStringWidth('累计已预扣税额')" header-align="left" align="right" prop="AccumulatedWithheldTax">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'accumulatedWithheldTax')">{{ getDecimalValueOrDefault(row.accumulatedWithheldTax) }}</span>
            </template>
          </el-table-column>
          <!--<el-table-column label="待个人汇算时由税务退还个税" sortable="custom" :min-width="getStringWidth('待个人汇算时由税务退还个税')" header-align="left" align="right" prop="TaxToBeReturnedUponPersonalSettlementByTaxAuthority">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'taxToBeReturnedUponPersonalSettlementByTaxAuthority')">{{ getDecimalValueOrDefault(row.taxToBeReturnedUponPersonalSettlementByTaxAuthority) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="计税工资" sortable="custom" :min-width="getStringWidth('计税工资')" header-align="left" align="right" prop="TaxableWage">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'taxableWage')">{{ getDecimalValueOrDefault(row.taxableWage) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="其他工资" sortable="custom" :min-width="getStringWidth('其他工资')" header-align="left" align="right" prop="OtherWages">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'otherWages')">{{ getDecimalValueOrDefault(row.otherWages) }}</span>
            </template>
          </el-table-column>-->
          <el-table-column label="计税" sortable="custom" :min-width="getStringWidth('计税')" header-align="left" align="right" prop="TaxCalculation">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'taxCalculation')">{{ row.taxCalculation }}</span>
            </template>
          </el-table-column>
          <!--<el-table-column label="免税" sortable="custom" :min-width="getStringWidth('免税')" align="center" prop="TaxFree">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'taxFree')">{{ row.taxFree ? "有" : "无" }}</span>
            </template>
          </el-table-column>-->
          <el-table-column label="应发总计" sortable="custom" :min-width="getStringWidth('应发总计')" header-align="left" align="right" prop="TotalPayable">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'totalPayable')">{{ getDecimalValueOrDefault(row.totalPayable) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="工行帐号" sortable="custom" :min-width="getStringWidth('工行帐号')" align="left" prop="ICBCAccount">
            <template slot-scope="{ row, $index }">
              <span :style="getStyle(row,pageList[$index - 1],'iCBCAccount')">{{ row.iCBCAccount }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" label="操作" align="left" header-align="center" width="150" class-name="small-padding fixed-width">
            <template slot-scope="{ row }">
              <el-button v-if="isEdit" type="primary" style="margin-left:35px !important; padding-left: 5px !important;" icon="el-icon-edit" size="mini" @click="editEmployeeSalaryDetailDialog(row,true)">
                编辑
              </el-button>
              <el-button v-if="!isEdit" type="primary" style="margin-left:35px !important; padding-left: 5px !important;" icon="el-icon-view" size="mini" @click="editEmployeeSalaryDetailDialog(row,false)">
                显示
              </el-button>
            </template>
          </el-table-column>
          <employeeTableColumns />
        </el-table>
        <c-pagination v-show="listQuery.total > 0" :total="listQuery.total" :page-sizes="[10, 20,50]" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getPageList" />
      </template>
    </layout4>

    <editDialog ref="editDialog" @refreshData="getPageList" />
    <employeeSalaryRecordDialog ref="employeeSalaryRecordDialog" />
    <socialSecurityWithhold ref="socialSecurityWithhold" />
  </div>
</template>

<script>
import salaryApi from '@/api/salary'
import sysManageApi from '@/api/sysManage'
import editDialog from '../components/editPage'
import employeeSalaryRecordDialog from '../components/employeeSalaryRecord.vue'
import socialSecurityWithhold from '../components/socialSecurityWithhold.vue'
import employeeTableColumns from '@/views/salary/monthSalary/components/hrSalary/employeeTableColumns'

export default {
  components: {
    editDialog,
    employeeSalaryRecordDialog,
    employeeTableColumns,
    socialSecurityWithhold
  },
  data() {
    return {
      isSalaryMonthComplete: true,
      showDialog: false,
      isEdit: false,
      title: '财务数据',
      pageList: [],
      groupList: [],
      salaryData: {},
      lastMonthData: {},
      salaryId: '',
      listQuery: {
        queryCondition: {},
        salaryId: '',
        total: 1,
        pageIndex: 1,
        pageSize: 10
      },
      searchList: [
        { id: '' }
      ],
      lastMonth: false,
      listLoading: false,
      dialogEditEmpSalaryVisible: false,
      dataColumns: [
      {
        value: '1',
        label: '唯一码',
        type: 'System.String',
        columnName: 'Uid'
      },
      {
        value: '2',
        label: '工号',
        type: 'System.String',
        columnName: 'EmpCode'
      },
      {
        value: '3',
        label: '姓名',
        type: 'System.String',
        columnName: 'DisplayName'
      },
      {
        value: '4',
        label: '证件号',
        type: 'System.String',
        columnName: 'IdentityNumber'
      },
      {
        value: '5',
        label: '手机号',
        type: 'System.String',
        columnName: 'Mobile'
      },
      {
        value: '6',
        label: '在职方式',
        type: 'System.String',
        columnName: 'HireStyle'
      },
      {
        value: '7',
        label: '受雇年份',
        type: 'System.String',
        columnName: 'EmployYear'
      },
      {
        value: '8',
        label: '受雇月份',
        type: 'System.String',
        columnName: 'EmployMonth'
      },
      {
        value: '9',
        label: '本年度受雇月数',
        type: 'System.String',
        columnName: 'EmployMonthNumber'
      },
      {
        value: '10',
        label: '四金不够扣标记',
        type: 'System.String',
        columnName: 'FourGoldDeficiencySign'
      },
      {
        value: '11',
        label: '社保基数',
        type: 'System.Decimal',
        columnName: 'SocialSecurityBase'
      },
      {
        value: '12',
        label: '公积金基数',
        type: 'System.Decimal',
        columnName: 'HousingFundBase'
      },
      {
        value: '13',
        label: '岗位级别',
        type: 'System.String',
        columnName: 'StationLevel'
      },
      {
        value: '14',
        label: '薪级',
        type: 'System.String',
        columnName: 'SalaryScale'
      },
      {
        value: '15',
        label: '工龄',
        type: 'System.Int32',
        columnName: 'SocietyAge'
      },
      {
        value: '16',
        label: '工龄段',
        type: 'System.String',
        columnName: 'SeniorityRange'
      },
      {
        value: '17',
        label: '补充公积金标记',
        type: 'System.Boolean',
        columnName: 'SupplementaryHousingFundFlag'
      },
      {
        value: '18',
        label: '职员年金标记',
        type: 'System.Boolean',
        columnName: 'EmployeePensionFlag'
      },
      {
        value: '19',
        label: '病产假天数',
        type: 'System.Decimal',
        columnName: 'SickLeaveDays'
      },
      {
        value: '20',
        label: '休假类型',
        type: 'System.String',
        columnName: 'SalaryLeaveDesc'
      },
      {
        value: '21',
        label: '基本工资小计',
        type: 'System.Decimal',
        columnName: 'BasicSalarySubtotal'
      },
      {
        value: '22',
        label: '岗资基数',
        type: 'System.Decimal',
        columnName: 'StationWageBase'
      },
      {
        value: '23',
        label: '岗资',
        type: 'System.Decimal',
        columnName: 'StationWage'
      },
      {
        value: '24',
        label: '薪资基数',
        type: 'System.Decimal',
        columnName: 'SalaryBase'
      },
      {
        value: '25',
        label: '薪资',
        type: 'System.Decimal',
        columnName: 'SalaryMoney'
      },
      {
        value: '26',
        label: '基本工资其它加',
        type: 'System.Decimal',
        columnName: 'BasicSalaryOtherAdd'
      },
      {
        value: '27',
        label: '津补贴小计',
        type: 'System.Decimal',
        columnName: 'AllowanceSubtotal'
      },
      {
        value: '28',
        label: '粮油补贴',
        type: 'System.Decimal',
        columnName: 'GrainOilSubsidy'
      },
      {
        value: '29',
        label: '上下班交通费基数',
        type: 'System.Decimal',
        columnName: 'CommuteSubsidyBase'
      },
      {
        value: '30',
        label: '上下班交通费',
        type: 'System.Decimal',
        columnName: 'CommuteSubsidy'
      },
      {
        value: '31',
        label: '护龄基数',
        type: 'System.Decimal',
        columnName: 'NursingBase'
      },
      {
        value: '32',
        label: '护龄',
        type: 'System.Decimal',
        columnName: 'Nursing'
      },
      {
        value: '33',
        label: '独子',
        type: 'System.Decimal',
        columnName: 'OnlyChild'
      },
      {
        value: '34',
        label: '独子补发',
        type: 'System.Decimal',
        columnName: 'BackPayOnlyChild'
      },
      {
        value: '35',
        label: '独子合计',
        type: 'System.Decimal',
        columnName: 'OnlyChildTotal'
      },
      {
        value: '36',
        label: '援外津贴',
        type: 'System.Decimal',
        columnName: 'ForeignAidAllowance'
      },
      {
        value: '37',
        label: '津补贴其它加',
        type: 'System.Decimal',
        columnName: 'AllowanceOtherAdd'
      },
      {
        value: '38',
        label: '绩效小计',
        type: 'System.Decimal',
        columnName: 'PerformanceSubtotal'
      },
      {
        value: '39',
        label: '岗位津贴基数',
        type: 'System.Decimal',
        columnName: 'StationAllowanceBase'
      },
      {
        value: '40',
        label: '岗位津贴',
        type: 'System.Decimal',
        columnName: 'StationAllowance'
      },
      {
        value: '41',
        label: '工作量津贴',
        type: 'System.Decimal',
        columnName: 'WorkloadAllowance1'
      },
      {
        value: '42',
        label: '工作量津贴合计',
        type: 'System.Decimal',
        columnName: 'WorkloadAllowance2Subtotal'
      },
      {
        value: '43',
        label: '停车补贴',
        type: 'System.Decimal',
        columnName: 'ParkingSubsidy'
      },
      {
        value: '44',
        label: '公派车贴补贴',
        type: 'System.Decimal',
        columnName: 'OfficialCarAllowance'
      },
      {
        value: '45',
        label: '电话费',
        type: 'System.Decimal',
        columnName: 'TelephoneFee'
      },
      {
        value: '46',
        label: '电话费合计',
        type: 'System.Decimal',
        columnName: 'TelephoneFeeTotal'
      },
      {
        value: '47',
        label: '长病假职工最低工资补助',
        type: 'System.Decimal',
        columnName: 'LongSickLeaveMinimumWageSubsidy'
      },
      {
        value: '48',
        label: '博士后房帖',
        type: 'System.Decimal',
        columnName: 'PostdoctoralHousingAllowance'
      },
      {
        value: '49',
        label: '卫生津贴',
        type: 'System.Decimal',
        columnName: 'HealthAllowance'
      },
      {
        value: '50',
        label: '零星补节假日加班绩效',
        type: 'System.Decimal',
        columnName: 'OccasionalHolidayOvertimePerformance'
      },
      {
        value: '51',
        label: '节假日加班绩效',
        type: 'System.Decimal',
        columnName: 'HolidayOvertimePerformance'
      },
      {
        value: '52',
        label: '中夜班绩效',
        type: 'System.Decimal',
        columnName: 'MidnightShiftPerformance'
      },
      {
        value: '53',
        label: '行政值班绩效',
        type: 'System.Decimal',
        columnName: 'AdministrativeDutyPerformance'
      },
      {
        value: '54',
        label: '一二值班绩效',
        type: 'System.Decimal',
        columnName: 'FirstSecondDutyPerformance'
      },
      {
        value: '55',
        label: '其它加',
        type: 'System.Decimal',
        columnName: 'OtherAdd'
      },
      {
        value: '56',
        label: '行政聘任补发',
        type: 'System.Decimal',
        columnName: 'AdministrativeAppointmentReissue'
      },
      {
        value: '57',
        label: '退还多扣养扣',
        type: 'System.Decimal',
        columnName: 'ReturnOverDeductPension'
      },
      {
        value: '58',
        label: '退还多扣医疗',
        type: 'System.Decimal',
        columnName: 'ReturnOverDeductMedical'
      },
      {
        value: '59',
        label: '退还多扣失业',
        type: 'System.Decimal',
        columnName: 'ReturnOverDeductUnemployment'
      },
      {
        value: '60',
        label: '退还多扣公扣',
        type: 'System.Decimal',
        columnName: 'ReturnOverDeductPublic'
      },
      {
        value: '61',
        label: '退还多扣补充公积金',
        type: 'System.Decimal',
        columnName: 'ReturnOverDeductSupplementaryHousingFund'
      },
      {
        value: '62',
        label: '退还多扣年金',
        type: 'System.Decimal',
        columnName: 'ReturnOverDeductOccupationalAnnuity'
      },
      {
          value: '63',
          label: '退还多扣出国人员扣款',
          type: 'System.Decimal',
          columnName: 'ReturnOverDeductDeductionForForeigners'
      },
      {
          value: '64',
          label: '退还多扣病假工资',
          type: 'System.Decimal',
          columnName: 'ReturnOverDeductSickLeaveSalary'
      },
      {
          value: '65',
          label: '定级补发',
          type: 'System.Decimal',
          columnName: 'BackPayGrading'
      },
      {
          value: '66',
          label: '应发工资',
          type: 'System.Decimal',
          columnName: 'GrossSalary'
      },
      {
          value: '67',
          label: '应发工资（申报个税用）',
          type: 'System.Decimal',
          columnName: 'GrossSalaryForTaxDeclaration'
      },
      {
          value: '68',
          label: '养扣',
          type: 'System.Decimal',
          columnName: 'PensionDeduction'
      },
      {
          value: '69',
          label: '补扣养扣',
          type: 'System.Decimal',
          columnName: 'BackDeductionPension'
      },
      {
          value: '70',
          label: '养扣合计',
          type: 'System.Decimal',
          columnName: 'PensionDeductionTotal'
      },
      {
          value: '71',
          label: '医疗保险',
          type: 'System.Decimal',
          columnName: 'MedicalInsurance'
      },
      {
          value: '72',
          label: '补扣医疗保险',
          type: 'System.Decimal',
          columnName: 'BackDeductionMedicalInsurance'
      },
      {
          value: '73',
          label: '医疗保险合计',
          type: 'System.Decimal',
          columnName: 'MedicalInsuranceTotal'
      },
      {
          value: '74',
          label: '失业保险',
          type: 'System.Decimal',
          columnName: 'UnemploymentInsurance'
      },
      {
          value: '75',
          label: '补扣失业保险',
          type: 'System.Decimal',
          columnName: 'BackDeductionUnemploymentInsurance'
      },
      {
          value: '76',
          label: '失业保险合计',
          type: 'System.Decimal',
          columnName: 'UnemploymentInsuranceTotal'
      },
      {
          value: '77',
          label: '基本公积金',
          type: 'System.Decimal',
          columnName: 'BasicHousingFund'
      },
      {
          value: '78',
          label: '补扣基本公积金',
          type: 'System.Decimal',
          columnName: 'BackDeductionBasicHousingFund'
      },
      {
          value: '79',
          label: '基本公积金合计',
          type: 'System.Decimal',
          columnName: 'HousingFundTotal'
      },
      {
          value: '80',
          label: '补充公积金',
          type: 'System.Decimal',
          columnName: 'SupplementaryHousingFund'
      },
      {
          value: '81',
          label: '补扣补充公积金',
          type: 'System.Decimal',
          columnName: 'BackDeductionSupplementaryHousingFund'
      },
      {
          value: '82',
          label: '补充公积金合计',
          type: 'System.Decimal',
          columnName: 'SupplementaryHousingFundTotal'
      },
      {
          value: '83',
          label: '职业年金',
          type: 'System.Decimal',
          columnName: 'OccupationalAnnuity'
      },
      {
          value: '84',
          label: '补扣职业年金',
          type: 'System.Decimal',
          columnName: 'BackDeductionOccupationalAnnuity'
      },
      {
          value: '85',
          label: '职业年金合计',
          type: 'System.Decimal',
          columnName: 'OccupationalAnnuityTotal'
      },
      {
          value: '86',
          label: '会费',
          type: 'System.Decimal',
          columnName: 'MembershipFee'
      },
      {
          value: '87',
          label: '补扣会费',
          type: 'System.Decimal',
          columnName: 'BackDeductionMembershipFee'
      },
      {
          value: '88',
          label: '会费合计',
          type: 'System.Decimal',
          columnName: 'MembershipFeeTotal'
      },
      {
          value: '89',
          label: '病假工资',
          type: 'System.Decimal',
          columnName: 'SickLeaveSalary'
      },
      {
          value: '90',
          label: '补扣病假',
          type: 'System.Decimal',
          columnName: 'BackDeductionSickLeave'
      },
      {
          value: '91',
          label: '病假工资合计',
          type: 'System.Decimal',
          columnName: 'SickLeaveSalaryTotal'
      },
      {
          value: '92',
          label: '代扣税金',
          type: 'System.Decimal',
          columnName: 'TaxWithholding'
      },
      {
          value: '93',
          label: '其它扣',
          type: 'System.Decimal',
          columnName: 'OtherDeduction'
      },
      {
          value: '94',
          label: '长病假补扣',
          type: 'System.Decimal',
          columnName: 'BackDeductionLongSickLeave'
      },
      {
          value: '95',
          label: '扣款合计',
          type: 'System.Decimal',
          columnName: 'DeductionTotal'
      },
      {
          value: '96',
          label: '实发工资',
          type: 'System.Decimal',
          columnName: 'NetSalary'
      },
      {
          value: '97',
          label: '养扣基础数据',
          type: 'System.Decimal',
          columnName: 'BasicDataDeduction'
      },
      {
          value: '98',
          label: '医疗保险基础数据',
          type: 'System.Decimal',
          columnName: 'MedicalInsuranceBasicData'
      },
      {
          value: '99',
          label: '失业保险基础数据',
          type: 'System.Decimal',
          columnName: 'UnemploymentInsuranceBasicData'
      },
      {
          value: '100',
          label: '公积金基础数据',
          type: 'System.Decimal',
          columnName: 'HousingFundBasicData'
      },
      {
          value: '101',
          label: '补充公积金基础数据',
          type: 'System.Decimal',
          columnName: 'SupplementaryHousingFundBasicData'
      },
      {
          value: '102',
          label: '年金基础数据',
          type: 'System.Decimal',
          columnName: 'PensionBasicData'
      },
      {
          value: '103',
          label: '养老（申报个税用）',
          type: 'System.Decimal',
          columnName: 'PensionTaxDeclaration'
      },
      {
          value: '104',
          label: '医疗（申报个税用）',
          type: 'System.Decimal',
          columnName: 'MedicalTaxDeclaration'
      },
      {
          value: '105',
          label: '失业（申报个税用）',
          type: 'System.Decimal',
          columnName: 'UnemploymentTaxDeclaration'
      },
      {
          value: '106',
          label: '公扣（申报个税用）',
          type: 'System.Decimal',
          columnName: 'PublicTaxDeclaration'
      },
      {
          value: '107',
          label: '年金（申报个税用）',
          type: 'System.Decimal',
          columnName: 'OccupationalAnnuityTaxDeclaration'
      },
      {
          value: '108',
          label: '申报个税四金合计',
          type: 'System.Decimal',
          columnName: 'TaxDeclarationFourGold'
      },
      {
          value: '109',
          label: '基本工资合计',
          type: 'System.Decimal',
          columnName: 'BasicSalaryTotal'
      },
      {
          value: '110',
          label: '津贴补贴合计',
          type: 'System.Decimal',
          columnName: 'AllowanceTotal'
      },
      {
          value: '111',
          label: '加班工资',
          type: 'System.Decimal',
          columnName: 'OvertimePay'
      },
      {
          value: '112',
          label: '奖金合计',
          type: 'System.Decimal',
          columnName: 'BonusTotal'
      },
      {
          value: '113',
          label: '年终一次性奖励',
          type: 'System.Decimal',
          columnName: 'YearEndBonus'
      },
      {
          value: '114',
          label: '其他各项奖金',
          type: 'System.Decimal',
          columnName: 'OtherBonusesTotal'
      },
      {
          value: '115',
          label: '实发奖金',
          type: 'System.Decimal',
          columnName: 'ActualBonus'
      },
      {
          value: '116',
          label: '税前奖金',
          type: 'System.Decimal',
          columnName: 'PreTaxBonus'
      },
      {
          value: '117',
          label: '奖金扣税',
          type: 'System.Decimal',
          columnName: 'BonusTax'
      },
      {
          value: '118',
          label: '累计收入额',
          type: 'System.Decimal',
          columnName: 'AccumulatedIncome'
      },
      {
          value: '119',
          label: '累计专项扣除',
          type: 'System.Decimal',
          columnName: 'AccumulatedDeduction'
      },
      {
          value: '120',
          label: '累计专项附加扣除',
          type: 'System.Decimal',
          columnName: 'AccumulatedAdditionalDeduction'
      },
      {
          value: '121',
          label: '累计应纳税所得额',
          type: 'System.Decimal',
          columnName: 'AccumulatedTaxableIncome'
      },
      {
          value: '122',
          label: '累计已预扣税额',
          type: 'System.Decimal',
          columnName: 'AccumulatedWithheldTax'
      },
      {
          value: '123',
          label: '应发总计',
          type: 'System.Decimal',
          columnName: 'TotalPayable'
      },
      {
          value: '124',
          label: '工行帐号',
          type: 'System.String',
          columnName: 'ICBCAccount'
      }
      ],
      selectConditionOptions: [],
      signDropdown: [
        {
          value: true,
          label: '有'
        }, {
          value: false,
          label: '无'
        }
      ]
    }
  },
  created() {
    this.initDialog()
  },
  mounted() {
  },
  methods: {
    initDialog() {
      this.salaryId = this.$route.query.salaryId
      if (this.salaryId) {
        this.showDialog = true
        this.listQuery.salaryId = this.salaryId
        this.getSalary()
        this.getPageList()
        this.loadConditions()
      }
      // else {
      //   this.$notice.message('系统错误，请刷新页面重试或联系管理员', 'error')
      // }
    },
    employeeSalaryRecord() {
      this.$refs.employeeSalaryRecordDialog.initDialog(this.salaryId)
    },
    loadConditions() {
      sysManageApi.getEnumInfos({ enumType: 'Operations' }).then(result => {
        this.selectConditionOptions = result.data.datas
      }).catch(error => {
        console.log(error)
      })
    },
    getSalary() {
      salaryApi.getSalary({ id: this.listQuery.salaryId }).then(result => {
        if (result.succeed) {
          this.salaryData = result.data
          if (this.salaryData.enumStatus === 2) {
            this.isEdit = true
          } else {
            this.isEdit = false
          }
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    closeDialog() {
      this.pageList = []
      this.showDialog = false
    },
    search() {
      this.listQuery.pageIndex = 1
      this.getPageList()
      this.clearCheckboxes()
    },
    sortChange(c) {
      if (c.order === 'ascending') {
        this.listQuery.order = c.prop + ' ' + 'asc'
      } else if (c.order === 'descending') {
        this.listQuery.order = c.prop + ' ' + 'desc'
      } else {
        this.$delete(this.listQuery, 'order')
      }
      this.search()
    },
    getPageList() {
      this.searchList.length = 0
      this.pageList = []
      this.listLoading = true
      if (this.listQuery.entityColumn && this.listQuery.entityColumn !== '' && this.listQuery.queryCondition.EnumOperation) {
        if (!this.listQuery.queryCondition.Keywords) {
          this.listQuery.queryCondition.Keywords = ''
        }
        this.listQuery.queryCondition.EntityColumnName = this.listQuery.entityColumn.columnName
        this.listQuery.queryCondition.EntityColumnType = this.listQuery.entityColumn.type
        this.listQuery.queryCondition.EnumLogicRelationship = 10
        this.listQuery.ConditionList = [this.listQuery.queryCondition]
      } else {
        this.listQuery.ConditionList = []
        this.listQuery.queryCondition = {}
      }
      // this.listQuery.enumSalaryStatusType = 1
      salaryApi.querySalaryDetail(this.listQuery).then(result => {
        if (result.succeed) {
          this.pageList = result.data.datas
          this.listQuery.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
          console.log(this.pageList)
          // 获取动态列
          /*
          this.groupList = []
          this.pageList.forEach(item => {
            if (item.universalReissueDeduction.length > 0) {
              item.universalReissueDeduction.forEach(deduction => {
                const groupModel = this.groupList.find(d => d.id === deduction.groupId)
                if (groupModel == null) {
                  this.groupList.push(deduction.groupModel)
                }
              })
            }
          })
          */
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      }).finally(() => {
        this.listLoading = false
      })
    },
    clearCheckboxes() {
      this.pageList.forEach((row) => {
        if (!row.id.includes('_lastMonth')) {
          const checkbox = document.getElementById('choose')
          if (checkbox) {
            checkbox.checked = false
          }
        }
      })
    },
    sarechLastMonthData(row) {
      if (!this.searchList.includes(row.id)) {
        salaryApi.queryLastMonthEmployeeSalary({ id: row.id }).then(result => {
          if (result.succeed) {
            this.lastMonthData = result.data
            this.searchList.push(row.id)
            const newRow = { ...row }
            Object.assign(newRow, this.lastMonthData)
            newRow.id = row.id + '_lastMonth'
            const index = this.pageList.indexOf(row)
            this.pageList.splice(index + 1, 0, newRow)
          } else {
            this.$notice.resultTip(result)
          }
        }).catch(error => {
          console.log(error)
        })
      } else {
        this.searchList.pop(row.id)
        const addedRowIndex = this.pageList.findIndex(item => item.id.includes('_lastMonth') && item.id.split('_lastMonth')[0] === row.id)
        if (addedRowIndex !== -1) {
          this.pageList.splice(addedRowIndex, 1)
        }
      }
    },
    editEmployeeSalaryDetailDialog(row, isEdit) {
      this.$refs.editDialog.salaryId = this.salaryId
      this.$refs.editDialog.initDialog(row, isEdit)
    },
    onRefresh() {
      if (this.isEdit) {
        this.getPageList()
      }
      this.dialogEditEmpSalaryVisible = false
    },
    exportData() {
      this.searchList.length = 0
      this.pageList = []
      if (this.listQuery.entityColumn && this.listQuery.entityColumn !== '' && this.listQuery.queryCondition.EnumOperation) {
        if (!this.listQuery.queryCondition.Keywords) {
          this.listQuery.queryCondition.Keywords = ''
        }
        this.listQuery.queryCondition.EntityColumnName = this.listQuery.entityColumn.columnName
        this.listQuery.queryCondition.EntityColumnType = this.listQuery.entityColumn.type
        this.listQuery.queryCondition.EnumLogicRelationship = 10
        this.listQuery.ConditionList = [this.listQuery.queryCondition]
      } else {
        this.listQuery.ConditionList = []
        this.listQuery.queryCondition = {}
      }
      salaryApi.exportSalaryDetail(this.listQuery).then(res => {
        const fileDownload = require('js-file-download')
        var filename = '员工薪资' + this.$moment().format('YYYYMMDDHHmmss') + '.xlsx'
        if (res.data) {
          fileDownload(res.data, filename)
        } else {
          fileDownload(res, filename)
        }
        this.getPageList()
      })
    },
    // 导入
    importExcel(params) {
      const file = params.file
      const formData = new FormData()
      salaryApi.importSalaryDetail(file, formData, this.listQuery.salaryId).then(res => {
        if (res.succeed) {
          this.$message({ message: '导入成功', type: 'success' })
          this.search()
        }
      }).catch(res => {
        this.search()
      })
    },
    exportHRData() {
      this.searchList.length = 0
      this.pageList = []
      salaryApi.exportHRData(this.listQuery).then(res => {
        const fileDownload = require('js-file-download')
        var filename = '人事数据' + this.$moment().format('YYYYMMDDHHmmss') + '.xlsx'
        if (res.data) {
          fileDownload(res.data, filename)
        } else {
          fileDownload(res, filename)
        }
        this.getPageList()
      })
    },
    getReissueDeduction(deductions, groupId) {
      const deduction = deductions.find(d => d.groupId === groupId)
      return deduction ? deduction.reissueDeduction : ''
    },
    getDecimalValueOrDefault(value) {
      if (value === 0 || value === undefined) {
        return ''
      } else if (value < 0) {
        return Math.abs(value).toFixed(2)
      } else {
        return value.toFixed(2)
      }
    },
    getStyle(lastMonthValue, currentValue, columnName) {
        // 使用方括号语法获取对象属性值
       if (lastMonthValue !== undefined && currentValue !== undefined) {
        const lastMonthFieldValue = lastMonthValue[columnName]
        const currentFieldValue = currentValue[columnName]
        const lastMonthUidValue = lastMonthValue.uid
        const currentUidValue = currentValue.uid

        if (lastMonthUidValue === currentUidValue && lastMonthFieldValue !== undefined && currentFieldValue !== undefined) {
        if (lastMonthFieldValue !== currentFieldValue) {
          return { color: 'red' }// 如果上月值和当前值不同，标红
        }
       }
      }
    },
    getStringWidth(label) {
      let width = 0
      if (label.length < 3) {
        return 'auto'
      } else if (label.length === 3) {
        width = label.length * 30
        return width + 'px'
      } else if (label.length === 4) {
        width = label.length * 25
        return width + 'px'
      } else if (label.length === 5) {
        width = label.length * 25
        return width + 'px'
      } else if (label.length > 5) {
        width = label.length * 20
        return width + 'px'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/* 只影响特定的 el-table 组件 */
::v-deep .my-table .el-table__fixed {
  height: calc(100% - 15px) !important;
}

::v-deep .my-table .el-table__fixed:before {
  height: 0px;
}

::v-deep .my-table .el-table__fixed-right {
  height: calc(100% - 15px) !important;
}
</style>
