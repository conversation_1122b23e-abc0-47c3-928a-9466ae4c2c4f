﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Renji.JHR.Entities
{
    public enum RetireEmpType
    {
        [Description("未知")]
        None = 0,

        /// <summary>
        /// 男性（60岁）
        /// </summary>
        [Description("男性（60岁）")]
        Male = 1,

        /// <summary>
        /// 女性（55岁）
        /// </summary>
        [Description("女性（55岁）")]
        Female_55 = 2,

        /// <summary>
        /// 女性（50岁）
        /// </summary>
        [Description("女性（50岁）")]
        Female_50 = 3
    }
}
