﻿using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.EntityFrameworkCore.Internal;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.EntityFrameworkCore;
using System.Linq;
using System.Transactions;
using System.Data;
using System.Reflection;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace Renji.JHR.Bll
{
    public class NoticeBll : BaseBll
    {
        #region Constructs

        public NoticeBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public NoticeBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public NoticeBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public NoticeBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        public Department? GetCurrentDept()
        {
            Employee? emp = null;

            if (this.OperatorUser != null)
            {
                emp = this.GetEntity<Employee>(path: "Department", predicate: s => s.Uid == this.OperatorUser.Uid && s.EmpCode == this.OperatorUser.EmpCode, includeDeleted: true);
            }

            return emp?.Department;
        }

        public User? GetUserByUserName(string? username)
        {
            return this.GetEntity<User>(predicate: s => s.Username == username);
        }

        #region 公告发布

        public BizResult SaveMsgCompany(MsgCompany entity)
        {
            var result = new BizResult();
            if (entity.Title.IsEmpty())
            {
                result.Error("标题是必填项!");
            }

            if (result.Succeed)
            {
                if (entity.ID == default)
                {
                    entity.ID = CombGuid.NewGuid();
                    this.Add(entity);
                }
                else
                {
                    this.Update(entity);
                }
            }

            return result;
        }

        public BizResult DeleteMsgCompany(MsgCompany entity)
        {
            var result = new BizResult();
            var e = this.Find<MsgCompany>(entity.ID);
            if (e == null)
            {
                result.Error("未找到记录");
            }

            if (result.Succeed)
            {
                e = e.Value();

                this.Delete(e);
            }
            return result;
        }

        #endregion 公告发布

        #region 消息发布

        public BizResult DeleteMsgPerson(MsgPerson entity)
        {
            var result = new BizResult();
            var e = this.Find<MsgPerson>(entity.ID);
            if (e == null)
            {
                result.Error("未找到记录");
            }

            if (result.Succeed)
            {
                e = e.Value();

                // this.DbContext.Database.ExecuteSqlInterpolated
                foreach (var item in e.MsgReadInfo)
                {
                    this.Delete(item, false);
                }
                this.Delete(e);
            }
            return result;
        }

        public BizResult SaveMsgPerson(MsgPerson entity)
        {
            var result = new BizResult();
            if (entity.Title.IsEmpty())
            {
                result.Error("标题是必填项!");
            }
            if (!entity.MsgReadInfo.Any())
            {
                result.Error("未选择员工!");
            }

            if (result.Succeed)
            {
                if (entity.ID != default)
                {
                    var e = this.Find<MsgPerson>(entity.ID);
                    if (result.Succeed)
                    {
                        e = e.Value();

                        foreach (var item in e.MsgReadInfo)
                        {
                            this.Delete(item, false);
                        }
                        this.Delete(e);
                    }
                }
                entity.ID = CombGuid.NewGuid();
                foreach (var item in entity.MsgReadInfo)
                {
                    item.ID = CombGuid.NewGuid();
                    item.MsgPersonId = entity.ID;
                    item.ReadFlag = 0;
                    item.PdateTime = SysDateTime.Now;
                }
                this.Add(entity);
            }

            return result;
        }

        public IList<Employee> GetEmpByDeptID(Guid id)
        {
            var dept = this.Find<Department>(id);

            if (dept == null || dept.Level == 1)
            {
                return new List<Employee>();
            }
            else
            {
                var deptIds = this.GetDeptByCurrentUser(id);

                var employees = this.GetEntities<Employee>(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));
                return employees;
            }
        }

        public IList<Employee> GetEmpByDeptCode(string code)
        {
            var dept = this.GetEntity<Department>(predicate: s => s.Code == code);

            if (dept == null || dept.Level == 1)
            {
                return new List<Employee>();
            }
            else
            {
                var deptIds = this.GetDeptByCurrentUser(dept.ID);

                var employees = this.GetEntities<Employee>(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));

                return employees;
            }
        }

        #endregion 消息发布
    }
}