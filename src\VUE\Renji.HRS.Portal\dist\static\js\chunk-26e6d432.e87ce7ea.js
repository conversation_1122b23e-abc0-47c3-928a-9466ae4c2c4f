(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-26e6d432"],{"147e":function(e,t,i){"use strict";i.r(t);var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container "},[i("layout3",{scopedSlots:e._u([{key:"aside",fn:function(){return[i("el-checkbox",{on:{change:e.selectedChange},model:{value:e.listQuery.IsContainSubDept,callback:function(t){e.$set(e.listQuery,"IsContainSubDept",t)},expression:"listQuery.IsContainSubDept"}},[e._v("包含下级部门")]),i("c-tree",{directives:[{name:"loading",rawName:"v-loading",value:e.treeLoading,expression:"treeLoading"}],attrs:{options:e.treeData,props:e.tree<PERSON>rops,"expanded-keys":e.treeExpandedKeys},on:{nodeClick:e.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[i("el-card",[i("div",{staticClass:"filter-container"},[i("el-form",{attrs:{inline:!0,model:e.listQuery}},[i("el-form-item",[i("el-select",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{placeholder:"请选择"},model:{value:e.listQuery.entityColumn,callback:function(t){e.$set(e.listQuery,"entityColumn",t)},expression:"listQuery.entityColumn"}},e._l(e.dataColumns,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e}})})),1)],1),i("el-form-item",[i("el-select",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{placeholder:"请选择"},model:{value:e.listQuery.queryCondition.EnumOperation,callback:function(t){e.$set(e.listQuery.queryCondition,"EnumOperation",t)},expression:"listQuery.queryCondition.EnumOperation"}},e._l(e.selectConditionOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),i("el-form-item",[i("el-input",{model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}})],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.querySetting}},[e._v("查询")])],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-edit"},on:{click:e.updateDept}},[e._v("变更")])],1)],1)],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageEmpList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"selection-change":e.handleSelectionChange}},[i("el-table-column",{attrs:{fixed:"",type:"selection",width:"40"}}),i("el-table-column",{attrs:{fixed:"",label:"唯一码",sortable:"custom",prop:"Uid","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("span",{staticClass:"link-type",on:{click:function(t){return e.handleWatchEmpInfo(s)}}},[e._v(e._s(s.uid))])]}}])}),i("el-table-column",{attrs:{label:"工号","min-width":"80px",sortable:"custom",prop:"EmpCode"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("span",[e._v(e._s(s.empCode))])]}}])}),i("el-table-column",{attrs:{label:"姓名","min-width":"80px",sortable:"custom",prop:"DisplayName"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("span",[e._v(e._s(s.displayName))])]}}])}),i("el-table-column",{attrs:{label:"部门","min-width":"80px",sortable:"custom",prop:"DepartmentName"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("span",[e._v(e._s(s.departmentName))])]}}])}),i("el-table-column",{attrs:{label:"岗位","min-width":"80px"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("span",[e._v(e._s(s.positionName))])]}}])}),i("el-table-column",{attrs:{label:"职别","min-width":"80px",sortable:"custom",prop:"OfficialRankName"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("span",[e._v(e._s(s.officialRankName))])]}}])}),i("el-table-column",{attrs:{label:"员工状态","min-width":"120px",sortable:"custom",prop:"EmpStatusName"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("span",[e._v(e._s(s.empStatusName))])]}}])}),i("el-table-column",{attrs:{label:"在职方式","min-width":"120px",sortable:"custom",prop:"HireStyleName"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("span",[e._v(e._s(s.hireStyleName))])]}}])}),i("el-table-column",{attrs:{label:"离职方式","min-width":"120px",sortable:"custom",prop:"LeaveStyleName"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("span",[e._v(e._s(s.leaveStyleName))])]}}])}),i("el-table-column",{attrs:{label:"操作",fixed:"right",width:"200",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("el-button",{staticClass:"el-icon-tickets",attrs:{type:"info",size:"mini"},on:{click:function(t){return e.viewHistory(s)}}},[e._v(" 变更历史 ")])]}}])})],1),i("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getEmpList}})],1)]},proxy:!0}])}),e.dialogAppInfoVisible?i("el-dialog",{staticClass:"empManager",attrs:{title:e.empDialogTitle,visible:e.dialogAppInfoVisible,width:"90%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogAppInfoVisible=t}}},[i("empInfo",{ref:"empInfo",attrs:{"dept-id":e.listQuery.deptId,"emp-id":e.empId},on:{"update-emp-id":e.updateEmpId}})],1):e._e(),i("el-dialog",{staticClass:"empManager",attrs:{title:"部门变更历史",visible:e.dialogHistoryVisible,width:"60%"},on:{"update:visible":function(t){e.dialogHistoryVisible=t}}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageHistoryList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}}},[i("el-table-column",{attrs:{fixed:"",label:"工号","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("span",[e._v(e._s(s.empCode))])]}}])}),i("el-table-column",{attrs:{label:"姓名","min-width":"80px"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("span",[e._v(e._s(s.empName))])]}}])}),i("el-table-column",{attrs:{label:"变更前值","min-width":"80px"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("span",[e._v(e._s(s.preDeptName))])]}}])}),i("el-table-column",{attrs:{label:"变更后值","min-width":"80px"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("span",[e._v(e._s(s.adjustDeptName))])]}}])}),i("el-table-column",{attrs:{label:"变更时间","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("span",[e._v(e._s(s.createTimeFormat))])]}}])}),i("el-table-column",{attrs:{label:"操作人","min-width":"80px"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[i("span",[e._v(e._s(s.creator))])]}}])})],1)],1),i("el-dialog",{attrs:{title:"部门变更",visible:e.dialogModifyVisible,width:"60%"},on:{"update:visible":function(t){e.dialogModifyVisible=t}}},[i("el-form",{ref:"dataFormForWork",attrs:{rules:e.rules,model:e.tempModifyModel,"label-position":"right","label-width":"200px"}},[i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"新部门",prop:"newDeptId"}},[i("c-select-tree",{attrs:{options:e.treeData,"tree-props":e.treeProps,props:{expandTrigger:"hover"},placeholder:"请选择新部门"},model:{value:e.tempModifyModel.newDeptId,callback:function(t){e.$set(e.tempModifyModel,"newDeptId",t)},expression:"tempModifyModel.newDeptId"}})],1)],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:24}},[i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-document"},on:{click:e.saveChange}},[e._v("保存")])],1)],1)],1)],1)],1)],1)},a=[],n=(i("d81d"),i("d3b7"),i("d368")),r=i("e44c"),o=i("91a7"),l=i("f9ac"),c={components:{empInfo:o["a"]},data:function(){return{treeData:[],treeExpandedKeys:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},empDialogTitle:"人事信息",rules:{newDeptId:[{required:!0,message:"请选择人员分配的目标部门！",trigger:["blur","change"]}]},selected:"",currentNode:{},includeDownDept:!1,dialogHistoryVisible:!1,dialogModifyVisible:!1,tempModifyModel:{},dataColumns:[{value:"1",label:"唯一码",type:"System.Int32",columnName:"Uid"},{value:"2",label:"姓名",type:"System.String",columnName:"DisplayName"},{value:"3",label:"工号",type:"System.String",columnName:"EmpCode"}],selectConditionOptions:[],pageHistoryList:[],empList:[],empDept:{},empDeptQuery:{},pageEmpList:[],total:1,totalHistory:1,listQuery:{queryCondition:{},total:1,pageIndex:1,pageSize:10},listLoading:!1,treeLoading:!1,empId:"",dialogAppInfoVisible:!1,editIds:[]}},created:function(){this.loadTree(),this.loadConditions()},mounted:function(){this.total=this.empList.length,this.getEmpList()},methods:{loadTree:function(){var e=this;n["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data,e.treeData&&e.treeData.length>0&&e.treeExpandedKeys.push(e.treeData[0].id)})).catch((function(e){console.log(e)})),this.resetCurrentNode()},loadConditions:function(){var e=this;l["a"].getEnumInfos({enumType:"Operations"}).then((function(t){e.selectConditionOptions=t.data.datas})).catch((function(e){console.log(e)}))},resetCurrentNode:function(){this.currentNode=null},treeNodeClick:function(e){this.currentNode=e,this.listQuery.deptId=e.id,this.listQuery.pageIndex=1,this.getEmpList()},statusSelectedChange:function(e){this.listQuery.EnumStatus=e.code,this.selectedChange()},selectedChange:function(){this.listQuery.pageIndex=1,this.getEmpList()},getEmpList:function(){var e=this;this.pageEmpList=[],this.listLoading=!0,this.listQuery.entityColumn&&(this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.columnName,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.CommonConditionList=[this.listQuery.queryCondition]),r["a"].queryEmployeeByCommonConditions(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageEmpList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},sortChange:function(e,t,i){this.listQuery.pageIndex=1;var s="";"descending"===e.order&&(s="desc"),"ascending"===e.order&&(s="asc"),this.listQuery.order=e.prop+" "+s,this.getEmpList()},updateEmpId:function(e){this.empId=e},handleWatchEmpInfo:function(e){this.empDialogTitle="人事信息——"+e.displayName,this.empId=e.id,this.dialogAppInfoVisible=!0},handleClose:function(){this.dialogAppInfoVisible=!1,this.clear()},clear:function(){this.$refs["empInfo"].clear()},saveChange:function(){var e=this;this.$refs["dataFormForWork"].validate((function(t){if(t){if(e.tempModifyModel.newDeptId===e.tempModifyModel.deptId)return void e.$notice.message("新部门不能和现部门相同。","info");e.empDept.employeeId=e.tempModifyModel.id,e.empDept.adjustDeptId=e.tempModifyModel.newDeptId,e.empDept.Ids=e.editIds,r["a"].batchUpdateEmployeeDept(e.empDept).then((function(t){t.succeed?e.$notice.message("新部门更新成功","success"):t.succeed||e.$notice.message("新部门更新失败，请联系管理员","info")})).catch((function(e){console.log(e)})).finally((function(){e.dialogModifyVisible=!1,e.querySetting()}))}}))},tabClick:function(e,t){console.log(e,t)},showModifyDept:function(e){this.dialogModifyVisible=!0,this.tempModifyModel=e},viewHistory:function(e){var t=this;this.empDeptQuery.employeeId=e.id,r["a"].queryEmployeeDeptHistory(this.empDeptQuery).then((function(e){e.succeed?(t.pageHistoryList=e.data.datas,console.log(t.pageHistoryList)):t.$notice.resultTip(e)})).catch((function(e){console.log(e),t.listLoading=!1})),this.dialogHistoryVisible=!0},querySetting:function(){this.listQuery.deptId=null,this.listQuery.pageIndex=1,this.getEmpList()},handleSelectionChange:function(e){this.editIds=e.map((function(e){return e.id}))},updateDept:function(){this.editIds.length>0?this.dialogModifyVisible=!0:this.$notice.message("请选择变更部门人员","warning")}}},d=c,u=(i("9b5c"),i("39a4"),i("2877")),p=Object(u["a"])(d,s,a,!1,null,"1d379b8c",null);t["default"]=p.exports},"39a4":function(e,t,i){"use strict";var s=i("fbfb"),a=i.n(s);a.a},"91a7":function(e,t,i){"use strict";var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container "},[i("el-tabs",{attrs:{type:"card","before-leave":e.beforeLeaveTab},on:{"tab-click":e.tabClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[e.userPermission.hrBaseInfo&&e.userPermission.hrBaseInfo.isShow?i("el-tab-pane",{attrs:{label:"基本信息",name:"baseInfoTab"}},[e.baseInfoVisiable?i("baseInfo",{ref:"baseInfo",attrs:{"user-permission":e.userPermission.hrBaseInfo,"dept-id":e.deptId,"emp-id":e.empId},on:{updateEmpId:e.updateEmpId}}):e._e()],1):e._e(),e.userPermission.hrInfo&&e.userPermission.hrInfo.isShow?i("el-tab-pane",{attrs:{label:"人事信息",name:"hRInfoTab"}},[e.hRInfoVisiable?i("hRInfo",{ref:"hRInfo",attrs:{"user-permission":e.userPermission.hrInfo,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.party&&e.userPermission.party.isShow?i("el-tab-pane",{attrs:{label:"政治面貌",name:"partyTab"}},[e.partyVisiable?i("party",{ref:"party",attrs:{"user-permission":e.userPermission.party,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.station&&e.userPermission.station.isShow?i("el-tab-pane",{attrs:{label:"聘任职务",name:"stationTab"}},[e.stationVisiable?i("station",{ref:"station",attrs:{"user-permission":e.userPermission.station,"emp-id":e.empId,"contain-rank":e.containRank,estype:1}}):e._e()],1):e._e(),e.userPermission.qualification&&e.userPermission.qualification.isShow?i("el-tab-pane",{attrs:{label:"聘任职称",name:"qualificationTab"}},[e.qualificationVisiable?i("station",{ref:"qualification",attrs:{"user-permission":e.userPermission.qualification,"emp-id":e.empId,"not-contain-rank":e.notContainRank,estype:2}}):e._e()],1):e._e(),e.userPermission.certify&&e.userPermission.certify.isShow?i("el-tab-pane",{attrs:{label:"职称资格",name:"certifyTab"}},[e.certifyVisiable?i("certify",{ref:"certify",attrs:{"user-permission":e.userPermission.certify,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.wages&&e.userPermission.wages.isShow?i("el-tab-pane",{attrs:{label:"工资",name:"wagesTab"}},[e.wagesVisiable?i("wages",{ref:"wages",attrs:{"user-permission":e.userPermission.wages,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.socialsecurity&&e.userPermission.socialsecurity.isShow?i("el-tab-pane",{attrs:{label:"社保",name:"socialsecurityTab"}},[e.socialsecurityVisiable?i("socialsecurity",{ref:"socialsecurity",attrs:{"user-permission":e.userPermission.socialsecurity,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.certieducation&&e.userPermission.certieducation.isShow?i("el-tab-pane",{attrs:{label:"学习经历",name:"educationTab"}},[e.educationVisiable?i("education",{ref:"education",attrs:{"user-permission":e.userPermission.certieducation,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.work&&e.userPermission.work.isShow?i("el-tab-pane",{attrs:{label:"工作经历",name:"workTab"}},[e.workVisiable?i("work",{ref:"work",attrs:{"user-permission":e.userPermission.work,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.abroadInfo&&e.userPermission.abroadInfo.isShow?i("el-tab-pane",{attrs:{label:"出国",name:"abroadInfoTab"}},[e.abroadInfoVisiable?i("abroadInfo",{ref:"abroadInfo",attrs:{"user-permission":e.userPermission.abroadInfo,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.contract&&e.userPermission.contract.isShow?i("el-tab-pane",{attrs:{label:"合同",name:"contractTab"}},[e.contractVisiable?i("contract",{ref:"contract",attrs:{"user-permission":e.userPermission.contract,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.train&&e.userPermission.train.isShow?i("el-tab-pane",{attrs:{label:"培养计划",name:"trainTab"}},[e.trainVisiable?i("train",{ref:"train",attrs:{"user-permission":e.userPermission.train,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.assessment&&e.userPermission.assessment.isShow?i("el-tab-pane",{attrs:{label:"考核",name:"assessmentTab"}},[e.assessmentVisiable?i("assessment",{ref:"assessment",attrs:{"user-permission":e.userPermission.assessment,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.incentive&&e.userPermission.incentive.isShow?i("el-tab-pane",{attrs:{label:"医德档案",name:"incentiveTab"}},[e.incentiveVisiable?i("incentive",{ref:"incentive",attrs:{"user-permission":e.userPermission.incentive,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.health&&e.userPermission.health.isShow?i("el-tab-pane",{attrs:{label:"健康信息",name:"healthTab"}},[e.healthVisiable?i("health",{ref:"health",attrs:{"user-permission":e.userPermission.health,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.accident&&e.userPermission.accident.isShow?i("el-tab-pane",{attrs:{label:"医疗",name:"accidentTab"}},[e.accidentVisiable?i("accident",{ref:"accident",attrs:{"user-permission":e.userPermission.accident,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.teach&&e.userPermission.teach.isShow?i("el-tab-pane",{attrs:{label:"教学信息",name:"teachTab"}},[e.teachVisiable?i("teach",{ref:"teach",attrs:{"user-permission":e.userPermission.teach,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.tech&&e.userPermission.tech.isShow?i("el-tab-pane",{attrs:{label:"科研信息",name:"empArticleTab"}},[e.empArticleVisiable?i("empArticle",{ref:"empArticle",attrs:{"user-permission":e.userPermission.tech,"emp-id":e.empId}}):e._e()],1):e._e(),e.userPermission.relation&&e.userPermission.relation.isShow?i("el-tab-pane",{attrs:{label:"社会关系",name:"relationTab"}},[e.relationVisiable?i("relation",{ref:"relation",attrs:{"user-permission":e.userPermission.relation,"emp-id":e.empId}}):e._e()],1):e._e()],1)],1)},a=[],n=(i("b0c0"),i("4fad"),i("498a"),i("2195")),r=i("2cf0"),o=i("185f"),l=i("e061"),c=i("c2f3"),d=i("de23"),u=i("12ed"),p=i("d8b5"),m=i("b96e"),h=i("3220"),b=i("f98d"),f=i("a8a1"),y=i("e675"),I=i("a4b1"),g=i("ff52"),w=i("d336"),v=i("5f06"),_=i("c69e"),V=i("cbd9"),k=i("f9ac"),P={components:{baseInfo:n["a"],hRInfo:r["a"],party:o["a"],station:l["a"],certify:c["a"],wages:d["a"],socialsecurity:u["a"],education:p["a"],work:m["a"],abroadInfo:h["a"],contract:b["a"],train:f["a"],assessment:y["a"],incentive:I["a"],health:g["a"],accident:w["a"],teach:v["a"],relation:_["a"],empArticle:V["a"]},props:{deptId:{type:String,default:""},empId:{type:String,default:""},userPermissionProp:{type:Object,default:function(){return{}}}},data:function(){return{userPermission:this.userPermissionProp,departmentId:"",activeName:"baseInfoTab",containRank:["06"],notContainRank:["06"],baseInfoVisiable:!0,hRInfoVisiable:!1,partyVisiable:!1,stationVisiable:!1,qualificationVisiable:!1,certifyVisiable:!1,wagesVisiable:!1,socialsecurityVisiable:!1,educationVisiable:!1,workVisiable:!1,abroadInfoVisiable:!1,contractVisiable:!1,trainVisiable:!1,assessmentVisiable:!1,incentiveVisiable:!1,healthVisiable:!1,accidentVisiable:!1,teachVisiable:!1,empArticleVisiable:!1,relationVisiable:!1}},watch:{deptId:function(e){this.departmentId=e}},created:function(){this.loadUserPermission()},mounted:function(){},methods:{beforeLeaveTab:function(e,t){return!!this.empId||(this.$notice.message("请先新增基本信息。","info"),!1)},tabClick:function(e){switch(e.name){case"baseInfoTab":this.baseInfoVisiable=!0;break;case"hRInfoTab":this.hRInfoVisiable=this.empId&&""!==this.empId.trim();break;case"partyTab":this.partyVisiable=this.empId&&""!==this.empId.trim();break;case"stationTab":this.stationVisiable=this.empId&&""!==this.empId.trim();break;case"qualificationTab":this.qualificationVisiable=this.empId&&""!==this.empId.trim();break;case"wagesTab":this.wagesVisiable=this.empId&&""!==this.empId.trim();break;case"socialsecurityTab":this.socialsecurityVisiable=this.empId&&""!==this.empId.trim();break;case"educationTab":this.educationVisiable=this.empId&&""!==this.empId.trim();break;case"workTab":this.workVisiable=this.empId&&""!==this.empId.trim();break;case"abroadInfoTab":this.abroadInfoVisiable=this.empId&&""!==this.empId.trim();break;case"contractTab":this.contractVisiable=this.empId&&""!==this.empId.trim();break;case"trainTab":this.trainVisiable=this.empId&&""!==this.empId.trim();break;case"assessmentTab":this.assessmentVisiable=this.empId&&""!==this.empId.trim();break;case"incentiveTab":this.incentiveVisiable=this.empId&&""!==this.empId.trim();break;case"healthTab":this.healthVisiable=this.empId&&""!==this.empId.trim();break;case"accidentTab":this.accidentVisiable=this.empId&&""!==this.empId.trim();break;case"teachTab":this.teachVisiable=this.empId&&""!==this.empId.trim();break;case"empArticleTab":this.empArticleVisiable=this.empId&&""!==this.empId.trim();break;case"relationTab":this.relationVisiable=this.empId&&""!==this.empId.trim();break;case"certifyTab":this.certifyVisiable=this.empId&&""!==this.empId.trim();break;default:this.baseInfoVisiable=!0;break}},clear:function(){this.baseInfoVisiable&&this.$refs["baseInfo"].clear(),this.hRInfoVisiable&&this.$refs["hRInfo"].clear(),this.partyVisiable&&this.$refs["party"].clear(),this.stationVisiable&&this.$refs["station"].clear(),this.qualificationVisiable&&this.$refs["qualification"].clear(),this.certifyVisiable&&this.$refs["certify"].clear(),this.wagesVisiable&&this.$refs["wages"].clear(),this.socialsecurityVisiable&&this.$refs["socialsecurity"].clear(),this.educationVisiable&&this.$refs["education"].clear(),this.workVisiable&&this.$refs["work"].clear(),this.abroadInfoVisiable&&this.$refs["abroadInfo"].clear(),this.contractVisiable&&this.$refs["contract"].clear(),this.trainVisiable&&this.$refs["train"].clear(),this.assessmentVisiable&&this.$refs["assessment"].clear(),this.incentiveVisiable&&this.$refs["incentive"].clear(),this.healthVisiable&&this.$refs["health"].clear(),this.accidentVisiable&&this.$refs["accident"].clear(),this.teachVisiable&&this.$refs["teach"].clear(),this.empArticleVisiable&&this.$refs["empArticle"].clear(),this.relationVisiable&&this.$refs["relation"].clear()},updateEmpId:function(e){this.$emit("updateEmpId",e)},loadUserPermission:function(){var e=this;0===Object.entries(this.userPermission).length&&k["a"].getControlRightByCurrentUser().then((function(t){e.userPermission=t.data})).catch((function(e){console.log(e)}))}}},C=P,S=i("2877"),T=Object(S["a"])(C,s,a,!1,null,null,null);t["a"]=T.exports},"9b5c":function(e,t,i){"use strict";var s=i("d970"),a=i.n(s);a.a},d970:function(e,t,i){},fbfb:function(e,t,i){}}]);