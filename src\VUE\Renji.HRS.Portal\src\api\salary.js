import HttpApi from './libs/api.request'

const controller = 'Salary'

const api = new HttpApi(controller)

export default {
  // 查询月度薪资
  querySalary (params) {
    return api.get('QuerySalary', params)
  },
  queryEmployeeSalaryStatus (params) {
    return api.get('QueryEmployeeSalaryStatus', params)
  },
  // 查询月度薪资
  getSalary (params) {
    return api.get('GetSalary', params)
  },
  // 新增月度薪资
  addSalary (params) {
    return api.post('AddSalary', params)
  },
  // 更新月度薪资
  updateSalary (params) {
    return api.post('UpdateSalary', params)
  },
  // 删除月度薪资
  deleteSalary (params) {
    return api.post('DeleteSalary', params)
  },
  // 审批月度薪资
  approvalSalary (params) {
    return api.post('ApprovalSalary', params)
  },
  // 查询薪资明细
  querySalaryDetail (params) {
    return api.post('QuerySalaryDetail', params)
  },
  // 查询上月薪资明细
  queryLastMonthEmployeeSalary (params) {
    return api.get('QueryLastMonthEmployeeSalary', params)
  },
  // 查询薪资明细
  getSalaryDetail (params) {
    return api.get('GetSalaryDetail', params)
  },
  // 查询HR挂账
  queryHROnAccountView (params) {
    return api.get('QueryHROnAccountView', params)
  },
  // 查询HR挂账
  queryHROnAccount (params) {
    return api.get('QueryHROnAccount', params)
  },
  // 销账
  updateHROnAccountWriteOff (params) {
    return api.post('UpdateHROnAccountWriteOff', params)
  },
  // 查询社保挂账
  querySocialSecurityWithhold (params) {
    return api.get('QuerySocialSecurityWithhold', params)
  },
  // 查询四金不够扣明细
  getfourGoldDeficiencySign (params) {
    return api.get('GetfourGoldDeficiencySign', params)
  },
  // 修改员工薪资明细
  updateSalaryDetail (params) {
    return api.post('UpdateSalaryDetail', params)
  },
  // 计算薪资明细
  calculateSalaryDetail (params) {
    return api.post('CalculateSalaryDetail', params)
  },
  // 导入博士后房贴
  importSalaryDetail (file, formData, salaryId) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    return api.postForm('ImportSalaryDetail', formData)
  },
  // 导出薪资明细
  exportSalaryDetail (params) {
    // return api.getFile('ExportSalaryDetail', params)
    return api.post('ExportSalaryDetail',
      {
        data: params,
        responseType: 'arraybuffer'
      })
  },
  // 导出薪资明细
  exportHRData (params) {
    return api.post('ExportHRData',
      {
        data: params,
        responseType: 'arraybuffer'
      })
  },
  // 查询员工薪资
  queryEmployeeSalary (params) {
    return api.get('QueryEmployeeSalary', params)
  },
  // 导出员工薪资记录
  exportEmployeeSalaryRecord (params) {
    return api.post('ExportEmployeeSalaryRecord',
      {
        data: params,
        responseType: 'arraybuffer'
      })
  },
  // 查询员工薪资详情
  getEmployeeSalary (params) {
    return api.get('GetEmployeeSalary', params)
  },
  // 修改员工薪资
  updateEmployeeSalary (params) {
    return api.post('UpdateEmployeeSalary', params)
  },
  // 计算员工薪资预估
  calculateEmployeeSalaryEstimate (params) {
    return api.post('CalculateEmployeeSalaryEstimate', params)
  },
  // 同步员工薪资
  syncEmployeeSalary () {
    return api.post('SyncEmployeeSalary')
  },
  // 查询基础数据
  querySalaryData (params) {
    return api.get('QuerySalaryData', params)
  },
  // 查询基础数据详情
  getSalaryData (params) {
    return api.get('GetSalaryData', params)
  },
  // 根据Code查询基础数据
  getSalaryDataCodes (params) {
    return api.get('GetSalaryDataCodes', params)
  },
  // 更新基础数据
  updateSalaryData (params) {
    return api.post('UpdateSalaryData', params)
  },
  // 查询日历
  queryCalendar (params) {
    return api.get('QueryCalendar', params)
  },
  // 查询日历详情
  getCalendar (params) {
    return api.get('GetCalendar', params)
  },
  // 更新日历
  updateCalendar (params) {
    return api.post('UpdateCalendar', params)
  },
  deleteCalendar (params) {
    return api.post('DeleteCalendar', params)
  },
  // 新增日历
  addCalendar (params) {
    return api.post('AddCalendar', params)
  },
  // 查询已故员工
  queryEmployeeDeceased (params) {
    return api.get('QueryEmployeeDeceased', params)
  },
  // 查询已故员工详情
  getEmployeeDeceased (params) {
    return api.get('GetEmployeeDeceased', params)
  },
  // 更新已故员工
  updateEmployeeDeceased (params) {
    return api.post('UpdateEmployeeDeceased', params)
  },
  // 新增已故员工
  addEmployeeDeceased (params) {
    return api.post('AddEmployeeDeceased', params)
  },
  // 删除已故员工
  deleteEmployeeDeceased (params) {
    return api.post('DeleteEmployeeDeceased', params)
  },
  // 自动导入已故员工
  autoImportEmployeeDeceased (params) {
    return api.get('AutoImportEmployeeDeceased', params)
  },
  // 查询退休员工
  queryEmployeeRetire (params) {
    return api.get('QueryEmployeeRetire', params)
  },
  // 查询退休员工详情
  getEmployeeRetire (params) {
    return api.get('GetEmployeeRetire', params)
  },
  // 更新退休员工
  updateEmployeeRetire (params) {
    return api.post('UpdateEmployeeRetire', params)
  },
  // 新增退休员工
  addEmployeeRetire (params) {
    return api.post('AddEmployeeRetire', params)
  },
  // 删除退休员工
  deleteEmployeeRetire (params) {
    return api.post('DeleteEmployeeRetire', params)
  },
  // 自动导入退休员工
  autoImportEmployeeRetire (params) {
    return api.post('AutoImportEmployeeRetire', params)
  },
  // 查询辞职员工
  queryEmployeeResign (params) {
    return api.get('QueryEmployeeResign', params)
  },
  // 查询辞职员工详情
  getEmployeeResign (params) {
    return api.get('GetEmployeeResign', params)
  },
  // 更新辞职员工
  updateEmployeeResign (params) {
    return api.post('UpdateEmployeeResign', params)
  },
  // 新增辞职员工
  addEmployeeResign (params) {
    return api.post('AddEmployeeResign', params)
  },
  // 删除辞职员工
  deleteEmployeeResign (params) {
    return api.post('DeleteEmployeeResign', params)
  },
  // 自动导入辞职员工
  autoImportEmployeeResign (params) {
    return api.post('AutoImportEmployeeResign', params)
  },
  // 薪资模块查询员工
  querySalaryEmployees (params) {
    return api.get('QuerySalaryEmployees', params)
  },
  // 查询博士后满两年停发员工
  queryEmployeePostdoctorTwoYears (params) {
    return api.get('QueryEmployeePostdoctorTwoYears', params)
  },
  // 查询博士后满两年停发员工详情
  getEmployeePostdoctorTwoYears (params) {
    return api.get('GetEmployeePostdoctorTwoYears', params)
  },
  // 更新博士后满两年停发员工
  updateEmployeePostdoctorTwoYears (params) {
    return api.post('UpdateEmployeePostdoctorTwoYears', params)
  },
  // 新增博士后满两年停发员工
  addEmployeePostdoctorTwoYears (params) {
    return api.post('AddEmployeePostdoctorTwoYears', params)
  },
  // 删除博士后满两年停发员工
  deleteEmployeePostdoctorTwoYears (params) {
    return api.post('DeleteEmployeePostdoctorTwoYears', params)
  },
  // 自动导入博士后满两年停发员工
  autoImportEmployeePostdoctorTwoYears (params) {
    return api.post('AutoImportEmployeePostdoctorTwoYears', params)
  },
  // 查询博士后出站/退站员工
  queryEmployeePostdoctorOutbound (params) {
    return api.get('QueryEmployeePostdoctorOutbound', params)
  },
  // 查询博士后出站/退站员工详情
  getEmployeePostdoctorOutbound (params) {
    return api.get('GetEmployeePostdoctorOutbound', params)
  },
  // 更新博士后出站/退站员工
  updateEmployeePostdoctorOutbound (params) {
    return api.post('UpdateEmployeePostdoctorOutbound', params)
  },
  // 新增博士后出站/退站员工
  addEmployeePostdoctorOutbound (params) {
    return api.post('AddEmployeePostdoctorOutbound', params)
  },
  // 删除博士后出站/退站员工
  deleteEmployeePostdoctorOutbound (params) {
    return api.post('DeleteEmployeePostdoctorOutbound', params)
  },
  // 自动导入博士后出站/退站员工
  autoImportEmployeePostdoctorOutbound (params) {
    return api.post('autoImportEmployeePostdoctorOutbound', params)
  },
  // 查询其他停工资员工
  queryEmployeeOtherConditionsStopSalary (params) {
    return api.get('QueryEmployeeOtherConditionsStopSalary', params)
  },
  // 查询其他停工资员工详情
  getEmployeeOtherConditionsStopSalary (params) {
    return api.get('GetEmployeeOtherConditionsStopSalary', params)
  },
  // 更新其他停工资员工
  updateEmployeeOtherConditionsStopSalary (params) {
    return api.post('UpdateEmployeeOtherConditionsStopSalary', params)
  },
  // 新增其他停工资员工
  addEmployeeOtherConditionsStopSalary (params) {
    return api.post('AddEmployeeOtherConditionsStopSalary', params)
  },
  // 删除其他停工资员工
  deleteEmployeeOtherConditionsStopSalary (params) {
    return api.post('DeleteEmployeeOtherConditionsStopSalary', params)
  },
  // 自动导入其他停工资员工
  autoImportEmployeeOtherConditionsStopSalary (params) {
    return api.post('AutoImportEmployeeOtherConditionsStopSalary', params)
  },
  // 查询回国人员恢复工资补发
  queryEmployeeOverseasReturnReissue (params) {
    return api.get('QueryEmployeeOverseasReturnReissue', params)
  },
  // 查询回国人员恢复工资补发详情
  getEmployeeOverseasReturnReissue (params) {
    return api.get('GetEmployeeOverseasReturnReissue', params)
  },
  // 查询回国人员恢复工资补发历史
  queryEmployeeOverseasReissueDetail (params) {
    return api.get('QueryEmployeeOverseasReissueDetail', params)
  },
  // 更新回国人员恢复工资补发
  updateEmployeeOverseasReturnReissue (params) {
    return api.post('UpdateEmployeeOverseasReturnReissue', params)
  },
  // 新增回国人员恢复工资补发
  addEmployeeOverseasReturnReissue (params) {
    return api.post('AddEmployeeOverseasReturnReissue', params)
  },
  // 删除回国人员恢复工资补发
  deleteEmployeeOverseasReturnReissue (params) {
    return api.post('DeleteEmployeeOverseasReturnReissue', params)
  },
  // 自动导入回国人员恢复工资补发
  autoImportEmployeeOverseasReturnReissue (params) {
    return api.post('AutoImportEmployeeOverseasReturnReissue', params)
  },
  // 导入回国人员恢复工资补发
  importEmployeeOverseasReissueDetail (file, formData, salaryId, employeeId) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    formData.append('employeeId', employeeId)
    return api.postForm('ImportEmployeeOverseasReissueDetail', formData)
  },
  // 查询出国人员扣款（月薪）
  queryEmployeeOverseasMonthlyDeduction (params) {
    return api.get('QueryEmployeeOverseasMonthlyDeduction', params)
  },
  // 查询出国人员扣款（月薪）详情
  getEmployeeOverseasMonthlyDeduction (params) {
    return api.get('GetEmployeeOverseasMonthlyDeduction', params)
  },
  // 更新出国人员扣款（月薪）
  updateEmployeeOverseasMonthlyDeduction (params) {
    return api.post('UpdateEmployeeOverseasMonthlyDeduction', params)
  },
  // 新增出国人员扣款（月薪）
  addEmployeeOverseasMonthlyDeduction (params) {
    return api.post('AddEmployeeOverseasMonthlyDeduction', params)
  },
  // 删除出国人员扣款（月薪）
  deleteEmployeeOverseasMonthlyDeduction (params) {
    return api.post('DeleteEmployeeOverseasMonthlyDeduction', params)
  },
  // 自动导入出国人员扣款（月薪）
  autoImportEmployeeOverseasMonthlyDeduction (params) {
    return api.post('AutoImportEmployeeOverseasMonthlyDeduction', params)
  },
  // 查询出国人员多扣款员工
  queryEmployeeOverseasExcessDeduction (params) {
    return api.get('QueryEmployeeOverseasExcessDeduction', params)
  },
  // 查询出国人员多扣款员工
  getEmployeeOverseasExcessDeduction (params) {
    return api.get('GetEmployeeOverseasExcessDeduction', params)
  },
  // 更新出国人员多扣款员工
  updateEmployeeOverseasExcessDeduction (params) {
    return api.post('UpdateEmployeeOverseasExcessDeduction', params)
  },
  // 新增出国人员多扣款员工
  addEmployeeOverseasExcessDeduction (params) {
    return api.post('AddEmployeeOverseasExcessDeduction', params)
  },
  // 删除出国人员多扣款员工
  deleteEmployeeOverseasExcessDeduction (params) {
    return api.post('DeleteEmployeeOverseasExcessDeduction', params)
  },
  // 查询出国停工资员工
  queryEmployeeOverseasSuspensionSalary (params) {
    return api.get('QueryEmployeeOverseasSuspensionSalary', params)
  },
  // 查询出国停工资员工详情
  getEmployeeOverseasSuspensionSalary (params) {
    return api.get('GetEmployeeOverseasSuspensionSalary', params)
  },
  // 更新出国停工资员工
  updateEmployeeOverseasSuspensionSalary (params) {
    return api.post('UpdateEmployeeOverseasSuspensionSalary', params)
  },
  // 新增出国停工资员工
  addEmployeeOverseasSuspensionSalary (params) {
    return api.post('AddEmployeeOverseasSuspensionSalary', params)
  },
  // 删除出国停工资员工
  deleteEmployeeOverseasSuspensionSalary (params) {
    return api.post('DeleteEmployeeOverseasSuspensionSalary', params)
  },
  // 自动导入出国停工资员工
  autoImportEmployeeOverseasSuspensionSalary (params) {
    return api.post('AutoImportEmployeeOverseasSuspensionSalary', params)
  },
  // 查询新进员工
  queryNewEmployee (params) {
    return api.get('QueryNewEmployee', params)
  },
  // 查询新进员工详情
  getNewEmployee (params) {
    return api.get('GetNewEmployee', params)
  },
  // 计算新进员工薪资
  calculateNewEmployee (params) {
    return api.post('CalculateNewEmployee', params)
  },
  // 导入员工薪资基数
  importEmployeeSalaryBase (file, formData, salaryId) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    return api.postForm('ImportEmployeeSalaryBase', formData)
  },
  // 导出员工薪资
  exportEmployeeSalary (params) {
    return api.post('ExportEmployeeSalary',
      {
        data: params,
        responseType: 'arraybuffer'
      })
  },
  // 导出员工薪资
  exportEmployeeSocialSecurityBaseCorrection (params) {
    return api.post('ExportEmployeeSocialSecurityBaseCorrection',
      {
        data: params,
        responseType: 'arraybuffer'
      })
  },
  // 更新新进员工
  updateNewEmployee (params) {
    return api.post('UpdateNewEmployee', params)
  },
  // 新增新进员工
  addNewEmployee (params) {
    return api.post('AddNewEmployee', params)
  },
  // 删除新进员工
  deleteNewEmployee (params) {
    return api.post('DeleteNewEmployee', params)
  },
  // 自动导入新进员工
  autoImportNewEmployee (params) {
    return api.get('AutoImportNewEmployee', params)
  },
  // 查询独子费调整员工
  queryEmployeeOnlyChildFeeAdjustment (params) {
    return api.get('QueryEmployeeOnlyChildFeeAdjustment', params)
  },
  // 查询独子费调整员工详情
  getEmployeeOnlyChildFeeAdjustment (params) {
    return api.get('GetEmployeeOnlyChildFeeAdjustment', params)
  },
  // 更新独子费调整员工
  updateEmployeeOnlyChildFeeAdjustment (params) {
    return api.post('UpdateEmployeeOnlyChildFeeAdjustment', params)
  },
  // 新增独子费调整员工
  addEmployeeOnlyChildFeeAdjustment (params) {
    return api.post('AddEmployeeOnlyChildFeeAdjustment', params)
  },
  // 删除独子费调整员工
  deleteEmployeeOnlyChildFeeAdjustment (params) {
    return api.post('DeleteEmployeeOnlyChildFeeAdjustment', params)
  },
  // 查询职业年金补扣
  queryEmployeeOccupationalAnnuityBackDeduction (params) {
    return api.get('QueryEmployeeOccupationalAnnuityBackDeduction', params)
  },
  // 查询职业年金补扣详情
  getEmployeeOccupationalAnnuityBackDeduction (params) {
    return api.get('GetEmployeeOccupationalAnnuityBackDeduction', params)
  },
  // 计算职业年金补扣
  calculateEmployeeOccupationalAnnuityBackDeduction (params) {
    return api.post('CalculateEmployeeOccupationalAnnuityBackDeduction', params)
  },
  // 更新职业年金补扣
  updateEmployeeOccupationalAnnuityBackDeduction (params) {
    return api.post('UpdateEmployeeOccupationalAnnuityBackDeduction', params)
  },
  // 新增职业年金补扣
  addEmployeeOccupationalAnnuityBackDeduction (params) {
    return api.post('AddEmployeeOccupationalAnnuityBackDeduction', params)
  },
  // 删除职业年金补扣
  deleteEmployeeOccupationalAnnuityBackDeduction (params) {
    return api.post('DeleteEmployeeOccupationalAnnuityBackDeduction', params)
  },
  // 查询缴金补发/补扣
  queryEmployeePaymentBackPayDeduction (params) {
    return api.get('QueryEmployeePaymentBackPayDeduction', params)
  },
  // 查询缴金补发/补扣详情
  getEmployeePaymentBackPayDeduction (params) {
    return api.get('GetEmployeePaymentBackPayDeduction', params)
  },
  // 计算缴金补发/补扣
  calculateEmployeePaymentBackPayDeduction (params) {
    return api.post('CalculateEmployeePaymentBackPayDeduction', params)
  },
  // 更新缴金补发/补扣
  updateEmployeePaymentBackPayDeduction (params) {
    return api.post('UpdateEmployeePaymentBackPayDeduction', params)
  },
  // 新增缴金补发/补扣
  addEmployeePaymentBackPayDeduction (params) {
    return api.post('AddEmployeePaymentBackPayDeduction', params)
  },
  // 删除缴金补发/补扣
  deleteEmployeePaymentBackPayDeduction (params) {
    return api.post('DeleteEmployeePaymentBackPayDeduction', params)
  },
  // 查询社保基数修正
  queryEmployeeSocialSecurityBaseCorrection (params) {
    return api.get('QueryEmployeeSocialSecurityBaseCorrection', params)
  },
  // 查询社保基数修正详情
  getEmployeeSocialSecurityBaseCorrection (params) {
    return api.get('GetEmployeeSocialSecurityBaseCorrection', params)
  },
  // 计算社保基数修正
  calculateEmployeeSocialSecurityBaseCorrection (params) {
    return api.post('CalculateEmployeeSocialSecurityBaseCorrection', params)
  },
  // 更新社保基数修正
  updateEmployeeSocialSecurityBaseCorrection (params) {
    return api.post('UpdateEmployeeSocialSecurityBaseCorrection', params)
  },
  // 新增社保基数修正
  addEmployeeSocialSecurityBaseCorrection (params) {
    return api.post('AddEmployeeSocialSecurityBaseCorrection', params)
  },
  // 删除社保基数修正
  deleteEmployeeSocialSecurityBaseCorrection (params) {
    return api.post('DeleteEmployeeSocialSecurityBaseCorrection', params)
  },
  // 查询行政聘任
  queryAdministrativeAppointment (params) {
    return api.get('QueryAdministrativeAppointment', params)
  },
  // 查询行政聘任详情
  getAdministrativeAppointment (params) {
    return api.get('GetAdministrativeAppointment', params)
  },
  // 计算行政聘任
  calculateAdministrativeAppointment (params) {
    return api.post('CalculateAdministrativeAppointment', params)
  },
  // 更新行政聘任
  updateAdministrativeAppointment (params) {
    return api.post('UpdateAdministrativeAppointment', params)
  },
  // 新增行政聘任
  addAdministrativeAppointment (params) {
    return api.post('AddAdministrativeAppointment', params)
  },
  // 删除行政聘任
  deleteAdministrativeAppointment (params) {
    return api.post('DeleteAdministrativeAppointment', params)
  },
  // 查询员工修正(月薪)
  queryEmployeeSalaryCorrectionMonth (params) {
    return api.get('QueryEmployeeSalaryCorrectionMonth', params)
  },
  // 查询员工修正(月薪)详情
  getEmployeeSalaryCorrectionMonth (params) {
    return api.get('GetEmployeeSalaryCorrectionMonth', params)
  },
  // 计算员工修正(月薪)
  calculateEmployeeSalaryCorrectionMonth (params) {
    return api.post('CalculateEmployeeSalaryCorrectionMonth', params)
  },
  // 更新员工修正(月薪)
  updateEmployeeSalaryCorrectionMonth (params) {
    return api.post('UpdateEmployeeSalaryCorrectionMonth', params)
  },
  // 新增员工修正(月薪)
  addEmployeeSalaryCorrectionMonth (params) {
    return api.post('AddEmployeeSalaryCorrectionMonth', params)
  },
  // 删除员工修正(月薪)
  deleteEmployeeSalaryCorrectionMonth (params) {
    return api.post('DeleteEmployeeSalaryCorrectionMonth', params)
  },
  // 查询博士后修正（年薪）
  queryPostdoctoralSalaryCorrectionYear (params) {
    return api.get('QueryPostdoctoralSalaryCorrectionYear', params)
  },
  // 查询博士后修正（年薪）详情
  getPostdoctoralSalaryCorrectionYear (params) {
    return api.get('GetPostdoctoralSalaryCorrectionYear', params)
  },
  // 计算博士后修正（年薪）
  calculatePostdoctoralSalaryCorrectionYear (params) {
    return api.post('CalculatePostdoctoralSalaryCorrectionYear', params)
  },
  // 更新博士后修正（年薪）
  updatePostdoctoralSalaryCorrectionYear (params) {
    return api.post('UpdatePostdoctoralSalaryCorrectionYear', params)
  },
  // 新增博士后修正（年薪）
  addPostdoctoralSalaryCorrectionYear (params) {
    return api.post('AddPostdoctoralSalaryCorrectionYear', params)
  },
  // 删除博士后修正（年薪）
  deletePostdoctoralSalaryCorrectionYear (params) {
    return api.post('DeletePostdoctoralSalaryCorrectionYear', params)
  },
  // 查询一值班二值班
  queryShiftAllowance (params) {
    return api.get('QueryShiftAllowance', params)
  },
  // 查询一值班二值班详情
  getShiftAllowance (params) {
    return api.get('GetShiftAllowance', params)
  },
  // 计算一值班二值班薪资
  calculateShiftAllowance (params) {
    return api.post('CalculateShiftAllowance', params)
  },
  // 更新一值班二值班
  updateShiftAllowance (params) {
    return api.post('UpdateShiftAllowance', params)
  },
  // 新增一值班二值班
  addShiftAllowance (params) {
    return api.post('AddShiftAllowance', params)
  },
  // 删除一值班二值班
  deleteShiftAllowance (params) {
    return api.post('DeleteShiftAllowance', params)
  },
  // 自动导入一值班二值班
  autoImportShiftAllowance (params) {
    return api.post('AutoImportShiftAllowance', params)
  },
  // 查询行政值班费详情
  getGeneralHospitalAdminDutyAllowance (params) {
    return api.get('GetGeneralHospitalAdminDutyAllowance', params)
  },
  // 查询行政值班费详情
  queryGeneralHospitalAdminDutyAllowance (params) {
    return api.get('QueryGeneralHospitalAdminDutyAllowance', params)
  },
  // 更新行政值班费
  updateGeneralHospitalAdminDutyAllowance (params) {
    return api.post('UpdateGeneralHospitalAdminDutyAllowance', params)
  },
  // 新增行政值班费
  addGeneralHospitalAdminDutyAllowance (params) {
    return api.post('AddGeneralHospitalAdminDutyAllowance', params)
  },
  // 删除行政值班费
  deleteGeneralHospitalAdminDutyAllowance (params) {
    return api.post('DeleteGeneralHospitalAdminDutyAllowance', params)
  },
  // 导入行政值班费
  importGeneralHospitalAdminDutyAllowance (file, formData, salaryId) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    return api.postForm('ImportGeneralHospitalAdminDutyAllowance', formData)
  },
  // 导入一值班二值班费
  importShiftAllowance (file, formData, salaryId) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    return api.postForm('ImportShiftAllowance', formData)
  },
  // 自动导入行政值班费
  autoImportAdminDutyAllowance (params) {
    return api.post('AutoImportAdminDutyAllowance', params)
  },
  // 查询中夜班费
  queryMiddleNightShiftAllowance (params) {
    return api.get('QueryMiddleNightShiftAllowance', params)
  },
  // 查询中夜班费详情
  getMiddleNightShiftAllowance (params) {
    return api.get('GetMiddleNightShiftAllowance', params)
  },
  // 计算中夜班费
  getMiddleNightShiftAllowanceParam () {
    return api.get('GetMiddleNightShiftAllowanceParam')
  },
  // 更新中夜班费
  updateMiddleNightShiftAllowance (params) {
    return api.post('UpdateMiddleNightShiftAllowance', params)
  },
  // 新增中夜班费
  addMiddleNightShiftAllowance (params) {
    return api.post('AddMiddleNightShiftAllowance', params)
  },
  // 删除中夜班费
  deleteMiddleNightShiftAllowance (params) {
    return api.post('DeleteMiddleNightShiftAllowance', params)
  },
  // 导入中夜班费
  importMiddleNightShiftAllowance (file, formData, salaryId) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    return api.postForm('ImportMiddleNightShiftAllowance', formData)
  },
  // 自动导入中夜班费
  autoMiddleNightShiftAllowance (params) {
    return api.post('AutoMiddleNightShiftAllowance', params)
  },
  // 查询考勤卫生津贴
  queryAttendanceHealthAllowance (params) {
    return api.get('QueryAttendanceHealthAllowance', params)
  },
  // 查询考勤卫生津贴详情
  getAttendanceHealthAllowance (params) {
    return api.get('GetAttendanceHealthAllowance', params)
  },
  // 更新考勤卫生津贴
  updateAttendanceHealthAllowance (params) {
    return api.post('UpdateAttendanceHealthAllowance', params)
  },
  // 新增考勤卫生津贴
  addAttendanceHealthAllowance (params) {
    return api.post('AddAttendanceHealthAllowance', params)
  },
  // 删除考勤卫生津贴
  deleteAttendanceHealthAllowance (params) {
    return api.post('DeleteAttendanceHealthAllowance', params)
  },
  // 导出卫生津贴
  exportAttendanceHealthAllowance (params) {
    return api.post('ExportAttendanceHealthAllowance',
      {
        data: params,
        responseType: 'arraybuffer'
      })
  },
  // 导入卫生津贴
  importAttendanceHealthAllowance (file, formData, salaryId) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    return api.postForm('ImportAttendanceHealthAllowance', formData)
  },
  // 自动导入考勤卫生津贴
  autoImportAttendanceHealthAllowance (params) {
    return api.post('AutoImportAttendanceHealthAllowance', params)
  },
  // 自动导入年度社保基数
  autoImportYearSocialSecurityBaseCorrection (params) {
    return api.post('AutoImportYearSocialSecurityBaseCorrection', params)
  },
  // 查询加班费
  queryOvertimeAllowance (params) {
    return api.get('QueryOvertimeAllowance', params)
  },
  // 查询加班费详情
  getOvertimeAllowance (params) {
    return api.get('GetOvertimeAllowance', params)
  },
  // 更新加班费
  updateOvertimeAllowance (params) {
    return api.post('UpdateOvertimeAllowance', params)
  },
  // 新增加班费
  addOvertimeAllowance (params) {
    return api.post('AddOvertimeAllowance', params)
  },
  // 删除加班费
  deleteOvertimeAllowance (params) {
    return api.post('DeleteOvertimeAllowance', params)
  },
  // 导入加班费
  importOvertimeAllowance (file, formData, salaryId) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    return api.postForm('ImportOvertimeAllowance', formData)
  },
  // 自动导入节假日加班费
  autoImportOvertimeAllowance (params) {
    return api.post('AutoImportOvertimeAllowance', params)
  },
  // 计算加班费
  calculateOvertimeAllowance (params) {
    return api.post('CalculateOvertimeAllowance', params)
  },
  // 查询援外津贴
  queryAssistanceForeign (params) {
    return api.get('QueryAssistanceForeign', params)
  },
  // 查询援外津贴详情
  getAssistanceForeign (params) {
    return api.get('GetAssistanceForeign', params)
  },
  // 更新援外津贴
  updateAssistanceForeign (params) {
    return api.post('UpdateAssistanceForeign', params)
  },
  // 新增援外津贴
  addAssistanceForeign (params) {
    return api.post('AddAssistanceForeign', params)
  },
  // 删除援外津贴
  deleteAssistanceForeign (params) {
    return api.post('DeleteAssistanceForeign', params)
  },
  // 导入援外津贴
  importAssistanceForeign (file, formData, salaryId) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    return api.postForm('ImportAssistanceForeign', formData)
  },
  // 自动导入上月援外津贴
  autoImportAssistanceForeign (params) {
    return api.post('AutoImportAssistanceForeign', params)
  },
  // 查询博士后房贴
  queryPostdoctoralHousingAllowance (params) {
    return api.get('QueryPostdoctoralHousingAllowance', params)
  },
  // 查询博士后房贴详情
  getPostdoctoralHousingAllowance (params) {
    return api.get('GetPostdoctoralHousingAllowance', params)
  },
  // 计算博士后房贴
  calculatePostdoctoralHousingAllowance (params) {
    return api.post('CalculatePostdoctoralHousingAllowance', params)
  },
  // 更新博士后房贴
  updatePostdoctoralHousingAllowance (params) {
    return api.post('UpdatePostdoctoralHousingAllowance', params)
  },
  // 新增博士后房贴
  addPostdoctoralHousingAllowance (params) {
    return api.post('AddPostdoctoralHousingAllowance', params)
  },
  // 删除博士后房贴
  deletePostdoctoralHousingAllowance (params) {
    return api.post('DeletePostdoctoralHousingAllowance', params)
  },
  // 导入博士后房贴
  importPostdoctoralHousingAllowance (file, formData, salaryId) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    return api.postForm('ImportPostdoctoralHousingAllowance', formData)
  },
  // 自动导入博士后房贴
  autoImportPostdoctoralHousingAllowance (params) {
    return api.get('AutoImportPostdoctoralHousingAllowance', params)
  },
  // 查询通用补发/补扣
  querySalaryExtendedDetails (params) {
    return api.get('QuerySalaryExtendedDetails', params)
  },
  // 查询通用补发/补扣特殊列详情
  getSalaryExtendedColumn (params) {
    return api.get('GetSalaryExtendedColumn', params)
  },
  // 查询通用补发/补扣详情
  getSalaryExtendedDetails (params) {
    return api.get('GetSalaryExtendedDetails', params)
  },
  // 更新通用补发/补扣
  updateSalaryExtendedDetails (params) {
    return api.post('UpdateSalaryExtendedDetails', params)
  },
  // 新增通用补发/补扣
  addSalaryExtendedDetails (params) {
    return api.post('AddSalaryExtendedDetails', params)
  },
  // 删除通用补发/补扣
  deleteSalaryExtendedDetails (params) {
    return api.post('DeleteSalaryExtendedDetails', params)
  },
  // 导入博士后房贴
  importSalaryExtendedDetails (file, formData, salaryId, salaryType) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    formData.append('salaryType', salaryType)
    return api.postForm('ImportSalaryExtendedDetails', formData)
  },
  // 查询薪资明细扩展列
  querySalaryDetailGroup (params) {
    return api.get('QuerySalaryDetailGroup', params)
  },
  // 查询薪资明细扩展列详情
  getSalaryDetailGroup (params) {
    return api.get('GetSalaryDetailGroup', params)
  },
  // 更新薪资明细扩展列
  updateSalaryDetailGroup (params) {
    return api.post('UpdateSalaryDetailGroup', params)
  },
  // 新增薪资明细扩展列
  addSalaryExtendedColumn (params) {
    return api.post('AddSalaryExtendedColumn', params)
  },
  // 删除薪资明细扩展列
  deleteSalaryDetailGroup (params) {
    return api.post('DeleteSalaryDetailGroup', params)
  },
  // 查询薪资基数（岗资+薪资+岗位津贴）
  getEmployeeBasePay (params) {
    return api.get('GetEmployeeBasePay', params)
  },
  // 查询病产假
  querySalaryLeave (params) {
    return api.get('QuerySalaryLeave', params)
  },
  // 查询病产假详情
  getSalaryLeave (params) {
    return api.get('GetSalaryLeave', params)
  },
  // 更新病产假
  updateSalaryLeave (params) {
    return api.post('UpdateSalaryLeave', params)
  },
  // 新增病产假
  addSalaryLeave (params) {
    return api.post('AddSalaryLeave', params)
  },
  // 删除病产假
  deleteSalaryLeave (params) {
    return api.post('DeleteSalaryLeave', params)
  },
  // 查询病产假计算天数
  getSalaryLeaveParam (params) {
    return api.get('GetSalaryLeaveParam', params)
  },
  // 计算请假信息
  calculateSalaryLeave (params) {
    return api.post('CalculateSalaryLeave', params)
  },
  // 自动导入病产假考勤数据
  autoImportSalaryLeave (params) {
    return api.post('AutoImportSalaryLeave', params)
  },
  // 导入病产假
  importSalaryLeave (file, formData, salaryId) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    return api.postForm('ImportSalaryLeave', formData)
  },
  // 查询非月薪病产假
  queryNonMonthlySalaryLeave (params) {
    return api.get('QueryNonMonthlySalaryLeave', params)
  },
  // 查询非月薪病产假详情
  getNonMonthlySalaryLeave (params) {
    return api.get('GetNonMonthlySalaryLeave', params)
  },
  // 更新非月薪病产假
  updateNonMonthlySalaryLeave (params) {
    return api.post('UpdateNonMonthlySalaryLeave', params)
  },
  // 新增非月薪病产假
  addNonMonthlySalaryLeave (params) {
    return api.post('AddNonMonthlySalaryLeave', params)
  },
  // 删除非月薪病产假
  deleteNonMonthlySalaryLeave (params) {
    return api.post('DeleteNonMonthlySalaryLeave', params)
  },
  // 导入非月薪病产假
  importNonMonthlySalaryLeave (file, formData, salaryId) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    return api.postForm('ImportNonMonthlySalaryLeave', formData)
  },
  // 查询零星补病产假
  querySporadicSalaryLeave (params) {
    return api.get('QuerySporadicSalaryLeave', params)
  },
  // 查询零星补病产假详情
  getSporadicSalaryLeave (params) {
    return api.get('GetSporadicSalaryLeave', params)
  },
  // 更新零星补病产假
  updateSporadicSalaryLeave (params) {
    return api.post('UpdateSporadicSalaryLeave', params)
  },
  // 新增零星补病产假
  addSporadicSalaryLeave (params) {
    return api.post('AddSporadicSalaryLeave', params)
  },
  // 删除零星补病产假
  deleteSporadicSalaryLeave (params) {
    return api.post('DeleteSporadicSalaryLeave', params)
  },
  // 导入零星补病产假
  importSporadicSalaryLeave (file, formData, salaryId) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    return api.postForm('ImportSporadicSalaryLeave', formData)
  },
  // 查询退休人员补发
  queryRetiredEmployeeReissue (params) {
    return api.get('QueryRetiredEmployeeReissue', params)
  },
  // 查询退休人员补发详情
  getRetiredEmployeeReissue (params) {
    return api.get('GetRetiredEmployeeReissue', params)
  },
  // 更新退休人员补发
  updateRetiredEmployeeReissue (params) {
    return api.post('UpdateRetiredEmployeeReissue', params)
  },
  // 新增退休人员补发
  addRetiredEmployeeReissue (params) {
    return api.post('AddRetiredEmployeeReissue', params)
  },
  // 删除退休人员补发
  deleteRetiredEmployeeReissue (params) {
    return api.post('DeleteRetiredEmployeeReissue', params)
  },
  // 导入退休人员补发
  importRetiredEmployeeReissue (file, formData, salaryId) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    return api.postForm('ImportRetiredEmployeeReissue', formData)
  },
  // 获取员工薪资明细
  getEmployeeSalaryDetail (params) {
    return api.get('GetEmployeeSalaryDetail', params)
  },
  // 查询基础参数调整
  querySalaryDataAdjust (params) {
    return api.get('QuerySalaryDataAdjust', params)
  },
  // 查询基础参数调整详情
  getSalaryDataAdjust (params) {
    return api.get('GetSalaryDataAdjust', params)
  },
  // 更新基础参数调整
  updateSalaryDataAdjust (params) {
    return api.post('UpdateSalaryDataAdjust', params)
  },
  // 新增基础参数调整
  addSalaryDataAdjust (params) {
    return api.post('AddSalaryDataAdjust', params)
  },
  // 删除基础参数调整
  deleteSalaryDataAdjust (params) {
    return api.post('DeleteSalaryDataAdjust', params)
  },
  // 导入基础参数调整
  importSalaryDataAdjust (file, formData, salaryId) {
    formData.append('file', file)
    formData.append('salaryId', salaryId)
    return api.postForm('ImportSalaryDataAdjust', formData)
  },
  // 查询年度社保基数修正
  queryEmployeeAnnualSocialSecurityBaseCorrection (params) {
    return api.get('QueryEmployeeAnnualSocialSecurityBaseCorrection', params)
  },
  // 查询年度社保基数修正详情
  getEmployeeAnnualSocialSecurityBaseCorrection (params) {
    return api.get('GetEmployeeAnnualSocialSecurityBaseCorrection', params)
  },
  // 计算年度社保基数修正
  calculateEmployeeAnnualSocialSecurityBaseCorrection (params) {
    return api.post('CalculateEmployeeAnnualSocialSecurityBaseCorrection', params)
  },
  // 更新年度社保基数修正
  updateEmployeeAnnualSocialSecurityBaseCorrection (params) {
    return api.post('UpdateEmployeeAnnualSocialSecurityBaseCorrection', params)
  },
  // 新增年度社保基数修正
  addEmployeeAnnualSocialSecurityBaseCorrection (params) {
    return api.post('AddEmployeeAnnualSocialSecurityBaseCorrection', params)
  },
  // 删除年度社保基数修正
  deleteEmployeeAnnualSocialSecurityBaseCorrection (params) {
    return api.post('DeleteEmployeeAnnualSocialSecurityBaseCorrection', params)
  },
  // 导出年度社保基数修正
  exportEmployeeAnnualSocialSecurityBaseCorrection (params) {
    return api.post('ExportEmployeeAnnualSocialSecurityBaseCorrection',
      {
        data: params,
        responseType: 'arraybuffer'
      })
  },
  // 查询十三薪
  queryThirteenthSalary (params) {
    return api.get('QueryThirteenthSalary', params)
  },
  // 获取十三薪详情
  getThirteenthSalary (params) {
    return api.get('GetThirteenthSalary', params)
  },
  // 新增十三薪
  addThirteenthSalary (model) {
    return api.post('AddThirteenthSalary', model)
  },
  // 修改十三薪
  updateThirteenthSalary (model) {
    return api.post('UpdateThirteenthSalary', model)
  },
  // 删除十三薪
  deleteThirteenthSalary (model) {
    return api.post('DeleteThirteenthSalary', model)
  },
  // 自动导入十三薪
  autoImportThirteenthSalary (model) {
    return api.post('AutoImportThirteenthSalary', model)
  },
  // 计算十三薪
  calculateThirteenthSalary (model) {
    return api.post('CalculateThirteenthSalary', model)
  },
  // 导出十三薪财务数据
  exportThirteenthFinanceData (params) {
    return api.post('ExportThirteenthFinanceData',
      {
        data: params,
        responseType: 'arraybuffer'
      })
  },

  // 最低工资补助管理
  // 查询最低工资补助
  queryMinimumWageSubsidy (params) {
    return api.get('QueryMinimumWageSubsidy', params)
  },
  // 获取最低工资补助详情
  getMinimumWageSubsidy (params) {
    return api.get('GetMinimumWageSubsidy', params)
  },
  // 新增最低工资补助
  addMinimumWageSubsidy (params) {
    return api.post('AddMinimumWageSubsidy', params)
  },
  // 修改最低工资补助
  updateMinimumWageSubsidy (params) {
    return api.post('UpdateMinimumWageSubsidy', params)
  },
  // 删除最低工资补助
  deleteMinimumWageSubsidy (params) {
    return api.post('DeleteMinimumWageSubsidy', params)
  },
  // 自动导入最低工资补助
  autoImportMinimumWageSubsidy (params) {
    return api.get('AutoImportMinimumWageSubsidy', params)
  }
}
