﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
	<DocumentationFile>Renji.JHR.Common.xml</DocumentationFile>
	<NoWarn>1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
	<None Remove="Renji.JHR.Common.csproj.vspscc" />
	<None Remove="Renji.JHR.Common.xml" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Renji.JHR.Entities" />
    <Using Include="Shinsoft.Core" />
  </ItemGroup>

  <ItemGroup>
	<Reference Include="Shinsoft.Core">
	  <HintPath>..\..\00.Reference\net6.0\Shinsoft.Core.dll</HintPath>
	</Reference>
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="DotNetCore.NPOI" Version="1.2.3" />
	<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
	<PackageReference Include="Portable.BouncyCastle" Version="1.9.0" />
  </ItemGroup>

  <ItemGroup>
	<ProjectReference Include="..\..\20.EntityFrameworkCore\Renji.JHR.Entities\Renji.JHR.Entities.csproj" />
  </ItemGroup>

</Project>
