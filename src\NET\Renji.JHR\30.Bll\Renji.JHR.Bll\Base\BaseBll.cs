﻿using Microsoft.EntityFrameworkCore;
using Renji.JHR.Bll.Caching;
using Renji.JHR.Common;
using Renji.JHR.Common.Configration;
using Renji.JHR.Dal;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.EntityFrameworkCore;
using Shinsoft.Core.Hosting;
using Shinsoft.Core.Mail;
using Shinsoft.Core.Tasks;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Renji.JHR.Bll
{
    public abstract class BaseBll : Repository<BizDbContext>, IRepo
    {
        #region Constructs

        protected BaseBll(IUser? operatorUser = null)
            : base(operatorUser)
        {
        }

        protected BaseBll(string operatorUniqueName)
            : base(operatorUniqueName)
        {
        }

        protected BaseBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        protected BaseBll(IRepo bll)
            : base(bll.BizRepo)
        {
        }

        #endregion Constructs

        #region IRepo

        protected virtual IRepo BizRepo => this.SysBll;
        protected virtual IRepo FileRepo => this.FileBll;
        protected virtual IRepo LogRepo => this.LogBll;
        protected virtual IRepo MailRepo => this.MailBll;

        protected virtual SysBll SysBll => this.GetRepo<SysBll>();
        protected virtual FileBll FileBll => this.GetRepo<FileBll>();
        protected virtual LogBll LogBll => this.GetRepo<LogBll>();
        protected virtual MailBll MailBll => this.GetRepo<MailBll>();

        IRepo IRepo.BizRepo => this.BizRepo;
        IRepo IRepo.FileRepo => this.FileRepo;
        IRepo IRepo.LogRepo => this.LogRepo;
        IRepo IRepo.MailRepo => this.MailRepo;

        #endregion IRepo

        #region Operator User

        /// <summary>
        /// 操作用户（可能为空）
        /// </summary>
        public new User? OperatorUser => base.OperatorUser as User;

        /// <summary>
        /// 操作用户ID（可能为空）
        /// </summary>
        public virtual Guid? OperatorUserId => this.OperatorUser?.ID;

        /// <summary>

        #endregion Operator User

        #region Current User

        /// <summary>
        /// 当前用户（操作用户为空时报错）
        /// </summary>
        public new User CurrentUser => (User)base.CurrentUser;

        /// <summary>
        /// 当前用户ID（操作用户为空时报错）
        /// </summary>
        public virtual Guid CurrentUserId => this.CurrentUser.ID;

        #endregion Current User

        #region SysCache

        private SysCache? _sysCache = null;

        protected virtual SysCache SysCache => _sysCache ??= this.GetRequiredService<SysCache>();

        protected virtual SysCache GetSysCache()
        {
            var cache = this.GetRequiredService<SysCache>();

            if (this.ServiceScope != null)
            {
                cache.SetServiceScope(this.ServiceScope);
            }

            return cache;
        }

        public override void ClearServiceScope()
        {
            base.ClearServiceScope();

            if (_sysCache != null)
            {
                _sysCache.ClearServiceScope();
            }
        }

        #endregion SysCache

        public Dict? GetDict(string typeCode, string code)
        {
            var dict = this.GetEntity<Dict>(c => c.Code == code && c.Parent != null && c.Parent.Code == typeCode);
            return dict;
        }

        public bool GetFullControlDeptByCurrentUser()
        {
            var q = this.GetEntities<Department>(predicate: d => d.Code == Depts.RJHospitalCode &&
               d.RightOfDept.Any(dr => dr.DepartmentId == d.ID && dr.Role.RoleMember.Any(rm => rm.RoleId == dr.RoleId
               && rm.UserId == this.OperatorUserId))).Select(d => d.UidPath).ToArray();

            //var q = (from d in this.DbContext.Department
            //         from r in this.DbContext.RightOfDept.Where(s => s.DepartmentId == d.ID && d.Code == Depts.RJHospitalCode && !d.Deleted && !s.Deleted)
            //         from ug in this.DbContext.RoleMember.Where(s => s.RoleId == r.RoleId && s.UserId == this.OperatorUserId)
            //         select d.UidPath).ToArray();
            return q.Length > 0;
        }

        public IList<Department> GetDeptByCurrentUser()
        {
            var q = this.GetEntities<Department>(predicate: d =>
                d.RightOfDept.Any(dr => dr.DepartmentId == d.ID && dr.Role.RoleMember.Any(rm => rm.RoleId == dr.RoleId
                && rm.UserId == this.OperatorUserId))).Select(d => d.UidPath).ToArray();

            //var q = (from d in this.DbContext.Department
            //         from r in this.DbContext.RightOfDept.Where(s => s.DepartmentId == d.ID && !d.Deleted && !s.Deleted)
            //         from ug in this.DbContext.RoleMember.Where(s => s.RoleId == r.RoleId && s.UserId == this.OperatorUserId)
            // select d.UidPath).ToArray();
            var uidPath = string.Join("|", q);
            return this.GetEntities<Department>(predicate: s => uidPath.Contains(s.Uid.ToString()));
        }

        public IList<Department> GetRealDeptByCurrentUser()
        {
            return this.GetEntities<Department>(predicate: d =>
               d.RightOfDept.Any(dr => dr.DepartmentId == d.ID && dr.Role.RoleMember.Any(rm => rm.RoleId == dr.RoleId
               && rm.UserId == this.OperatorUserId)));

            //var q = (from d in this.DbContext.Department
            //         from r in this.DbContext.RightOfDept.Where(s => s.DepartmentId == d.ID && !d.Deleted && !s.Deleted)
            //         from ug in this.DbContext.RoleMember.Where(s => s.RoleId == r.RoleId && s.UserId == this.OperatorUserId)
            //         select d.ID).ToArray();
            //return this.GetEntities<Department>(predicate: s => q.Contains(s.ID));
        }

        public List<Guid> GetDeptByCurrentUser(Guid deptId)
        {
            var q = this.GetEntities<Department>(predicate: d => d.ID == deptId &&
                d.RightOfDept.Any(dr => dr.DepartmentId == d.ID && dr.Role.RoleMember.Any(rm => rm.RoleId == dr.RoleId
                && rm.UserId == this.OperatorUserId))).Select(d => d.UidPath).ToArray();

            //var q = (from d in this.DbContext.Department.Where(d => deptId == d.ID)
            //         from r in this.DbContext.RightOfDept.Where(s => s.DepartmentId == d.ID && !d.Deleted && !s.Deleted)
            //         from ug in this.DbContext.RoleMember.Where(s => s.RoleId == r.RoleId && s.UserId == this.OperatorUserId)
            //         select d.UidPath).ToArray();
            var uidPath = "";
            if (q.Length > 0)
                uidPath = q[0];
            var list = this.GetEntities<Department>(predicate: s => s.UidPath.Contains(uidPath));
            return list.Select(c => c.ID).ToList();
        }
    }
}