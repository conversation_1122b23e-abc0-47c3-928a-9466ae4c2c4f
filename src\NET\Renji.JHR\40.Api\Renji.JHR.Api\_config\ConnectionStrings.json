{
  "BizDbContext": {
    "DbProvider": "SqlServer",
    "ConnectionString": "Server=xl8.corp.shinsoft.net,9229;Database=Renji_JHR_01; uid=*****;pwd=*****;TrustServerCertificate=True;",
    //"ConnectionString": "Server=xl8.corp.shinsoft.net,9229;Database=Renji_JHR; uid=*****;pwd=*****;TrustServerCertificate=True;",
    //"ConnectionString": "Server=xl8.corp.shinsoft.net,9229;Database=Renji_JHR2; uid=*****;pwd=*****;TrustServerCertificate=True;",
    //"ConnectionString": "Server=xl7.corp.shinsoft.net,9210;Database=Renji_JHR_Dev; uid=*****;pwd=*****;TrustServerCertificate=True;",
    "DbProvider1": "DM",
    "ConnectionString1": "Server=192.168.0.71;Port=5236;User=******;Password=******;",
    "SchemaDefault": "JHR",
    "SchemaPrefix": "",
    "TableSpace": "Renji_JHR"
  },
  "FileDbContext": {
    "DbProvider": "SqlServer",
    "ConnectionString": "Server=xl7.corp.shinsoft.net,9210;Database=Renji_JHR_File; uid=*****;pwd=*****;TrustServerCertificate=True;",
    "DbProvider1": "DM",
    "ConnectionString1": "Server=192.168.0.71;Port=5236;User=******;Password=******;",
    "SchemaDefault": "File",
    "SchemaPrefix": "",
    "TableSpace": "Renji_JHR"
  },
  "LogDbContext": {
    "DbProvider": "SqlServer",
    "ConnectionString": "Server=xl7.corp.shinsoft.net,9210;Database=Renji_JHR_Log; uid=*****;pwd=*****;TrustServerCertificate=True;",
    "DbProvider1": "DM",
    "ConnectionString1": "Server=192.168.0.71;Port=5236;User=******;Password=******;",
    "SchemaDefault": "Log",
    "SchemaPrefix": "",
    "TableSpace": "Renji_JHR"
  },
  "MailDbContext": {
    "DbProvider": "SqlServer",
    "ConnectionString": "Server=xl7.corp.shinsoft.net,9210;Database=Renji_JHR_Mail; uid=*****;pwd=*****;TrustServerCertificate=True;",
    "DbProvider1": "DM",
    "ConnectionString1": "Server=192.168.0.71;Port=5236;User=******;Password=******;",
    "SchemaDefault": "Mail",
    "SchemaPrefix": "",
    "TableSpace": "Renji_JHR"
  }
}