﻿using Shinsoft.Core;
using Shinsoft.Core.Caching;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;
using System.Text.Json.Serialization;
using System.Xml.Serialization;

namespace Renji.JHR.Entities
{
    public partial class SysSetting : IKeyValue<string, string>, IHashCache
    {
        #region IHashCache

        [NotMapped, XmlIgnore, JsonIgnore]
        public string Field => this.Key;

        #endregion IHashCache
    }
}