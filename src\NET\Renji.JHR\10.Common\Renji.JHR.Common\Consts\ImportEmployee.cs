﻿using Renji.JHR.Common.Configration;
using System;
using System.Collections.Generic;
using System.Text;

namespace Renji.JHR.Common.Consts
{
    public static class ImportEmployee
    {
        /// <summary>
        /// 导入用户信息 配置
        /// </summary>
        public static Dictionary<string, Tuple<string, string, string>> ImportEmp => new Dictionary<string, Tuple<string, string, string>>
        {
            {"importemp",new Tuple<string, string, string>("EmployeeTemp",(Config.IsDM ? "\"JHR\".usp_Import_Employee" : "usp_Import_Employee"),"EmployeeTemplate.xlsx") },
            {"importemphr",new Tuple<string, string, string>("EmployeeHRTemp",(Config.IsDM ? "\"JHR\".usp_Import_EmployeeHR" : "usp_Import_EmployeeHR"),"EmployeeHRTemplate.xlsx") },
            {"importempcertify",new Tuple<string, string, string>("EmployeeCertifyTemp",(Config.IsDM ? "\"JHR\".usp_Import_EmployeeCertify" : "usp_Import_EmployeeCertify"),"EmployeeCertifyTemplate.xlsx") },  //职称资格
            {"importempstationpost",new Tuple<string, string, string>("EmployeeStationTemp",(Config.IsDM ? "\"JHR\".usp_Import_EmployeeStationPost" : "usp_Import_EmployeeStationPost"),"EmployeeStationTemplate.xlsx") },  //聘任职务
            {"importempstationtitle",new Tuple<string, string, string>("EmployeeStationTemp",(Config.IsDM ? "\"JHR\".usp_Import_EmployeeStationTitle" : "usp_Import_EmployeeStationTitle"),"EmployeeStationTemplate.xlsx") },  //聘任职称
            {"importempeducation",new Tuple<string, string, string>("EmployeeEducationTemp",(Config.IsDM ? "\"JHR\".usp_Import_EmployeeEducation" : "usp_Import_EmployeeEducation"),"EmployeeEducationTemplate.xlsx") }, // 学习经历

            {"importempword",new Tuple<string, string, string>("EmployeeWorkTemp",(Config.IsDM ? "\"JHR\".usp_Import_EmployeeWork" : "usp_Import_EmployeeWork"),"EmployeeWorkTemplate.xlsx") },  //工作经历
            {"importempcontract",new Tuple<string, string, string>("EmployeeContractTemp",(Config.IsDM ? "\"JHR\".usp_Import_EmployeeContract" : "usp_Import_EmployeeContract"),"EmployeeContractTemplate.xlsx") },  //合同
            {"importempsoc",new Tuple<string, string, string>("EmployeeSocialInsuranceTemp","","EmployeeSocialInsuranceTemplate.xlsx") },  //银行账号

            {"importotheremp",new Tuple<string, string, string>("OtherEmployeeInfoTemp",(Config.IsDM ? "\"JHR\".usp_Import_OtherEmployeeInfo" : "usp_Import_OtherEmployeeInfo"),"OtherEmployeeInfoTemplate.xlsx") },  //其他人员库

            {"importseniority",new Tuple<string, string, string>("","","SeniorityTemplate.xlsx") },  //工龄津贴

            {"importsalaryScale",new Tuple<string, string, string>("","","SalaryScaleTemplate.xlsx") },  //薪级工资

            {"importemployeeSalaryBase",new Tuple<string, string, string>("","","EmployeeSalaryBaseTemplate.xlsx") },  //员工社保公积金基数

            {"importsalaryEmployeeOverseasReissueDetail",new Tuple<string, string, string>("","","EmployeeOverseasReissueDetailTemplate.xlsx") },  //回国人员恢复工资补发

            {"importRetiredEmployeeReissue",new Tuple<string, string, string>("","","RetiredEmployeeReissueTemplate.xlsx") },  //退休人员补发

            {"importsalaryGeneralHospitalAdminDuty",new Tuple<string, string, string>("","","GeneralHospitalAdminDutyAllowanceTemplate.xlsx") },  //总院行政值班费
            {"importShiftAllowance",new Tuple<string, string, string>("","","ShiftAllowanceTemplate.xlsx") },  //一值班二值班费
            {"importMiddleNightShiftAllowance",new Tuple<string, string, string>("","","MiddleNightShiftAllowanceTemplate.xlsx") },  //中夜班费
            {"importAttendanceHealthAllowance",new Tuple<string, string, string>("","","AttendanceHealthAllowanceTemplate.xlsx") },  //考勤卫生津贴
            {"importsalaryOvertimeAllowance",new Tuple<string, string, string>("","","OvertimeAllowanceTemplate.xlsx") },  //加班费
            {"importSalaryLeave",new Tuple<string, string, string>("","","SalaryLeaveTemplate.xlsx") },  //病产假
            {"importSporadicSalaryLeave",new Tuple<string, string, string>("","","SporadicSalaryLeaveTemplate.xlsx") },  //非月薪病产假
            {"importNonMonthlySalaryLeave",new Tuple<string, string, string>("","","NonMonthlySalaryLeaveTemplate.xlsx") },  //零星补病产假
            {"importAssistanceForeign",new Tuple<string, string, string>("","","AssistanceForeignTemplate.xlsx") },  //援外津贴
            {"importAssistanceYunnan",new Tuple<string, string, string>("","","AssistanceYunnanTemplate.xlsx") },  //援滇津贴
            {"importPostdoctoralHousingAllowance",new Tuple<string, string, string>("","","PostdoctoralHousingAllowanceTemplate.xlsx") },  //博士后房贴
            {"importSalaryExtendedDetails",new Tuple<string, string, string>("","","SalaryExtendedDetailsTemplate.xlsx") },  //通用补发/补扣
            {"importEmployeeSalaryBase",new Tuple<string, string, string>("","","EmployeeSalaryBaseTemplate.xlsx") },  //员工社保公积金基数
            {"importEmployeeSalaryCorrectionMonth",new Tuple<string, string, string>("","","EmployeeSalaryCorrectionMonthTemplate.xlsx") },  //员工修正(月薪)
        };
    }
}