(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9d6e4780"],{"40fe":function(t,e,a){},"5ce7":function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:t._u([{key:"main",fn:function(){return[a("el-row",{staticClass:"filter-container",attrs:{gutter:11,type:"flex"}},[a("el-col",{attrs:{span:4}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"岗位类型"},model:{value:t.listQuery.stationId,callback:function(e){t.$set(t.listQuery,"stationId",e)},expression:"listQuery.stationId"}},t._l(t.stationList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("el-col",{attrs:{span:4}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"薪级",max:999999999,min:0,precision:0,controls:!1},model:{value:t.listQuery.scale,callback:function(e){t.$set(t.listQuery,"scale",e)},expression:"listQuery.scale"}})],1),a("el-col",{staticClass:"filter-button",attrs:{span:8}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.search}},[t._v("查询")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(e){return t.showDialog()}}},[t._v("添加")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-download"},on:{click:t.downloadexceltemplate}},[t._v("下载模板")]),a("el-upload",{staticStyle:{"margin-left":"10px"},attrs:{action:"","http-request":t.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{slot:"trigger",icon:"el-icon-upload2",type:"primary"},slot:"trigger"},[t._v("导入")])],1),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-download"},on:{click:t.exportExcel}},[t._v("导出")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:t.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","default-sort":{prop:"Station.Name",order:"ascending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":t.sortChange}},[a("el-table-column",{attrs:{sortable:"custom",prop:"Station.Name",label:"岗位类型"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.stationName))])]}}])}),a("el-table-column",{attrs:{sortable:"custom",prop:"Scale",label:"薪级"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.scale))])]}}])}),a("el-table-column",{attrs:{sortable:"custom",prop:"Wage",label:"工资标准"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(t._f("formatMoney2")(l.wage)))])]}}])}),a("el-table-column",{attrs:{label:"备注","min-width":"150px ","header-align":"center",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",{staticStyle:{"white-space":"pre-wrap"}},[t._v(t._s(l.memo))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","header-align":"center",width:"160","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("el-button",{staticStyle:{"margin-left":"5px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(e){return t.showDialog(l)}}},[t._v(" 编辑 ")]),t._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[10,20,50],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.getPageList}})]},proxy:!0}])}),a("editDialog",{ref:"editDialog",on:{refreshData:t.getPageList}})],1)},i=[],o=(a("ac1f"),a("841c"),a("d368")),n=a("e44c"),s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-dialog",{attrs:{title:t.title,visible:t.showDialog,width:"40%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:t.closeDialog}},[a("el-form",{ref:"dataForm",attrs:{rules:t.rules,model:t.dataModel,"label-width":"100px"}},[a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"岗位类型",prop:"stationId"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{disabled:t.isEdit,filterable:"",clearable:"",placeholder:"岗位类型"},model:{value:t.dataModel.stationId,callback:function(e){t.$set(t.dataModel,"stationId",e)},expression:"dataModel.stationId"}},t._l(t.stationList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"薪级",prop:"scale"}},[a("el-input",{staticClass:"numrule",attrs:{type:"number",disabled:t.isEdit,placeholder:"薪级",clearable:""},model:{value:t.dataModel.scale,callback:function(e){t.$set(t.dataModel,"scale",e)},expression:"dataModel.scale"}})],1)],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"薪级工资",prop:"wage"}},[a("el-input",{staticClass:"numrule",staticStyle:{width:"100%"},attrs:{type:"number",placeholder:"薪级工资"},on:{change:t.wageChange},model:{value:t.dataModel.wage,callback:function(e){t.$set(t.dataModel,"wage",e)},expression:"dataModel.wage"}})],1)],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{type:"textarea",rows:3,clearable:"",placeholder:"备注"},model:{value:t.dataModel.memo,callback:function(e){t.$set(t.dataModel,"memo",e)},expression:"dataModel.memo"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:t.closeDialog}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:t.btnSaveLoading},on:{click:t.saveDialog}},[t._v("保 存")])],1)],1)],1)},r=[],c=(a("a9e3"),a("b680"),a("5319"),{data:function(){var t=function(t,e,a){/^[+]{0,1}(\d+)$|^[+]{0,1}$/.test(e)?e<=0?a(new Error("必须大于0")):a():a(new Error("请输入正整数"))};return{showDialog:!1,title:"",stationList:[],rules:{stationId:[{required:!0,message:"岗位类型必选",trigger:"change"}],scale:[{required:!0,message:"请输入薪级",trigger:"blur"},{validator:t,trigger:"blur"}],wage:[{required:!0,trigger:"blur",message:"薪级工资必填"},{pattern:/(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/,trigger:"blur",message:"薪级工资必须大于零，且最多两位小数"}]},btnSaveLoading:!1,isEdit:!1,dataModel:{}}},methods:{initDialog:function(t){this.queryOneLevelStation(),t?(this.title="编辑薪级工资",this.isEdit=!0,this.getData(t.id)):(this.title="新增薪级工资",this.isEdit=!1),this.showDialog=!0},getData:function(t){var e=this;o["a"].getSalaryScale({id:t}).then((function(t){t.succeed&&(e.dataModel=t.data)})).catch((function(t){}))},saveDialog:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&(t.btnSaveLoading=!0,t.isEdit?o["a"].updateSalaryScale(t.dataModel).then((function(e){e.succeed&&(t.$message({message:"修改成功",type:"success"}),t.btnSaveLoading=!1,t.$emit("refreshData"),t.closeDialog())})).catch((function(e){t.btnSaveLoading=!1})):o["a"].addSalaryScale(t.dataModel).then((function(e){e.succeed&&(t.$message({message:"添加成功",type:"success"}),t.btnSaveLoading=!1,t.$emit("refreshData"),t.closeDialog())})).catch((function(e){t.btnSaveLoading=!1})))}))},wageChange:function(){var t=(this.dataModel.wage+"").replace(/,/g,""),e=Number(t).toFixed(2);this.$set(this.dataModel,"wage",Number(e))},queryOneLevelStation:function(){var t=this;o["a"].queryOneLevelStation().then((function(e){t.stationList=e.data.datas})).catch((function(t){console.log(t)}))},closeDialog:function(){this.dataModel={},this.showDialog=!1,this.$refs.dataForm.resetFields()}}}),d=c,u=(a("7ee8"),a("9a62"),a("2877")),g=Object(u["a"])(d,s,r,!1,null,"75306f3b",null),p=g.exports,f={components:{editDialog:p},data:function(){return{addForm:{},dataList:[],stationList:[],total:0,listQuery:{pageIndex:1,pageSize:10,order:"+Station.Name",stationId:null},listLoading:!1,temp:{}}},created:function(){this.queryOneLevelStation(),this.getPageList()},methods:{search:function(){this.listQuery.pageIndex=1,this.getPageList()},sortChange:function(t,e,a){this.listQuery.pageIndex=1;var l="";"descending"===t.order&&(l="-"),"ascending"===t.order&&(l="+"),this.listQuery.order=l+t.prop,this.getPageList()},importExcel:function(t){var e=this,a=t.file,l=new FormData;o["a"].importSalaryScale(a,l).then((function(t){t.succeed&&(e.$message({message:"导入成功",type:"success"}),e.search())})).catch((function(t){e.search()}))},exportExcel:function(){var t=this;o["a"].exportSalaryScale(this.listQuery).then((function(e){var l=a("19de"),i="薪级工资"+t.$moment().format("YYYYMMDDHHmmss")+".xlsx";e.data?l(e.data,i):l(e,i)}))},downloadexceltemplate:function(){n["a"].downlodaImportExcelTemplate({type:"importsalaryScale"}).then((function(t){var e=a("19de"),l="SalaryScaleTemplate.xlsx";t.data?e(t.data,l):e(t,l)})).catch((function(t){}))},getPageList:function(){var t=this;this.listLoading=!0,o["a"].querySalaryScale(this.listQuery).then((function(e){t.listLoading=!1,e.succeed?(t.dataList=e.data.datas,t.total=e.data.recordCount,t.listQuery.pageIndex=e.data.pageIndex):t.$notice.resultTip(e)})).catch((function(e){console.log(e),t.listLoading=!1}))},queryOneLevelStation:function(){var t=this;o["a"].queryOneLevelStation().then((function(e){t.stationList=e.data.datas,console.log(t.stationList)})).catch((function(t){console.log(t)}))},deleteSalaryScale:function(t){var e=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){o["a"].deleteSalaryScale(t).then((function(t){t.succeed?(e.getPageList(),e.$notice.message("删除成功","success")):t.succeed||e.$notice.message("删除失败，请联系管理员","info")})).catch((function(t){e.listLoading=!1,console.log(t)}))})).catch((function(t){e.listLoading=!1}))},showDialog:function(t){this.$refs.editDialog.initDialog(t)},closeAddDialog:function(){this.$refs["ref_addForm"].resetFields(),this.$refs["ref_addForm"].clearValidate()}}},m=f,h=Object(u["a"])(m,l,i,!1,null,null,null);e["default"]=h.exports},"7ee8":function(t,e,a){"use strict";var l=a("40fe"),i=a.n(l);i.a},"9a62":function(t,e,a){"use strict";var l=a("b5b1"),i=a.n(l);i.a},b5b1:function(t,e,a){}}]);