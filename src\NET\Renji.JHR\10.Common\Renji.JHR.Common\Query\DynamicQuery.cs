﻿using Renji.JHR.Common.Configration;
using Renji.JHR.Common.Consts;
using Renji.JHR.Entities;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace Renji.JHR.Common
{
    public class DynamicQuery
    {
        #region GetDynamicQuery

        /// <summary>
        /// 动态获取查询表达式
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public Expression<Func<T, bool>> GetDynamicQuery<T>(List<QueryCondition> queryConditionList)
        {
            ParameterExpression param = Expression.Parameter(typeof(T), "e");

            Expression constantExpression = GetDynamicQueryExpression<T>(queryConditionList, param);

            //生成表达式
            return (Expression<Func<T, bool>>)Expression.Lambda(constantExpression, param);
        }

        private Expression GetDynamicQueryExpression<T>(List<QueryCondition> queryConditionList, ParameterExpression param, bool objectValue = true)
        {
            Expression constantExpression = Expression.Constant(objectValue);

            foreach (var queryCondition in queryConditionList)
            {
                if (queryCondition.Children != null && queryCondition.Children.Any())
                {
                    var v = queryCondition.Children.First().EnumLogicRelationship == LogicRelationships.OR ? false : true;
                    var expre = GetDynamicQueryExpression<T>(queryCondition.Children, param, v);
                    switch (queryCondition.EnumLogicRelationship)
                    {
                        case LogicRelationships.AND:
                            constantExpression = Expression.And(constantExpression, expre);
                            break;

                        case LogicRelationships.OR:
                            constantExpression = Expression.Or(constantExpression, expre);
                            break;

                        default:
                            throw new NotImplementedException("不支持此操作");
                    }
                }
                else
                {
                    var propertyValue = queryCondition.Keywords;

                    //如果该查询条件值为空或者null，且操作符需要参数，不添加动态lambda表达式
                    if (propertyValue == null)
                    {
                        //if (!(queryCondition.EnumOperation == Entities.Operation.IsNull
                        //    || queryCondition.EnumOperation == Entities.Operation.NotNull
                        //    || queryCondition.EnumOperation == Entities.Operation.NotNullAndNotEmpty))
                        //{
                        //    continue;
                        //}
                    }
                    if (string.IsNullOrWhiteSpace(queryCondition.EntityColumnType))
                        continue;
                    if (string.IsNullOrWhiteSpace(queryCondition.EntityColumnName))
                        continue;

                    Expression? propertyExpression = null;
                    var propertyType = Type.GetType(queryCondition.EntityColumnType);

                    Expression? memberExpression = null;

                    var entityArray = queryCondition.EntityColumnName.Split('.');
                    memberExpression = GetMemberExpression(param, typeof(T), entityArray, 0);

                    #region String 类型查询条件

                    if (memberExpression.Type == typeof(string))
                    {
                        if (string.IsNullOrWhiteSpace(propertyValue))
                        {
                            // if (!(queryCondition.EnumOperation == Entities.Operation.IsNull
                            //|| queryCondition.EnumOperation == Entities.Operation.NotNull
                            //|| queryCondition.EnumOperation == Entities.Operation.NotNullAndNotEmpty))
                            // {
                            //     continue;
                            // }
                        }
                        //因为CompareTo有重载，所以这里指定了下参数的类型，否则会报反射异常
                        var methodInfo = typeof(String).GetMethod("CompareTo", new Type[] { typeof(String) })!;

                        switch (queryCondition.EnumOperation)
                        {
                            case Entities.Operations.Contains:
                                propertyExpression = Expression.Call(memberExpression,
                                                       typeof(string).GetMethod("Contains", new Type[] { typeof(string) })!,
                                                       Expression.Constant(propertyValue));
                                break;

                            case Entities.Operations.Equals:

                                propertyExpression = Expression.Call(memberExpression,
                                                             typeof(string).GetMethod("Equals", new Type[] { typeof(string) })!,
                                                             Expression.Constant(propertyValue));
                                break;

                            case Entities.Operations.NotEqual:

                                propertyExpression = Expression.NotEqual(Expression.Call(memberExpression,
                                                                            methodInfo,
                                                                            Expression.Constant(propertyValue, typeof(String))),
                                                                            Expression.Constant(0, typeof(Int32))
                                                                        );
                                break;

                            case Entities.Operations.GreaterThan:

                                //比较String.CompareTo的返回结果和0，来实现>的效果
                                propertyExpression = Expression.GreaterThan(Expression.Call(memberExpression,
                                                                                methodInfo,
                                                                                Expression.Constant(propertyValue, typeof(String))),
                                                                                Expression.Constant(0, typeof(Int32))
                                                                            );
                                break;

                            case Entities.Operations.GreaterThanOrEqual:

                                //比较String.CompareTo的返回结果和0，来实现>=的效果
                                propertyExpression = Expression.GreaterThanOrEqual(Expression.Call(memberExpression,
                                                                                        methodInfo,
                                                                                        Expression.Constant(propertyValue, typeof(String))),
                                                                                        Expression.Constant(0, typeof(Int32))
                                                                                    );
                                break;

                            case Entities.Operations.LessThan:

                                //比较String.CompareTo的返回结果和0，来实现<的效果
                                propertyExpression = Expression.LessThan(Expression.Call(memberExpression,
                                                                                                         methodInfo,
                                                                                                         Expression.Constant(propertyValue, typeof(String))),
                                                                                                         Expression.Constant(0, typeof(Int32))
                                                                                                       );
                                break;

                            case Entities.Operations.LessThanOrEqual:

                                //比较String.CompareTo的返回结果和0，来实现<=的效果
                                propertyExpression = Expression.LessThanOrEqual(Expression.Call(memberExpression,
                                                                                                          methodInfo,
                                                                                                          Expression.Constant(propertyValue, typeof(String))),
                                                                                                          Expression.Constant(0, typeof(Int32))
                                                                                                        );
                                break;
                            //case Entities.Operation.InList:
                            //    if (propertyValue.Contains(','))
                            //    {
                            //        var keywords = propertyValue.Split(',');

                            //        var filters = keywords.Select(key => Expression.Call(memberExpression,
                            //                                                            typeof(string).GetMethod("Contains", new Type[] { typeof(string) }),
                            //                                                            Expression.Constant(key))).Cast<Expression>().ToList();

                            //        if (filters.Count == 1)
                            //            propertyExpression = filters[0];

                            //        if (filters.Count > 1)
                            //        {
                            //            propertyExpression = Expression.Constant(false);
                            //            propertyExpression = filters.Aggregate(propertyExpression, Expression.Or);
                            //        }
                            //    }
                            //    else
                            //    {
                            //        propertyExpression = Expression.Call(memberExpression,
                            //                               typeof(string).GetMethod("Contains", new Type[] { typeof(string) }),
                            //                               Expression.Constant(propertyValue));
                            //    }
                            //    break;
                            //case Entities.Operation.NotInList:
                            //    if (propertyValue.Contains(','))
                            //    {
                            //        var keywords = propertyValue.Split(',');
                            //        var filters = (from key in keywords
                            //                       let left = memberExpression
                            //                       let right = Expression.Constant(key)
                            //                       select Expression.NotEqual(left, right)).Cast<Expression>().ToList();

                            //        if (filters.Count == 1)
                            //            propertyExpression = filters[0];

                            //        if (filters.Count > 1)
                            //        {
                            //            propertyExpression = Expression.Constant(true);
                            //            propertyExpression = filters.Aggregate(propertyExpression, Expression.And);
                            //        }
                            //    }
                            //    break;
                            //case Entities.Operation.IsNull:
                            //    propertyExpression = Expression.Call(memberExpression,
                            //                                 typeof(string).GetMethod("Equals", new Type[] { typeof(string) }),
                            //                                 Expression.Constant(null, typeof(String)));
                            //    break;
                            //case Entities.Operation.NotNull:
                            //    propertyExpression = Expression.NotEqual(Expression.Call(memberExpression,
                            //                                                methodInfo,
                            //                                                Expression.Constant(null, typeof(String))),
                            //                                                Expression.Constant(0, typeof(Int32))
                            //                                            );
                            //    break;
                            //case Entities.Operation.NotNullAndNotEmpty:
                            //    List<Expression> expressions = new List<Expression>();
                            //    expressions.Add(Expression.NotEqual(Expression.Call(memberExpression,
                            //                                            methodInfo,
                            //                                            Expression.Constant(null, typeof(String))),
                            //                                            Expression.Constant(0, typeof(Int32))
                            //                                        ));
                            //    expressions.Add(Expression.NotEqual(Expression.Call(memberExpression,
                            //                                            methodInfo,
                            //                                            Expression.Constant(string.Empty, typeof(String))),
                            //                                            Expression.Constant(0, typeof(Int32))
                            //                                        ));
                            //    propertyExpression = Expression.Constant(true);
                            //    propertyExpression = expressions.Aggregate(propertyExpression, Expression.And);
                            //    break;
                            //case Entities.Operation.StartsWith:
                            //    propertyExpression = Expression.Call(memberExpression,
                            //                           typeof(string).GetMethod("StartsWith", new Type[] { typeof(string) }),
                            //                           Expression.Constant(propertyValue));
                            //    break;
                            //case Entities.Operation.EndsWith:
                            //    propertyExpression = Expression.Call(memberExpression,
                            //                           typeof(string).GetMethod("EndsWith", new Type[] { typeof(string) }),
                            //                           Expression.Constant(propertyValue));
                            //    break;

                            default:
                                break;
                        }
                    }

                    #endregion String 类型查询条件

                    #region DateTime 类型查询条件

                    //时间段查询条件
                    if (memberExpression.Type == typeof(DateTime))
                    {
                        var dateTime = SysDateTime.Now;
                        if (DateTime.TryParse(propertyValue, out dateTime))
                        {
                            var convertMember = Expression.Convert(memberExpression, typeof(DateTime));
                            switch (queryCondition.EnumOperation)
                            {
                                case Entities.Operations.Equals:
                                    propertyExpression = Expression.Equal(convertMember, Expression.Constant(dateTime, typeof(DateTime)));
                                    break;

                                case Entities.Operations.NotEqual:
                                    propertyExpression = Expression.NotEqual(convertMember, Expression.Constant(dateTime, typeof(DateTime)));
                                    break;

                                case Entities.Operations.GreaterThan:
                                    propertyExpression = Expression.GreaterThan(convertMember, Expression.Constant(dateTime, typeof(DateTime)));
                                    break;

                                case Entities.Operations.GreaterThanOrEqual:
                                    propertyExpression = Expression.GreaterThanOrEqual(convertMember, Expression.Constant(dateTime, typeof(DateTime)));
                                    break;

                                case Entities.Operations.LessThan:
                                    propertyExpression = Expression.LessThan(convertMember, Expression.Constant(dateTime, typeof(DateTime)));
                                    break;

                                case Entities.Operations.LessThanOrEqual:
                                    propertyExpression = Expression.LessThanOrEqual(convertMember, Expression.Constant(dateTime, typeof(DateTime)));
                                    break;

                                default:
                                    break;
                            }
                        }
                    }

                    #endregion DateTime 类型查询条件

                    #region int类型

                    if (memberExpression.Type == typeof(int))
                    {
                        int value = 0;
                        if (int.TryParse(propertyValue, out value))
                        {
                            var convertMember = Expression.Convert(memberExpression, typeof(int));

                            switch (queryCondition.EnumOperation)
                            {
                                case Entities.Operations.Equals:
                                    propertyExpression = Expression.Equal(convertMember, Expression.Constant(value));
                                    break;

                                case Entities.Operations.NotEqual:
                                    propertyExpression = Expression.NotEqual(convertMember, Expression.Constant(value));
                                    break;

                                case Entities.Operations.GreaterThan:
                                    propertyExpression = Expression.GreaterThan(convertMember, Expression.Constant(value));
                                    break;

                                case Entities.Operations.GreaterThanOrEqual:
                                    propertyExpression = Expression.GreaterThanOrEqual(convertMember, Expression.Constant(value));
                                    break;

                                case Entities.Operations.LessThan:
                                    propertyExpression = Expression.LessThan(convertMember, Expression.Constant(value));
                                    break;

                                case Entities.Operations.LessThanOrEqual:
                                    propertyExpression = Expression.LessThanOrEqual(convertMember, Expression.Constant(value));
                                    break;

                                default:
                                    break;
                            }
                        }
                    }

                    #endregion int类型

                    if (memberExpression.Type == typeof(decimal))
                    {
                        decimal value = 0;
                        if (decimal.TryParse(propertyValue, out value))
                        {
                            var convertMember = Expression.Convert(memberExpression, typeof(decimal));
                            switch (queryCondition.EnumOperation)
                            {
                                case Entities.Operations.Equals:
                                    propertyExpression = Expression.Equal(convertMember, Expression.Constant(value));
                                    break;

                                case Entities.Operations.NotEqual:
                                    propertyExpression = Expression.NotEqual(convertMember, Expression.Constant(value));
                                    break;

                                case Entities.Operations.GreaterThan:
                                    propertyExpression = Expression.GreaterThan(convertMember, Expression.Constant(value));
                                    break;

                                case Entities.Operations.GreaterThanOrEqual:
                                    propertyExpression = Expression.GreaterThanOrEqual(convertMember, Expression.Constant(value));
                                    break;

                                case Entities.Operations.LessThan:
                                    propertyExpression = Expression.LessThan(convertMember, Expression.Constant(value));
                                    break;

                                case Entities.Operations.LessThanOrEqual:
                                    propertyExpression = Expression.LessThanOrEqual(convertMember, Expression.Constant(value));
                                    break;

                                default:
                                    break;
                            }
                        }
                    }

                    if (memberExpression.Type == typeof(bool))
                    {
                        bool value = false;
                        if (bool.TryParse(propertyValue, out value))
                        {
                            var convertMember = Expression.Convert(memberExpression, typeof(bool));
                            switch (queryCondition.EnumOperation)
                            {
                                case Entities.Operations.Equals:
                                    propertyExpression = Expression.Equal(convertMember, Expression.Constant(value));
                                    break;

                                case Entities.Operations.NotEqual:
                                    propertyExpression = Expression.NotEqual(convertMember, Expression.Constant(value));
                                    break;

                                default:
                                    break;
                            }
                        }
                    }

                    if (propertyExpression != null)
                    {
                        switch (queryCondition.EnumLogicRelationship)
                        {
                            case Entities.LogicRelationships.AND:
                                constantExpression = Expression.And(constantExpression, propertyExpression);
                                break;

                            case Entities.LogicRelationships.OR:
                                constantExpression = Expression.Or(constantExpression, propertyExpression);
                                break;

                            default:
                                throw new NotImplementedException("不支持此操作");
                        }
                    }
                }
            }
            return constantExpression;
        }

        /// <summary>
        /// 获取Member表达式
        /// </summary>
        /// <param name="member"></param>
        /// <param name="type"></param>
        /// <param name="property"></param>
        /// <param name="index"></param>
        /// <returns></returns>
        private Expression GetMemberExpression(Expression member, Type type, string[] property, int index)
        {
            var prop = type.GetProperty(property[index])!;

            return index == property.Length - 1 ? Expression.Property(member, prop) : GetMemberExpression(Expression.Property(member, prop), prop.PropertyType, property, ++index);
        }

        #endregion GetDynamicQuery

        #region GetSqlQuery

        public string GetSqlQuery(List<QueryCondition> queryConditionList)
        {
            string expression = "";

            foreach (var condition in queryConditionList)
            {
                expression += GetSqlQueryExpression(condition);
            }

            return expression;
        }

        private string GetSqlQueryExpression(QueryCondition queryCondition)
        {
            string expression = "";

            switch (queryCondition.EnumLogicRelationship)
            {
                case Entities.LogicRelationships.AND:
                    expression += " And ";
                    break;

                case Entities.LogicRelationships.OR:
                    expression += " Or ";
                    break;

                default:
                    throw new NotImplementedException("不支持此操作");
            }
            if (Config.IsDM)
            {
                var str = queryCondition.EntityColumnName!.Split('.');
                if (str.Length > 1)
                {
                    expression += $"\"JHR\".\"{str[0]}\".\"{str[1]}\"";
                }
            }
            else
            {
                expression += queryCondition.EntityColumnName;
            }
            

            FieldType ftype = queryCondition.EntityColumnType?.ToEnum<FieldType>() ?? FieldType.String;

            switch (queryCondition.EnumOperation)
            {
                case Operations.NotEqual:
                    if (string.IsNullOrEmpty(queryCondition.Keywords))
                    {
                        expression += " is not null ";
                    }
                    else
                    {
                        expression += string.Format(" != '{0}'", queryCondition.Keywords);
                    }
                    break;

                case Operations.Equals:
                    if (string.IsNullOrEmpty(queryCondition.Keywords))
                    {
                        expression += " is null ";
                    }
                    else
                    {
                        expression += string.Format(" = '{0}'", queryCondition.Keywords);
                    }
                    break;

                case Operations.GreaterThan:
                    if (!(ftype == FieldType.Bool || ftype == FieldType.Dict))
                    {
                        if (string.IsNullOrEmpty(queryCondition.Keywords))
                            expression += " > NULL ";
                        else
                            expression += string.Format(" > '{0}'", queryCondition.Keywords);
                    }
                    else
                        expression = string.Empty;
                    break;

                case Operations.GreaterThanOrEqual:
                    if (!(ftype == FieldType.Bool || ftype == FieldType.Dict))
                    {
                        if (string.IsNullOrEmpty(queryCondition.Keywords))
                            expression += " >= NULL ";
                        else
                            expression += string.Format(" >= '{0}'", queryCondition.Keywords);
                    }
                    else
                        expression = string.Empty;
                    break;

                case Operations.LessThan:
                    if (!(ftype == FieldType.Bool || ftype == FieldType.Dict))
                    {
                        if (string.IsNullOrEmpty(queryCondition.Keywords))
                            expression += " < NULL ";
                        else
                            expression += string.Format(" < '{0}'", queryCondition.Keywords);
                    }
                    else
                        expression = string.Empty;
                    break;

                case Operations.LessThanOrEqual:
                    if (!(ftype == FieldType.Bool || ftype == FieldType.Dict))
                    {
                        if (string.IsNullOrEmpty(queryCondition.Keywords))
                            expression += " <= NULL ";
                        else
                            expression += string.Format(" <= '{0}'", queryCondition.Keywords);
                    }
                    else
                        expression = string.Empty;
                    break;

                case Operations.Contains:
                    if (ftype == FieldType.String || ftype == FieldType.Numeric)
                    {
                        expression += string.Format(" like '%{0}%'", queryCondition.Keywords);
                    }
                    //else if(ftype == FieldType.Dict)
                    //{
                    //    expression += string.Format(" in ('{0}') ", queryCondition.Keywords);
                    //}
                    else
                    {
                        if (!string.IsNullOrEmpty(queryCondition.Keywords))
                            expression += string.Format(" in ('{0}') ", queryCondition.Keywords);
                        else
                            expression += " in ( NULL ) ";
                    }
                    break;
            }

            return expression;
        }

        #endregion GetSqlQuery
    }
}