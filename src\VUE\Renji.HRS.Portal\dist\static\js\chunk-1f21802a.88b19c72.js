(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1f21802a"],{"3f3f":function(t,e,o){"use strict";var a=o("96f4"),r=o.n(a);r.a},"96f4":function(t,e,o){},cbd2:function(t,e,o){"use strict";var a=o("cfe3"),r="AttendanceManage",n=new a["a"](r);e["a"]={getAttMonthShiftRecord:function(t){return n.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return n.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return n.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return n.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return n.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return n.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return n.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return n.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return n.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return n.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return n.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return n.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return n.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return n.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return n.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return n.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return n.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return n.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return n.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return n.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return n.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return n.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return n.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return n.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return n.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return n.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return n.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return n.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return n.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return n.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return n.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return n.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return n.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return n.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return n.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return n.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return n.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return n.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return n.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return n.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return n.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return n.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return n.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return n.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return n.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return n.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return n.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return n.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return n.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return n.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return n.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return n.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return n.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return n.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return n.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return n.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return n.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return n.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return n.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return n.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return n.get("GetSameDeptEmployeeWithHealthAllowance",t)}}},cc51:function(t,e,o){"use strict";o.r(e);var a=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"app-container"},[o("layout3",{scopedSlots:t._u([{key:"aside",fn:function(){return[o("el-button",{on:{click:t.syncTreeColor}},[t._v("颜色同步")]),o("el-button",{on:{click:t.batchConfirm}},[t._v("批量审批")]),o("c-tree",{ref:"tree",attrs:{options:t.treeData,props:t.treeProps,"expand-all":!0},on:{nodeClick:t.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[o("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.headModel}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"申请日期"}},[o("el-date-picker",{attrs:{type:"date",placeholder:"请选择申请日期","value-format":"yyyy-MM-dd"},on:{change:t.dateChange},model:{value:t.recordDate,callback:function(e){t.recordDate=e},expression:"recordDate"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"填表人"}},[t._v(" "+t._s(t.headModel.documentMaker)+" ")])],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"状态"}},[o("el-select",{attrs:{placeholder:"请选择"},on:{change:t.statusChanged},model:{value:t.status,callback:function(e){t.status=e},expression:"status"}},t._l(t.statusList,(function(t){return o("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"部门"}},[o("el-select",{attrs:{placeholder:"请选择"},model:{value:t.statusDept,callback:function(e){t.statusDept=e},expression:"statusDept"}},t._l(t.statusDeptList,(function(t){return o("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1)],1),o("el-row",[2==t.headModel.enumStatus?o("el-col",{attrs:{span:12}},[o("el-button",{attrs:{type:"primary"},on:{click:t.confirm}},[t._v("确认")]),o("el-button",{attrs:{type:"primary"},on:{click:t.reject}},[t._v("退回")])],1):t._e()],1),o("el-row",[2==t.headModel.enumStatus?o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:"退回原因"}},[o("el-input",{attrs:{type:"textarea"},model:{value:t.headModel.rejectReason,callback:function(e){t.$set(t.headModel,"rejectReason",e)},expression:"headModel.rejectReason"}})],1)],1):t._e()],1)],1),o("div",{staticClass:"tip_div"},[o("span",{staticClass:"tip_content"},[t._v(t._s(t.headModel.tip))])]),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",attrs:{data:t.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"}},[o("el-table-column",{attrs:{prop:"date",label:"工号",align:"center",width:"130",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[o("span",[t._v(t._s(a.empCode))])]}}])}),o("el-table-column",{attrs:{prop:"date",label:"姓名",align:"center",width:"160",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[o("span",[t._v(t._s(a.empName))])]}}])}),o("el-table-column",{attrs:{prop:"date",label:"在职方式",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[o("span",[t._v(t._s(a.hireStyleName))])]}}])}),o("el-table-column",{attrs:{prop:"name",label:"加班类型",align:"center",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[2==t.headModel.enumStatus?o("el-select",{attrs:{placeholder:"请选择"},model:{value:a.enumOverTimeType,callback:function(e){t.$set(a,"enumOverTimeType",e)},expression:"row.enumOverTimeType"}},t._l(t.overTimeType,(function(t){return o("el-option",{key:t.value,attrs:{label:t.desc,value:t.value}})})),1):o("span",[t._v(t._s(a.enumOverTimeTypeDesc))])]}}])}),o("el-table-column",{attrs:{prop:"name",label:"人事修改人",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[o("span",[t._v(t._s(a.updator))])]}}])})],1),o("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[20,50,100],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.paginationChanged}})]},proxy:!0}])})],1)},r=[],n=(o("99af"),o("4de4"),o("fb6a"),o("d3b7"),o("25f0"),o("4d90"),o("cbd2")),c=o("f9ac"),i={components:{},data:function(){return{recordDate:this.getNowTime(),headModel:{},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeLoading:!1,currentNode:null,total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,tableData:[],allData:[],treeExpandedKeys:[],status:"",statusList:[{label:"未提交",value:1},{label:"已提交",value:2},{label:"人事部门已确认",value:3}],statusDept:"",statusDeptList:[],overTimeType:[],allStatusDepts:[]}},created:function(){this.syncTreeColor(),this.loadOverTimeType()},methods:{loadOverTimeType:function(){var t=this;c["a"].getEnumInfos({enumType:"OverTimeType"}).then((function(e){t.overTimeType=e.data.datas})).catch((function(t){console.log(t)}))},getNowTime:function(){var t=new Date,e=t.getFullYear(),o=t.getMonth(),a=t.getDate();o+=1,o=o.toString().padStart(2,"0");var r="".concat(e,"-").concat(o,"-").concat(a);return r},resetCurrentNode:function(){this.currentNode=null},treeNodeClick:function(t){this.currentNode=t,this.listQuery.pageIndex=1,this.getAttHolidayOTRecord()},statusChanged:function(t){var e=this.allStatusDepts.filter((function(e,o,a){return e.status===t}))[0];e&&e.depts.length>0?this.statusDeptList=e.depts:(this.statusDeptList=[],this.statusDept="")},dateChange:function(){this.listQuery.pageIndex=1,this.syncTreeColor(),this.getAttHolidayOTRecord()},getAttHolidayOTRecord:function(){var t=this;if(this.currentNode){var e={RecordDate:this.recordDate,DeptId:this.currentNode.id};n["a"].getAttHolidayOTRecord(e).then((function(e){e.succeed?(t.headModel=e.data,console.log(t.headModel),t.queryAttHolidayOTRecordDetail(e.data.id)):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))}else this.$notice.message("请选择部门。","info")},queryAttHolidayOTRecordDetail:function(t){var e=this,o={RecordId:t,DeptId:this.currentNode.id,RecordMonth:this.recordDate};n["a"].queryAttHolidayOTRecordDetail(o).then((function(t){e.listLoading=!1,t.succeed?(e.allData=t.data.datas,e.total=t.data.recordCount,e.getTableData()):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},paginationChanged:function(){this.getTableData()},getTableData:function(){var t=this.listQuery.pageSize,e=this.listQuery.pageIndex;this.tableData=this.allData.slice((e-1)*t,e*t)},syncTreeColor:function(){var t=this,e={RecordDate:this.recordDate};n["a"].getColorDeptTree_HolidayOT(e).then((function(e){e.succeed&&(t.treeLoading=!1,t.treeData=e.data.colorDepts,t.treeExpandedKeys.push(e.data.colorDepts[0].id),t.allStatusDepts=e.data.statusDepts,t.currentNode&&t.$refs.tree.setCurrentKey(t.currentNode.id))})).catch((function(e){t.treeLoading=!1,console.log(e)}))},batchConfirm:function(){var t=this;if(this.recordDate){var e="你确定要确认"+this.recordDate+"的所有已经递交的节日加班费吗？";this.$confirm(e,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.headModel.RecordDate=t.recordDate,n["a"].batchConfirmAttHolidayOTRecord(t.headModel).then((function(e){e.succeed?(t.$notice.message("批量确认成功","success"),t.getAttHolidayOTRecord(),t.syncTreeColor()):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))}))}else this.$notice.message("请选择申请日期","warning")},confirm:function(){var t=this;this.recordDate&&this.currentNode&&this.$confirm("确认审批吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.headModel.details=t.allData,n["a"].ConfirmAttHolidayOTRecord(t.headModel).then((function(e){e.succeed?(t.$notice.message("人事已审阅","success"),t.getAttHolidayOTRecord(),t.syncTreeColor()):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))})),this.currentNode?this.recordDate||this.$notice.message("请选择申请日期","warning"):this.$notice.message("请选择部门","warning")},reject:function(){var t=this;this.recordDate&&this.currentNode&&this.$confirm("确认退回吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){"undefined"===typeof t.headModel.rejectReason||null===t.headModel.rejectReason||""===t.headModel.rejectReason?t.$notice.message("请填写退回原因！","warning"):(t.headModel.details=t.allData,n["a"].rejectAttHolidayOTRecord(t.headModel).then((function(e){e.succeed?(t.getAttHolidayOTRecord(),t.syncTreeColor(),t.$notice.message("操作成功","success")):t.$notice.resultTip(e)})).catch((function(t){console.log(t)})))})),this.currentNode?this.recordDate||this.$notice.message("请选择申请日期","warning"):this.$notice.message("请选择部门","warning")}}},l=i,d=(o("3f3f"),o("2877")),u=Object(d["a"])(l,a,r,!1,null,null,null);e["default"]=u.exports}}]);