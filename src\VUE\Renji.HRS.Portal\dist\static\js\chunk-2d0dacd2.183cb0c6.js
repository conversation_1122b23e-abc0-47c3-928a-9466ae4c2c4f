(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0dacd2"],{"6cc2":function(a,e,t){"use strict";t.r(e);var l=function(){var a=this,e=a.$createElement,t=a._self._c||e;return t("div",{staticClass:"app-container"},[t("layout4",{scopedSlots:a._u([{key:"main",fn:function(){return[t("el-row",{staticClass:"filter-container",attrs:{gutter:11,type:"flex"}},[t("el-col",{attrs:{span:5}},[t("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"参数"},model:{value:a.listQuery.name,callback:function(e){a.$set(a.listQuery,"name",e)},expression:"listQuery.name"}})],1),t("el-col",{attrs:{span:8}},[t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:a.search}},[a._v("查询")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:a.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:a.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","default-sort":{prop:"CreateTime",order:"descending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":a.sortChange}},[t("el-table-column",{attrs:{sortable:"custom",prop:"Name",label:"参数"},scopedSlots:a._u([{key:"default",fn:function(e){var l=e.row;return[t("span",[a._v(a._s(l.name))])]}}])}),t("el-table-column",{attrs:{sortable:"custom",prop:"Value",label:"值","header-align":"left",align:"right"},scopedSlots:a._u([{key:"default",fn:function(e){var l=e.row;return[t("span",[a._v(a._s(0==l.value||null==l.value||void 0==l.value||0==l.enumSalaryDataType||null==l.enumSalaryDataType||void 0==l.enumSalaryDataType?"-":l.value+" "+l.enumSalaryDataTypeDesc.slice(2)))])]}}])}),t("el-table-column",{attrs:{sortable:"custom",prop:"Remark",label:"备注"},scopedSlots:a._u([{key:"default",fn:function(e){var l=e.row;return[t("span",[a._v(a._s(l.remark))])]}}])}),t("el-table-column",{attrs:{label:"操作",align:"center","header-align":"center",width:"160","class-name":"small-padding fixed-width"},scopedSlots:a._u([{key:"default",fn:function(e){var l=e.row;return[t("el-button",{staticStyle:{"margin-left":"45px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(e){return a.showDialog(l)}}},[a._v(" 编辑 ")])]}}])})],1),t("c-pagination",{directives:[{name:"show",rawName:"v-show",value:a.total>0,expression:"total > 0"}],attrs:{total:a.total,"page-sizes":[10,20,50],page:a.listQuery.pageIndex,limit:a.listQuery.pageSize},on:{"update:page":function(e){return a.$set(a.listQuery,"pageIndex",e)},"update:limit":function(e){return a.$set(a.listQuery,"pageSize",e)},pagination:a.getPageList}})]},proxy:!0}])}),t("editDialog",{ref:"editDialog",on:{refreshData:a.getPageList}})],1)},o=[],i=t("2efc"),n=t("f9ac"),s=function(){var a=this,e=a.$createElement,t=a._self._c||e;return t("div",[t("el-dialog",{attrs:{title:a.title,visible:a.showDialog,width:"40%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:a.closeDialog}},[t("el-form",{ref:"dataForm",attrs:{rules:a.rules,model:a.dataModel,"label-width":"100px"}},[t("el-row",[t("el-col",[t("el-form-item",{attrs:{label:"参数名",prop:"name"}},[t("span",[a._v(a._s(a.dataModel.name))])])],1)],1),t("el-row",[t("el-col",[t("el-form-item",{attrs:{label:"类型",prop:"enumSalaryDataType"}},[t("span",[a._v(a._s(a.dataModel.enumSalaryDataTypeDesc))])])],1)],1),t("el-row",[t("el-col",[t("el-form-item",{attrs:{label:"值",prop:"value"}},[t("el-input",{attrs:{type:"textarea",rows:1,maxlength:"500",placeholder:"值"},model:{value:a.dataModel.value,callback:function(e){a.$set(a.dataModel,"value",e)},expression:"dataModel.value"}})],1)],1)],1),t("el-row",[t("el-col",[t("el-form-item",{attrs:{label:"备注"}},[t("el-input",{attrs:{type:"textarea",rows:3,maxlength:"100",placeholder:"备注"},model:{value:a.dataModel.remark,callback:function(e){a.$set(a.dataModel,"remark",e)},expression:"dataModel.remark"}})],1)],1)],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:a.closeDialog}},[a._v("取 消")]),t("el-button",{attrs:{type:"primary",loading:a.btnSaveLoading},on:{click:a.saveDialog}},[a._v("保 存")])],1)],1)],1)},r=[],c={data:function(){var a=function(a,e,t){if(e){var l=/^(\d|[1-9]\d+)(\.\d{1,2})?$/;l.test(e)?t():t(new Error("请输入正确格式数字,小数不超过2位"))}else t()};return{showDialog:!1,title:"",rules:{value:[{required:!0,message:"值不能为空"},{validator:a,trigger:"blur"}]},btnSaveLoading:!1,dataModel:{},salaryDataTypeList:[],salaryDataPayTypeList:[]}},methods:{initDialog:function(a){this.title="编辑基础参数",this.getData(a.id),this.showDialog=!0},getData:function(a){var e=this;i["a"].getSalaryData({id:a}).then((function(a){a.succeed&&(e.dataModel=a.data)})).catch((function(a){console.log(a)}))},saveDialog:function(){var a=this;this.$refs["dataForm"].validate((function(e){e&&(a.btnSaveLoading=!0,i["a"].updateSalaryData(a.dataModel).then((function(e){e.succeed&&(a.$message({message:"修改成功",type:"success"}),a.btnSaveLoading=!1,a.$emit("refreshData"),a.closeDialog())})).catch((function(e){a.btnSaveLoading=!1})))}))},closeDialog:function(){this.dataModel={},this.showDialog=!1,this.$refs.dataForm.resetFields()},initSalaryDataTypeList:function(){var a=this,e={enumType:"SalaryDataType"};n["a"].getEnumInfos(e).then((function(e){a.salaryDataTypeList=e.data.datas})).catch((function(a){console.log(a)}))}}},d=c,u=t("2877"),p=Object(u["a"])(d,s,r,!1,null,null,null),g=p.exports,f={components:{editDialog:g},data:function(){return{addForm:{},dataList:[],salaryDataPayTypeList:[],total:0,listQuery:{pageIndex:1,pageSize:10,order:"+CreateTime",enumSalaryDataPayType:null},listLoading:!1}},created:function(){this.loadSalaryDataType(),this.getPageList()},methods:{getPageList:function(){var a=this;this.listLoading=!0,i["a"].querySalaryData(this.listQuery).then((function(e){a.listLoading=!1,e.succeed?(a.dataList=e.data.datas,a.total=e.data.recordCount,a.listQuery.pageIndex=e.data.pageIndex):a.$notice.resultTip(e)})).catch((function(e){console.log(e),a.listLoading=!1}))},loadSalaryDataType:function(){var a=this;n["a"].getEnumInfos({enumType:"SalaryDataType"}).then((function(e){a.salaryDataTypeList=e.data.datas})).catch((function(a){console.log(a)}))},search:function(){this.listQuery.pageIndex=1,this.getPageList()},sortChange:function(a,e,t){this.listQuery.pageIndex=1;var l="";"descending"===a.order&&(l="-"),"ascending"===a.order&&(l="+"),this.listQuery.order=l+a.prop,this.getPageList()},showDialog:function(a){this.$refs.editDialog.initDialog(a)}}},m=f,y=Object(u["a"])(m,l,o,!1,null,null,null);e["default"]=y.exports}}]);