﻿CREATE TABLE [dbo].[RetireType]
(
    [ID]                    UNIQUEIDENTIFIER NOT NULL,
    [HireStyleId]           UNIQUEIDENTIFIER NOT NULL,
    [OfficialRankId]        UNIQUEIDENTIFIER NOT NULL,
    [RetireAge]            INT             NOT NULL,
    [Deleted]               BIT              NOT NULL,
    [Creator]               NVARCHAR (50)    NULL,
    [CreateTime]            DATETIME         NULL,
    [LastEditor]            NVARCHAR (50)    NULL,
    [LastEditTime]          DATETIME         NULL,
    CONSTRAINT [PK_RetireType] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_RetireType_Dict_01_HireStyle] FOREIGN KEY ([HireStyleId]) REFERENCES [dbo].[Dict] ([ID]),
    CONSTRAINT [FK_RetireType_Dict_02_OfficialRank] FOREIGN KEY ([OfficialRankId]) REFERENCES [dbo].[Dict] ([ID])
)

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'在职方式',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RetireType',
    @level2type = N'COLUMN',
    @level2name = N'HireStyleId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'职别',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RetireType',
    @level2type = N'COLUMN',
    @level2name = N'OfficialRankId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'女性退休年龄（50，55）',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RetireType',
    @level2type = N'COLUMN',
    @level2name = N'RetireAge'