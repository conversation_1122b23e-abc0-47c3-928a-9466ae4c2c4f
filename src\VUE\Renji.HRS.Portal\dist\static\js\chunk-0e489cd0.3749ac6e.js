(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0e489cd0"],{"09a6":function(t,e,n){"use strict";n.r(e);var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("layout3",{scopedSlots:t._u([{key:"aside",fn:function(){return[n("el-button",{on:{click:t.syncTreeColor}},[t._v("颜色同步")]),n("el-button",{on:{click:t.batchConfirm}},[t._v("批量审批")]),n("c-tree",{ref:"tree",attrs:{options:t.treeData,props:t.treeProps,"expand-all":!0},on:{nodeClick:t.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[n("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.headModel}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"申领月份"}},[n("el-date-picker",{attrs:{type:"month",placeholder:"请选择申领月份","value-format":"yyyy-MM"},on:{change:t.dateChange},model:{value:t.recordMonth,callback:function(e){t.recordMonth=e},expression:"recordMonth"}})],1)],1),2==t.headModel.enumStatus?n("el-col",{attrs:{span:12}},[n("el-button",{attrs:{type:"primary"},on:{click:t.confirm}},[t._v("确认")]),n("el-button",{attrs:{type:"primary"},on:{click:t.reject}},[t._v("退回")])],1):t._e()],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"填表人"}},[t._v(" "+t._s(t.headModel.documentMaker)+" ")])],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"填表日期"}},[t._v(" "+t._s(t.headModel.documentMakeTimeStr)+" ")])],1)],1),n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"申领表状态"}},[t._v(" "+t._s(t.headModel.enumStatusDesc)+" ")])],1)],1),2==t.headModel.enumStatus?n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"退回原因"}},[n("el-input",{attrs:{type:"textarea"},model:{value:t.headModel.rejectReason,callback:function(e){t.$set(t.headModel,"rejectReason",e)},expression:"headModel.rejectReason"}})],1)],1)],1):t._e()],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:t.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"}},[n("el-table-column",{attrs:{prop:"date",label:"工号",align:"center",width:"60",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[n("span",[t._v(t._s(o.empCode))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"姓名",align:"center",width:"60",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[n("span",[t._v(t._s(o.empName))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"在职方式",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[n("span",[t._v(t._s(o.hireStyleName))])]}}])}),n("el-table-column",{attrs:{label:"班次",align:"center"}},[n("el-table-column",{attrs:{prop:"name",label:"中班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[2==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return o.zb=Math.abs(o.zb)}},model:{value:o.zb,callback:function(e){t.$set(o,"zb",t._n(e))},expression:"row.zb"}}):n("span",[t._v(t._s(o.zb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"夜班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[2==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return o.yb=Math.abs(o.yb)}},model:{value:o.yb,callback:function(e){t.$set(o,"yb",t._n(e))},expression:"row.yb"}}):n("span",[t._v(t._s(o.yb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"24小时班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[2==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return o.b24=Math.abs(o.b24)}},model:{value:o.b24,callback:function(e){t.$set(o,"b24",t._n(e))},expression:"row.b24"}}):n("span",[t._v(t._s(o.b24))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"急诊中班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[2==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return o.jzzb=Math.abs(o.jzzb)}},model:{value:o.jzzb,callback:function(e){t.$set(o,"jzzb",t._n(e))},expression:"row.jzzb"}}):n("span",[t._v(t._s(o.jzzb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"急诊夜班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[2==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return o.jzyb=Math.abs(o.jzyb)}},model:{value:o.jzyb,callback:function(e){t.$set(o,"jzyb",t._n(e))},expression:"row.jzyb"}}):n("span",[t._v(t._s(o.jzyb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"急诊24小时",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[2==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return o.jZ24=Math.abs(o.jZ24)}},model:{value:o.jZ24,callback:function(e){t.$set(o,"jZ24",t._n(e))},expression:"row.jZ24"}}):n("span",[t._v(t._s(o.jZ24))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"护理A档中班",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[2==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return o.hlazb=Math.abs(o.hlazb)}},model:{value:o.hlazb,callback:function(e){t.$set(o,"hlazb",t._n(e))},expression:"row.hlazb"}}):n("span",[t._v(t._s(o.hlazb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"护理A档夜班",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[2==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return o.hlayb=Math.abs(o.hlayb)}},model:{value:o.hlayb,callback:function(e){t.$set(o,"hlayb",t._n(e))},expression:"row.hlayb"}}):n("span",[t._v(t._s(o.hlayb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"护理A档24小时",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[2==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return o.hlA24=Math.abs(o.hlA24)}},model:{value:o.hlA24,callback:function(e){t.$set(o,"hlA24",t._n(e))},expression:"row.hlA24"}}):n("span",[t._v(t._s(o.hlA24))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"其他值班1",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[2==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return o.qT1=Math.abs(o.qT1)}},model:{value:o.qT1,callback:function(e){t.$set(o,"qT1",t._n(e))},expression:"row.qT1"}}):n("span",[t._v(t._s(o.qT1))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"其他值班2",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[2==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return o.qT2=Math.abs(o.qT2)}},model:{value:o.qT2,callback:function(e){t.$set(o,"qT2",t._n(e))},expression:"row.qT2"}}):n("span",[t._v(t._s(o.qT2))])]}}])})],1),n("el-table-column",{attrs:{prop:"name",label:"修改人",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[n("span",[t._v(t._s(o.updator))])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[20,50,100],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.paginationChanged}})]},proxy:!0}])})],1)},r=[],a=(n("99af"),n("fb6a"),n("d3b7"),n("25f0"),n("4d90"),n("d368")),i=n("cbd2"),c={components:{},data:function(){return{recordMonth:this.getNowTime(),headModel:{},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeLoading:!1,currentNode:null,total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,tableData:[],allData:[],treeExpandedKeys:[]}},created:function(){this.syncTreeColor()},methods:{getNowTime:function(){var t=new Date,e=t.getFullYear(),n=t.getMonth();n=n.toString().padStart(2,"0"),"00"===n&&(e-=1,n="12");var o="".concat(e,"-").concat(n);return o},loadTree:function(){var t=this;this.treeLoading=!0,a["a"].QueryOrganization({}).then((function(e){t.treeData=e.data,t.treeExpandedKeys.push(e.data[0].id)})).catch((function(t){console.log(t)})).finally((function(){t.treeLoading=!1})),this.resetCurrentNode()},resetCurrentNode:function(){this.currentNode=null},treeNodeClick:function(t){this.currentNode=t,this.listQuery.pageIndex=1,this.getAttMonthShiftRecord()},dateChange:function(){this.listQuery.pageIndex=1,this.syncTreeColor(),this.getAttMonthShiftRecord()},getAttMonthShiftRecord:function(){var t=this;if(this.currentNode){var e={RecordMonth:this.recordMonth,DeptId:this.currentNode.id};i["a"].getAttMonthShiftRecord(e).then((function(e){e.succeed?(t.headModel=e.data,t.queryAttMonthShiftRecordDetail(e.data.id)):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))}else this.$notice.message("请选择部门。","info")},queryAttMonthShiftRecordDetail:function(t){var e=this,n={RecordId:t,DeptId:this.currentNode.id,RecordMonth:this.recordMonth};i["a"].queryAttMonthShiftRecordDetail(n).then((function(t){e.listLoading=!1,t.succeed?(e.allData=t.data.datas,e.total=t.data.recordCount,e.getTableData()):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},paginationChanged:function(){this.getTableData()},getTableData:function(){var t=this.listQuery.pageSize,e=this.listQuery.pageIndex;this.tableData=this.allData.slice((e-1)*t,e*t)},syncTreeColor:function(){var t=this,e={RecordMonth:this.recordMonth};i["a"].getColorDeptTree_MiddleNightShift(e).then((function(e){e.succeed&&(t.treeData=e.data,t.treeExpandedKeys.push(e.data[0].id),t.currentNode&&t.$refs.tree.setCurrentKey(t.currentNode.id))})).catch((function(t){console.log(t)})).finally((function(){t.treeLoading=!1}))},batchConfirm:function(){var t=this;if(this.recordMonth){var e="你确定要确认"+this.recordMonth+"的所有已经递交的中夜班费吗？";this.$confirm(e,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.headModel.RecordMonth=t.recordMonth,i["a"].batchConfirmAttMonthShiftRecord(t.headModel).then((function(e){e.succeed?(t.$notice.message("批量确认成功","success"),t.getAttMonthShiftRecord(),t.syncTreeColor()):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))}))}else this.$notice.message("请选择月份","warning")},confirm:function(){var t=this;this.recordMonth&&this.currentNode&&this.$confirm("确认审批吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.headModel.details=t.allData,i["a"].ConfirmAttMonthShiftRecord(t.headModel).then((function(e){e.succeed?(t.$notice.message("人事已审阅","success"),t.getAttMonthShiftRecord(),t.syncTreeColor()):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))})),this.currentNode?this.recordMonth||this.$notice.message("请选择月份","warning"):this.$notice.message("请选择部门","warning")},reject:function(){var t=this;this.recordMonth&&this.currentNode&&this.$confirm("确认退回吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){"undefined"===typeof t.headModel.rejectReason||null===t.headModel.rejectReason||""===t.headModel.rejectReason?t.$notice.message("请填写退回原因！","warning"):(t.headModel.details=t.allData,i["a"].rejectAttMonthShiftRecord(t.headModel).then((function(e){e.succeed?(t.getAttMonthShiftRecord(),t.syncTreeColor(),t.$notice.message("操作成功","success")):t.$notice.resultTip(e)})).catch((function(t){console.log(t)})))})),this.currentNode?this.recordMonth||this.$notice.message("请选择月份","warning"):this.$notice.message("请选择部门","warning")}}},l=c,u=n("2877"),s=Object(u["a"])(l,o,r,!1,null,null,null);e["default"]=s.exports},cbd2:function(t,e,n){"use strict";var o=n("cfe3"),r="AttendanceManage",a=new o["a"](r);e["a"]={getAttMonthShiftRecord:function(t){return a.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return a.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return a.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return a.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return a.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return a.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return a.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return a.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return a.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return a.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return a.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return a.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return a.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return a.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return a.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return a.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return a.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return a.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return a.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return a.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return a.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return a.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return a.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return a.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return a.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return a.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return a.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return a.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return a.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return a.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return a.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return a.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return a.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return a.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return a.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return a.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return a.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return a.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return a.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return a.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return a.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return a.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return a.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return a.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return a.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return a.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return a.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return a.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return a.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return a.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return a.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return a.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return a.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return a.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return a.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return a.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return a.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return a.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return a.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return a.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return a.get("GetSameDeptEmployeeWithHealthAllowance",t)}}}}]);