(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-06b9bafe"],{"19fc":function(t,e,a){"use strict";var l=a("829e"),i=a.n(l);i.a},"829e":function(t,e,a){},"836d":function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:t._u([{key:"main",fn:function(){return[a("el-row",{staticClass:"filter-container",attrs:{gutter:5,type:"flex"}},[a("el-col",{attrs:{span:3}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"month",placeholder:"请选择月份",editable:!1,clearable:!1,"value-format":"yyyy-MM"},on:{change:function(e){return t.monthChange()}},model:{value:t.listQuery.recordMonth,callback:function(e){t.$set(t.listQuery,"recordMonth",e)},expression:"listQuery.recordMonth"}})],1),a("el-col",{attrs:{span:3}},[a("c-select-tree",{attrs:{options:t.treeData,selectplaceholder:"请选择部门",placeholder:"请输入关键字","tree-props":t.treeProps},model:{value:t.listQuery.deptId,callback:function(e){t.$set(t.listQuery,"deptId",e)},expression:"listQuery.deptId"}})],1),a("el-col",{attrs:{span:3}},[a("el-input",{attrs:{clearable:"",placeholder:"工号"},model:{value:t.listQuery.empCode,callback:function(e){t.$set(t.listQuery,"empCode",e)},expression:"listQuery.empCode"}})],1),a("el-col",{attrs:{span:3}},[a("el-input",{attrs:{clearable:"",placeholder:"姓名"},model:{value:t.listQuery.empName,callback:function(e){t.$set(t.listQuery,"empName",e)},expression:"listQuery.empName"}})],1),a("el-col",{attrs:{span:3}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{placeholder:"请假类别",multiple:"","collapse-tags":"",clearable:""},model:{value:t.listQuery.leaveTypes,callback:function(e){t.$set(t.listQuery,"leaveTypes",e)},expression:"listQuery.leaveTypes"}},t._l(t.leaveTypeList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.desc,value:t.value}})})),1)],1),a("el-col",{attrs:{span:3}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{placeholder:"假期类型",multiple:"","collapse-tags":"",clearable:""},model:{value:t.listQuery.holidayTypes,callback:function(e){t.$set(t.listQuery,"holidayTypes",e)},expression:"listQuery.holidayTypes"}},t._l(t.holidayTypeList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.desc,value:t.value}})})),1)],1),a("el-col",{attrs:{span:3}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{placeholder:"状态",multiple:"","collapse-tags":"",clearable:""},model:{value:t.listQuery.attDayOffRecordProphylacticDetailStatus,callback:function(e){t.$set(t.listQuery,"attDayOffRecordProphylacticDetailStatus",e)},expression:"listQuery.attDayOffRecordProphylacticDetailStatus"}},t._l(t.statusList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.desc,value:t.value}})})),1)],1),a("el-col",{attrs:{span:3}},[a("el-button",{staticClass:"filter-item-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleFilter}},[t._v(" 查询 ")])],1)],1),a("el-row",{staticStyle:{"margin-top":"10px"}},[a("el-col",{staticStyle:{"text-align":"left"},attrs:{span:12}},[a("span",{staticStyle:{color:"#369","font-weight":"bold"}},[t._v(" "+t._s(t.listQuery.recordMonth)+"状态："+t._s(t.recordProphylacticData.enumStatusDesc))]),1==t.recordProphylacticData.enumStatus&&t.recordProphylacticData.isAllowSubmit?a("el-button",{staticStyle:{background:"#60db2c",color:"#FFFFFF","margin-left":"20px"},attrs:{icon:"el-icon-check"},on:{click:t.handleSubmit}},[t._v(" 提交 ")]):t._e()],1),a("el-col",{staticStyle:{"text-align":"right"},attrs:{span:12}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:t.handleCreate}},[t._v(" 新增 ")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-download"},on:{click:t.exportData}},[t._v(" 导出 ")])],1)],1),a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.list,stripe:"",border:"",fit:"","highlight-current-row":"","default-sort":{prop:"UpdateTime",order:"descending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"},"row-class-name":t.handleRowClass},on:{"sort-change":t.sortChange}},[a("el-table-column",{attrs:{prop:"Employee.Uid",label:"唯一码",align:"center",width:"85",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.empUid))])]}}])}),a("el-table-column",{attrs:{prop:"Employee.EmpCode",label:"工号","header-align":"center",align:"center",width:"70px",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.empCode))])]}}])}),a("el-table-column",{attrs:{prop:"Employee.DisplayName",label:"姓名","header-align":"center",align:"left","min-width":"80px",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.empName))])]}}])}),a("el-table-column",{attrs:{prop:"Employee.EnumGender",label:"性别","header-align":"center",align:"left","min-width":"70px",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.genderDesc))])]}}])}),a("el-table-column",{attrs:{label:"部门","header-align":"center",align:"left","min-width":"130px"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.empDept))])]}}])}),a("el-table-column",{attrs:{label:"院区","header-align":"center",align:"left","min-width":"130px"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.hospitalAreaNameText))])]}}])}),a("el-table-column",{attrs:{label:"请假类别","header-align":"center",align:"center",width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.enumLeaveTypeDesc))])]}}])}),a("el-table-column",{attrs:{label:"假期类型","header-align":"center",align:"center",width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.enumHolidayTypeDesc))])]}}])}),a("el-table-column",{attrs:{label:"休假开始日期","header-align":"center",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.leaveStartDate?new Date(l.leaveStartDate).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"休假结束日期","header-align":"center",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.leaveEndDate?new Date(l.leaveEndDate).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"开具时间","header-align":"center",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.issuingTime?new Date(l.issuingTime).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"状态","header-align":"center",align:"center",width:"65"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.enumStatusDesc))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"病假",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.h2))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"事假",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.h3))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"产假",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.h4))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"哺乳假",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.h5))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"探亲假",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.h6))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"计生假",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.h7))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"婚丧假",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.h8))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"脱产读研",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.h9))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因公出国",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.h10))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因私出国",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.h11))])]}}])}),a("el-table-column",{attrs:{label:"操作",fixed:"right",align:"center","header-align":"center",width:"230","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("el-button",{staticStyle:{"margin-left":"3px !important"},attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.handleUpdate(l)}}},[t._v(" 编辑 ")]),a("el-button",{staticStyle:{"margin-left":"3px !important"},attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.handleView(l)}}},[t._v(" 详细 ")]),1==l.enumStatus?a("el-button",{staticStyle:{"margin-left":"3px !important"},attrs:{size:"mini",type:"danger"},on:{click:function(e){return t.handleDelete(l)}}},[t._v(" 删除 ")]):t._e(),a("el-button",{staticStyle:{"margin-left":"3px !important"},attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.print(l)}}},[t._v(" 打印 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[10,20,50],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.getList}})]},proxy:!0}])}),t.dialogEditFormVisible?a("modifyAttDayOffRecordProphylacticDetail",{attrs:{id:t.itemId,title:t.modifyDialogTitle},on:{hidden:function(e){return t.onHidden()},refresh:t.onRefresh}}):t._e(),a("el-dialog",{attrs:{"append-to-body":"",title:t.viewDialogTitle,"close-on-click-modal":!1,visible:t.dialogViewFormVisible,width:"90%"},on:{close:t.onHidden}},[a("viewAttDayOffRecordProphylacticDetail",{ref:"refAttDayOffRecordProphylacticDetail",attrs:{id:t.itemId,"show-dialog":t.dialogViewFormVisible}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{icon:"el-icon-close"},on:{click:t.onHidden}},[t._v(" 关闭 ")])],1)],1),a("printAttDayOffRecordProphylacticDetail",{ref:"childPrintAttDayOffRecordProphylacticDetail"})],1)},i=[],n=(a("99af"),a("d3b7"),a("25f0"),a("3ca3"),a("4d90"),a("ddb0"),a("2b3d"),a("afb1")),o=a("4cf0"),r=a("c126"),s=a("d368"),c=a("cbd2"),d=a("f9ac"),u=(a("2f62"),{name:"AttDayOffRecordProphylacticDetail",components:{modifyAttDayOffRecordProphylacticDetail:n["a"],viewAttDayOffRecordProphylacticDetail:o["a"],printAttDayOffRecordProphylacticDetail:r["a"]},data:function(){return{span:4,total:0,listQuery:{pageIndex:1,pageSize:10,order:"-UpdateTime",recordMonth:this.getNowTime()},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},leaveTypeList:[],holidayTypeList:[],statusList:[],listLoading:!1,recordProphylacticData:{},list:[],dialogEditFormVisible:!1,dialogViewFormVisible:!1,dialogStatus:"",textMap:{update:"编辑防保科考勤申报",create:"新增防保科考勤申报",view:"查看防保科考勤申报"},modifyDialogTitle:"",viewDialogTitle:"",itemId:null}},created:function(){this.loadTree(),this.initLeaveTypeList(),this.initHolidayTypeList(),this.initAttDayOffRecordProphylacticDetailStatusList(),this.getList()},computed:{},methods:{getNowTime:function(){var t=new Date,e=t.getFullYear(),a=t.getMonth();a=a.toString().padStart(2,"0"),"00"===a&&(e-=1,a="12");var l="".concat(e,"-").concat(a);return l},handleFilter:function(){this.listQuery.pageIndex=1,this.getList()},monthChange:function(){this.handleFilter(),this.getAttDayOffRecordProphylactic()},getAttDayOffRecordProphylactic:function(){var t=this;c["a"].getAttDayOffRecordProphylactic({recordMonth:this.listQuery.recordMonth}).then((function(e){e.succeed&&(t.recordProphylacticData=e.data)})).catch((function(t){}))},handleSubmit:function(){var t=this;this.$confirm("防保科考勤申报数据每月仅可提交一次，请确定提交月份的防保科考勤申报数据已全部填写完毕? 数据提交后，修改数据以及新增数据只能进入下个月的差异数据中，请谨慎操作","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){c["a"].subjectAttDayOffRecordProphylactic(t.recordProphylacticData).then((function(e){e.succeed?(t.getList(),t.$notice.message("提交成功","success")):-3!==e.type&&t.$notice.resultTip(e)})).catch((function(e){e.processed||t.$notice.message("提交成功","error")}))})).catch((function(t){t.succeed}))},getList:function(){var t=this;this.listLoading=!0,c["a"].queryAttDayOffRecordProphylacticDetail(this.listQuery).then((function(e){t.listLoading=!1,e.succeed?(t.list=e.data.datas,t.total=e.data.recordCount,t.listQuery.pageIndex=e.data.pageIndex):t.$notice.resultTip(e)})).catch((function(e){console.log(e),t.listLoading=!1})),this.getAttDayOffRecordProphylactic()},loadTree:function(){var t=this;s["a"].queryDeptByUser({}).then((function(e){t.treeData=e.data})).catch((function(t){console.log(t)})),this.resertCurrentNode()},resertCurrentNode:function(){this.currentNode=null},initLeaveTypeList:function(){var t=this,e={enumType:"LeaveType"};d["a"].getEnumInfos(e).then((function(e){t.leaveTypeList=e.data.datas})).catch((function(t){console.log(t)}))},initHolidayTypeList:function(){var t=this,e={enumType:"HolidayType"};d["a"].getEnumInfos(e).then((function(e){t.holidayTypeList=e.data.datas})).catch((function(t){console.log(t)}))},initAttDayOffRecordProphylacticDetailStatusList:function(){var t=this,e={enumType:"AttDayOffRecordProphylacticDetailStatus"};d["a"].getEnumInfos(e).then((function(e){t.statusList=e.data.datas})).catch((function(t){console.log(t)}))},handleRowClass:function(t,e){return t.rowIndex%2===0?"cellStyle":"stripedStyle"},sortChange:function(t,e,a){this.listQuery.pageIndex=1;var l="";"descending"===t.order&&(l="-"),"ascending"===t.order&&(l="+"),this.listQuery.order=l+t.prop,this.getList()},sizeChange:function(t){this.listQuery.pageSize=t,this.handleFilter()},handleCreate:function(){this.itemId=null,this.dialogEditFormVisible=!0,this.dialogStatus="create",this.modifyDialogTitle=this.textMap[this.dialogStatus]},handleUpdate:function(t){this.itemId=t.attDayOffRecordProphylacticCaseId,this.dialogEditFormVisible=!0,this.dialogStatus="update",this.modifyDialogTitle=this.textMap[this.dialogStatus]},handleView:function(t){this.itemId=t.attDayOffRecordProphylacticCaseId,this.dialogViewFormVisible=!0,this.dialogStatus="view",this.viewDialogTitle=this.textMap[this.dialogStatus]},handleDelete:function(t){var e=this;this.$confirm("确定删除防保科考勤申报?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){c["a"].deleteAttDayOffRecordProphylacticDetail(t).then((function(t){t.succeed?(e.getList(),e.$notice.message("删除成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("删除失败","error")}))})).catch((function(t){t.succeed}))},exportData:function(){c["a"].exportAttDayOffRecordProphylacticDetail(this.listQuery).then((function(t){var e=new Blob([t],{type:t.type}),a="防保科考勤申报.xlsx",l=document.createElement("a"),i=window.URL.createObjectURL(e);l.href=i,l.download=a,document.body.appendChild(l),l.click(),document.body.removeChild(l),window.URL.revokeObjectURL(i)}))},onHidden:function(){this.dialogEditFormVisible=!1,this.dialogViewFormVisible=!1},onRefresh:function(t){this.getList(),this.dialogEditFormVisible=!1,t&&t.attDayOffRecordProphylacticCaseId&&this.print(t)},print:function(t){var e=this;c["a"].getAttDayOffRecordProphylacticCase({id:t.attDayOffRecordProphylacticCaseId}).then((function(t){e.$refs.childPrintAttDayOffRecordProphylacticDetail.Print(t.data)})).catch((function(t){}))}}}),p=u,f=(a("19fc"),a("2877")),h=Object(f["a"])(p,l,i,!1,null,null,null);e["default"]=h.exports}}]);