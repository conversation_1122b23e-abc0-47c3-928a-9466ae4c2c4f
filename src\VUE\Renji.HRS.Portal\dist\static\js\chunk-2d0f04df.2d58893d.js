(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0f04df"],{"9c98":function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:t._u([{key:"main",fn:function(){return[a("el-row",{staticClass:"filter-container",attrs:{gutter:10,type:"flex"}},[a("el-col",{attrs:{span:4}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"岗位类型"},on:{change:t.changeParentStation},model:{value:t.listQuery.parentStationId,callback:function(e){t.$set(t.listQuery,"parentStationId",e)},expression:"listQuery.parentStationId"}},t._l(t.parentStationList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("el-col",{attrs:{span:4}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"岗位"},model:{value:t.listQuery.stationId,callback:function(e){t.$set(t.listQuery,"stationId",e)},expression:"listQuery.stationId"}},t._l(t.stationList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("el-col",{attrs:{span:4}},[a("el-input",{attrs:{placeholder:"工龄",clearable:""},model:{value:t.listQuery.workAge,callback:function(e){t.$set(t.listQuery,"workAge",t._n(e))},expression:"listQuery.workAge"}})],1),a("el-col",{staticClass:"filter-button",attrs:{span:8}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.search}},[t._v("查询")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(e){return t.showDialog()}}},[t._v("添加")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-download"},on:{click:t.downloadexceltemplate}},[t._v("下载模板")]),a("el-upload",{staticStyle:{"margin-left":"10px"},attrs:{action:"","http-request":t.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{slot:"trigger",icon:"el-icon-upload2",type:"primary"},slot:"trigger"},[t._v("导入")])],1),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-download"},on:{click:t.exportExcel}},[t._v("导出")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:t.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":t.sortChange}},[a("el-table-column",{attrs:{label:"岗位类型",sortable:"custom",prop:"Parent.Name"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.parentStationName))])]}}])}),a("el-table-column",{attrs:{label:"岗位",sortable:"custom",prop:"Station.Name"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.stationName))])]}}])}),a("el-table-column",{attrs:{label:"工龄",sortable:"custom",prop:"WorkAge"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(l.workAge))])]}}])}),a("el-table-column",{attrs:{label:"岗位津贴",sortable:"custom",prop:"Allowance"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("span",[t._v(t._s(t._f("formatMoney2")(l.allowance)))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","header-align":"center",width:"230","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("el-button",{staticStyle:{"margin-left":"45px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(e){return t.showDialog(l)}}},[t._v(" 编辑 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[10,20,50],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.getPageList}})]},proxy:!0}])}),a("editDialog",{ref:"editDialog",attrs:{"parent-station-list":t.parentStationList},on:{refreshData:t.getPageList}})],1)},o=[],i=(a("ac1f"),a("841c"),a("d368")),n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-dialog",{attrs:{title:t.title,visible:t.showDialog,width:"40%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:t.closeDialog}},[a("el-form",{ref:"dataForm",attrs:{rules:t.rules,model:t.dataModel,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:20}},[a("el-form-item",{attrs:{label:"岗位类型",prop:"parentStationId"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"岗位类型",disabled:t.isEdit},on:{change:t.changeParentStation},model:{value:t.dataModel.parentStationId,callback:function(e){t.$set(t.dataModel,"parentStationId",e)},expression:"dataModel.parentStationId"}},t._l(t.parentStationList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:20}},[a("el-form-item",{attrs:{label:"岗位",prop:"stationId"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"岗位",disabled:t.isEdit},model:{value:t.dataModel.stationId,callback:function(e){t.$set(t.dataModel,"stationId",e)},expression:"dataModel.stationId"}},t._l(t.stationList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:20}},[a("el-form-item",{attrs:{label:"工龄",prop:"workAge"}},[a("el-input",{attrs:{placeholder:"工龄",disabled:t.isEdit,clearable:""},model:{value:t.dataModel.workAge,callback:function(e){t.$set(t.dataModel,"workAge",e)},expression:"dataModel.workAge"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:20}},[a("el-form-item",{attrs:{label:"岗位津贴",prop:"allowance"}},[a("el-input",{attrs:{placeholder:"岗位津贴"},model:{value:t.dataModel.allowance,callback:function(e){t.$set(t.dataModel,"allowance",e)},expression:"dataModel.allowance"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:t.closeDialog}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:t.btnSaveLoading},on:{click:t.saveDialog}},[t._v("保 存")])],1)],1)],1)},s=[],r={props:{parentStationList:{type:Array,default:function(){return[]}}},data:function(){var t=function(t,e,a){/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/.test(e)?a():a(new Error("请输入0以上的数字"))},e=function(t,e,a){/^[+]{0,1}(\d+)$|^[+]{0,1}$/.test(e)?a():a(new Error("请输入正整数"))};return{showDialog:!1,title:"",stationList:[],rules:{parentStationId:[{required:!0,message:"请选择岗位类型",trigger:"change"}],stationId:[{required:!0,message:"请选择岗位",trigger:"change"}],workAge:[{required:!0,message:"请输入工龄",trigger:"blur"},{validator:e,trigger:"blur"}],allowance:[{required:!0,message:"请输入岗位津贴",trigger:"blur"},{validator:t,trigger:"blur"}]},btnSaveLoading:!1,isEdit:!1,dataModel:{}}},created:function(){},methods:{initDialog:function(t){t?(this.title="编辑岗位津贴",this.isEdit=!0,this.getData(t.id)):(this.title="新增岗位津贴",this.isEdit=!1),this.showDialog=!0},getData:function(t){var e=this;i["a"].getStationAllowance({id:t}).then((function(t){t.succeed&&(e.dataModel=t.data,e.queryTwoLevelStation(e.dataModel.parentStationId))})).catch((function(t){}))},queryTwoLevelStation:function(t){var e=this;i["a"].queryTwoLevelStation({id:t}).then((function(t){e.stationList=t.data.datas})).catch((function(t){console.log(t)}))},changeParentStation:function(t){t?this.queryTwoLevelStation(t):(this.stationList=[],this.dataModel.stationId="")},saveDialog:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&(t.btnSaveLoading=!0,t.isEdit?i["a"].updateStationAllowance(t.dataModel).then((function(e){e.succeed&&(t.$message({message:"修改成功",type:"success"}),t.btnSaveLoading=!1,t.$emit("refreshData"),t.closeDialog())})).catch((function(e){t.btnSaveLoading=!1})):i["a"].addStationAllowance(t.dataModel).then((function(e){e.succeed&&(t.$message({message:"添加成功",type:"success"}),t.btnSaveLoading=!1,t.$emit("refreshData"),t.closeDialog())})).catch((function(e){t.btnSaveLoading=!1})))}))},closeDialog:function(){this.dataModel={},this.showDialog=!1,this.$refs.dataForm.resetFields()}}},c=r,d=a("2877"),u=Object(d["a"])(c,n,s,!1,null,null,null),p=u.exports,g={components:{editDialog:p},data:function(){return{addForm:{},dataList:[],total:0,listQuery:{pageIndex:1,pageSize:10},parentStationList:[],stationList:[],listLoading:!1}},created:function(){this.queryOneLevelStation(),this.getPageList()},methods:{search:function(){this.listQuery.pageIndex=1,this.getPageList()},queryOneLevelStation:function(){var t=this;i["a"].queryOneLevelStation().then((function(e){t.parentStationList=e.data.datas})).catch((function(t){console.log(t)}))},sortChange:function(t){this.listQuery.pageIndex=1;var e="";"descending"===t.order&&(e="-"),"ascending"===t.order&&(e="+"),this.listQuery.order=e+t.prop,this.getPageList()},queryTwoLevelStation:function(t){var e=this;i["a"].queryTwoLevelStation({id:t}).then((function(t){e.stationList=t.data.datas})).catch((function(t){console.log(t)}))},changeParentStation:function(t){t?this.queryTwoLevelStation(t):(this.stationList=[],this.listQuery.stationId="")},importExcel:function(t){var e=this,a=t.file,l=new FormData;i["a"].importStationAllowance(a,l).then((function(t){t.succeed&&(e.$message({message:"导入成功",type:"success"}),e.search())})).catch((function(t){e.search()}))},exportExcel:function(){var t=this;i["a"].exportStationAllowance(this.listQuery).then((function(e){console.log(e);var l=a("19de"),o="岗位津贴"+t.$moment().format("YYYYMMDDHHmmss")+".xlsx";e.data?l(e.data,o):l(e,o)}))},downloadexceltemplate:function(){i["a"].downloadStationAllowanceTemplate().then((function(t){var e=a("19de"),l="StationAllowanceTemplate.xlsx";t.data?e(t.data,l):e(t,l)})).catch((function(t){}))},getPageList:function(){var t=this;this.listLoading=!0,i["a"].queryStationAllowance(this.listQuery).then((function(e){t.listLoading=!1,e.succeed?(t.dataList=e.data.datas,console.log(t.dataList),t.total=e.data.recordCount,t.listQuery.pageIndex=e.data.pageIndex):t.$notice.resultTip(e)})).catch((function(e){console.log(e),t.listLoading=!1}))},showDialog:function(t){this.$refs.editDialog.initDialog(t)},closeAddDialog:function(){this.$refs["ref_addForm"].resetFields(),this.$refs["ref_addForm"].clearValidate()}}},f=g,h=Object(d["a"])(f,l,o,!1,null,null,null);e["default"]=h.exports}}]);