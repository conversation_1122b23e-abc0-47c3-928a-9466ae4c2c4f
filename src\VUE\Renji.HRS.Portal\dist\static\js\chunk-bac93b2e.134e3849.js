(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-bac93b2e"],{"95f8":function(e,t,a){"use strict";var l=a("ee62"),i=a.n(l);i.a},b8a9:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-row",{staticClass:"filter-container",attrs:{gutter:5,type:"flex"}},[a("el-col",{attrs:{span:3}},[a("c-select-tree",{attrs:{options:e.treeData,selectplaceholder:"请选择部门",placeholder:"请输入关键字","tree-props":e.treeProps},model:{value:e.listQuery.deptId,callback:function(t){e.$set(e.listQuery,"deptId",t)},expression:"listQuery.deptId"}})],1),a("el-col",{attrs:{span:3}},[a("el-input",{attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),a("el-col",{attrs:{span:3}},[a("el-input",{attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQuery.empName,callback:function(t){e.$set(e.listQuery,"empName",t)},expression:"listQuery.empName"}})],1),a("el-col",{attrs:{span:3}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{placeholder:"请假类别",multiple:"","collapse-tags":"",clearable:""},model:{value:e.listQuery.leaveTypes,callback:function(t){e.$set(e.listQuery,"leaveTypes",t)},expression:"listQuery.leaveTypes"}},e._l(e.leaveTypeList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),a("el-col",{attrs:{span:3}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{placeholder:"假期类型",multiple:"","collapse-tags":"",clearable:""},model:{value:e.listQuery.holidayTypes,callback:function(t){e.$set(e.listQuery,"holidayTypes",t)},expression:"listQuery.holidayTypes"}},e._l(e.holidayTypeList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),a("el-col",{attrs:{span:3}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{placeholder:"状态",multiple:"","collapse-tags":"",clearable:""},model:{value:e.listQuery.attDayOffRecordProphylacticDetailStatus,callback:function(t){e.$set(e.listQuery,"attDayOffRecordProphylacticDetailStatus",t)},expression:"listQuery.attDayOffRecordProphylacticDetailStatus"}},e._l(e.statusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),a("el-col",{attrs:{span:6}},[a("el-date-picker",{staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"休假开始日期","end-placeholder":"休假结束日期",clearable:!1},model:{value:e.listQuery.times,callback:function(t){e.$set(e.listQuery,"times",t)},expression:"listQuery.times"}})],1)],1),a("el-row",{staticStyle:{"margin-top":"10px"}},[a("el-col",{staticStyle:{"text-align":"left"},attrs:{span:12}}),a("el-col",{staticStyle:{"text-align":"right"},attrs:{span:12}},[a("el-button",{staticClass:"filter-item-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleFilter}},[e._v(" 查询 ")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-download"},on:{click:e.exportData}},[e._v(" 导出 ")])],1)],1),a("el-table",{staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.list,stripe:"",border:"",fit:"","highlight-current-row":"","default-sort":{prop:"Employee.EmpCode",order:"descending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"},"row-class-name":e.handleRowClass},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{prop:"Employee.Uid",label:"唯一码",align:"center",width:"85",sortable:"custom",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.empUid))])]}}])}),a("el-table-column",{attrs:{prop:"Employee.EmpCode",label:"工号","header-align":"center",align:"center",width:"70px",sortable:"custom",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.empCode))])]}}])}),a("el-table-column",{attrs:{prop:"Employee.DisplayName",label:"姓名","header-align":"center",align:"left","min-width":"80px",sortable:"custom",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.empName))])]}}])}),a("el-table-column",{attrs:{prop:"Employee.EnumGender",label:"性别","header-align":"center",align:"left","min-width":"70px",sortable:"custom",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.genderDesc))])]}}])}),a("el-table-column",{attrs:{label:"部门","header-align":"center",align:"left","min-width":"130px"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.empDept))])]}}])}),a("el-table-column",{attrs:{label:"院区","header-align":"center",align:"left","min-width":"130px"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.hospitalAreaNameText))])]}}])}),a("el-table-column",{attrs:{label:"月份",prop:"recordMonth",sortable:"custom","header-align":"center",align:"center","min-width":"90px"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.recordMonth?new Date(l.recordMonth).Format("yyyy-MM"):""))])]}}])}),a("el-table-column",{attrs:{label:"请假类别","header-align":"center",align:"center",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.enumLeaveTypeDesc))])]}}])}),a("el-table-column",{attrs:{label:"假期类型","header-align":"center",align:"center",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.enumHolidayTypeDesc))])]}}])}),a("el-table-column",{attrs:{label:"休假开始日期","header-align":"center",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.leaveStartDate?new Date(l.leaveStartDate).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"休假结束日期","header-align":"center",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.leaveEndDate?new Date(l.leaveEndDate).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"开具时间","header-align":"center",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.issuingTime?new Date(l.issuingTime).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"状态","header-align":"center",align:"center",width:"65"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.enumStatusDesc))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"病假",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.h2))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"事假",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.h3))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"产假",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.h4))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"哺乳假",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.h5))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"探亲假",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.h6))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"计生假",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.h7))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"婚丧假",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.h8))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"脱产读研",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.h9))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因公出国",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.h10))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因私出国",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.h11))])]}}])}),a("el-table-column",{attrs:{label:"操作",fixed:"right",align:"center","header-align":"center",width:"180","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-button",{staticStyle:{"padding-left":"3px !important"},attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.handleView(l)}}},[e._v(" 详细 ")]),a("el-button",{staticStyle:{"margin-left":"3px !important"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.handleUpdate(l)}}},[e._v(" 编辑 ")]),a("el-button",{staticStyle:{"padding-left":"3px !important"},attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.print(l)}}},[e._v(" 打印 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getList}})]},proxy:!0}])}),e.dialogEditFormVisible?a("modifyAttDayOffRecordProphylacticDetail",{attrs:{id:e.itemId,title:e.modifyDialogTitle},on:{hidden:function(t){return e.onHidden()},refresh:e.onRefresh}}):e._e(),a("el-dialog",{attrs:{"append-to-body":"",title:e.viewDialogTitle,"close-on-click-modal":!1,visible:e.dialogViewFormVisible,width:"80%"},on:{close:e.onHidden}},[a("viewAttDayOffRecordProphylacticDetail",{ref:"refAttDayOffRecordProphylacticDetail",attrs:{id:e.itemId,"show-dialog":e.dialogViewFormVisible}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{icon:"el-icon-close"},on:{click:e.onHidden}},[e._v(" 关闭 ")])],1)],1),a("printAttDayOffRecordProphylacticDetail",{ref:"childPrintAttDayOffRecordProphylacticDetail"})],1)},i=[],n=(a("d3b7"),a("3ca3"),a("ddb0"),a("2b3d"),a("afb1")),r=a("4cf0"),o=a("c126"),s=a("d368"),c=a("cbd2"),d=a("f9ac"),u={name:"QueryAttDayOffRecordProphylacticDetail",components:{modifyAttDayOffRecordProphylacticDetail:n["a"],viewAttDayOffRecordProphylacticDetail:r["a"],printAttDayOffRecordProphylacticDetail:o["a"]},data:function(){return{span:4,total:0,listQuery:{pageIndex:1,pageSize:10,times:[],order:"-Employee.EmpCode"},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},leaveTypeList:[],holidayTypeList:[],statusList:[],listLoading:!1,recordProphylacticData:{},list:[],dialogEditFormVisible:!1,dialogViewFormVisible:!1,dialogStatus:"",textMap:{update:"编辑防保科考勤申报",create:"新增防保科考勤申报",view:"查看防保科考勤申报"},modifyDialogTitle:"",viewDialogTitle:"",itemId:null}},created:function(){var e=(new Date).getFullYear(),t=e+"-01-01",a=(new Date).Format("yyyy-MM-dd");this.listQuery.times=[t,a],this.loadTree(),this.initLeaveTypeList(),this.initHolidayTypeList(),this.initAttDayOffRecordProphylacticDetailStatusList(),this.getList()},computed:{},methods:{handleFilter:function(){this.listQuery.pageIndex=1,this.getList()},monthChange:function(){this.handleFilter()},getList:function(){var e=this;this.listLoading=!0,this.listQuery.isQuery=!0,null==this.listQuery.times&&(this.listQuery.times=[]),c["a"].queryAttDayOffRecordProphylacticDetail(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.list=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},loadTree:function(){var e=this;s["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data})).catch((function(e){console.log(e)})),this.resertCurrentNode()},resertCurrentNode:function(){this.currentNode=null},initLeaveTypeList:function(){var e=this,t={enumType:"LeaveType"};d["a"].getEnumInfos(t).then((function(t){e.leaveTypeList=t.data.datas})).catch((function(e){console.log(e)}))},initHolidayTypeList:function(){var e=this,t={enumType:"HolidayType"};d["a"].getEnumInfos(t).then((function(t){e.holidayTypeList=t.data.datas})).catch((function(e){console.log(e)}))},initAttDayOffRecordProphylacticDetailStatusList:function(){var e=this,t={enumType:"AttDayOffRecordProphylacticDetailStatus"};d["a"].getEnumInfos(t).then((function(t){e.statusList=t.data.datas})).catch((function(e){console.log(e)}))},handleRowClass:function(e,t){return e.rowIndex%2===0?"cellStyle":"stripedStyle"},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var l="";"descending"===e.order&&(l="-"),"ascending"===e.order&&(l="+"),this.listQuery.order=l+e.prop,this.getList()},sizeChange:function(e){this.listQuery.pageSize=e,this.handleFilter()},handleUpdate:function(e){this.itemId=e.attDayOffRecordProphylacticCaseId,this.dialogEditFormVisible=!0,this.dialogStatus="update",this.modifyDialogTitle=this.textMap[this.dialogStatus]},handleView:function(e){this.itemId=e.attDayOffRecordProphylacticCaseId,this.dialogViewFormVisible=!0,this.dialogStatus="view",this.viewDialogTitle=this.textMap[this.dialogStatus]},exportData:function(){this.listQuery.isQuery=!0,null==this.listQuery.times&&(this.listQuery.times=[]),c["a"].exportAttDayOffRecordProphylacticDetail(this.listQuery).then((function(e){var t=new Blob([e],{type:e.type}),a="防保科考勤申报.xlsx",l=document.createElement("a"),i=window.URL.createObjectURL(t);l.href=i,l.download=a,document.body.appendChild(l),l.click(),document.body.removeChild(l),window.URL.revokeObjectURL(i)}))},onRefresh:function(e){this.getList(),this.dialogEditFormVisible=!1,e&&e.attDayOffRecordProphylacticCaseId&&this.print(e)},onHidden:function(){this.dialogViewFormVisible=!1,this.dialogEditFormVisible=!1},print:function(e){var t=this;c["a"].getAttDayOffRecordProphylacticCase({id:e.attDayOffRecordProphylacticCaseId}).then((function(e){t.$refs.childPrintAttDayOffRecordProphylacticDetail.Print(e.data)})).catch((function(e){}))}}},p=u,y=(a("95f8"),a("2877")),f=Object(y["a"])(p,l,i,!1,null,null,null);t["default"]=f.exports},ee62:function(e,t,a){}}]);