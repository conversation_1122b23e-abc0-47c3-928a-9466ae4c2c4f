(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c0711bf0"],{"06c5":function(e,t,a){"use strict";a.d(t,"a",(function(){return o}));a("a630"),a("fb6a"),a("b0c0"),a("d3b7"),a("25f0"),a("3ca3");var l=a("6b75");function o(e,t){if(e){if("string"===typeof e)return Object(l["a"])(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?Object(l["a"])(e,t):void 0}}},"6b75":function(e,t,a){"use strict";function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,l=new Array(t);a<t;a++)l[a]=e[a];return l}a.d(t,"a",(function(){return l}))},"852c":function(e,t,a){"use strict";var l=a("89c8"),o=a.n(l);o.a},"89c8":function(e,t,a){},ed82:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{visible:e.showEmployee,width:"80%",top:"5vh"},on:{"update:visible":function(t){e.showEmployee=t}}},[a("div",[a("layout3",{scopedSlots:e._u([{key:"aside",fn:function(){return[a("el-checkbox",{model:{value:e.listQuery.isContainSubDept,callback:function(t){e.$set(e.listQuery,"isContainSubDept",t)},expression:"listQuery.isContainSubDept"}},[e._v("包含下级部门")]),a("c-tree",{attrs:{options:e.treeData,props:e.treeProps,"expanded-keys":e.treeExpandedKeys},on:{nodeClick:e.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[a("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[a("el-form-item",{attrs:{label:"唯一码",prop:"uid"}},[a("el-input",{attrs:{placeholder:"",clearable:""},model:{value:e.listQuery.uid,callback:function(t){e.$set(e.listQuery,"uid",t)},expression:"listQuery.uid"}})],1),a("el-form-item",{attrs:{label:"工号",prop:"empCode"}},[a("el-input",{attrs:{placeholder:"",clearable:""},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),a("el-form-item",{attrs:{label:"中文名",prop:"displayName"}},[a("el-input",{attrs:{placeholder:"",clearable:""},model:{value:e.listQuery.displayName,callback:function(t){e.$set(e.listQuery,"displayName",t)},expression:"listQuery.displayName"}})],1),a("el-form-item",{attrs:{label:"薪资状态",prop:"sex"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.listQuery.salaryStatus,callback:function(t){e.$set(e.listQuery,"salaryStatus",t)},expression:"listQuery.salaryStatus"}},e._l(e.salaryStatuss,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label||e.desc,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"在职方式"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.listQuery.hireStyleId,callback:function(t){e.$set(e.listQuery,"hireStyleId",t)},expression:"listQuery.hireStyleId"}},e._l(e.empHireStyleOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-form-item",{attrs:{label:"离职方式"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.listQuery.leaveStyleId,callback:function(t){e.$set(e.listQuery,"leaveStyleId",t)},expression:"listQuery.leaveStyleId"}},e._l(e.empLeaveStyleOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.search()}}},[e._v("查询")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{label:"唯一码",sortable:"custom",prop:"uid"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.uid))])]}}])}),a("el-table-column",{attrs:{label:"工号",sortable:"custom",prop:"empCode"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.empCode))])]}}])}),a("el-table-column",{attrs:{label:"中文名",sortable:"custom",prop:"displayName"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.displayName))])]}}])}),a("el-table-column",{attrs:{label:"院区"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.hospitalAreaNameText))])]}}])}),a("el-table-column",{attrs:{label:"部门",sortable:"custom",prop:"Department.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.deptName))])]}}])}),a("el-table-column",{attrs:{label:"薪资状态",sortable:"custom",prop:"employeeSalary.enumEmployeeSalaryStatus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.enumEmployeeSalaryStatusDesc))])]}}])}),a("el-table-column",{attrs:{fixed:"right",label:"选择",align:"left","header-align":"center",width:"120","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-button",{staticStyle:{"padding-left":"5px !important","margin-left":"20%"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.selectRow(l)}}},[e._v(" 选择 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])})],1)])},o=[],n=(a("99af"),a("d3b7"),a("6b75"));function i(e){if(Array.isArray(e))return Object(n["a"])(e)}a("a4d3"),a("e01a"),a("d28b"),a("a630"),a("3ca3"),a("ddb0");function s(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}var r=a("06c5");function c(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e){return i(e)||s(e)||Object(r["a"])(e)||c()}var d=a("d368"),p=a("2efc"),m=a("f9ac"),y=a("e44c"),f={components:{},data:function(){return{showEmployee:!1,listLoading:!1,treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeExpandedKeys:["1"],pageList:[],listQuery:{total:1,pageIndex:1,pageSize:10,leaveStyleCode:"",salaryStatus:""},genderDropdown:[{value:1,label:"男"},{value:2,label:"女"}],salaryStatuss:[],empHireStyleOptions:[],empLeaveStyleOptions:[]}},created:function(){this.initSalaryStatusList(),this.loadEmployeeHireStyle(),this.loadEmployeeLeaveStyle()},methods:{initSalaryStatusList:function(){var e=this,t={enumType:"EmployeeSalaryStatus"};m["a"].getEnumInfos(t).then((function(t){e.salaryStatuss=[{value:"",label:"全部"}].concat(u(t.data.datas))})).catch((function(e){console.log(e)}))},loadEmployeeHireStyle:function(){var e=this;y["a"].queryHireStyle().then((function(t){e.empHireStyleOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadEmployeeLeaveStyle:function(){var e=this;y["a"].queryLeaveStyle().then((function(t){e.empLeaveStyleOptions=t.data.datas})).catch((function(e){console.log(e)}))},treeNodeClick:function(e){this.listQuery.deptId=e.id,this.getPageList()},selectRow:function(e){this.showEmployee=!1,this.$emit("selectRow",e)},sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.$delete(this.listQuery,"order"),this.listQuery.pageIndex=1,this.getPageList()},loadTree:function(){var e=this;d["a"].queryDeptByUser().then((function(t){e.treeData=t.data,e.treeExpandedKeys.push(t.data[0].id)})).catch((function(e){console.log(e)}))},search:function(){this.listQuery.pageIndex=1,this.getPageList()},getPageList:function(){this.listQuery.deptId?this.queryEmployeesMethods(this.listQuery):alert("请先选择部门!")},queryEmployeesMethods:function(e){var t=this;this.listLoading=!0,console.log(e),p["a"].querySalaryEmployees(e).then((function(e){e.succeed?(t.pageList=e.data.datas,t.listQuery.total=e.data.recordCount,t.listQuery.pageIndex=e.data.pageIndex):t.$notice.resultTip(e)})).catch((function(e){console.log(e)})).finally((function(){t.listLoading=!1}))}}},h=f,g=a("2877"),b=Object(g["a"])(h,l,o,!1,null,"36d46f7e",null);t["a"]=b.exports},f744:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-row",{staticClass:"filter-container",attrs:{gutter:11,type:"flex"}},[a("el-col",{attrs:{span:5}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"年度"},model:{value:e.listQuery.year,callback:function(t){e.$set(e.listQuery,"year",t)},expression:"listQuery.year"}})],1),a("el-col",{attrs:{span:5}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"状态"},model:{value:e.listQuery.enumDeductType,callback:function(t){e.$set(e.listQuery,"enumDeductType",t)},expression:"listQuery.enumDeductType"}},e._l(e.deductTypeList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),a("el-col",{attrs:{span:5}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.showAddDialog()}}},[e._v("添加")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","default-sort":{prop:"CreateTime",order:"descending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{sortable:"custom",prop:"Year",label:"年度"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.year))])]}}])}),a("el-table-column",{attrs:{sortable:"custom",prop:"LastEditor",label:"操作人"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.lastEditor))])]}}])}),a("el-table-column",{attrs:{sortable:"custom",prop:"EnumDeductType",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.enumDeductTypeDesc))])]}}])}),a("el-table-column",{attrs:{sortable:"custom",prop:"Remark",label:"备注"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.remark))])]}}])}),a("el-table-column",{attrs:{sortable:"custom",prop:"LastEditTime",label:"操作时间"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.lastEditTime))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","header-align":"center",width:"280","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-button",{attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.showDialog(l)}}},[e._v(" 详情 ")]),1==l.enumDeductType?a("el-button",{attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.deductCalculate(l)}}},[e._v(" 处理 ")]):e._e(),a("el-button",{attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.exportData(l)}}},[e._v("导出")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),a("editDialog",{ref:"editDialog",on:{refreshData:e.getPageList}}),a("addDialog",{ref:"addDialog",on:{refreshData:e.getPageList}})],1)},o=[],n=a("e44c"),i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{title:e.title,visible:e.showDialog,width:"80%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:e.closeDialog}},[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.dataModel,"label-width":"100px"}},[a("el-tabs",{on:{"tab-click":e.handleTabClickMethod},model:{value:e.actionName,callback:function(t){e.actionName=t},expression:"actionName"}},[a("el-tab-pane",{attrs:{label:"不计算人员",name:"unCal"}},[a("el-row",{staticClass:"filter-container",attrs:{gutter:15,type:"flex"}},[a("el-col",{attrs:{span:2}},[a("el-input",{attrs:{clearable:"",placeholder:"唯一码"},model:{value:e.listQueryUnCal.uid,callback:function(t){e.$set(e.listQueryUnCal,"uid",t)},expression:"listQueryUnCal.uid"}})],1),a("el-col",{attrs:{span:2}},[a("el-input",{attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQueryUnCal.empCode,callback:function(t){e.$set(e.listQueryUnCal,"empCode",t)},expression:"listQueryUnCal.empCode"}})],1),a("el-col",{attrs:{span:2}},[a("el-input",{attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQueryUnCal.displayName,callback:function(t){e.$set(e.listQueryUnCal,"displayName",t)},expression:"listQueryUnCal.displayName"}})],1),a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.searchUnCal}},[e._v("查询")])],1),e.isEdit?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",title:"选择员工"},on:{click:e.selectEmployeeDialog}},[e._v("选 择")])],1):e._e(),e.isEdit?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary"},on:{click:e.downloadexceltemplate}},[e._v("模板下载")])],1):e._e(),e.isEdit?a("el-col",{attrs:{span:5}},[a("el-upload",{attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{type:"primary"}},[e._v("导入")])],1)],1):e._e()],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.dataListUnCal,border:"",stripe:"",fit:"","highlight-current-row":"","default-sort":{prop:"CreateTime",order:"descending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"Uid",label:"唯一码"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.uid))])]}}])}),a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"EmpCode",label:"工号"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.empCode))])]}}])}),a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"DisplayName",label:"姓名"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.displayName))])]}}])}),a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"DeptName",label:"部门"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.deptName))])]}}])}),a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"SocietyAge",label:"不计算工龄"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.isEdit?a("el-checkbox",{model:{value:t.row.notCalAge,callback:function(a){e.$set(t.row,"notCalAge",a)},expression:"scope.row.notCalAge"}}):e._e(),e.isEdit?e._e():a("el-checkbox",{attrs:{disabled:""},model:{value:t.row.notCalAge,callback:function(a){e.$set(t.row,"notCalAge",a)},expression:"scope.row.notCalAge"}})]}}])}),a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"NewSocietyAge",label:"不计算薪级"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.isEdit?a("el-checkbox",{model:{value:t.row.notCalSalaryScale,callback:function(a){e.$set(t.row,"notCalSalaryScale",a)},expression:"scope.row.notCalSalaryScale"}}):e._e(),e.isEdit?e._e():a("el-checkbox",{attrs:{disabled:""},model:{value:t.row.notCalSalaryScale,callback:function(a){e.$set(t.row,"notCalSalaryScale",a)},expression:"scope.row.notCalSalaryScale"}})]}}])}),e.isEdit?a("el-table-column",{attrs:{fixed:"right",label:"操作",align:"left","header-align":"center",width:"150","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-button",{staticStyle:{"margin-left":"45px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.saveDeductCalculate(l)}}},[e._v(" 保存 ")])]}}],null,!1,3543744862)}):e._e()],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.totalUnCal>0,expression:"totalUnCal > 0"}],attrs:{total:e.totalUnCal,"page-sizes":[10,20,50],page:e.listQueryUnCal.pageIndex,limit:e.listQueryUnCal.pageSize},on:{"update:page":function(t){return e.$set(e.listQueryUnCal,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQueryUnCal,"pageSize",t)},pagination:e.getUnCalPageList}})],1),a("el-tab-pane",{attrs:{label:"计算人员",name:"Cal"}},[a("el-row",{staticClass:"filter-container",attrs:{gutter:15,type:"flex"}},[a("el-col",{attrs:{span:2}},[a("el-input",{attrs:{clearable:"",placeholder:"唯一码"},model:{value:e.listQueryCal.uid,callback:function(t){e.$set(e.listQueryCal,"uid",t)},expression:"listQueryCal.uid"}})],1),a("el-col",{attrs:{span:2}},[a("el-input",{attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQueryCal.empCode,callback:function(t){e.$set(e.listQueryCal,"empCode",t)},expression:"listQueryCal.empCode"}})],1),a("el-col",{attrs:{span:2}},[a("el-input",{attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQueryCal.displayName,callback:function(t){e.$set(e.listQueryCal,"displayName",t)},expression:"listQueryCal.displayName"}})],1),a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.searchCal}},[e._v("查询")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.dataListCal,border:"",stripe:"",fit:"","highlight-current-row":"","default-sort":{prop:"CreateTime",order:"descending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"Uid",label:"唯一码"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.uid))])]}}])}),a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"EmpCode",label:"工号"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.empCode))])]}}])}),a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"DisplayName",label:"姓名"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.displayName))])]}}])}),a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"DeptName",label:"部门"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.deptName))])]}}])}),a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"SocietyAge",label:"实际工龄"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.societyAge))])]}}])}),e.isEdit?e._e():a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"NewSocietyAge",label:"新实际工龄"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.newSocietyAge))])]}}],null,!1,2310409861)}),a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"CompanyAge",label:"本院工龄"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.companyAge))])]}}])}),e.isEdit?e._e():a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"NewCompanyAge",label:"新本院工龄"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.newCompanyAge))])]}}],null,!1,1647486076)}),a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"SalaryScale",label:"薪级"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.salaryScale))])]}}])}),e.isEdit?e._e():a("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"NewSalaryScale",label:"新薪级"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.newSalaryScale))])]}}],null,!1,*********)})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.totalCal>0,expression:"totalCal > 0"}],attrs:{total:e.totalCal,"page-sizes":[10,20,50],page:e.listQueryCal.pageIndex,limit:e.listQueryCal.pageSize},on:{"update:page":function(t){return e.$set(e.listQueryCal,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQueryCal,"pageSize",t)},pagination:e.getCalPageList}})],1)],1)],1)],1),a("selectUserComponent",{ref:"selectEmployee",on:{selectRow:e.setEmployee}})],1)},s=[],r=(a("d81d"),a("ed82")),c={components:{selectUserComponent:r["a"]},data:function(){return{showDialog:!1,title:"工龄薪级详情",rules:{},btnSaveLoading:!1,isEdit:!1,dataModel:{},deductId:null,enumDeductType:1,totalUnCal:0,listQueryUnCal:{order:"-CreateTime",pageIndex:1,pageSize:10},dataListUnCal:[],totalCal:0,listQueryCal:{order:"-CreateTime",pageIndex:1,pageSize:10},dataListCal:[],listLoading:!1,actionName:"unCal",editIds:[]}},methods:{initDialog:function(e){1===e.enumDeductType?this.isEdit=!0:this.isEdit=!1,console.log(this.isEdit),this.enumDeductType=e.enumDeductType,this.deductId=e.id,this.listQueryUnCal.deductId=e.id,this.listQueryCal.deductId=e.id,this.loadData(),this.showDialog=!0},getUnCalPageList:function(){var e=this;this.listLoading=!0,n["a"].queryEmployeeDeductUnCalculate(this.listQueryUnCal).then((function(t){e.listLoading=!1,t.succeed?(e.dataListUnCal=t.data.datas,e.totalUnCal=t.data.recordCount,e.listQueryUnCal.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},searchUnCal:function(){this.listQueryUnCal.pageIndex=1,this.getUnCalPageList()},getCalPageList:function(){var e=this;this.listLoading=!0,n["a"].queryEmployeeDeductCalculate(this.listQueryCal).then((function(t){e.listLoading=!1,t.succeed?(e.dataListCal=t.data.datas,e.totalCal=t.data.recordCount,e.listQueryCal.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},handleSelectionChange:function(e){this.editIds=e.map((function(e){return e.id}))},searchCal:function(){this.listQueryCal.pageIndex=1,this.getCalPageList()},closeDialog:function(){this.dataModel={},this.showDialog=!1,this.$refs.dataForm.resetFields()},sortChange:function(e){e.column,e.prop,e.order},selectEmployeeDialog:function(){this.$refs.selectEmployee.loadTree(),this.$refs.selectEmployee.showEmployee=!0},setEmployee:function(e){this.dataModel={employeeModel:{}},this.setEmployeeUnCal(e)},setEmployeeUnCal:function(e){this.dataModel.employeeId=e.id,this.dataModel.deductId=this.deductId,this.dataModel.notCalAge=!0,this.dataModel.notCalSalaryScale=!0,console.log(this.dataModel),this.saveDeductCalculate(this.dataModel)},saveDeductCalculate:function(e){var t=this;1===this.enumDeductType&&n["a"].updateEmployeeDeductCalculate(e).then((function(e){e.succeed&&t.getUnCalPageList()})).catch()},exportData:function(){var e=this;this.$confirm("确定导出工龄薪级数据吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n["a"].exportEmployeeDeductCalculate(e.listQueryCal).then((function(t){var l=a("19de"),o="工龄薪级"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?l(t.data,o):l(t,o)})).catch((function(t){e.listLoadingForArticle=!1,console.log(t)}))})).catch((function(t){e.listLoadingForArticle=!1,t.succeed||e.$notice.message("取消导出","info")}))},downloadexceltemplate:function(){n["a"].downloadEmployeeDeductTemplate().then((function(e){var t=a("19de"),l="UpdateCompanyAgeSalaryScaleTemplate.xlsx";e.data?t(e.data,l):t(e,l)})).catch((function(e){}))},importExcel:function(e){var t=this,a=e.file;n["a"].importEmployeeDeduct(a,{deductId:this.deductId}).then((function(e){if(e.succeed){t.getUnCalPageList();var a=e.data;t.$message.success(a)}})).catch((function(e){}))},handleTabClickMethod:function(e,t){this.loadData()},loadData:function(){switch(this.actionName){case"unCal":this.loadUnCal();break;case"Cal":this.loadCal();break;default:break}},loadUnCal:function(){this.getUnCalPageList()},loadCal:function(){this.getCalPageList()}}},u=c,d=a("2877"),p=Object(d["a"])(u,i,s,!1,null,null,null),m=p.exports,y=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{title:e.title,visible:e.showDialog,width:"40%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:e.closeDialog}},[a("el-form",{ref:"dataForm",attrs:{model:e.dataModel,"label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"年度",prop:"Year"}},[[a("el-date-picker",{attrs:{type:"year",placeholder:"选择年"},model:{value:e.year,callback:function(t){e.year=t},expression:"year"}})]],2)],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{type:"textarea",rows:3,maxlength:"500",clearable:"",placeholder:"备注"},model:{value:e.dataModel.remark,callback:function(t){e.$set(e.dataModel,"remark",t)},expression:"dataModel.remark"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.closeDialog}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnSaveLoading},on:{click:e.saveDialog}},[e._v("保 存")])],1)],1)],1)},f=[],h=(a("a630"),a("d3b7"),a("25f0"),a("3ca3"),{data:function(){return{showDialog:!1,title:"",btnSaveLoading:!1,isEdit:!1,dataModel:{},year:new Date,selectedMonth:null,months:Array.from({length:13},(function(e,t){return t+1}))}},methods:{initDialog:function(e){if(this.showDialog=!0,!e){this.title="新增薪级工龄计算",this.isEdit=!1,this.getNowTime();var t=this.dataModel.month?new Date(this.dataModel.month).getMonth()+1:0;8===t&&(this.dataModel.yearSocialSecurityBaseCorrection=!0)}},getNowTime:function(){this.selectedMonth=(new Date).getMonth()+1},saveDialog:function(){var e=this;this.$refs["dataForm"].validate((function(t){e.dataModel.year=new Date(e.year).getFullYear().toString(),t&&(e.isEdit||n["a"].addEmployeeDeduct(e.dataModel).then((function(t){t.succeed&&(e.$message({message:"添加成功",type:"success"}),e.btnSaveLoading=!1,e.$emit("refreshData"),e.closeDialog())})).catch((function(t){e.btnSaveLoading=!1,e.closeDialog()})))}))},closeDialog:function(){this.dataModel={},this.showDialog=!1,this.$refs.dataForm.resetFields()}}}),g=h,b=(a("852c"),Object(d["a"])(g,y,f,!1,null,null,null)),v=b.exports,C=a("f9ac"),w={components:{editDialog:m,addDialog:v},data:function(){return{addForm:{},dataList:[],deductTypeList:[],total:0,listQuery:{enumDeductType:"",pageIndex:1,pageSize:10},listLoading:!1}},created:function(){this.initDeductTypeList(),this.getPageList()},methods:{initDeductTypeList:function(){var e=this,t={enumType:"DeductType"};C["a"].getEnumInfos(t).then((function(t){e.deductTypeList=t.data.datas})).catch((function(e){console.log(e)}))},getPageList:function(){var e=this;this.listLoading=!0,n["a"].queryEmployeeDeduct(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.dataList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},search:function(){this.listQuery.pageIndex=1,this.getPageList()},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var l="";"descending"===e.order&&(l="-"),"ascending"===e.order&&(l="+"),this.listQuery.order=l+e.prop,this.getPageList()},showDialog:function(e){this.$refs.editDialog.initDialog(e)},showAddDialog:function(e){this.$refs.addDialog.initDialog(e)},deductCalculate:function(e){var t=this;this.$confirm("确定更新工龄薪级吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n["a"].updateEmployeeDeduct({deductId:e.id}).then((function(e){e.succeed&&(t.$message({message:"更新工龄薪级成功",type:"success"}),t.getPageList())})).catch((function(e){console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消工龄薪级更新","info")}))},exportData:function(e){var t=this;this.$confirm("确定导出工龄薪级数据吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n["a"].exportEmployeeDeductCalculate({deductId:e.id}).then((function(e){var l=a("19de"),o="工龄薪级"+t.$moment().format("YYYYMMDDHHmmss")+".xlsx";e.data?l(e.data,o):l(e,o)})).catch((function(e){t.listLoadingForArticle=!1,console.log(e)}))})).catch((function(e){t.listLoadingForArticle=!1,e.succeed||t.$notice.message("取消导出","info")}))}}},S=w,_=Object(d["a"])(S,l,o,!1,null,null,null);t["default"]=_.exports}}]);