(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e8fa3"],{"8c24":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[a("el-form-item",[a("el-input",{attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),a("el-form-item",[a("el-input",{attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQuery.displayName,callback:function(t){e.$set(e.listQuery,"displayName",t)},expression:"listQuery.displayName"}})],1),a("el-form-item",[a("el-select",{attrs:{clearable:"",placeholder:"合同状态"},model:{value:e.listQuery.queryType,callback:function(t){e.$set(e.listQuery,"queryType",t)},expression:"listQuery.queryType"}},e._l(e.queryTypes,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-edit"},on:{click:e.remind}},[e._v("发送提醒")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{fixed:"",type:"selection",width:"40"}}),a("el-table-column",{attrs:{label:"唯一码",sortable:"custom",prop:"Employee.Uid"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.empUid))])]}}])}),a("el-table-column",{attrs:{label:"工号",sortable:"custom",prop:"Employee.EmpCode"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.empCode))])]}}])}),a("el-table-column",{attrs:{label:"姓名",sortable:"custom",prop:"Employee.DisplayName"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.empName))])]}}])}),a("el-table-column",{attrs:{label:"合同开始日期",sortable:"custom",prop:"StartDate"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.startDate?new Date(r.startDate).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"合同结束日期",sortable:"custom",prop:"EndDate"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.endDate?new Date(r.endDate).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"试用期合同结束日期","min-width":"120px",sortable:"custom",prop:"ProEndDate"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.proEndDate?new Date(r.proEndDate).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"是否下次提醒",sortable:"custom",prop:"IsNextTime"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.isNextTime?"是":"否"))])]}}])}),a("el-table-column",{attrs:{label:"下次提醒时间",sortable:"custom",prop:"IsNextTime"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.reminderDate?new Date(r.reminderDate).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{fixed:"right",label:"操作",align:"left","header-align":"center",width:"150","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("el-button",{staticStyle:{"margin-left":"45px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.sendRemindDialog(r)}}},[e._v(" 提醒 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),a("el-dialog",{attrs:{top:"5vh",title:"合同签订提醒",visible:e.sendRemindDialogVisible,width:"55%"},on:{"update:visible":function(t){e.sendRemindDialogVisible=t},close:e.closeSendRemindDialog}},[a("el-form",{ref:"ref_remindForm",attrs:{rules:e.rules,model:e.remindModel,"label-width":"140px"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"唯一码",prop:"empUid"}},[e._v(" "+e._s(e.empContract.empUid)+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"工号",prop:"empCode"}},[e._v(" "+e._s(e.empContract.empCode)+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"姓名",prop:"empName"}},[e._v(" "+e._s(e.empContract.empName)+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合同开始日期",prop:"oldStartDate"}},[e._v(" "+e._s(e.empContract.oldStartDate?new Date(e.empContract.oldStartDate).Format("yyyy-MM-dd"):"")+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合同结束日期",prop:"oldEndDate"}},[e._v(" "+e._s(e.empContract.oldEndDate?new Date(e.empContract.oldEndDate).Format("yyyy-MM-dd"):"")+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"试用期合同结束日期",prop:"proEndDate"}},[e._v(" "+e._s(e.empContract.proEndDate?new Date(e.empContract.proEndDate).Format("yyyy-MM-dd"):"")+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否发送提醒"}},[a("el-checkbox",{model:{value:e.remindModel.isSendRemind,callback:function(t){e.$set(e.remindModel,"isSendRemind",t)},expression:"remindModel.isSendRemind"}},[e._v("发送")])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否临近退休"}},[a("el-checkbox",{on:{change:e.retireChange},model:{value:e.isNearRetirement,callback:function(t){e.isNearRetirement=t},expression:"isNearRetirement"}})],1)],1),e.remindModel.isSendRemind?e._e():a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合同到期提醒时间",prop:"reminderDate"}},[a("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择下次合同到期提醒时间"},model:{value:e.remindModel.reminderDate,callback:function(t){e.$set(e.remindModel,"reminderDate",t)},expression:"remindModel.reminderDate"}})],1)],1)],1),e.remindModel.isSendRemind?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合同起始日期",prop:"startDate"}},[a("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择合同起始日期"},model:{value:e.remindModel.startDate,callback:function(t){e.$set(e.remindModel,"startDate",t)},expression:"remindModel.startDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合同结束日期",prop:e.isNearRetirement?"":"endDate"}},[a("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择合同结束日期"},model:{value:e.remindModel.endDate,callback:function(t){e.$set(e.remindModel,"endDate",t)},expression:"remindModel.endDate"}})],1)],1)],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.sendRemindDialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submitRemindForm}},[e._v("保 存")])],1)],1),a("el-dialog",{attrs:{top:"5vh",title:"合同签订提醒",visible:e.dialogBatchRemindVisible,width:"55%"},on:{"update:visible":function(t){e.dialogBatchRemindVisible=t},close:e.closeBatchRemindDialog}},[a("el-form",{ref:"ref_batchRemindForm",attrs:{rules:e.rules,model:e.remindModel,"label-width":"140px"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否发送提醒"}},[a("el-checkbox",{model:{value:e.remindModel.isSendRemind,callback:function(t){e.$set(e.remindModel,"isSendRemind",t)},expression:"remindModel.isSendRemind"}},[e._v("发送")])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否临近退休"}},[a("el-checkbox",{on:{change:e.retireChange2},model:{value:e.isNearRetirement,callback:function(t){e.isNearRetirement=t},expression:"isNearRetirement"}})],1)],1),e.remindModel.isSendRemind?e._e():a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合同到期提醒时间",prop:"reminderDate"}},[a("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择下次合同到期提醒时间"},model:{value:e.remindModel.reminderDate,callback:function(t){e.$set(e.remindModel,"reminderDate",t)},expression:"remindModel.reminderDate"}})],1)],1)],1),e.remindModel.isSendRemind?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合同起始日期",prop:"startDate"}},[a("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择合同起始日期"},model:{value:e.remindModel.startDate,callback:function(t){e.$set(e.remindModel,"startDate",t)},expression:"remindModel.startDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合同结束日期",prop:e.isNearRetirement?"":"endDate"}},[a("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择合同结束日期"},model:{value:e.remindModel.endDate,callback:function(t){e.$set(e.remindModel,"endDate",t)},expression:"remindModel.endDate"}})],1)],1)],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogBatchRemindVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.batchRemindForm}},[e._v("保 存")])],1)],1)],1)},i=[],n=(a("d81d"),a("d3b7"),a("ac1f"),a("841c"),a("e44c")),l={components:{},data:function(){var e=this,t=function(t,a,r){if(!a)return r();setTimeout((function(){var t=e.remindModel.endDate;if(t){var i=new Date(t),n=new Date(a);n<=i?r():r(new Error("合同起始日期不得晚于合同结束日期"))}else r()}),100)},a=function(t,a,r){if(!a)return r();setTimeout((function(){var t=e.remindModel.startDate;if(t){var i=new Date(t),n=new Date(a);n>=i?r():r(new Error("合同结束日期不得早于合同起始日期。"))}else r()}),100)};return{rules:{startDate:[{required:!0,message:"请选择合同起始日期",trigger:"blur"},{validator:t,trigger:"blur"}],endDate:[{required:!0,message:"请选择合同结束日期",trigger:"blur"},{validator:a,trigger:"blur"}],reminderDate:[{required:!0,message:"请选择合同结束日期",trigger:"blur"}]},sendRemindDialogVisible:!1,pageList:[],listQuery:{queryCondition:{},total:1,pageIndex:1,pageSize:10},listLoading:!1,queryTypes:[{id:1,name:"试用期合同"},{id:2,name:"到期合同"},{id:3,name:"下次提醒合同"}],empContract:{},remindModel:{},editIds:[],dialogBatchRemindVisible:!1,isNearRetirement:!1}},created:function(){this.getPageList()},methods:{sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.listQuery.order="",this.search()},search:function(){this.listQuery.pageIndex=1,this.getPageList()},getPageList:function(){var e=this;this.listLoading=!0,n["a"].queryRenewEmployeeContract(this.listQuery).then((function(t){t.succeed?(e.pageList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1}))},sendRemindDialog:function(e){this.remindModel={id:e.id,isSendRemind:!0},this.empContract={empUid:e.empUid,empCode:e.empCode,empName:e.empName,oldStartDate:e.startDate,oldEndDate:e.endDate,proEndDate:e.proEndDate},this.sendRemindDialogVisible=!0},closeSendRemindDialog:function(){this.$refs["ref_remindForm"].resetFields(),this.$refs["ref_remindForm"].clearValidate()},submitRemindForm:function(){var e=this;this.$refs["ref_remindForm"].validate((function(t){t&&(e.remindModel.isSendRemind?n["a"].sendEmailForRenewRemind(e.remindModel).then((function(t){t.succeed?(e.sendRemindDialogVisible=!1,e.search(),e.$notice.message("邮件提醒发送成功","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})):n["a"].updateNextTimeRemind(e.remindModel).then((function(t){t.succeed?(e.sendRemindDialogVisible=!1,e.search(),e.$notice.message("已更改为下个月提醒","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})))}))},handleSelectionChange:function(e){this.editIds=e.map((function(e){return e.id}))},remind:function(){this.editIds.length>0?this.dialogBatchRemindVisible=!0:this.$notice.message("请选择合同到期人员","warning")},closeBatchRemindDialog:function(){this.$refs["ref_batchRemindForm"].resetFields(),this.$refs["ref_batchRemindForm"].clearValidate()},batchRemindForm:function(){var e=this;this.$refs["ref_batchRemindForm"].validate((function(t){t&&(e.remindModel.Ids=e.editIds,e.remindModel.isSendRemind?n["a"].batchSendEmailForRenewRemind(e.remindModel).then((function(t){t.succeed?(e.dialogBatchRemindVisible=!1,e.search(),e.$notice.message("邮件提醒发送成功","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})):n["a"].batchUpdateNextTimeRemind(e.remindModel).then((function(t){t.succeed?(e.dialogBatchRemindVisible=!1,e.search(),e.$notice.message("已更改为下个月提醒","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})))}))},retireChange:function(){this.isNearRetirement&&this.$refs["ref_remindForm"].clearValidate("endDate")},retireChange2:function(){this.isNearRetirement&&this.$refs["ref_batchRemindForm"].clearValidate("endDate")}}},o=l,s=a("2877"),d=Object(s["a"])(o,r,i,!1,null,"16404c28",null);t["default"]=d.exports}}]);