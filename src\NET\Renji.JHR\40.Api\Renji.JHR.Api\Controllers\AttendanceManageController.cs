﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NPOI.SS.Formula.Functions;
using Renji.JHR.Api.Models;
using Renji.JHR.Api.Utils;
using Renji.JHR.Bll;
using Renji.JHR.Common;
using Renji.JHR.Common.Utility;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.DynamicQuery;
using Shinsoft.Core.EntityFrameworkCore;
using Shinsoft.Core.Json;
using Shinsoft.Core.Mvc;
using Shinsoft.Core.NLog;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;

namespace Renji.JHR.Api.Controllers
{
    public class AttendanceManageController : BaseApiController<AttendanceManageBll>
    {
        #region MiddleNightShiftFee 中夜班费

        /// <summary>
        /// 获取部门树（中夜班费人事），不同状态显示不同节点颜色
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取部门树（中夜班费人事）")]
        [Permission(Permissions.AttendanceManage.MiddleNightShiftFee, Permissions.AttendanceManage.MonthShiftRecordHR)]
        public BizResult<List<OrganizationQuery>> GetColorDeptTree_MiddleNightShift([FromQuery] AttMonthShiftRecordFilter filter)
        {
            var exps = this.NewExps<Entities.AttMonthShiftRecord>();
            exps.And(p => p.RecordMonth == filter.RecordMonth);
            var rEntities = this.Repo.GetEntities(exps);
            var colorList = new List<DeptColorQuery>();
            foreach (var item in rEntities)
            {
                if (item.DeptId.HasValue)
                {
                    var q = new DeptColorQuery
                    {
                        DeptId = item.DeptId.Value
                    };
                    q.Color = item.EnumStatus switch
                    {
                        AttMonthShiftRecordStatus.WaitLeaderConfirm or AttMonthShiftRecordStatus.WaitHrConfirm => "red",
                        AttMonthShiftRecordStatus.Confirmed => "green",
                        AttMonthShiftRecordStatus.UnCommitted => "orange",
                        _ => "",
                    };
                    colorList.Add(q);
                }
            }

            var entities = this.Repo.GetEntities<Department>();

            var all = entities.Maps<OrganizationQuery>();

            SemiNumericComparer comp = new();

            foreach (var model in all)
            {
                var dColor = colorList.FirstOrDefault(e => e.DeptId == model.ID);
                if (dColor != null)
                {
                    model.Color = dColor.Color;
                }
                var childern = all.Where(p => p.ParentId == model.ID).OrderBy(p => p.Code, comp).ToList();
                if (childern != null && childern.Any())
                {
                    childern.ForEach(cd =>
                    {
                        var cdColor = colorList.FirstOrDefault(e => e.DeptId == cd.ID);
                        if (cdColor != null)
                        {
                            cd.Color = cdColor.Color;
                        }
                    });
                    model.Children = childern;
                }
            }

            var models = all
                .Where(p => !p.ParentId.HasValue)
                .OrderBy(p => p.Code, comp)
                .ToList();

            return this.BizResult(models);
        }

        /// <summary>
        /// 获取部门中夜班费记录
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取部门中夜班费记录")]
        public BizResult<AttMonthShiftRecordModel> GetAttMonthShiftRecord([FromQuery] AttMonthShiftRecordFilter filter)
        {
            var result = this.BizResult<AttMonthShiftRecordModel>();
            Guid? deptId = filter.DeptId?.As<Guid>();
            var entity = this.Repo.GetEntity<AttMonthShiftRecord>(p => p.DeptId == deptId && p.RecordMonth == filter.RecordMonth);

            if (entity == null)
            {
                entity = new AttMonthShiftRecord()
                {
                    DeptId = deptId,
                    EnumStatus = AttMonthShiftRecordStatus.UnCommitted,
                    RecordMonth = filter.RecordMonth
                };
            }

            var model = entity.Map<AttMonthShiftRecordModel>();

            result.Data = model;
            return result;
        }

        /// <summary>
        /// 查询部门中夜班费明细
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询部门中夜班费明细")]
        public QueryResult<AttMonthShiftRecordDetailQuery> QueryAttMonthShiftRecordDetail([FromQuery] AttMonthShiftRecordDetailFilter filter)
        {
            DateTime? dt = filter.RecordMonth?.As<DateTime>();
            DateTime? dt2 = dt?.AddMonths(1);

            var empExps = this.NewExps<Entities.Employee>();
            if (!string.IsNullOrEmpty(filter.DeptId))
            {
                Guid deptId = filter.DeptId.As<Guid>();
                var deptIds = this.Repo.GetDeptByCurrentUser(deptId);
                if (deptIds.IndexOf(deptId) >= 0)
                {
                    empExps.And(p => deptId == p.DeptId);
                }
                else
                {
                    empExps.And(p => Guid.Empty == p.DeptId);
                }
            }
            else
            {
                empExps.And(p => Guid.Empty == p.DeptId);
            }

            var hiredId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Hired)?.ID;
            var leaveId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Leave)?.ID;
            empExps.And(p => p.EmployeeHR != null && (p.EmployeeHR.EmpStatusId == hiredId
                || p.EmployeeHR.EmpStatusId == leaveId && p.EmployeeHR.LeaveDate.HasValue &&
                (p.EmployeeHR.LeaveDate.Value > dt && p.EmployeeHR.LeaveDate.Value <= dt2 || p.EmployeeHR.LeaveDate.Value > dt2)
                || p.EmployeeHR.EmpStatusId == leaveId && p.EmployeeHR.RetireDate.HasValue &&
                (p.EmployeeHR.RetireDate.Value > dt && p.EmployeeHR.RetireDate.Value <= dt2 || p.EmployeeHR.RetireDate.Value > dt2)));
            int recoredCount = -1;

            var empEntities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(empExps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(empExps, filter.Order, filter.PageIndex, filter.PageSize);

            var empIds = empEntities.Select(e => e.ID).ToList();

            var attExps = this.NewExps<Entities.AttMonthShiftRecordDetail>();
            attExps.And(p => p.EmployeeId.HasValue && empIds.Contains(p.EmployeeId.Value));
            attExps.And(p => p.AttMonthShiftRecord != null && p.AttMonthShiftRecord.RecordMonth == filter.RecordMonth);
            if (filter.RecordId != null)
            {
                Guid recordId = Guid.Parse(filter.RecordId);
                attExps.And(p => p.RecordId == recordId);
            }
            else
            {
                attExps.And(p => p.RecordId == null);
            }
            List<AttMonthShiftRecordDetail> attDetailEntites;
            if (!string.IsNullOrEmpty(filter.Order))
            {
                attDetailEntites = this.Repo.GetEntities(attExps, "Employee." + filter.Order);
            }
            else
            {
                attDetailEntites = this.Repo.GetEntities(attExps);
            }

            var list = new List<AttMonthShiftRecordDetail>();
            empEntities.ForEach(e =>
            {
                var att = attDetailEntites.FirstOrDefault(d => d.EmployeeId == e.ID);
                if (att != null)
                {
                    list.Add(att);
                }
                else
                {
                    list.Add(new AttMonthShiftRecordDetail()
                    {
                        EmployeeId = e.ID,
                        Employee = e
                    });
                }
            });

            var models = list.Maps<AttMonthShiftRecordDetailQuery>();
            var attDetails = this.QueryResult(models, recoredCount, filter);
            return attDetails;
        }

        /// <summary>
        /// 保存中夜班费记录明细
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "保存中夜班费记录明细")]
        [Permission(Permissions.AttendanceManage.MiddleNightShiftFee, Permissions.AttendanceManage.MonthShiftRecordApply)]
        public BizResult<AttMonthShiftRecordModel> SaveAttMonthShiftRecord([FromBody] AttMonthShiftRecordModel recordModel)
        {
            var entity = recordModel.Map<AttMonthShiftRecord>();
            entity.AttMonthShiftRecordDetail = recordModel.Details.Map<List<AttMonthShiftRecordDetail>>();
            var result = this.Repo.HandleAttMonthShiftRecord(entity, AttMonthShiftRecord_Handle.Save);
            return result.Map<AttMonthShiftRecordModel>();
        }

        /// <summary>
        /// 批量确认中夜班费
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [Permission(Permissions.AttendanceManage.MiddleNightShiftFee, Permissions.AttendanceManage.MonthShiftRecordHR)]
        [LogApi(ApiType.Save, Operate = "批量确认中夜班费")]
        public BizResult<AttMonthShiftRecordModel> BatchConfirmAttMonthShiftRecord([FromBody] AttMonthShiftRecordModel recordModel)
        {
            var result = this.Repo.BatchConfirmAttMonthShiftRecord(recordModel.RecordMonth);
            return result.Map<AttMonthShiftRecordModel>();
        }

        /// <summary>
        /// 提交中夜班费记录明细
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "提交中夜班费记录明细")]
        [Permission(Permissions.AttendanceManage.MiddleNightShiftFee, Permissions.AttendanceManage.MonthShiftRecordApply)]
        public BizResult<AttMonthShiftRecordModel> SubmitAttMonthShiftRecord([FromBody] AttMonthShiftRecordModel recordModel)
        {
            var entity = recordModel.Map<AttMonthShiftRecord>();
            entity.AttMonthShiftRecordDetail = recordModel.Details.Map<List<AttMonthShiftRecordDetail>>();
            var result = this.Repo.HandleAttMonthShiftRecord(entity, AttMonthShiftRecord_Handle.Submit);
            return result.Map<AttMonthShiftRecordModel>();
        }

        /// <summary>
        /// 确认（通过）中夜班费
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "确认（通过）中夜班费")]
        [Permission(Permissions.AttendanceManage.MiddleNightShiftFee, Permissions.AttendanceManage.MonthShiftRecordHR)]
        public BizResult<AttMonthShiftRecordModel> ConfirmAttMonthShiftRecord([FromBody] AttMonthShiftRecordModel recordModel)
        {
            var entity = recordModel.Map<AttMonthShiftRecord>();
            entity.AttMonthShiftRecordDetail = recordModel.Details.Map<List<AttMonthShiftRecordDetail>>();
            var result = this.Repo.HandleAttMonthShiftRecord(entity, AttMonthShiftRecord_Handle.Confirm);
            return result.Map<AttMonthShiftRecordModel>();
        }

        /// <summary>
        /// 退回中夜班费
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "退回中夜班费")]
        [Permission(Permissions.AttendanceManage.MiddleNightShiftFee, Permissions.AttendanceManage.MonthShiftRecordHR)]
        public BizResult<AttMonthShiftRecordModel> RejectAttMonthShiftRecord([FromBody] AttMonthShiftRecordModel recordModel)
        {
            var entity = recordModel.Map<AttMonthShiftRecord>();
            entity.AttMonthShiftRecordDetail = recordModel.Details.Map<List<AttMonthShiftRecordDetail>>();
            var result = this.Repo.HandleAttMonthShiftRecord(entity, AttMonthShiftRecord_Handle.Reject);
            return result.Map<AttMonthShiftRecordModel>();
        }

        /// <summary>
        /// 查询中夜班费明细
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询中夜班费明细")]
        [Permission(Permissions.AttendanceManage.MiddleNightShiftFee, Permissions.AttendanceManage.MonthShiftRecordUpdate, Permissions.AttendanceManage.holidayOvertimeFeeSearch)]
        public QueryResult<AttMonthShiftRecordDetailQuery> SearchAttMonthShiftRecordDetail([FromQuery] AttMonthShiftSearchFilter filter)
        {
            var exps = this.NewExps<AttMonthShiftRecord>();

            if (!filter.RecordMonth.IsEmpty())
            {
                if (filter.EndRecordMonth.IsEmpty())
                {
                    exps.And(p => p.RecordMonth == filter.RecordMonth);
                }
                else
                {
                    exps.And(p => p.RecordMonth != null && p.RecordMonth.CompareTo(filter.RecordMonth) >= 0);
                }
            }

            if (!filter.EndRecordMonth.IsEmpty())
            {
                exps.And(p => p.RecordMonth != null && p.RecordMonth.CompareTo(filter.EndRecordMonth) <= 0);
            }

            if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
            {
                var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                exps.And(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));
            }
            if (filter.DeptIds != null && filter.DeptIds.Length > 0)
            {
                exps.And(p => p.DeptId.HasValue && filter.DeptIds.Contains(p.DeptId.Value));
            }

            //人事已审批的数据
            exps.And(p => p.EnumStatus == AttMonthShiftRecordStatus.Confirmed);

            var entities = this.Repo.GetEntities(exps);
            var recordIds = entities.Select(e => e.ID).ToList();
            var detailExps = this.NewExps<AttMonthShiftRecordDetail>();
            detailExps.Add(d => d.RecordId.HasValue && recordIds.Contains(d.RecordId.Value));

            if (!filter.EmpCode.IsEmpty())
            {
                detailExps.Add(d => d.Employee != null && d.Employee.EmpCode.Contains(filter.EmpCode!));
            }
            if (!filter.EmpUid.IsEmpty())
            {
                detailExps.Add(d => d.Employee != null && d.Employee.Uid.Equals(filter.EmpUid!));
            }
            if (!filter.EmpName.IsEmpty())
            {
                detailExps.Add(d => d.Employee != null && d.Employee.DisplayName.Contains(filter.EmpName!));
            }
            if (filter.GtZeroValue)
            {
                detailExps.Add(d => d.YB.HasValue && d.YB.Value > 0
                                    || d.ZB.HasValue && d.ZB.Value > 0
                                    || d.B24.HasValue && d.B24.Value > 0
                                    || d.JZZB.HasValue && d.JZZB.Value > 0
                                    || d.JZYB.HasValue && d.JZYB.Value > 0
                                    || d.JZ24.HasValue && d.JZ24.Value > 0
                                    || d.HLAZB.HasValue && d.HLAZB.Value > 0
                                    || d.HLAYB.HasValue && d.HLAYB.Value > 0
                                    || d.HLA24.HasValue && d.HLA24.Value > 0
                                    || d.QT1.HasValue && d.QT1.Value > 0
                                    || d.QT2.HasValue && d.QT2.Value > 0
                                    || d.HQZB.HasValue && d.HQZB.Value > 0
                                    || d.HQYB.HasValue && d.HQYB.Value > 0
                                    || d.HQ12.HasValue && d.HQ12.Value > 0
                                    || d.HQ24.HasValue && d.HQ24.Value > 0);
            }

            int recoredCount = -1;
            var detaileEntities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(detailExps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(detailExps, filter.Order, filter.PageIndex, filter.PageSize);
            var models = detaileEntities.Maps<AttMonthShiftRecordDetailQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 查询中夜班费明细—中夜班费修改
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询中夜班费明细—中夜班费修改")]
        [Permission(Permissions.AttendanceManage.MiddleNightShiftFee, Permissions.AttendanceManage.MonthShiftRecordUpdate)]
        public QueryResult<AttMonthShiftRecordDetailQuery> SearchAttMonthShiftRecordDetail_Update([FromQuery] AttMonthShiftSearchFilter filter)
        {
            if (filter.GtZeroValue)
            {
                var exps = this.NewExps<AttMonthShiftRecord>();

                if (!filter.RecordMonth.IsEmpty())
                {
                    exps.And(p => p.RecordMonth == filter.RecordMonth);
                }

                if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
                {
                    var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                    exps.And(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));
                }

                var entities = this.Repo.GetEntities(exps);
                var recordIds = entities.Select(e => e.ID).ToList();
                var detailExps = this.NewExps<AttMonthShiftRecordDetail>();
                detailExps.Add(d => d.RecordId.HasValue && recordIds.Contains(d.RecordId.Value));

                if (!filter.EmpCode.IsEmpty())
                {
                    detailExps.Add(d => d.Employee != null && d.Employee.EmpCode.Contains(filter.EmpCode!));
                }
                if (!filter.EmpName.IsEmpty())
                {
                    detailExps.Add(d => d.Employee != null && d.Employee.DisplayName.Contains(filter.EmpName!));
                }
                if (filter.GtZeroValue)
                {
                    detailExps.Add(d => d.YB.HasValue || d.ZB.HasValue || d.B24.HasValue || d.JZZB.HasValue
                                        || d.JZYB.HasValue || d.JZ24.HasValue || d.HLAZB.HasValue || d.HLAYB.HasValue
                                        || d.HLA24.HasValue || d.QT1.HasValue || d.QT2.HasValue || d.HQZB.HasValue
                                        || d.HQYB.HasValue || d.HQ12.HasValue || d.HQ24.HasValue);
                }

                int recoredCount = -1;
                var detaileEntities = filter.RequireRecordCount
                     ? this.Repo.GetEntities(detailExps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                     : this.Repo.GetEntities(detailExps, filter.Order, filter.PageIndex, filter.PageSize);
                var models = detaileEntities.Maps<AttMonthShiftRecordDetailQuery>();

                return this.QueryResult(models, recoredCount, filter);
            }
            else
            {
                DateTime? dt = filter.RecordMonth?.As<DateTime>();

                var empExps = this.NewExps<Entities.Employee>();

                if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
                {
                    var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                    empExps.And(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));
                }
                if (!filter.EmpCode.IsEmpty())
                {
                    empExps.Add(d => d.EmpCode.Contains(filter.EmpCode!));
                }
                if (!filter.EmpName.IsEmpty())
                {
                    empExps.Add(d => d.DisplayName.Contains(filter.EmpName!));
                }

                //var hiredId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Hired).ID;
                //var leaveId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Leave).ID;
                //empExps.And(p => p.EmployeeHR.EmpStatusId == hiredId
                //	|| p.EmployeeHR.EmpStatusId == leaveId && p.EmployeeHR.LeaveDate.HasValue && p.EmployeeHR.LeaveDate.Value > SysDateTime.Now);
                int recoredCount = -1;

                var empEntities = filter.RequireRecordCount
                     ? this.Repo.GetEntities(empExps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                     : this.Repo.GetEntities(empExps, filter.Order, filter.PageIndex, filter.PageSize);

                var empIds = empEntities.Select(e => e.ID).ToList();

                var attExps = this.NewExps<Entities.AttMonthShiftRecordDetail>();
                attExps.And(p => p.EmployeeId.HasValue && empIds.Contains(p.EmployeeId.Value));
                attExps.And(p => p.AttMonthShiftRecord != null && p.AttMonthShiftRecord.RecordMonth == filter.RecordMonth);

                //if (filter.GtZeroValue)
                //{
                //	attExps.Add(d => d.YB.HasValue || d.ZB.HasValue || d.B24.HasValue || d.JZZB.HasValue
                //						|| d.JZYB.HasValue || d.JZ24.HasValue || d.HLAZB.HasValue || d.HLAYB.HasValue
                //						|| d.HLA24.HasValue || d.QT1.HasValue || d.QT2.HasValue || d.HQZB.HasValue
                //						|| d.HQYB.HasValue || d.HQ12.HasValue || d.HQ24.HasValue);
                //}
                List<AttMonthShiftRecordDetail> attDetailEntites;
                attDetailEntites = this.Repo.GetEntities(attExps);

                var list = new List<AttMonthShiftRecordDetail>();
                empEntities.ForEach(e =>
                {
                    var att = attDetailEntites.FirstOrDefault(d => d.EmployeeId == e.ID);
                    if (att != null)
                    {
                        list.Add(att);
                    }
                    else
                    {
                        list.Add(new AttMonthShiftRecordDetail()
                        {
                            EmployeeId = e.ID,
                            Employee = e
                        });
                    }
                });

                var models = list.Maps<AttMonthShiftRecordDetailQuery>();
                var attDetails = this.QueryResult(models, recoredCount, filter);
                return attDetails;
            }
        }

        /// <summary>
        /// 修改中夜班费明细
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "修改中夜班费明细")]
        [Permission(Permissions.AttendanceManage.MiddleNightShiftFee, Permissions.AttendanceManage.MonthShiftRecordUpdate)]
        public BizResult UpdateAttMonthShiftRecordDetail([FromBody] AttMonthShiftRecordDetailModel recordModel)
        {
            var entity = recordModel.Map<AttMonthShiftRecordDetail>();
            var result = this.Repo.UpdateAttMonthShiftRecordDetail(entity, recordModel.RecordMonth);
            return result;
        }

        /// <summary>
        /// 导出中夜班费明细
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "导出中夜班费明细")]
        [Permission(Permissions.AttendanceManage.MiddleNightShiftFee, Permissions.AttendanceManage.MonthShiftRecordUpdate)]
        public IActionResult Get_MiddleNightShiftReportExcel([FromQuery] AttMonthShiftSearchFilter filter)
        {
            var exps = this.NewExps<AttMonthShiftRecord>();

            if (!filter.RecordMonth.IsEmpty())
            {
                if (filter.EndRecordMonth.IsEmpty())
                {
                    exps.And(p => p.RecordMonth == filter.RecordMonth);
                }
                else
                {
                    exps.And(p => p.RecordMonth != null && p.RecordMonth.CompareTo(filter.RecordMonth) >= 0);
                }
            }

            if (!filter.EndRecordMonth.IsEmpty())
            {
                exps.And(p => p.RecordMonth != null && p.RecordMonth.CompareTo(filter.EndRecordMonth) <= 0);
            }

            if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
            {
                var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                exps.And(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));
            }
            if (filter.DeptIds != null && filter.DeptIds.Length > 0)
            {
                exps.And(p => p.DeptId.HasValue && filter.DeptIds.Contains(p.DeptId.Value));
            }
            //人事已审批的数据
            exps.And(p => p.EnumStatus == AttMonthShiftRecordStatus.Confirmed);

            var entities = this.Repo.GetEntities(exps);
            var recordIds = entities.Select(e => e.ID).ToList();
            var detailExps = this.NewExps<AttMonthShiftRecordDetail>();
            detailExps.Add(d => d.RecordId.HasValue && recordIds.Contains(d.RecordId.Value));

            if (!filter.EmpCode.IsEmpty())
            {
                detailExps.Add(d => d.Employee != null && d.Employee.EmpCode.Contains(filter.EmpCode!));
            }
            if (!filter.EmpUid.IsEmpty())
            {
                detailExps.Add(d => d.Employee != null && d.Employee.Uid.Equals(filter.EmpUid!));
            }
            if (!filter.EmpName.IsEmpty())
            {
                detailExps.Add(d => d.Employee != null && d.Employee.DisplayName.Contains(filter.EmpName!));
            }
            if (filter.GtZeroValue)
            {
                detailExps.Add(d => d.YB.HasValue && d.YB.Value > 0
                                    || d.ZB.HasValue && d.ZB.Value > 0
                                    || d.B24.HasValue && d.B24.Value > 0
                                    || d.JZZB.HasValue && d.JZZB.Value > 0
                                    || d.JZYB.HasValue && d.JZYB.Value > 0
                                    || d.JZ24.HasValue && d.JZ24.Value > 0
                                    || d.HLAZB.HasValue && d.HLAZB.Value > 0
                                    || d.HLAYB.HasValue && d.HLAYB.Value > 0
                                    || d.HLA24.HasValue && d.HLA24.Value > 0
                                    || d.QT1.HasValue && d.QT1.Value > 0
                                    || d.QT2.HasValue && d.QT2.Value > 0
                                    || d.HQZB.HasValue && d.HQZB.Value > 0
                                    || d.HQYB.HasValue && d.HQYB.Value > 0
                                    || d.HQ12.HasValue && d.HQ12.Value > 0
                                    || d.HQ24.HasValue && d.HQ24.Value > 0);
            }

            var detaileEntities = this.Repo.GetEntities(detailExps, filter.Order);
            var models = detaileEntities.Maps<AttMonthShift>();

            DataTable? dt = Utility.ToDataTable(models);//list转datatable

            string exportFileName = $"{AppDomain.CurrentDomain.BaseDirectory}" + string.Format(Common.Consts.Exports.FileExcel, Common.Consts.Exports.FileMiddleNight);//生成后存放的地址 和文件名

            var _NpoiExcelUtility = new NpoiExcelUtility();
            if (dt != null)
            {
				dt.Columns[0].Caption = "唯一码";
				dt.Columns[1].Caption = "工号";
				dt.Columns[2].Caption = "姓名";
				dt.Columns[3].Caption = "部门";
				dt.Columns[4].Caption = "在职方式";
				dt.Columns[5].Caption = "中班";
				dt.Columns[6].Caption = "夜班";
				dt.Columns[7].Caption = "24小时班";
				dt.Columns[8].Caption = "急诊中班";
				dt.Columns[9].Caption = "急诊夜班";
				dt.Columns[10].Caption = "急诊24小时";
				dt.Columns[11].Caption = "护理A挡中班";
				dt.Columns[12].Caption = "护理A挡夜班";
				dt.Columns[13].Caption = "护理A档24小时";
				dt.Columns[14].Caption = "其他值班1";
				dt.Columns[15].Caption = "其他值班2";

				_NpoiExcelUtility.CreatExcelSheet(Common.Consts.Exports.FileMiddleNight, dt);//生成数据   这里需要注意  如果datatable没值会报错  所以要判断
            }
            var bytes = _NpoiExcelUtility.SaveExcelBytes();
            return File(bytes, Common.Consts.Exports.ContentType, Path.GetFileName(exportFileName));
        }

        #endregion MiddleNightShiftFee 中夜班费

        #region HolidayOT 节日加班

        /// <summary>
        /// 获取部门树（节日加班人事），不同状态显示不同节点颜色
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取部门树（节日加班人事）")]
        [Permission(Permissions.AttendanceManage.HolidayOvertimeFee, Permissions.AttendanceManage.holidayOvertimeFeeHr)]
        public BizResult<AttHolidayOTDepts> GetColorDeptTree_HolidayOT([FromQuery] AttHolidayOTRecordFilter filter)
        {
            var exps = this.NewExps<Entities.AttHolidayOTRecord>();
            exps.And(p => p.RecordDate == filter.RecordDate);
            var rEntities = this.Repo.GetEntities(exps);
            var colorList = new List<DeptColorQuery>();
            foreach (var item in rEntities)
            {
                if (item.DeptId.HasValue)
                {
                    var q = new DeptColorQuery
                    {
                        DeptId = item.DeptId.Value,
                        Color = item.EnumStatus switch
                        {
                            AttHolidayOTRecordStatus.DocMakerCommit => "red",
                            AttHolidayOTRecordStatus.Confirmed => "green",
                            AttHolidayOTRecordStatus.UnCommitted => "orange",
                            _ => "",
                        }
                    };
                    colorList.Add(q);
                }
            }

            var entities = this.Repo.GetEntities<Department>();

            var all = entities.Maps<OrganizationQuery>();

            SemiNumericComparer comp = new();

            foreach (var model in all)
            {
                var dColor = colorList.FirstOrDefault(e => e.DeptId == model.ID);
                if (dColor != null)
                {
                    model.Color = dColor.Color;
                }
                var childern = all.Where(p => p.ParentId == model.ID).OrderBy(p => p.Code, comp).ToList();
                if (childern != null && childern.Any())
                {
                    childern.ForEach(cd =>
                    {
                        var cdColor = colorList.FirstOrDefault(e => e.DeptId == cd.ID);
                        if (cdColor != null)
                        {
                            cd.Color = cdColor.Color;
                        }
                    });
                    model.Children = childern;
                }
            }

            var models = all
                .Where(p => !p.ParentId.HasValue)
                .OrderBy(p => p.Code, comp)
                .ToList();

            var asdList = new List<AttHolidayOTStatusDept>();

            AttHolidayOTStatusDept commitedDept = new()
            {
                Status = (int)AttHolidayOTRecordStatus.DocMakerCommit,
                Depts = rEntities.Where(e => e.EnumStatus == AttHolidayOTRecordStatus.DocMakerCommit && e.DeptId.HasValue).Select(e =>
                {
                    return new SelectModel { Label = e.Department?.Name, Value = e.DeptId?.ToString() };
                }).ToList()
            };
            asdList.Add(commitedDept);

            AttHolidayOTStatusDept approvedDept = new();
            approvedDept.Status = (int)AttHolidayOTRecordStatus.Confirmed;
            approvedDept.Depts = rEntities.Where(e => e.EnumStatus == AttHolidayOTRecordStatus.Confirmed && e.DeptId.HasValue).Select(e =>
            {
                return new SelectModel { Label = e.Department?.Name, Value = e.DeptId?.ToString() };
            }).ToList();
            asdList.Add(approvedDept);

            AttHolidayOTStatusDept unCommitedDept = new()
            {
                Status = (int)AttHolidayOTRecordStatus.UnCommitted
            };

            var outDeptIds = rEntities.Where(e => (e.EnumStatus == AttHolidayOTRecordStatus.Confirmed || e.EnumStatus == AttHolidayOTRecordStatus.DocMakerCommit) && e.DeptId.HasValue).Select(e => e.DeptId);
            unCommitedDept.Depts = entities.Where(e => !outDeptIds.Contains(e.ID)).Select(e =>
            {
                return new SelectModel { Label = e.Name, Value = e.ID.ToString() };
            }).ToList();
            asdList.Add(unCommitedDept);

            AttHolidayOTDepts resultModel = new()
            {
                ColorDepts = models,
                StatusDepts = asdList
            };

            return this.BizResult(resultModel);
        }

        /// <summary>
        /// 获取节日加班记录
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取节日加班记录")]
        public BizResult<AttHolidayOTRecordModel> GetAttHolidayOTRecord([FromQuery] AttHolidayOTRecordFilter filter)
        {
            var result = this.BizResult<AttHolidayOTRecordModel>();
            AttHolidayOTRecord entity;
            Guid? deptId = filter.DeptId?.As<Guid>();
            var exps = this.Repo.GetEntities<AttHolidayOTRecord>(p => p.DeptId == deptId
            && p.RecordDate == filter.RecordDate);

            if (exps.Count == 0)
            {
                entity = new AttHolidayOTRecord()
                {
                    DeptId = deptId,
                    EnumStatus = AttHolidayOTRecordStatus.UnCommitted,
                    RecordDate = filter.RecordDate
                };
            }
            else
            {
                entity = exps.First();
            }

            var model = entity.Map<AttHolidayOTRecordModel>();

            result.Data = model;
            return result;
        }

        /// <summary>
        /// 查询节日加班记录明细
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询节日加班记录明细")]
        public QueryResult<AttHolidayOTRecordDetailQuery> QueryAttHolidayOTRecordDetail([FromQuery] AttMonthShiftRecordDetailFilter filter)
        {
            Guid? deptId = filter.DeptId?.As<Guid>();
            DateTime? rm = filter.RecordMonth?.As<DateTime>();
            DateTime? dt = rm?.AddDays(1);
            var empExps = this.NewExps<Entities.Employee>();
            //var deptIds = this.Repo.GetDeptByCurrentUser(deptId);
            empExps.And(p => deptId == p.DeptId);
            var hiredId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Hired)?.ID;
            var leaveId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Leave)?.ID;
            empExps.And(p => p.EmployeeHR != null && (p.EmployeeHR.EmpStatusId == hiredId
                || p.EmployeeHR.EmpStatusId == leaveId && p.EmployeeHR.LeaveDate.HasValue && (p.EmployeeHR.LeaveDate.Value > dt)
                || p.EmployeeHR.EmpStatusId == leaveId && p.EmployeeHR.RetireDate.HasValue && (p.EmployeeHR.RetireDate.Value > dt)));
            int recoredCount = -1;

            var empEntities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(empExps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(empExps, filter.Order, filter.PageIndex, filter.PageSize);

            var empIds = empEntities.Select(e => e.ID).ToList();

            var attExps = this.NewExps<Entities.AttHolidayOTRecordDetail>();
            attExps.And(p => p.EmployeeId.HasValue && empIds.Contains(p.EmployeeId.Value));
            attExps.And(p => p.AttHolidayOTRecord != null && p.AttHolidayOTRecord.RecordDate == rm);
            if (filter.RecordId != null)
            {
                Guid recordId = Guid.Parse(filter.RecordId);
                attExps.And(p => p.RecordId == recordId);
            }
            else
            {
                attExps.And(p => p.RecordId == null);
            }
            List<AttHolidayOTRecordDetail> attDetailEntites;
            if (!string.IsNullOrEmpty(filter.Order))
            {
                attDetailEntites = this.Repo.GetEntities(attExps, "Employee." + filter.Order);
            }
            else
            {
                attDetailEntites = this.Repo.GetEntities(attExps);
            }

            var list = new List<AttHolidayOTRecordDetail>();
            empEntities.ForEach(e =>
            {
                var att = attDetailEntites.FirstOrDefault(d => d.EmployeeId == e.ID);
                if (att != null)
                {
                    list.Add(att);
                }
                else
                {
                    list.Add(new AttHolidayOTRecordDetail()
                    {
                        EmployeeId = e.ID,
                        Employee = e,
                        EnumOverTimeType = OverTimeType.None
                    });
                }
            });

            var models = list.Maps<AttHolidayOTRecordDetailQuery>();
            var attDetails = this.QueryResult(models, recoredCount, filter);
            return attDetails;
        }

        /// <summary>
        /// 保存节日加班记录明细
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "保存节日加班记录明细")]
        [Permission(Permissions.AttendanceManage.HolidayOvertimeFee, Permissions.AttendanceManage.holidayOvertimeFeeApply)]
        public BizResult<AttHolidayOTRecordModel> SaveAttHolidayOTRecord([FromBody] AttHolidayOTRecordModel recordModel)
        {
            var entity = recordModel.Map<AttHolidayOTRecord>();
            entity.AttHolidayOTRecordDetail = recordModel.Details?.Map<List<AttHolidayOTRecordDetail>>();
            var result = this.Repo.HandleAttHolidayOTRecord(entity, AttHolidayOTRecord_Handle.Save);
            return result.Map<AttHolidayOTRecordModel>();
        }

        /// <summary>
        /// 批量确认节日加班记录
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "批量确认节日加班记录")]
        [Permission(Permissions.AttendanceManage.HolidayOvertimeFee, Permissions.AttendanceManage.holidayOvertimeFeeHr)]
        public BizResult<AttHolidayOTRecordModel> BatchConfirmAttHolidayOTRecord([FromBody] AttHolidayOTRecordModel recordModel)
        {
            var result = this.Repo.BatchConfirmAttHolidayOTRecord(recordModel.RecordDate);
            return result.Map<AttHolidayOTRecordModel>();
        }

        /// <summary>
        /// 提交节日加班记录明细
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "提交节日加班记录明细")]
        [Permission(Permissions.AttendanceManage.HolidayOvertimeFee, Permissions.AttendanceManage.holidayOvertimeFeeApply)]
        public BizResult<AttHolidayOTRecordModel> SubmitAttHolidayOTRecord([FromBody] AttHolidayOTRecordModel recordModel)
        {
            var entity = recordModel.Map<AttHolidayOTRecord>();
            entity.AttHolidayOTRecordDetail = recordModel.Details?.Map<List<AttHolidayOTRecordDetail>>();
            var result = this.Repo.HandleAttHolidayOTRecord(entity, AttHolidayOTRecord_Handle.Submit);
            return result.Map<AttHolidayOTRecordModel>();
        }

        /// <summary>
        /// 确认节日加班
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "确认节日加班")]
        [Permission(Permissions.AttendanceManage.HolidayOvertimeFee, Permissions.AttendanceManage.holidayOvertimeFeeHr)]
        public BizResult<AttHolidayOTRecordModel> ConfirmAttHolidayOTRecord([FromBody] AttHolidayOTRecordModel recordModel)
        {
            var entity = recordModel.Map<AttHolidayOTRecord>();
            entity.AttHolidayOTRecordDetail = recordModel.Details?.Map<List<AttHolidayOTRecordDetail>>();
            var result = this.Repo.HandleAttHolidayOTRecord(entity, AttHolidayOTRecord_Handle.Confirm);
            return result.Map<AttHolidayOTRecordModel>();
        }

        /// <summary>
        /// 退回节日加班
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "退回节日加班")]
        [Permission(Permissions.AttendanceManage.HolidayOvertimeFee, Permissions.AttendanceManage.holidayOvertimeFeeHr)]
        public BizResult<AttHolidayOTRecordModel> RejectAttHolidayOTRecord([FromBody] AttHolidayOTRecordModel recordModel)
        {
            var entity = recordModel.Map<AttHolidayOTRecord>();
            entity.AttHolidayOTRecordDetail = recordModel.Details?.Map<List<AttHolidayOTRecordDetail>>();
            var result = this.Repo.HandleAttHolidayOTRecord(entity, AttHolidayOTRecord_Handle.Reject);
            return result.Map<AttHolidayOTRecordModel>();
        }

        /// <summary>
        /// 查询节日加班
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询节日加班")]
        [Permission(Permissions.AttendanceManage.HolidayOvertimeFee, Permissions.AttendanceManage.holidayOvertimeFeeSearch, Permissions.AttendanceManage.holidayOvertimeFeeUpdate)]
        public QueryResult<Emp_AttHolidayOT> SearchAttHolidayOT([FromQuery] AttHolidayOTSearchFilter filter)
        {
            var oTDates = new List<DateTime>();
            var oTDetailExps = this.NewExps<AttHolidayOTRecordDetail>();

            if (filter.RecordDate.HasValue)
            {
                if (filter.AddDay.HasValue)
                {
                    var recordDate = filter.RecordDate.Value;
                    for (int i = 0; i < filter.AddDay; i++)
                    {
                        var aDate = recordDate.AddDays(i);
                        oTDates.Add(aDate);
                    }

                    var addDay = filter.AddDay.Value - 1;
                    var dateAdd = recordDate.AddDays(addDay);

                    oTDetailExps.And(e => e.OTDate >= recordDate && e.OTDate <= dateAdd);
                }
                else
                {
                    oTDates.Add(filter.RecordDate.Value);
                    oTDetailExps.And(e => e.OTDate == filter.RecordDate.Value);
                }
            }
            if (filter.OnlyOTValue)
            {
                oTDetailExps.And(e => e.EnumOverTimeType != OverTimeType.None);
            }
            oTDates = oTDates.OrderBy(e => e).ToList();
            var oTDateStrs = oTDates.Select(e => e.ToString("yyyy-MM-dd")).ToList();

            var oTDetails = this.Repo.GetEntities(oTDetailExps);

            var empIds = oTDetails.Select(e => e.EmployeeId).Distinct().ToList();

            var exps = this.NewExps<Employee>();
            if (!string.IsNullOrEmpty(filter.EmpCode))
            {
                exps.Add(e => e.EmpCode.Contains(filter.EmpCode));
            }
            if (!string.IsNullOrEmpty(filter.EmpName))
            {
                exps.Add(e => e.DisplayName.Contains(filter.EmpName));
            }
            if (!filter.EmpUid.IsEmpty())
            {
                exps.Add(e => e.Uid.Equals(filter.EmpUid));
            }
            if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
            {
                var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                exps.And(e => e.DeptId.HasValue && deptIds.Contains(e.DeptId.Value));
            }
            if (filter.DeptIds != null && filter.DeptIds.Length > 0)
            {
                exps.And(p => p.DeptId.HasValue && filter.DeptIds.Contains(p.DeptId.Value));
            }
            if (filter.OnlyOTValue)
            {
                if (empIds != null && empIds.Count > 0)
                {
                    exps.And(e => empIds.Contains(e.ID));
                }
                else
                {
                    exps.And(e => 1 == -1);
                }
            }

            int recoredCount = -1;
            var empEntities = new List<Employee>();
            empEntities = filter.RequireRecordCount
                ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var empOts = new List<Emp_AttHolidayOT>();
            empEntities.ForEach(e =>
            {
                var empOt = new Emp_AttHolidayOT
                {
                    EmpUid = e.Uid.ToString(),
                    EmpCode = e.EmpCode,
                    EmpName = e.DisplayName,
                    HireStyleName = e.EmployeeHR?.HireStyle?.Name,
                    EmpDept = e.Department?.Name,
                    Details = new List<Emp_AttHolidayOTDetail>()
                };
                var empOTDetails = oTDetails.Where(otd => otd.EmployeeId == e.ID);
                oTDates.ForEach(date =>
                {
                    var otDetail = empOTDetails.FirstOrDefault(e => e.OTDate == date);
                    Emp_AttHolidayOTDetail emp_AttHolidayOTDetail = new();
                    if (otDetail != null)
                    {
                        emp_AttHolidayOTDetail.EnumOverTimeType = otDetail.EnumOverTimeType;
                        emp_AttHolidayOTDetail.RecordDate = otDetail.OTDate;
                        if (empOt.EnumStatusDesc.IsEmpty())
                        {
                            empOt.EnumStatusDesc = otDetail.AttHolidayOTRecord?.EnumStatusDesc;
                        }
                    }
                    empOt.Details.Add(emp_AttHolidayOTDetail);
                    empOt.RecordDates = oTDateStrs;
                });
                empOts.Add(empOt);
            });

            return this.QueryResult(empOts, recoredCount, filter);
        }

        /// <summary>
        /// 查询节日加班
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询节日加班")]
        [Permission(Permissions.AttendanceManage.HolidayOvertimeFee, Permissions.AttendanceManage.holidayOvertimeFeeSearch, Permissions.AttendanceManage.holidayOvertimeFeeUpdate)]
        public QueryResult<Emp_AttHolidayOT> SearchAttHolidayOTRecordDetail([FromQuery] AttHolidayOTSearchFilter filter)
        {
            var exps = this.NewExps<AttHolidayOTRecord>();

            if (filter.RecordDate != null)
            {
                if (filter.AddDay.HasValue)
                {
                    var addDay = filter.AddDay.Value - 1;
                    var recordDate = filter.RecordDate.Value;
                    var dateAdd = recordDate.AddDays(addDay);
                    exps.And(p => p.RecordDate >= recordDate && p.RecordDate <= dateAdd);
                }
                else
                {
                    exps.And(p => p.RecordDate == filter.RecordDate);
                }
            }

            if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
            {
                var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                exps.And(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));
            }
            //人事已审批的数据
            exps.And(p => p.EnumStatus == AttHolidayOTRecordStatus.Confirmed);

            var entities = this.Repo.GetEntities(exps);
            var recordIds = entities.Select(e => e.ID).ToList();
            var detailExps = this.NewExps<AttHolidayOTRecordDetail>();
            detailExps.Add(d => d.RecordId.HasValue && recordIds.Contains(d.RecordId.Value));

            if (!filter.EmpCode.IsEmpty())
            {
                detailExps.Add(d => d.Employee != null && d.Employee.EmpCode.Contains(filter.EmpCode!));
            }
            if (!filter.EmpName.IsEmpty())
            {
                detailExps.Add(d => d.Employee != null && d.Employee.DisplayName.Contains(filter.EmpName!));
            }
            if (filter.OnlyOTValue)
            {
                detailExps.Add(d => d.EnumOverTimeType != OverTimeType.None);
            }

            int recoredCount = -1;
            var detaileEntities = this.Repo.GetEntities(detailExps, filter.Order);
            //var detaileEntities = filter.RequireRecordCount
            //     ? this.Repo.GetEntities(detailExps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
            //     : this.Repo.GetEntities(detailExps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = detaileEntities.Maps<AttHolidayOTRecordDetailQuery>();

            var rmodels = models.GroupBy(e => new { e.EmployeeId }).Select(m =>
            {
                var empOt = new Emp_AttHolidayOT
                {
                    EmpUid = m.FirstOrDefault()?.EmpUid,
                    EmpCode = m.FirstOrDefault()?.EmpCode,
                    EmpName = m.FirstOrDefault()?.EmpName,
                    HireStyleName = m.FirstOrDefault()?.HireStyleName,
                    EmpDept = m.FirstOrDefault()?.EmpDept
                };
                var details = new List<Emp_AttHolidayOTDetail>();
                m.OrderBy(e => e.RecordDate).ForEach(md =>
                {
                    details.Add(new Emp_AttHolidayOTDetail { EnumOverTimeType = md.EnumOverTimeType, RecordDate = md.RecordDate });
                });
                empOt.Details = details;
                empOt.RecordDates = m.OrderBy(e => e.RecordDate).Select(e => e.RecordDateStr ?? "").ToList();
                return empOt;
            }).ToList();

            return this.QueryResult(rmodels, recoredCount, filter);
        }

        /// <summary>
        /// 查询节日加班明细—节日加班修改
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询节日加班明细")]
        [Permission(Permissions.AttendanceManage.HolidayOvertimeFee, Permissions.AttendanceManage.holidayOvertimeFeeUpdate)]
        public QueryResult<AttHolidayOTRecordDetailQuery> SearchAttHolidayOTRecordDetail_Update([FromQuery] AttHolidayOTSearchFilter filter)
        {
            if (filter.OnlyOTValue)
            {
                var exps = this.NewExps<AttHolidayOTRecord>();

                if (filter.RecordDate != null)
                {
                    if (filter.WithMonth)
                    {
                        var start = new DateTime(filter.RecordDate.Value.Year, filter.RecordDate.Value.Month, 1);
                        var end = start.AddMonths(1);

                        exps.And(p => p.RecordDate >= start && p.RecordDate < end);
                    }
                    else
                    {
                        exps.And(p => p.RecordDate == filter.RecordDate);
                    }
                }

                if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
                {
                    var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                    exps.And(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));
                }

                var entities = this.Repo.GetEntities(exps);
                var recordIds = entities.Select(e => e.ID).ToList();
                var detailExps = this.NewExps<AttHolidayOTRecordDetail>();
                detailExps.Add(d => d.RecordId.HasValue && recordIds.Contains(d.RecordId.Value));

                if (!filter.EmpCode.IsEmpty())
                {
                    detailExps.Add(d => d.Employee != null && d.Employee.EmpCode.Contains(filter.EmpCode!));
                }
                if (!filter.EmpName.IsEmpty())
                {
                    detailExps.Add(d => d.Employee != null && d.Employee.DisplayName.Contains(filter.EmpName!));
                }
                if (filter.OnlyOTValue)
                {
                    detailExps.Add(d => d.EnumOverTimeType != OverTimeType.None);
                }

                int recoredCount = -1;
                var detaileEntities = filter.RequireRecordCount
                     ? this.Repo.GetEntities(detailExps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                     : this.Repo.GetEntities(detailExps, filter.Order, filter.PageIndex, filter.PageSize);
                var models = detaileEntities.Maps<AttHolidayOTRecordDetailQuery>();

                return this.QueryResult(models, recoredCount, filter);
            }
            else
            {
                var empExps = this.NewExps<Entities.Employee>();
                if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
                {
                    var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                    empExps.And(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));
                }
                if (!filter.EmpCode.IsEmpty())
                {
                    empExps.Add(d => d.EmpCode.Contains(filter.EmpCode!));
                }
                if (!filter.EmpName.IsEmpty())
                {
                    empExps.Add(d => d.DisplayName.Contains(filter.EmpName!));
                }
                //var hiredId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Hired).ID;
                //var leaveId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Leave).ID;
                //empExps.And(p => p.EmployeeHR.EmpStatusId == hiredId
                //	|| p.EmployeeHR.EmpStatusId == leaveId && p.EmployeeHR.LeaveDate.HasValue && p.EmployeeHR.LeaveDate.Value > dt);
                int recoredCount = -1;

                var empEntities = filter.RequireRecordCount
                     ? this.Repo.GetEntities(empExps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                     : this.Repo.GetEntities(empExps, filter.Order, filter.PageIndex, filter.PageSize);

                var empIds = empEntities.Select(e => e.ID).ToList();

                var attExps = this.NewExps<Entities.AttHolidayOTRecordDetail>();
                attExps.And(p => p.EmployeeId.HasValue && empIds.Contains(p.EmployeeId.Value));
                attExps.And(p => p.AttHolidayOTRecord != null && p.AttHolidayOTRecord.RecordDate == filter.RecordDate);

                List<AttHolidayOTRecordDetail> attDetailEntites;
                attDetailEntites = this.Repo.GetEntities(attExps);

                var list = new List<AttHolidayOTRecordDetail>();
                empEntities.ForEach(e =>
                {
                    var att = attDetailEntites.FirstOrDefault(d => d.EmployeeId == e.ID);
                    if (att != null)
                    {
                        list.Add(att);
                    }
                    else
                    {
                        list.Add(new AttHolidayOTRecordDetail()
                        {
                            EmployeeId = e.ID,
                            Employee = e,
                            EnumOverTimeType = OverTimeType.None
                        });
                    }
                });

                var models = list.Maps<AttHolidayOTRecordDetailQuery>();
                var attDetails = this.QueryResult(models, recoredCount, filter);
                return attDetails;
            }
        }

        /// <summary>
        /// 修改节日加班明细
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "修改节日加班明细")]
        [Permission(Permissions.AttendanceManage.HolidayOvertimeFee, Permissions.AttendanceManage.holidayOvertimeFeeUpdate)]
        public BizResult<AttHolidayOTRecordDetailModel> UpdateAttHolidayOTRecordDetail([FromBody] AttHolidayOTRecordDetailModel recordModel)
        {
            var entity = recordModel.Map<AttHolidayOTRecordDetail>();
            var bizResult = this.Repo.UpdateAttHolidayOTRecordDetail(ref entity);
            var result = new BizResult<AttHolidayOTRecordDetailModel>();
            result.CopyFrom(bizResult);
            result.Data = entity.Map<AttHolidayOTRecordDetailModel>();
            return result;
        }

        /// <summary>
        /// 导出节日加班明细
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "导出节日加班明细")]
        [Permission(Permissions.AttendanceManage.HolidayOvertimeFee, Permissions.AttendanceManage.holidayOvertimeFeeSearch)]
        public IActionResult GetOTReportExcel([FromQuery] AttHolidayOTSearchFilter filter)
        {
            var oTDates = new List<DateTime>();
            var oTDetailExps = this.NewExps<AttHolidayOTRecordDetail>();

            if (filter.RecordDate.HasValue)
            {
                if (filter.AddDay.HasValue)
                {
                    var recordDate = filter.RecordDate.Value;
                    for (int i = 0; i < filter.AddDay; i++)
                    {
                        var aDate = recordDate.AddDays(i);
                        oTDates.Add(aDate);
                    }

                    var addDay = filter.AddDay.Value - 1;
                    var dateAdd = recordDate.AddDays(addDay);

                    oTDetailExps.And(e => e.OTDate >= recordDate && e.OTDate <= dateAdd);
                }
                else
                {
                    oTDates.Add(filter.RecordDate.Value);
                    oTDetailExps.And(e => e.OTDate == filter.RecordDate.Value);
                }
            }
            if (filter.OnlyOTValue)
            {
                oTDetailExps.And(e => e.EnumOverTimeType != OverTimeType.None);
            }
            oTDates = oTDates.OrderBy(e => e).ToList();
            var oTDateStrs = oTDates.Select(e => e.ToString("yyyy-MM-dd")).ToList();

            var oTDetails = this.Repo.GetEntities(oTDetailExps);

            var empIds = oTDetails.Select(e => e.EmployeeId).Distinct().ToList();

            var exps = this.NewExps<Employee>();
            if (!string.IsNullOrEmpty(filter.EmpCode))
            {
                exps.Add(e => e.EmpCode.Contains(filter.EmpCode));
            }
            if (!filter.EmpUid.IsEmpty())
            {
                exps.Add(e => e.Uid.Equals(filter.EmpUid));
            }
            if (!string.IsNullOrEmpty(filter.EmpName))
            {
                exps.Add(e => e.DisplayName.Contains(filter.EmpName));
            }
            if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
            {
                var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                exps.And(e => e.DeptId.HasValue && deptIds.Contains(e.DeptId.Value));
            }
            if (filter.DeptIds != null && filter.DeptIds.Length > 0)
            {
                exps.And(p => p.DeptId.HasValue && filter.DeptIds.Contains(p.DeptId.Value));
            }
            if (filter.OnlyOTValue)
            {
                if (empIds != null && empIds.Count > 0)
                {
                    exps.And(e => empIds.Contains(e.ID));
                }
                else
                {
                    exps.And(e => 1 == -1);
                }
            }

            var empEntities = new List<Employee>();

            empEntities = this.Repo.GetEntities(exps, filter.Order);
            //empEntities = filter.RequireRecordCount
            //    ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
            //    : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var empOts = new List<Emp_AttHolidayOT>();
            empEntities.ForEach(e =>
            {
                var empOt = new Emp_AttHolidayOT
                {
                    EmpUid = e.Uid.ToString(),
                    EmpCode = e.EmpCode,
                    EmpName = e.DisplayName,
                    HireStyleName = e.EmployeeHR?.HireStyle?.Name,
                    EmpDept = e.Department?.Name,
                    Details = new List<Emp_AttHolidayOTDetail>()
                };
                var empOTDetails = oTDetails.Where(otd => otd.EmployeeId == e.ID);
                oTDates.ForEach(date =>
                {
                    var otDetail = empOTDetails.FirstOrDefault(e => e.OTDate == date);
                    Emp_AttHolidayOTDetail emp_AttHolidayOTDetail = new();
                    if (otDetail != null)
                    {
                        emp_AttHolidayOTDetail.EnumOverTimeType = otDetail.EnumOverTimeType;
                        emp_AttHolidayOTDetail.RecordDate = otDetail.OTDate;
                    }
                    empOt.Details.Add(emp_AttHolidayOTDetail);
                    empOt.RecordDates = oTDateStrs;
                });
                empOts.Add(empOt);
            });

            //DataTable dt = Utility.ToDataTable(empOts);//list转datatable
            string exportFileName = $"{AppDomain.CurrentDomain.BaseDirectory}" + string.Format(Common.Consts.Exports.FileExcel, Common.Consts.Exports.FileHolidayOT);//生成后存放的地址 和文件名

            //string sWebRootFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, Common.Consts.Exports.PathTemplate); //模板地址 这里我把模板存到了程序执行路径
            //string templateFileName = $"{ sWebRootFolder }" + string.Format(Common.Consts.Exports.FileExcelTemplate, Common.Consts.Exports.FileHolidayOT);

            var _NpoiExcelUtility = new NpoiExcelUtility();
            _NpoiExcelUtility.ExportOTExcel(empOts, oTDates, Common.Consts.Exports.FileHolidayOT);
            var bytes = _NpoiExcelUtility.SaveExcelBytes();
            //_NpoiExcelUtility.SaveExcel();
            //var stream = System.IO.File.OpenRead(exportFileName);
            return File(bytes, Common.Consts.Exports.ContentType, Path.GetFileName(exportFileName));
        }

        #endregion HolidayOT 节日加班

        #region AttDayOff 考勤数据

        /// <summary>
		/// 获取考勤记录
		/// </summary>
		/// <param name="filter"></param>
		/// <returns></returns>
		[HttpGet]
        [LogApi(ApiType.Query, Operate = "获取考勤记录")]
        public BizResult<AttDayOffRecordModel> GetAttDayOffRecord([FromQuery] AttDayOffRecordFilter filter)
        {
            var result = this.BizResult<AttDayOffRecordModel>();
            var deptId = filter.DeptId?.As<Guid>();
            var entity = this.Repo.GetEntity<AttDayOffRecord>(p => p.DeptId == deptId && p.RecordMonth == filter.RecordMonth);

            if (entity == null)
            {
                entity = new AttDayOffRecord()
                {
                    DeptId = deptId,
                    EnumStatus = AttDayOffRecordStatus.None,
                    RecordMonth = filter.RecordMonth
                };
            }

            var model = entity.Map<AttDayOffRecordModel>();

            if (!filter.RecordMonth.IsEmpty())
            {
                var recordMonth = filter.RecordMonth!.As<DateTime>();
                var attDayOffRecordProphylactic = this.Repo.GetEntity<AttDayOffRecordProphylactic>(p => p.RecordMonth == recordMonth);
                if (attDayOffRecordProphylactic != null)
                {
                    model.AttDayOffRecordProphylacticStatus = attDayOffRecordProphylactic.EnumStatus;
                }
                else
                {
                    model.AttDayOffRecordProphylacticStatus = AttDayOffRecordProphylacticStatus.Pending;
                }

                model.IsAllowReject = recordMonth == SysDateTime.Now.Date.AddDays(1 - SysDateTime.Now.Date.Day).AddMonths(-1);
            }

            result.Data = model;
            return result;
        }

        /// <summary>
        /// 查询考勤记录明细
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询考勤记录明细")]
        public QueryResult<AttDayOffRecordDetailQuery> QueryAttDayOffRecordDetail([FromQuery] AttDayOffRecordDetailFilter filter)
        {
            DateTime? dt = filter.RecordMonth?.As<DateTime>();
            DateTime? dt2 = dt?.AddMonths(1);
            var empExps = this.NewExps<Entities.Employee>();
            if (!string.IsNullOrEmpty(filter.DeptId))
            {
                Guid deptId = Guid.Parse(filter.DeptId);
                var deptIds = this.Repo.GetDeptByCurrentUser(deptId);
                if (deptIds.IndexOf(deptId) >= 0)
                {
                    empExps.And(p => deptId == p.DeptId);
                }
                else
                {
                    empExps.And(p => Guid.Empty == p.DeptId);
                }
            }
            else
            {
                empExps.And(p => Guid.Empty == p.DeptId);
            }

            var hiredId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Hired)?.ID;
            var leaveId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Leave)?.ID;
            empExps.And(p => p.EmployeeHR != null && (p.EmployeeHR.EmpStatusId == hiredId
                || p.EmployeeHR.EmpStatusId == leaveId && p.EmployeeHR.LeaveDate.HasValue &&
                (p.EmployeeHR.LeaveDate.Value > dt && p.EmployeeHR.LeaveDate.Value <= dt2 || p.EmployeeHR.LeaveDate.Value > dt2)
                || p.EmployeeHR.EmpStatusId == leaveId && p.EmployeeHR.RetireDate.HasValue &&
                (p.EmployeeHR.RetireDate.Value > dt && p.EmployeeHR.RetireDate.Value <= dt2 || p.EmployeeHR.RetireDate.Value > dt2)));
            int recoredCount = -1;

            var empEntities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(empExps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(empExps, filter.Order, filter.PageIndex, filter.PageSize);

            var empIds = empEntities.Select(e => e.ID).ToList();

            var attExps = this.NewExps<Entities.AttDayOffRecordDetail>();
            attExps.And(p => p.EmployeeId.HasValue && empIds.Contains(p.EmployeeId.Value));
            attExps.And(p => p.AttDayOffRecord != null && p.AttDayOffRecord.RecordMonth == filter.RecordMonth);
            if (filter.RecordId != null)
            {
                Guid recordId = Guid.Parse(filter.RecordId);
                attExps.And(p => p.RecordId == recordId);
            }
            else
            {
                attExps.And(p => p.RecordId == null);
            }
            List<AttDayOffRecordDetail> attDetailEntites;
            if (!string.IsNullOrEmpty(filter.Order))
            {
                attDetailEntites = this.Repo.GetEntities(attExps, "Employee." + filter.Order);
            }
            else
            {
                attDetailEntites = this.Repo.GetEntities(attExps);
            }

            var list = new List<AttDayOffRecordDetail>();
            empEntities.ForEach(e =>
            {
                var att = attDetailEntites.FirstOrDefault(d => d.EmployeeId == e.ID);
                if (att != null)
                {
                    list.Add(att);
                }
                else
                {
                    list.Add(new AttDayOffRecordDetail()
                    {
                        EmployeeId = e.ID,
                        Employee = e
                        //H1 = 70
                    });
                }
            });
            var models = list.Maps<AttDayOffRecordDetailQuery>();

            //上月卫生津贴
            var preAttExps = this.NewExps<Entities.AttDayOffRecord>();
            var recordMonth = filter.RecordMonth;
            var preMonthStr = "";
            if (!string.IsNullOrEmpty(recordMonth))
            {
                var preMonth = DateTime.Parse(recordMonth).AddMonths(-1);
                preMonthStr = preMonth.ToString("yyyy-MM");
                preAttExps.And(p => p.RecordMonth == preMonthStr);
                if (!string.IsNullOrEmpty(filter.DeptId))
                {
                    Guid deptId = Guid.Parse(filter.DeptId);
                    preAttExps.And(p => p.DeptId == deptId);

                    var preAtt = this.Repo.GetEntity(preAttExps);
                    if (preAtt != null && preAtt.AttDayOffRecordDetail.Any())
                    {
                        preAtt.AttDayOffRecordDetail.ForEach(preD =>
                        {
                            var empD = models.FirstOrDefault(m => m.EmployeeId == preD.EmployeeId);
                            if (empD != null)
                            {
                                empD.PreMonthH1 = preD.H1;
                            }
                        });
                    }
                }
            }

            var attDetails = this.QueryResult(models, recoredCount, filter);
            return attDetails;
        }

        /// <summary>
		/// 暂存考勤记录
		/// </summary>
		/// <param name="recordModel"></param>
		/// <returns></returns>
		[HttpPost]
        [LogApi(ApiType.Save, Operate = "暂存考勤记录")]
        [Permission(Permissions.AttendanceManage.AttendanceData, Permissions.AttendanceManage.AttendanceApply)]
        public BizResult<AttDayOffRecordModel> SaveAttDayOffRecord([FromBody] AttDayOffRecordModel recordModel)
        {
            var entity = recordModel.Map<AttDayOffRecord>();

            if (recordModel.Fillings != null)
            {
                entity.AttDayOffRecordFilling = recordModel.Fillings.Maps<AttDayOffRecordFilling>();
            }
            var result = this.Repo.UnCommittedAttDayOffRecord(entity);
            return result.Map<AttDayOffRecordModel>();
        }

        /// <summary>
        /// 提交考勤记录
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "提交考勤记录")]
        [Permission(Permissions.AttendanceManage.AttendanceData, Permissions.AttendanceManage.AttendanceApply)]
        public BizResult<AttDayOffRecordModel> SubmitAttDayOffRecord([FromBody] AttDayOffRecordModel recordModel)
        {
            var entity = recordModel.Map<AttDayOffRecord>();

            if (recordModel.Fillings != null)
            {
                entity.AttDayOffRecordFilling = recordModel.Fillings.Maps<AttDayOffRecordFilling>();
            }

            var result = this.Repo.SubmitAttDayOffRecord(entity);
            return result.Map<AttDayOffRecordModel>();
        }

        /// <summary>
        /// 拒绝考勤记录
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "拒绝考勤记录")]
        [Permission(Permissions.AttendanceManage.AttendanceData, Permissions.AttendanceManage.AttendanceHR)]
        public BizResult<AttDayOffRecordModel> RejectAttDayOffRecord([FromBody] AttDayOffRecordModel recordModel)
        {
            var entity = recordModel.Map<AttDayOffRecord>();
            var result = this.Repo.RejectAttDayOffRecord(entity);
            return result.Map<AttDayOffRecordModel>();
        }

        /// <summary>
		/// 修改考勤记录明细（单条）
		/// </summary>
		/// <param name="recordModel"></param>
		/// <returns></returns>
		[HttpPost]
        [LogApi(ApiType.Save, Operate = "修改考勤记录明细（单条）")]
        [Permission(Permissions.AttendanceManage.AttendanceData, Permissions.AttendanceManage.AttendanceUpdate)]
        public BizResult<AttDayOffRecordDetailModel> UpdateAttDayOffRecordDetail([FromBody] AttDayOffRecordDetailModel recordModel)
        {
            var entity = recordModel.Map<AttDayOffRecordDetail>();
            var bizResult = this.Repo.UpdateAttDayOffRecordDetail(ref entity);
            var result = new BizResult<AttDayOffRecordDetailModel>();
            result.CopyFrom(bizResult);

            if (bizResult.Succeed)
            {
                var ent = this.Repo.Get<AttDayOffRecordDetail>(entity.ID);
                result.Data = ent?.Map<AttDayOffRecordDetailModel>();
            }

            var preAttExps = this.NewExps<Entities.AttDayOffRecordDetail>();
            var recordMonth = entity.RecordMonth;
            var preMonthStr = "";
            if (!string.IsNullOrEmpty(recordMonth))
            {
                var preMonth = DateTime.Parse(recordMonth).AddMonths(-1);
                preMonthStr = preMonth.ToString("yyyy-MM");
                preAttExps.And(p => p.RecordMonth == preMonthStr);
                preAttExps.And(p => p.EmployeeId == entity.EmployeeId);
                var preAtt = this.Repo.GetEntity(preAttExps);
                if (preAtt != null && result.Data != null)
                {
                    result.Data.PreMonthH1 = preAtt.H1;
                }
            }

            return result;
        }

        /// <summary>
        /// 查询考勤数据明细--考勤数据修改
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询考勤数据明细")]
        [Permission(Permissions.AttendanceManage.AttendanceData, Permissions.AttendanceManage.AttendanceUpdate)]
        public QueryResult<AttDayOffRecordDetailQuery> SearchAttDayOffRecordDetail_Update([FromQuery] AttDayOffSearchFilter filter)
        {
            if (filter.OnlyDayOffValue || filter.OnlyH1Value)
            {
                var onlyMonthCondition = true;

                var detailExps = this.NewExps<AttDayOffRecordDetail>(p => p.EnumStatus != AttDayOffRecordDetailStatus.None && p.EnumStatus != AttDayOffRecordDetailStatus.Pending);

                //开始月份
                if (!filter.RecordMonth.IsEmpty())
                {
                    detailExps.And(p => p.RecordMonth == filter.RecordMonth);
                }

                // 显示有假期
                if (filter.OnlyDayOffValue)
                {
                    onlyMonthCondition = false;
                    detailExps.Add(p => p.H2.HasValue && p.H2.Value > 0 || p.H3.HasValue && p.H3.Value > 0
                                        || p.H4.HasValue && p.H4.Value > 0 || p.H5.HasValue && p.H5.Value > 0
                                          || p.H6.HasValue && p.H6.Value > 0 || p.H7.HasValue && p.H7.Value > 0
                                          || p.H8.HasValue && p.H8.Value > 0 || p.H9.HasValue && p.H9.Value > 0
                                          || p.H10.HasValue && p.H10.Value > 0 || p.H11.HasValue && p.H11.Value > 0
                                          || p.H12.HasValue && p.H12.Value > 0);
                }
                //显示有卫贴
                if (filter.OnlyH1Value)
                {
                    onlyMonthCondition = false;
                    detailExps.And(p => p.H1.HasValue && p.H1.Value > 0);
                }
                //工号
                if (!string.IsNullOrEmpty(filter.EmpCode))
                {
                    onlyMonthCondition = false;
                    detailExps.Add(p => p.Employee != null && p.Employee.EmpCode.Contains(filter.EmpCode));
                }
                //姓名
                if (!string.IsNullOrEmpty(filter.EmpName))
                {
                    onlyMonthCondition = false;
                    detailExps.Add(p => p.Employee != null && p.Employee.DisplayName.Contains(filter.EmpName));
                }
                //部门
                if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
                {
                    onlyMonthCondition = false;
                    var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                    detailExps.And(p => p.Employee != null && p.Employee.DeptId.HasValue && deptIds.Contains(p.Employee.DeptId.Value));
                }

                var detaileEntities = this.Repo.GetEntities(detailExps);

                var empExps = this.NewExps<Employee>();
                //条件查询找出的员工
                var detailEmpIds = detaileEntities.Select(e => e.EmployeeId).Distinct().ToList();
                if (onlyMonthCondition)
                {
                    empExps.And(e => 1 == 1);
                }
                else
                {
                    empExps.And(e => detailEmpIds.Contains(e.ID));
                }

                int recoredCount = -1;
                var empEntities = new List<Employee>();
                empEntities = filter.RequireRecordCount
                    ? this.Repo.GetEntities(empExps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                    : this.Repo.GetEntities(empExps, filter.Order, filter.PageIndex, filter.PageSize);

                #region 上月记录

                var preAttDetails = new List<AttDayOffRecordDetail>();

                var preAttDetailsExps = this.NewExps<Entities.AttDayOffRecordDetail>(p => p.EnumStatus != AttDayOffRecordDetailStatus.Pending && p.EnumStatus != AttDayOffRecordDetailStatus.None);

                if (filter.EndRecordMonth.IsEmpty())
                {
                    var empIds = empEntities.Select(e => e.ID).ToList();
                    preAttDetailsExps.Add(e => empIds.Contains(e.EmployeeId.Value));
                    //var preMonth = DateTime.Parse(filter.RecordMonth).AddMonths(-1);
                    //var preMonthStr = preMonth.ToString("yyyy-MM");
                    //preAttDetailsExps.Add(e => e.RecordMonth == preMonthStr);

                    // 若上月没提交数据则获取不到，取查询当年的数据，如果当年度都取不到，空白
                    List<string> months = new List<string>();
                    for (int i = 1; i <= 12; i++)
                    {
                        var month = DateTime.Parse(filter.RecordMonth).AddMonths(-i).ToString("yyyy-MM");
                        if (month.Substring(0, 4) == filter.RecordMonth.Substring(0, 4))
                        {
                            months.Add(month);
                        }
                        else
                        {
                            continue;
                        }
                    }
                    preAttDetailsExps.Add(e => months.Contains(e.RecordMonth));
                }
                preAttDetails = this.Repo.GetEntities(preAttDetailsExps);

                #endregion 上月记录

                var recordMonth = filter.RecordMonth?.As<DateTime>();
                int days = recordMonth.HasValue
                    ? DateTime.DaysInMonth(recordMonth.Value.Year, recordMonth.Value.Month)
                    : 0;

                var empRecords = new List<AttDayOffRecordDetailQuery>();
                empEntities.ForEach(e =>
                {
                    var empRecord = new AttDayOffRecordDetailQuery();

                    var ent = detaileEntities.FirstOrDefault(ent => ent.EmployeeId == e.ID);
                    if (ent != null)
                    {
                        empRecord = ent.Map<AttDayOffRecordDetailQuery>();
                    }
                    else
                    {
                        empRecord.EmployeeId = e.ID;
                        empRecord.EmpUid = e.Uid.ToString();
                        empRecord.EmpCode = e.EmpCode;
                        empRecord.EmpName = e.DisplayName;
                        empRecord.HireStyleName = e.EmployeeHR?.HireStyle?.Name;
                        empRecord.EmpDept = e.Department?.Name;
                        empRecord.GeneralHoliday = e.EmployeeHR?.GeneralHoliday;
                        empRecord.HistoryH12 = e.EmployeeHR?.GeneralHolidayCalculateTime?.ToString("yyyy-MM") == filter.RecordMonth ? e.EmployeeHR?.GeneralHoliday : null;
                    }


                    //var preEnt = preAttDetails.FirstOrDefault(pre => pre.EmployeeId == e.ID);
                    //if (preEnt != null)
                    //{
                    //    empRecord.PreMonthH1 = preEnt.H1;
                    //    if (!empRecord.HistoryH12.HasValue && e.EmployeeHR.GeneralHolidayCalculateTime?.ToString("yyyy-MM") != filter.RecordMonth)
                    //    {
                    //        var calculatedValue = preEnt.HistoryH12.GetValueOrDefault() - preEnt.H12.GetValueOrDefault();
                    //        empRecord.HistoryH12 = calculatedValue <= 0 ? null : calculatedValue;
                    //    }
                    //}

                    // 若上月没提交数据则获取不到，取查询当年的数据，如果当年度都取不到，空白
                    List<Guid> empList = new List<Guid>();
                    foreach (var preAttDayOffRecordDetail in preAttDetails)
                    {
                        var EmpId = preAttDayOffRecordDetail.EmployeeId.Value;
                        if (empList.IndexOf(EmpId) < 0)
                        {
                            var preEnt = preAttDetails.Where(pre => pre.EmployeeId == e.ID).OrderByDescending(x => x.RecordMonth).FirstOrDefault();
                            if (preEnt != null)
                            {
                                empRecord.PreMonthH1 = preEnt.H1;
                                if (!empRecord.HistoryH12.HasValue && e.EmployeeHR.GeneralHolidayCalculateTime?.ToString("yyyy-MM") != filter.RecordMonth)
                                {
                                    var calculatedValue = preEnt.HistoryH12.GetValueOrDefault() - preEnt.H12.GetValueOrDefault();
                                    empRecord.HistoryH12 = calculatedValue <= 0 ? null : calculatedValue;
                                }
                            }
                            empList.Add(EmpId);
                        }
                    }
                    empRecords.Add(empRecord);
                });

                return this.QueryResult(empRecords, recoredCount, filter);
            }
            else
            {
                var empExps = this.NewExps<Entities.Employee>();
                if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
                {
                    var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                    empExps.And(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));
                }

                //var hiredId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Hired).ID;
                //var leaveId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Leave).ID;
                //empExps.And(p => p.EmployeeHR.EmpStatusId == hiredId
                //	|| p.EmployeeHR.EmpStatusId == leaveId && p.EmployeeHR.LeaveDate.HasValue && p.EmployeeHR.LeaveDate.Value > dt);
                int recoredCount = -1;

                //工号
                if (!string.IsNullOrEmpty(filter.EmpCode))
                {
                    empExps.Add(p => p.EmpCode.Contains(filter.EmpCode));
                }
                //姓名
                if (!string.IsNullOrEmpty(filter.EmpName))
                {
                    empExps.Add(p => p.DisplayName.Contains(filter.EmpName));
                }

                var empEntities = filter.RequireRecordCount
                     ? this.Repo.GetEntities(empExps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                     : this.Repo.GetEntities(empExps, filter.Order, filter.PageIndex, filter.PageSize);

                var empIds = empEntities.Select(e => e.ID).ToList();

                #region 上月记录

                var preAttDetails = new List<AttDayOffRecordDetail>();

                var preAttDetailsExps = this.NewExps<Entities.AttDayOffRecordDetail>(p => p.EnumStatus != AttDayOffRecordDetailStatus.Pending && p.EnumStatus != AttDayOffRecordDetailStatus.None);

                if (filter.EndRecordMonth.IsEmpty())
                {
                    preAttDetailsExps.Add(e => empIds.Contains(e.EmployeeId.Value));
                    //var preMonth = DateTime.Parse(filter.RecordMonth).AddMonths(-1);
                    //var preMonthStr = preMonth.ToString("yyyy-MM");
                    //preAttDetailsExps.Add(e => e.RecordMonth == preMonthStr);

                    // 若上月没提交数据则获取不到，取查询当年的数据，如果当年度都取不到，空白
                    List<string> months = new List<string>();
                    for (int i = 1; i <= 12; i++)
                    {
                        var month = DateTime.Parse(filter.RecordMonth).AddMonths(-i).ToString("yyyy-MM");
                        if (month.Substring(0, 4) == filter.RecordMonth.Substring(0, 4))
                        {
                            months.Add(month);
                        }
                        else
                        {
                            continue;
                        }
                    }
                    preAttDetailsExps.Add(e => months.Contains(e.RecordMonth));
                }
                preAttDetails = this.Repo.GetEntities(preAttDetailsExps);

                #endregion 上月记录

                var attExps = this.NewExps<Entities.AttDayOffRecordDetail>(p => p.EnumStatus != AttDayOffRecordDetailStatus.None && p.EnumStatus != AttDayOffRecordDetailStatus.Pending);
                attExps.And(p => p.EmployeeId != null && empIds.Contains(p.EmployeeId.Value));
                attExps.And(p => p.AttDayOffRecord != null && p.AttDayOffRecord.RecordMonth == filter.RecordMonth);

                List<AttDayOffRecordDetail> attDetailEntites;
                attDetailEntites = this.Repo.GetEntities(attExps);

                var list = new List<AttDayOffRecordDetail>();
                empEntities.ForEach(e =>
                {
                    var att = attDetailEntites.FirstOrDefault(d => d.EmployeeId == e.ID);
                    if (att != null)
                    {
                        list.Add(att);
                    }
                    else
                    {
                        list.Add(new AttDayOffRecordDetail()
                        {
                            EmployeeId = e.ID,
                            Employee = e,
                            HistoryH12 = e.EmployeeHR?.GeneralHolidayCalculateTime?.ToString("yyyy-MM") == filter.RecordMonth ? e.EmployeeHR?.GeneralHoliday : null
                        });
                    }
                });

                //foreach (var preAttDayOffRecordDetail in preAttDetails)
                //{
                //    var model = list.FirstOrDefault(m => m.EmployeeId == preAttDayOffRecordDetail.EmployeeId);
                //    if (model != null)
                //    {
                //        if (!model.HistoryH12.HasValue && model.Employee.EmployeeHR.GeneralHolidayCalculateTime?.ToString("yyyy-MM") != filter.RecordMonth)
                //        {
                //            var calculatedValue = preAttDayOffRecordDetail.HistoryH12.GetValueOrDefault() - preAttDayOffRecordDetail.H12.GetValueOrDefault();
                //            model.HistoryH12 = calculatedValue <= 0 ? null : calculatedValue;
                //        }
                //    }
                //}

                // 若上月没提交数据则获取不到，取查询当年的数据，如果当年度都取不到，空白
                List<Guid> empList = new List<Guid>();
                foreach (var preAttDayOffRecordDetail in preAttDetails)
                {
                    var EmpId = preAttDayOffRecordDetail.EmployeeId.Value;
                    if (empList.IndexOf(EmpId) < 0)
                    {
                        var preAttDetail = preAttDetails.Where(m => m.EmployeeId == EmpId).OrderByDescending(x => x.RecordMonth).FirstOrDefault();

                        var model = list.FirstOrDefault(m => m.EmployeeId == preAttDetail.EmployeeId);
                        if (model != null)
                        {
                            if (!model.HistoryH12.HasValue && model.Employee.EmployeeHR.GeneralHolidayCalculateTime?.ToString("yyyy-MM") != filter.RecordMonth)
                            {
                                var calculatedValue = preAttDetail.HistoryH12.GetValueOrDefault() - preAttDetail.H12.GetValueOrDefault();
                                model.HistoryH12 = calculatedValue <= 0 ? null : calculatedValue;
                            }
                        }
                        empList.Add(EmpId);
                    }
                }


                var models = list.Maps<AttDayOffRecordDetailQuery>();

                var attDetails = this.QueryResult(models, recoredCount, filter);
                return attDetails;
            }
        }

        /// <summary>
        /// 考勤数据查询1
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "考勤数据查询1")]
        [Permission(Permissions.AttendanceManage.AttendanceData, Permissions.AttendanceManage.AttendanceSearch1)]
        public QueryResult<AttDayOffRecordDetailQuery> SearchAttDayOffRecordDetail1([FromQuery] AttDayOffSearchFilter filter)
        {
            //只有月份条件
            var onlyMonthCondition = true;
            var detailExps = this.NewExps<AttDayOffRecordDetail>(p => p.EnumStatus != AttDayOffRecordDetailStatus.None && p.EnumStatus != AttDayOffRecordDetailStatus.Pending);
            //开始月份
            if (!filter.RecordMonth.IsEmpty())
            {
                if (filter.EndRecordMonth.IsEmpty())
                {
                    detailExps.And(p => p.RecordMonth == filter.RecordMonth);
                }
                else
                {
                    detailExps.And(p => p.RecordMonth != null && p.RecordMonth.CompareTo(filter.RecordMonth) >= 0);
                }
            }
            //结束月份
            if (!filter.EndRecordMonth.IsEmpty())
            {
                detailExps.And(p => p.RecordMonth != null && p.RecordMonth.CompareTo(filter.EndRecordMonth) <= 0);
            }

            //假期筛选
            if (!string.IsNullOrEmpty(filter.Holiday) && filter.Operation.HasValue && filter.HolidayValue.HasValue)
            {
                onlyMonthCondition = false;
                var queryCondition = new QueryCondition
                {
                    EntityColumnName = filter.Holiday,
                    EnumOperation = (Operations)filter.Operation,
                    EntityColumnType = "System.Decimal",
                    Keywords = filter.HolidayValue.ToString(),
                    EnumLogicRelationship = LogicRelationships.AND
                };

                List<QueryCondition> queryConditions = new() { queryCondition };
                var expression = new Common.DynamicQuery().GetDynamicQuery<AttDayOffRecordDetail>(queryConditions);
                detailExps.And(expression);
            }
            // 显示有假期
            if (filter.OnlyDayOffValue)
            {
                onlyMonthCondition = false;
                detailExps.Add(p => p.H2.HasValue && p.H2.Value > 0 || p.H3.HasValue && p.H3.Value > 0
                                    || p.H4.HasValue && p.H4.Value > 0 || p.H5.HasValue && p.H5.Value > 0
                                      || p.H6.HasValue && p.H6.Value > 0 || p.H7.HasValue && p.H7.Value > 0
                                      || p.H8.HasValue && p.H8.Value > 0 || p.H9.HasValue && p.H9.Value > 0
                                      || p.H10.HasValue && p.H10.Value > 0 || p.H11.HasValue && p.H11.Value > 0
                                      || p.H12.HasValue && p.H12.Value > 0);
            }
            //显示有卫贴
            if (filter.OnlyH1Value)
            {
                onlyMonthCondition = false;
                detailExps.And(p => p.H1.HasValue && p.H1.Value > 0);
            }
            //工号
            if (!string.IsNullOrEmpty(filter.EmpCode))
            {
                onlyMonthCondition = false;
                detailExps.Add(p => p.Employee != null && p.Employee.EmpCode.Contains(filter.EmpCode));
            }
            //UID
            if (!filter.EmpUid.IsEmpty())
            {
                onlyMonthCondition = false;
                detailExps.Add(p => p.Employee != null && p.Employee.Uid.Equals(filter.EmpUid));
            }
            //姓名
            if (!string.IsNullOrEmpty(filter.EmpName))
            {
                onlyMonthCondition = false;
                detailExps.Add(p => p.Employee != null && p.Employee.DisplayName.Contains(filter.EmpName));
            }
            //部门
            if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
            {
                onlyMonthCondition = false;
                var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                detailExps.And(p => p.Employee != null && p.Employee.DeptId.HasValue && deptIds.Contains(p.Employee.DeptId.Value));
            }
            if (filter.DeptIds != null && filter.DeptIds.Length > 0)
            {
                detailExps.And(p => p.Employee != null && p.Employee.DeptId.HasValue && filter.DeptIds.Contains(p.Employee.DeptId.Value));
            }

            if (string.IsNullOrEmpty(filter.Order))
                filter.Order = "Employee.EmpCode asc";
            int recoredCount = -1;
            //var detaileEntities = this.Repo.GetEntities(detailExps);
            var detaileEntities = filter.RequireRecordCount
               ? this.Repo.GetEntities(detailExps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
               : this.Repo.GetEntities(detailExps, filter.Order, filter.PageIndex, filter.PageSize);

            var empExps = this.NewExps<Employee>();

            //已提交的员工
            var submitEmpIds = detaileEntities.Where(p => p.AttDayOffRecord != null && p.AttDayOffRecord.EnumStatus == AttDayOffRecordStatus.Committed)
                                        .Select(e => e.EmployeeId).Distinct().ToList();

            //未提交的员工
            var unSubmitEmpIds = detaileEntities.Where(p => p.AttDayOffRecord != null && p.AttDayOffRecord.EnumStatus == AttDayOffRecordStatus.UnCommitted)
                                      .Select(e => e.EmployeeId).Distinct().ToList();

            //条件查询找出的员工
            var detailEmpIds = detaileEntities.Select(e => e.EmployeeId).Distinct().ToList();

            //未提交
            if (filter.Statue == 0)
            {
                empExps.And(e => unSubmitEmpIds.Contains(e.ID));
            }
            //已提交
            else if (filter.Statue == 1)
            {
                empExps.And(e => submitEmpIds.Contains(e.ID));
            }
            //全部
            if (filter.Statue == -1)
            {
                if (onlyMonthCondition)
                {
                    empExps.And(e => 1 == 1);
                }
                else
                {
                    empExps.And(e => detailEmpIds.Contains(e.ID));
                }
            }

            var empEntities = new List<Employee>();
            empEntities = this.Repo.GetEntities(empExps);
            //empEntities = filter.RequireRecordCount
            //    ? this.Repo.GetEntities(empExps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
            //    : this.Repo.GetEntities(empExps, filter.Order, filter.PageIndex, filter.PageSize);

            #region 上月记录

            var preAttDetails = new List<AttDayOffRecordDetail>();
            if (filter.EndRecordMonth.IsEmpty() || filter.EndRecordMonth == filter.RecordMonth)
            {
                var preAttDetailsExps = this.NewExps<Entities.AttDayOffRecordDetail>(p => p.EnumStatus != AttDayOffRecordDetailStatus.None && p.EnumStatus != AttDayOffRecordDetailStatus.Pending);

                if (filter.EndRecordMonth.IsEmpty())
                {
                    var empIds = empEntities.Select(e => e.ID).ToList();
                    preAttDetailsExps.Add(e => e.EmployeeId.HasValue && empIds.Contains(e.EmployeeId.Value));
                    var preMonth = filter.RecordMonth?.As<DateTime>().AddMonths(-1);
                    var preMonthStr = preMonth?.ToString("yyyy-MM");
                    preAttDetailsExps.Add(e => e.RecordMonth == preMonthStr);
                }
                preAttDetails = this.Repo.GetEntities(preAttDetailsExps);
            }

            #endregion 上月记录

            var recordMonth = filter.RecordMonth?.As<DateTime>();
            int days = recordMonth.HasValue
                ? DateTime.DaysInMonth(recordMonth.Value.Year, recordMonth.Value.Month)
                : 0;

            var empRecords = new List<AttDayOffRecordDetailQuery>();
            foreach (var detail in detaileEntities)
            {
                var emp = empEntities.Where(e => e.ID == detail.EmployeeId).First();
                var empRecord = new AttDayOffRecordDetailQuery
                {
                    EmployeeId = emp.ID,
                    EmpUid = emp.Uid.ToString(),
                    EmpCode = emp.EmpCode,
                    EmpName = emp.DisplayName,
                    HireStyleName = emp.EmployeeHR?.HireStyle?.Name,
                    EmpDept = detail.AttDayOffRecord?.Department?.Name,
                    H1 = detail.H1,
                    H2 = detail.H2,
                    H3 = detail.H3,
                    H4 = detail.H4,
                    H5 = detail.H5,
                    H6 = detail.H6,
                    H7 = detail.H7,
                    H8 = detail.H8,
                    H9 = detail.H9,
                    H10 = detail.H10,
                    H11 = detail.H11,
                    H12 = detail.H12,
                    HistoryH12 = detail.HistoryH12
                };

                var sumholiday = (empRecord.H2 ?? 0)
                                         + (empRecord.H3 ?? 0)
                                         + (empRecord.H4 ?? 0)
                                         + (empRecord.H5 ?? 0)
                                         + (empRecord.H6 ?? 0)
                                         + (empRecord.H7 ?? 0)
                                         + (empRecord.H8 ?? 0)
                                         + (empRecord.H9 ?? 0)
                                         + (empRecord.H10 ?? 0)
                                         + (empRecord.H11 ?? 0)
                                         + (empRecord.H12 ?? 0);
                empRecord.SumHoliday = sumholiday;

                //没有下月或下月为开始月
                if (filter.EndRecordMonth.IsEmpty() || filter.EndRecordMonth == filter.RecordMonth)
                {
                    var preEnt = preAttDetails.FirstOrDefault(pre => pre.EmployeeId == emp.ID);
                    if (preEnt != null)
                    {
                        empRecord.PreMonthH1 = preEnt.H1;
                    }

                    var h1 = empRecord.H1 ?? 0;
                    var calcValue = (h1 - sumholiday) * h1 / days;
                    var subsidies = Math.Round(calcValue);
                    empRecord.Subsidies = subsidies;
                }

                empRecords.Add(empRecord);
            }

            return this.QueryResult(empRecords, recoredCount, filter.PageIndex, filter.PageSize);
        }

        /// <summary>
        /// 考勤数据查询1导出
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "考勤数据查询1导出")]
        [Permission(Permissions.AttendanceManage.AttendanceData, Permissions.AttendanceManage.AttendanceSearch1)]
        public IActionResult GetAttDayOffRecordDetail1Excel([FromQuery] AttDayOffSearchFilter filter)
        {
            //只有月份条件
            var onlyMonthCondition = true;

            var detailExps = this.NewExps<AttDayOffRecordDetail>(p => p.EnumStatus != AttDayOffRecordDetailStatus.None && p.EnumStatus != AttDayOffRecordDetailStatus.Pending);

            //开始月份
            if (!filter.RecordMonth.IsEmpty())
            {
                if (filter.EndRecordMonth.IsEmpty())
                {
                    detailExps.And(p => p.RecordMonth == filter.RecordMonth);
                }
                else
                {
                    detailExps.And(p => p.RecordMonth != null && p.RecordMonth.CompareTo(filter.RecordMonth) >= 0);
                }
            }
            //结束月份
            if (!filter.EndRecordMonth.IsEmpty())
            {
                detailExps.And(p => p.RecordMonth != null && p.RecordMonth.CompareTo(filter.EndRecordMonth) <= 0);
            }

            //假期筛选
            if (!string.IsNullOrEmpty(filter.Holiday) && filter.Operation.HasValue && filter.HolidayValue.HasValue)
            {
                onlyMonthCondition = false;
                var queryCondition = new QueryCondition
                {
                    EntityColumnName = filter.Holiday,
                    EnumOperation = (Operations)filter.Operation,
                    EntityColumnType = "System.Decimal",
                    Keywords = filter.HolidayValue.ToString(),
                    EnumLogicRelationship = LogicRelationships.AND
                };

                List<QueryCondition> queryConditions = new() { queryCondition };
                var expression = new DynamicQuery().GetDynamicQuery<AttDayOffRecordDetail>(queryConditions);
                detailExps.And(expression);
            }
            // 显示有假期
            if (filter.OnlyDayOffValue)
            {
                onlyMonthCondition = false;
                detailExps.Add(p => p.H2.HasValue && p.H2.Value > 0 || p.H3.HasValue && p.H3.Value > 0
                                    || p.H4.HasValue && p.H4.Value > 0 || p.H5.HasValue && p.H5.Value > 0
                                      || p.H6.HasValue && p.H6.Value > 0 || p.H7.HasValue && p.H7.Value > 0
                                      || p.H8.HasValue && p.H8.Value > 0 || p.H9.HasValue && p.H9.Value > 0
                                      || p.H10.HasValue && p.H10.Value > 0 || p.H11.HasValue && p.H11.Value > 0
                                      || p.H12.HasValue && p.H12.Value > 0);
            }
            //显示有卫贴
            if (filter.OnlyH1Value)
            {
                onlyMonthCondition = false;
                detailExps.And(p => p.H1.HasValue && p.H1.Value > 0);
            }
            //工号
            if (!string.IsNullOrEmpty(filter.EmpCode))
            {
                onlyMonthCondition = false;
                detailExps.Add(p => p.Employee != null && p.Employee.EmpCode.Contains(filter.EmpCode));
            }
            //UID
            if (!filter.EmpUid.IsEmpty())
            {
                onlyMonthCondition = false;
                detailExps.Add(p => p.Employee != null && p.Employee.Uid.Equals(filter.EmpUid));
            }
            //姓名
            if (!string.IsNullOrEmpty(filter.EmpName))
            {
                onlyMonthCondition = false;
                detailExps.Add(p => p.Employee != null && p.Employee.DisplayName.Contains(filter.EmpName));
            }
            //部门
            if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
            {
                onlyMonthCondition = false;
                var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                detailExps.And(p => p.Employee != null && p.Employee.DeptId.HasValue && deptIds.Contains(p.Employee.DeptId.Value));
            }
            if (filter.DeptIds != null && filter.DeptIds.Length > 0)
            {
                detailExps.And(p => p.Employee != null && p.Employee.DeptId.HasValue && filter.DeptIds.Contains(p.Employee.DeptId.Value));
            }

            if (string.IsNullOrEmpty(filter.Order))
                filter.Order = "Employee.EmpCode asc";
            var detaileEntities = this.Repo.GetEntities(detailExps, filter.Order);

            var empExps = this.NewExps<Employee>();

            //已提交的员工
            var submitEmpIds = detaileEntities.Where(p => p.AttDayOffRecord != null && p.AttDayOffRecord.EnumStatus == AttDayOffRecordStatus.Committed)
                                        .Select(e => e.EmployeeId).Distinct().ToList();

            //未提交的员工
            var unSubmitEmpIds = detaileEntities.Where(p => p.AttDayOffRecord != null && p.AttDayOffRecord.EnumStatus == AttDayOffRecordStatus.UnCommitted)
                                      .Select(e => e.EmployeeId).Distinct().ToList();

            //条件查询找出的员工
            var detailEmpIds = detaileEntities.Select(e => e.EmployeeId).Distinct().ToList();

            //未提交
            if (filter.Statue == 0)
            {
                empExps.And(e => unSubmitEmpIds.Contains(e.ID));
            }
            //已提交
            else if (filter.Statue == 1)
            {
                empExps.And(e => submitEmpIds.Contains(e.ID));
            }
            //全部
            if (filter.Statue == -1)
            {
                if (onlyMonthCondition)
                {
                    empExps.And(e => 1 == 1);
                }
                else
                {
                    empExps.And(e => detailEmpIds.Contains(e.ID));
                }
            }

            //int recoredCount = -1;
            var empEntities = new List<Employee>();
            empEntities = this.Repo.GetEntities(empExps);

            #region 上月记录

            var preAttDetails = new List<AttDayOffRecordDetail>();
            if (filter.EndRecordMonth.IsEmpty() || filter.EndRecordMonth == filter.RecordMonth)
            {
                var preAttDetailsExps = this.NewExps<Entities.AttDayOffRecordDetail>(p => p.EnumStatus != AttDayOffRecordDetailStatus.None && p.EnumStatus != AttDayOffRecordDetailStatus.Pending);

                if (filter.EndRecordMonth.IsEmpty())
                {
                    var empIds = empEntities.Select(e => e.ID).ToList();
                    preAttDetailsExps.Add(e => e.EmployeeId.HasValue && empIds.Contains(e.EmployeeId.Value));
                    var preMonth = filter.RecordMonth?.As<DateTime>().AddMonths(-1);
                    var preMonthStr = preMonth?.ToString("yyyy-MM");
                    preAttDetailsExps.Add(e => e.RecordMonth == preMonthStr);
                }
                preAttDetails = this.Repo.GetEntities(preAttDetailsExps);
            }

            #endregion 上月记录

            var recordMonth = filter.RecordMonth?.As<DateTime>();
            int days = recordMonth.HasValue
                ? DateTime.DaysInMonth(recordMonth.Value.Year, recordMonth.Value.Month)
                : 0;

            var empRecords = new List<AttDayOffRecordDetailQuery>();
            foreach (var detail in detaileEntities)
            {
                var emp = empEntities.Where(e => e.ID == detail.EmployeeId).First();
                var empRecord = new AttDayOffRecordDetailQuery
                {
                    EmployeeId = emp.ID,
                    EmpUid = emp.Uid.ToString(),
                    EmpCode = emp.EmpCode,
                    EmpName = emp.DisplayName,
                    HireStyleName = emp.EmployeeHR?.HireStyle?.Name,
                    EmpDept = detail.AttDayOffRecord?.Department?.Name,
                    H1 = detail.H1,
                    H2 = detail.H2,
                    H3 = detail.H3,
                    H4 = detail.H4,
                    H5 = detail.H5,
                    H6 = detail.H6,
                    H7 = detail.H7,
                    H8 = detail.H8,
                    H9 = detail.H9,
                    H10 = detail.H10,
                    H11 = detail.H11,
                    H12 = detail.H12,
                    HistoryH12 = detail.HistoryH12
                };

                var sumholiday = (empRecord.H2 ?? 0)
                                         + (empRecord.H3 ?? 0)
                                         + (empRecord.H4 ?? 0)
                                         + (empRecord.H5 ?? 0)
                                         + (empRecord.H6 ?? 0)
                                         + (empRecord.H7 ?? 0)
                                         + (empRecord.H8 ?? 0)
                                         + (empRecord.H9 ?? 0)
                                         + (empRecord.H10 ?? 0)
                                         + (empRecord.H11 ?? 0)
                                         + (empRecord.H12 ?? 0);
                empRecord.SumHoliday = sumholiday;

                //没有下月或下月为开始月
                if (filter.EndRecordMonth.IsEmpty() || filter.EndRecordMonth == filter.RecordMonth)
                {
                    var preEnt = preAttDetails.FirstOrDefault(pre => pre.EmployeeId == emp.ID);
                    if (preEnt != null)
                    {
                        empRecord.PreMonthH1 = preEnt.H1;
                    }

                    var h1 = empRecord.H1 ?? 0;
                    var calcValue = (h1 - sumholiday) * h1 / days;
                    var subsidies = Math.Round(calcValue);
                    empRecord.Subsidies = subsidies;
                }

                empRecords.Add(empRecord);
            }

            //empEntities.ForEach(e =>
            //{
            //	var empDetails = detaileEntities.Where(de => de.EmployeeId == e.ID);
            //	var empRecord = new AttDayOffRecordDetailQuery
            //	{
            //		EmployeeId = e.ID,
            //		EmpUid = e.Uid.ToString(),
            //		EmpCode = e.EmpCode,
            //		EmpName = e.DisplayName,
            //		HireStyleName = e.EmployeeHR?.HireStyle?.Name,
            //		EmpDept = e.Department?.Name,
            //		H1 = empDetails.Sum(de => de.H1),
            //		H2 = empDetails.Sum(de => de.H2),
            //		H3 = empDetails.Sum(de => de.H3),
            //		H4 = empDetails.Sum(de => de.H4),
            //		H5 = empDetails.Sum(de => de.H5),
            //		H6 = empDetails.Sum(de => de.H6),
            //		H7 = empDetails.Sum(de => de.H7),
            //		H8 = empDetails.Sum(de => de.H8),
            //		H9 = empDetails.Sum(de => de.H9),
            //		H10 = empDetails.Sum(de => de.H10),
            //		H11 = empDetails.Sum(de => de.H11)
            //	};

            //	var sumholiday = (empRecord.H2 ?? 0)
            //							 + (empRecord.H3 ?? 0)
            //							 + (empRecord.H4 ?? 0)
            //							 + (empRecord.H5 ?? 0)
            //							 + (empRecord.H6 ?? 0)
            //							 + (empRecord.H7 ?? 0)
            //							 + (empRecord.H8 ?? 0)
            //							 + (empRecord.H9 ?? 0)
            //							 + (empRecord.H10 ?? 0)
            //							 + (empRecord.H11 ?? 0);
            //	empRecord.SumHoliday = sumholiday;

            //	//没有下月或下月为开始月
            //	if (filter.EndRecordMonth.IsEmpty() || filter.EndRecordMonth == filter.RecordMonth)
            //	{
            //		var preEnt = preAttDetails.FirstOrDefault(pre => pre.EmployeeId == e.ID);
            //		if (preEnt != null)
            //		{
            //			empRecord.PreMonthH1 = preEnt.H1;
            //		}

            //		var h1 = empRecord.H1 ?? 0;
            //		var calcValue = (h1 - sumholiday) * h1 / days;
            //		var subsidies = Math.Round(calcValue);
            //		empRecord.Subsidies = subsidies;
            //	}

            //	empRecords.Add(empRecord);
            //});

            var models = empRecords.Maps<AttDayOff>();
            DataTable? dt = Utility.ToDataTable(models);//list转datatable
            if (dt != null)
            {
                dt.Columns[0].Caption = "唯一码";
                dt.Columns[1].Caption = "工号";
                dt.Columns[2].Caption = "姓名";
                dt.Columns[3].Caption = "部门";
                dt.Columns[4].Caption = "上月卫贴标准";
                dt.Columns[5].Caption = "本月卫贴标准";
                dt.Columns[6].Caption = "病假";
                dt.Columns[7].Caption = "事假";
                dt.Columns[8].Caption = "产假";
                dt.Columns[9].Caption = "哺乳假";
                dt.Columns[10].Caption = "探亲假";
                dt.Columns[11].Caption = "计生假";
                dt.Columns[12].Caption = "婚丧假";
                dt.Columns[13].Caption = "脱产读研";
                dt.Columns[14].Caption = "因公出国";
                dt.Columns[15].Caption = "因私出国";
                dt.Columns[16].Caption = "假期合计";
                dt.Columns[17].Caption = "卫贴";
            }

            string exportFileName = $"{AppDomain.CurrentDomain.BaseDirectory}" + string.Format(Common.Consts.Exports.FileExcel, Common.Consts.Exports.FileDayOff);//生成后存放的地址 和文件名

            string sWebRootFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, Common.Consts.Exports.PathTemplate); //模板地址 这里我把模板存到了程序执行路径
            string templateFileName = $"{sWebRootFolder}" + string.Format(Common.Consts.Exports.FileExcelTemplate, Common.Consts.Exports.FileDayOff);

            var _NpoiExcelUtility = new NpoiExcelUtility();

            if (dt != null)
            {
                _NpoiExcelUtility.CreatExcelSheet(Common.Consts.Exports.FileDayOff, dt);//生成数据   这里需要注意  如果datatable没值会报错  所以要判断
            }
            var bytes = _NpoiExcelUtility.SaveExcelBytes();
            return File(bytes, Common.Consts.Exports.ContentType, Path.GetFileName(exportFileName));
        }

        /// <summary>
        /// 获取部门树（考勤数据人事），不同状态显示不同节点颜色
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取部门树（考勤数据人事）")]
        [Permission(Permissions.AttendanceManage.AttendanceData, Permissions.AttendanceManage.AttendanceHR)]
        public BizResult<AttDayOffDepts> GetColorDeptTree_DayOff([FromQuery] AttDayOffRecordFilter filter)
        {
            var exps = this.NewExps<Entities.AttDayOffRecord>();
            exps.And(p => p.RecordMonth == filter.RecordMonth);
            var rEntities = this.Repo.GetEntities(exps);
            var colorList = new List<DeptColorQuery>();
            foreach (var item in rEntities)
            {
                if (item.DeptId.HasValue)
                {
                    var q = new DeptColorQuery
                    {
                        DeptId = item.DeptId.Value
                    };

                    q.Color = "";
                    if (item.EnumStatus == AttDayOffRecordStatus.Committed)
                    {
                        q.Color = "green";
                    }
                    else if (item.EnumProphylacticStatus == ProphylacticStatus.Submitted)
                    {
                        q.Color = "red";
                    }
                    else if (item.EnumStatus == AttDayOffRecordStatus.UnCommitted)
                    {
                        q.Color = "orange";
                    }

                    colorList.Add(q);
                }
            }

            var entities = this.Repo.GetEntities<Department>();

            var all = entities.Maps<OrganizationQuery>();

            SemiNumericComparer comp = new();

            foreach (var model in all)
            {
                var dColor = colorList.FirstOrDefault(e => e.DeptId == model.ID);
                if (dColor != null)
                {
                    model.Color = dColor.Color;
                }
                var childern = all.Where(p => p.ParentId == model.ID).OrderBy(p => p.Code, comp).ToList();
                if (childern != null && childern.Any())
                {
                    childern.ForEach(cd =>
                    {
                        var cdColor = colorList.FirstOrDefault(e => e.DeptId == cd.ID);
                        if (cdColor != null)
                        {
                            cd.Color = cdColor.Color;
                        }
                    });
                    model.Children = childern;
                }
            }

            var models = all
                .Where(p => !p.ParentId.HasValue)
                .OrderBy(p => p.Code, comp)
                .ToList();

            var asdList = new List<AttDayOffStatusDept>();

            AttDayOffStatusDept allDept = new()
            {
                Status = 0,
                Depts = entities.Select(e =>
                {
                    return new SelectModel { Label = e.Name, Value = e.ID.ToString() };
                }).ToList()
            };
            asdList.Add(allDept);

            AttDayOffStatusDept unCommitedDept = new()
            {
                Status = 1
            };

            var outDeptIds = rEntities.Where(e => e.EnumStatus == AttDayOffRecordStatus.Committed && e.DeptId.HasValue).Select(e => e.DeptId);
            unCommitedDept.Depts = entities.Where(e => !outDeptIds.Contains(e.ID)).Select(e =>
            {
                return new SelectModel { Label = e.Name, Value = e.ID.ToString() };
            }).ToList();
            asdList.Add(unCommitedDept);

            AttDayOffStatusDept commitedDept = new AttDayOffStatusDept();
            commitedDept.Status = 2;
            commitedDept.Depts = rEntities.Where(e => e.EnumStatus == AttDayOffRecordStatus.Committed && e.DeptId.HasValue).Select(e =>
            {
                return new SelectModel { Label = e.Department?.Name, Value = e.DeptId?.ToString() };
            }).ToList();
            asdList.Add(commitedDept);

            AttDayOffDepts resultModel = new AttDayOffDepts();
            resultModel.ColorDepts = models;
            resultModel.StatusDepts = asdList;

            return this.BizResult(resultModel);
        }

        /// <summary>
        /// 查询高级查询内容
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询高级查询内容")]
        public QueryResult<EmployeeListQuery> QueryEmployeeList()
        {
            var entities = this.Repo.GetEntities<EmployeeList>(c => c.Fields != null && c.Fields.Contains("Uid"));

            var models = entities.Maps<EmployeeListQuery>();

            return this.QueryResult(models);
        }

        /// <summary>
        /// 考勤数据查询2
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "考勤数据查询2")]
        [Permission(Permissions.AttendanceManage.AttendanceData, Permissions.AttendanceManage.AttendanceSearch2)]
        public DynamicModel SearchAttDayOffRecordDetail2([FromQuery] AttDayOffSearchFilter filter)
        {
            List<QueryCondition> conList = new List<QueryCondition>();
            //工号
            if (!string.IsNullOrEmpty(filter.EmpCode))
            {
                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "EmpCode";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.String).ToString();
                queryCondition.Keywords = filter.EmpCode;
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }
            //姓名
            if (!string.IsNullOrEmpty(filter.EmpName))
            {
                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "DisplayName";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.String).ToString();
                queryCondition.Keywords = filter.EmpName;
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }
            //部门
            if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
            {
                var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                var values = "";
                foreach (var depIid in deptIds)
                {
                    values += string.Format(" '{0}',", depIid.ToString());
                }

                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "DeptId";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.Dict).ToString();
                queryCondition.Keywords = values.Substring(0, values.Length - 1);
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }
            if (filter.DeptIds != null && filter.DeptIds.Length > 0)
            {
                var values = string.Join("','", filter.DeptIds);

                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "DeptId";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.Dict).ToString();
                queryCondition.Keywords = values;
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }

            var condition = "";
            if (conList.Count > 0)
            {
                condition = new Common.DynamicQuery().GetSqlQuery(conList);
            }

            var startmonth = filter.RecordMonth?.Replace("-", "");
            var endmonth = string.IsNullOrEmpty(filter.EndRecordMonth) ? filter.RecordMonth : filter.EndRecordMonth;
            endmonth = endmonth?.Replace("-", "");

            var table = this.Repo.SearchAttDayOffRecordDetail2(filter.EmployeeListId,
                startmonth, endmonth,
                filter.OnlyDayOffValue, filter.OnlyH1Value, filter.Statue.ToString(), condition);

            var result = new DynamicModel();
            var listdata = Utility.ConvertToList(table);
            result.RecordCount = listdata.Count;
            result.PageIndex = filter.PageIndex;
            result.PageSize = filter.PageSize;
            if (filter.PageIndex > 0 && listdata.Count > filter.PageSize)
                listdata = listdata.AsQueryable().Skip((filter.PageIndex - 1) * filter.PageSize).Take(filter.PageSize).ToList();
            result.TableData = listdata;
            result.TableHead = GetHeadData(table);
            result.Success = true;

            return result;
        }

        /// <summary>
        /// 考勤数据查询2导出
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "考勤数据查询2导出")]
        [Permission(Permissions.AttendanceManage.AttendanceData, Permissions.AttendanceManage.AttendanceSearch2)]
        public IActionResult GetAttDayOffRecordDetail2Excel([FromQuery] AttDayOffSearchFilter filter)
        {
            List<QueryCondition> conList = new List<QueryCondition>();
            //工号
            if (!string.IsNullOrEmpty(filter.EmpCode))
            {
                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "EmpCode";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.String).ToString();
                queryCondition.Keywords = filter.EmpCode;
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }
            //姓名
            if (!string.IsNullOrEmpty(filter.EmpName))
            {
                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "DisplayName";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.String).ToString();
                queryCondition.Keywords = filter.EmpName;
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }
            //部门
            if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
            {
                var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                var values = "";
                foreach (var depIid in deptIds)
                {
                    values += string.Format(" '{0}',", depIid.ToString());
                }

                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "DeptId";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.Dict).ToString();
                queryCondition.Keywords = values.Substring(0, values.Length - 1);
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }
            if (filter.DeptIds != null && filter.DeptIds.Length > 0)
            {
                //var values = "";
                var values = string.Join("','", filter.DeptIds);

                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "DeptId";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.Dict).ToString();
                queryCondition.Keywords = values;
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }

            var condition = "";
            if (conList.Count > 0)
            {
                condition = new Common.DynamicQuery().GetSqlQuery(conList);
            }

            var startmonth = filter.RecordMonth?.Replace("-", "");
            var endmonth = string.IsNullOrEmpty(filter.EndRecordMonth) ? filter.RecordMonth : filter.EndRecordMonth;
            endmonth = endmonth?.Replace("-", "");

            var dt = this.Repo.SearchAttDayOffRecordDetail2(filter.EmployeeListId,
                startmonth, endmonth,
                filter.OnlyDayOffValue, filter.OnlyH1Value, filter.Statue.ToString(), condition);

            string exportFileName = $"{AppDomain.CurrentDomain.BaseDirectory}" + string.Format(Common.Consts.Exports.FileExcel, Common.Consts.Exports.FileDayOff);//生成后存放的地址 和文件名

            //string sWebRootFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, Common.Consts.Exports.PathTemplate); //模板地址 这里我把模板存到了程序执行路径
            //string templateFileName = $"{ sWebRootFolder }" + string.Format(Common.Consts.Exports.FileExcelTemplate, Common.Consts.Exports.FileDayOff);

            var _NpoiExcelUtility = new NpoiExcelUtility();
            if (dt != null)
            {
                _NpoiExcelUtility.CreatExcelSheet(Common.Consts.Exports.FileDayOff, dt);//生成数据   这里需要注意  如果datatable没值会报错  所以要判断
            }
            var bytes = _NpoiExcelUtility.SaveExcelBytes();
            //_NpoiExcelUtility.SaveExcel();
            //var stream = System.IO.File.OpenRead(exportFileName);
            return File(bytes, Common.Consts.Exports.ContentType, Path.GetFileName(exportFileName));
        }

        /// <summary>
        /// 考勤数据查询3
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "考勤数据查询3")]
        [Permission(Permissions.AttendanceManage.AttendanceData, Permissions.AttendanceManage.AttendanceSearch3)]
        public DynamicModel SearchAttDayOffRecordDetail3([FromQuery] AttDayOffSearchFilter filter)
        {
            List<QueryCondition> conList = new List<QueryCondition>();
            //工号
            if (!string.IsNullOrEmpty(filter.EmpCode))
            {
                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "EmpCode";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.String).ToString();
                queryCondition.Keywords = filter.EmpCode;
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }
            //姓名
            if (!string.IsNullOrEmpty(filter.EmpName))
            {
                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "DisplayName";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.String).ToString();
                queryCondition.Keywords = filter.EmpName;
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }
            //部门
            if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
            {
                var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                var values = "";
                foreach (var depIid in deptIds)
                {
                    values += string.Format(" '{0}',", depIid.ToString());
                }

                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "DeptId";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.Dict).ToString();
                queryCondition.Keywords = values.Substring(0, values.Length - 1);
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }
            if (filter.DeptIds != null && filter.DeptIds.Length > 0)
            {
                var values = string.Join("','", filter.DeptIds);

                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "DeptId";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.Dict).ToString();
                queryCondition.Keywords = values;
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }

            var condition = "";
            if (conList.Count > 0)
            {
                condition = new Common.DynamicQuery().GetSqlQuery(conList);
            }

            var startmonth = filter.RecordMonth;
            var endmonth = string.IsNullOrEmpty(filter.EndRecordMonth) ? filter.RecordMonth : filter.EndRecordMonth;

            var table = this.Repo.SearchAttDayOffRecordDetail3(filter.EmployeeListId,
                startmonth, endmonth,
                filter.OnlyDayOffValue, filter.OnlyH1Value, filter.Statue.ToString(), condition);

            var result = new DynamicModel();
            var listdata = Utility.ConvertToList(table);
            result.RecordCount = listdata.Count;
            result.PageIndex = filter.PageIndex;
            result.PageSize = filter.PageSize;
            if (filter.PageIndex > 0 && listdata.Count > filter.PageSize)
                listdata = listdata.AsQueryable().Skip((filter.PageIndex - 1) * filter.PageSize).Take(filter.PageSize).ToList();
            result.TableData = listdata;
            result.TableHead = GetHeadData(table);
            result.Success = true;

            return result;
        }

        /// <summary>
        /// 考勤数据查询3导出
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "考勤数据查询3导出")]
        [Permission(Permissions.AttendanceManage.AttendanceData, Permissions.AttendanceManage.AttendanceSearch3)]
        public IActionResult GetAttDayOffRecordDetail3Excel([FromQuery] AttDayOffSearchFilter filter)
        {
            List<QueryCondition> conList = new List<QueryCondition>();
            //工号
            if (!string.IsNullOrEmpty(filter.EmpCode))
            {
                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "EmpCode";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.String).ToString();
                queryCondition.Keywords = filter.EmpCode;
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }
            //姓名
            if (!string.IsNullOrEmpty(filter.EmpName))
            {
                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "DisplayName";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.String).ToString();
                queryCondition.Keywords = filter.EmpName;
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }
            //部门
            if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
            {
                var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                var values = "";
                foreach (var depIid in deptIds)
                {
                    values += string.Format(" '{0}',", depIid.ToString());
                }

                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "DeptId";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.Dict).ToString();
                queryCondition.Keywords = values.Substring(0, values.Length - 1);
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }
            if (filter.DeptIds != null && filter.DeptIds.Length > 0)
            {
                var values = string.Join("','", filter.DeptIds);

                var queryCondition = new QueryCondition();
                queryCondition.EntityColumnName = "DeptId";
                queryCondition.EnumOperation = Operations.Contains;
                queryCondition.EntityColumnType = ((int)FieldType.Dict).ToString();
                queryCondition.Keywords = values;
                queryCondition.EnumLogicRelationship = LogicRelationships.AND;

                conList.Add(queryCondition);
            }

            var condition = "";
            if (conList.Count > 0)
            {
                condition = new Common.DynamicQuery().GetSqlQuery(conList);
            }

            var startmonth = filter.RecordMonth;
            var endmonth = string.IsNullOrEmpty(filter.EndRecordMonth) ? filter.RecordMonth : filter.EndRecordMonth;

            var dt = this.Repo.SearchAttDayOffRecordDetail3(filter.EmployeeListId,
                startmonth, endmonth,
                filter.OnlyDayOffValue, filter.OnlyH1Value, filter.Statue.ToString(), condition);

            string exportFileName = $"{AppDomain.CurrentDomain.BaseDirectory}" + string.Format(Common.Consts.Exports.FileExcel, Common.Consts.Exports.FileDayOff);//生成后存放的地址 和文件名

            var _NpoiExcelUtility = new NpoiExcelUtility();
            if (dt != null)
            {
                _NpoiExcelUtility.CreatExcelSheet(Common.Consts.Exports.FileDayOff, dt);//生成数据   这里需要注意  如果datatable没值会报错  所以要判断
            }
            var bytes = _NpoiExcelUtility.SaveExcelBytes();
            return File(bytes, Common.Consts.Exports.ContentType, Path.GetFileName(exportFileName));
        }

        private List<object> GetHeadData(DataTable table)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add(new DataColumn("displayName"));
            dt.Columns.Add(new DataColumn("propName"));

            foreach (DataColumn col in table.Columns)
            {
                var newrow1 = dt.NewRow();
                newrow1[0] = col.ColumnName;
                newrow1[1] = col.ColumnName;
                dt.Rows.Add(newrow1);
            }

            return Utility.ConvertToList(dt);
        }

        #endregion AttDayOff 考勤数据

        #region MonthWatch 一值班二值班

        /// <summary>
        /// 查询一值班二值班
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询一值班二值班")]
        [Permission(Permissions.AttendanceManage.DutyFee)]
        public QueryResult<AttMonthWatchRecordQuery> SearchAttMonthWatchRecord([FromQuery] AttMonthWatchSearchFilter filter)
        {
            var exps = this.NewExps<AttMonthWatchRecord>();

            if (!filter.RecordMonth.IsEmpty())
            {
                exps.And(p => p.RecordMonth == filter.RecordMonth);
            }

            if (filter.GtZeroValue)
            {
                exps.Add(p => p.YZB.HasValue || p.EZB.HasValue);
            }

            var watchEntities = this.Repo.GetEntities(exps);

            var empIds = watchEntities.Select(e => e.EmployeeId).Distinct().ToList();

            var empExps = this.NewExps<Employee>();

            DateTime? dt = filter.RecordMonth?.As<DateTime>();
            var hiredId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Hired)?.ID;
            var leaveId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Leave)?.ID;
            empExps.And(p => p.EmployeeHR != null && (p.EmployeeHR.EmpStatusId == hiredId
                || p.EmployeeHR.EmpStatusId == leaveId && p.EmployeeHR.LeaveDate.HasValue && p.EmployeeHR.LeaveDate.Value > dt));
            if (!string.IsNullOrEmpty(filter.EmpCode))
            {
                empExps.Add(e => e.EmpCode.Contains(filter.EmpCode));
            }
            if (!string.IsNullOrEmpty(filter.EmpName))
            {
                empExps.Add(e => e.DisplayName.Contains(filter.EmpName));
            }
            if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
            {
                var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                empExps.And(e => e.DeptId.HasValue && deptIds.Contains(e.DeptId.Value));
            }
            if (filter.GtZeroValue)
            {
                if (empIds != null && empIds.Count > 0)
                {
                    empExps.And(e => empIds.Contains(e.ID));
                }
                else
                {
                    empExps.And(e => 1 == -1);
                }
            }

            int recoredCount = -1;
            var empEntities = new List<Employee>();
            empEntities = filter.RequireRecordCount
                ? this.Repo.GetEntities(empExps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                : this.Repo.GetEntities(empExps, filter.Order, filter.PageIndex, filter.PageSize);

            var empRecords = new List<AttMonthWatchRecordQuery>();
            empEntities.ForEach(e =>
            {
                var empRecord = new AttMonthWatchRecordQuery();

                var ent = watchEntities.FirstOrDefault(ent => ent.EmployeeId == e.ID);
                if (ent != null)
                {
                    empRecord = ent.Map<AttMonthWatchRecordQuery>();
                }
                else
                {
                    empRecord.EmployeeId = e.ID;
                    empRecord.EmpUid = e.Uid.ToString();
                    empRecord.EmpCode = e.EmpCode;
                    empRecord.EmpName = e.DisplayName;
                    empRecord.HireStyleName = e.EmployeeHR?.HireStyle?.Name;
                    empRecord.EmpDept = e.Department?.Name;
                }
                empRecords.Add(empRecord);
            });

            return this.QueryResult(empRecords, recoredCount, filter);
        }

        /// <summary>
        /// 修改一值班二值班
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "修改一值班二值班")]
        [Permission(Permissions.AttendanceManage.DutyFee)]
        public BizResult<AttMonthWatchRecordModel> UpdateAttMonthWatchRecord([FromBody] AttMonthWatchRecordModel recordModel)
        {
            var entity = recordModel.Map<AttMonthWatchRecord>();
            var result = this.Repo.UpdateAttMonthShiftRecord(entity);
            return result.Map<AttMonthWatchRecordModel>();
        }

        /// <summary>
        /// 导出一值班二值班明细
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "导出一值班二值班明细")]
        [Permission(Permissions.AttendanceManage.DutyFee)]
        public IActionResult GetMonthWatchTReportExcel([FromQuery] AttMonthWatchSearchFilter filter)
        {
            var exps = this.NewExps<AttMonthWatchRecord>();

            if (!filter.RecordMonth.IsEmpty())
            {
                exps.And(p => p.RecordMonth == filter.RecordMonth);
            }

            if (filter.GtZeroValue)
            {
                exps.Add(p => p.YZB.HasValue || p.EZB.HasValue);
            }

            var watchEntities = this.Repo.GetEntities(exps);

            var empIds = watchEntities.Select(e => e.EmployeeId).Distinct().ToList();

            var empExps = this.NewExps<Employee>();
            if (!string.IsNullOrEmpty(filter.EmpCode))
            {
                empExps.Add(e => e.EmpCode.Contains(filter.EmpCode));
            }
            if (!string.IsNullOrEmpty(filter.EmpName))
            {
                empExps.Add(e => e.DisplayName.Contains(filter.EmpName));
            }
            if (filter.DeptId.HasValue && filter.DeptId.Value != Guid.Empty)
            {
                var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                empExps.And(e => e.DeptId.HasValue && deptIds.Contains(e.DeptId.Value));
            }
            if (filter.GtZeroValue)
            {
                if (empIds != null && empIds.Count > 0)
                {
                    empExps.And(e => empIds.Contains(e.ID));
                }
                else
                {
                    empExps.And(e => 1 == -1);
                }
            }

            var empEntities = new List<Employee>();
            empEntities = this.Repo.GetEntities(empExps);

            var empRecords = new List<AttMonthWatchRecordQuery>();
            empEntities.ForEach(e =>
            {
                var empRecord = new AttMonthWatchRecordQuery();

                var ent = watchEntities.FirstOrDefault(ent => ent.EmployeeId == e.ID);
                if (ent != null)
                {
                    empRecord = ent.Map<AttMonthWatchRecordQuery>();
                }
                else
                {
                    empRecord.EmployeeId = e.ID;
                    empRecord.EmpUid = e.Uid.ToString();
                    empRecord.EmpCode = e.EmpCode;
                    empRecord.EmpName = e.DisplayName;
                    empRecord.HireStyleName = e.EmployeeHR?.HireStyle?.Name;
                    empRecord.EmpDept = e.Department?.Name;
                }
                empRecords.Add(empRecord);
            });

            var models = empRecords.Maps<AttMonthWatch>();
            DataTable? dt = Utility.ToDataTable(models);//list转datatable

            string exportFileName = $"{AppDomain.CurrentDomain.BaseDirectory}" + string.Format(Common.Consts.Exports.FileExcel, Common.Consts.Exports.FileMonthWatch);//生成后存放的地址 和文件名

            var _NpoiExcelUtility = new NpoiExcelUtility();
            if (dt != null)
            {
                dt.Columns[0].Caption = "唯一码";
                dt.Columns[1].Caption = "工号";
                dt.Columns[2].Caption = "姓名";
                dt.Columns[3].Caption = "部门";
                dt.Columns[4].Caption = "一值班班次";
                dt.Columns[5].Caption = "二值班班次";
                dt.Columns[6].Caption = "修改人";

                _NpoiExcelUtility.CreatExcelSheet(Common.Consts.Exports.FileMonthWatch, dt);//生成数据   这里需要注意  如果datatable没值会报错  所以要判断
            }
            var bytes = _NpoiExcelUtility.SaveExcelBytes();
            return File(bytes, Common.Consts.Exports.ContentType, Path.GetFileName(exportFileName));
        }

        #endregion MonthWatch 一值班二值班

        #region 防保部门考勤填报 AttDayOffRecordProphylacticDetail

        /// <summary>
        /// 查询防保科考勤申报
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询防保科考勤申报")]
        [Permission(Permissions.AttendanceManage.AttDayOffRecordProphylactic)]
        public QueryResult<AttDayOffRecordProphylacticDetailQuery> QueryAttDayOffRecordProphylacticDetail([FromQuery] AttDayOffRecordProphylacticDetailFilter filter)
        {
            var exps = this.NewExps<AttDayOffRecordProphylacticDetail>();

            if (filter.IsQuery)
            {
                if (filter.Times != null && filter.Times.Count == 2)
                {
                    exps.And(p => p.AttDayOffRecordProphylacticCase.LeaveStartDate <= filter.Times[1] && p.AttDayOffRecordProphylacticCase.LeaveEndDate >= filter.Times[0]);
                }
            }
            else
            {
                if (!filter.RecordMonth.IsEmpty())
                {
                    exps.And(p => p.RecordMonth == filter.RecordMonth);
                }
                else
                {
                    exps.And(p => false);
                }
            }

            var result = this.Repo.GetDynamicQuery<AttDayOffRecordProphylacticDetail, AttDayOffRecordProphylacticDetailQuery>(filter, exps);

            return result;
        }

        /// <summary>
        /// 导出防保科考勤申报
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "导出防保科考勤申报")]
        [Permission(Permissions.AttendanceManage.AttDayOffRecordProphylactic)]
        public IActionResult ExportAttDayOffRecordProphylacticDetail([FromQuery] AttDayOffRecordProphylacticDetailFilter filter)
        {
            var exps = this.NewExps<AttDayOffRecordProphylacticDetail>();

            if (!filter.DeptId.IsEmpty())
            {
                exps.And(p => p.AttDayOffRecord.DeptId == filter.DeptId);
            }

            if (filter.IsQuery)
            {
                if (filter.Times != null && filter.Times.Count == 2)
                {
                    exps.And(p => p.AttDayOffRecordProphylacticCase.LeaveStartDate <= filter.Times[1] && p.AttDayOffRecordProphylacticCase.LeaveEndDate >= filter.Times[0]);
                }
            }
            else
            {
                if (!filter.RecordMonth.IsEmpty())
                {
                    exps.And(p => p.RecordMonth == filter.RecordMonth);
                }
                else
                {
                    exps.And(p => false);
                }
            }

            if (!filter.EmpCode.IsEmpty())
            {
                exps.And(p => p.Employee.EmpCode.Contains(filter.EmpCode!));
            }

            if (!filter.EmpName.IsEmpty())
            {
                exps.And(p => p.Employee.DisplayName.Contains(filter.EmpName!));
            }

            if (filter.LeaveTypes != null && filter.LeaveTypes.Any())
            {
                exps.And(p => filter.LeaveTypes.Contains(p.AttDayOffRecordProphylacticCase.EnumLeaveType));
            }

            if (filter.HolidayTypes != null && filter.HolidayTypes.Any())
            {
                exps.And(p => filter.HolidayTypes.Contains(p.AttDayOffRecordProphylacticCase.EnumHolidayType));
            }

            if (filter.AttDayOffRecordProphylacticDetailStatus != null && filter.AttDayOffRecordProphylacticDetailStatus.Any())
            {
                exps.And(p => filter.AttDayOffRecordProphylacticDetailStatus.Contains(p.EnumStatus));
            }

            var entitys = this.Repo.GetEntities(AttDayOffRecordProphylacticDetail.Foreigns.AttDayOffRecordProphylacticCase, exps, filter.Order);
            var tables = this.FormatAttDayOffRecordProphylacticDetailTable(entitys);
            var bytes = Excel.WriteToExcelBytes(tables, "防保科考勤申报");
            return this.File(bytes, ConstDefinition.Common.Export_Excel, "防保科考勤申报" + SysDateTime.Now.ToString("yyyy-MM-dd") + ConstDefinition.Common.ExtensionOfHighVersionExcel);
        }

        private DataTable FormatAttDayOffRecordProphylacticDetailTable(List<AttDayOffRecordProphylacticDetail> entitys)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add(new DataColumn("唯一码", typeof(string)));
            dt.Columns.Add(new DataColumn("工号", typeof(string)));
            dt.Columns.Add(new DataColumn("姓名", typeof(string)));
            dt.Columns.Add(new DataColumn("性别", typeof(string)));
            dt.Columns.Add(new DataColumn("年龄", typeof(int)));
            dt.Columns.Add(new DataColumn("部门", typeof(string)));
            dt.Columns.Add(new DataColumn("院区", typeof(string)));
            dt.Columns.Add(new DataColumn("身份证号", typeof(string)));
            dt.Columns.Add(new DataColumn("月份", typeof(string)));
            dt.Columns.Add(new DataColumn("请假类别", typeof(string)));
            dt.Columns.Add(new DataColumn("诊断意见", typeof(string)));
            dt.Columns.Add(new DataColumn("就诊医院", typeof(string)));
            dt.Columns.Add(new DataColumn("就诊科室", typeof(string)));
            dt.Columns.Add(new DataColumn("就诊医师", typeof(string)));
            dt.Columns.Add(new DataColumn("假期类型", typeof(string)));
            dt.Columns.Add(new DataColumn("假期备注", typeof(string)));
            dt.Columns.Add(new DataColumn("休假开始日期", typeof(string)));
            dt.Columns.Add(new DataColumn("休假结束日期", typeof(string)));
            dt.Columns.Add(new DataColumn("开具时间", typeof(string)));
            dt.Columns.Add(new DataColumn("状态", typeof(string)));
            dt.Columns.Add(new DataColumn("病假", typeof(int)));
            dt.Columns.Add(new DataColumn("事假", typeof(int)));
            dt.Columns.Add(new DataColumn("产假", typeof(int)));
            dt.Columns.Add(new DataColumn("哺乳假", typeof(int)));
            dt.Columns.Add(new DataColumn("探亲假", typeof(int)));
            dt.Columns.Add(new DataColumn("计生假", typeof(int)));
            dt.Columns.Add(new DataColumn("婚丧假", typeof(int)));
            dt.Columns.Add(new DataColumn("脱产读研", typeof(int)));
            dt.Columns.Add(new DataColumn("因公出国", typeof(int)));
            dt.Columns.Add(new DataColumn("因私出国", typeof(int)));

            foreach (var entity in entitys ?? new List<AttDayOffRecordProphylacticDetail>())
            {
                var model = entity.Map<AttDayOffRecordProphylacticDetailQuery>();
                var dr = dt.NewRow();
                dr["唯一码"] = model.EmpUid;
                dr["工号"] = model.EmpCode;
                dr["姓名"] = model.EmpName;
                dr["性别"] = model.GenderDesc;
                dr["年龄"] = model.Age;
                dr["部门"] = model.EmpDept;
                dr["院区"] = model.HospitalAreaNameText;
                dr["身份证号"] = model.IdentityNumber;
                dr["月份"] = model.RecordMonth.ToString("yyyy-MM");
                dr["请假类别"] = entity.AttDayOffRecordProphylacticCase.EnumLeaveTypeDesc;
                dr["诊断意见"] = entity.AttDayOffRecordProphylacticCase.DiagnostiOpinion;
                dr["就诊医院"] = entity.AttDayOffRecordProphylacticCase.VisitingHospital;
                dr["就诊科室"] = entity.AttDayOffRecordProphylacticCase.VisitingDepartment;
                dr["就诊医师"] = entity.AttDayOffRecordProphylacticCase.VisitingPhysician;
                dr["假期类型"] = entity.AttDayOffRecordProphylacticCase.EnumHolidayTypeDesc;
                dr["假期备注"] = entity.AttDayOffRecordProphylacticCase.HolidayRemark;
                dr["休假开始日期"] = entity.AttDayOffRecordProphylacticCase.LeaveStartDate.ToString("yyyy-MM-dd");
                dr["休假结束日期"] = entity.AttDayOffRecordProphylacticCase.LeaveEndDate.ToString("yyyy-MM-dd");
                dr["开具时间"] = entity.AttDayOffRecordProphylacticCase.IssuingTime.ToString("yyyy-MM-dd");
                dr["状态"] = model.EnumStatusDesc;
                if (model.H2.HasValue)
                {
                    dr["病假"] = Convert.ToInt32(Math.Truncate(model.H2.Value));
                }
                if (model.H3.HasValue)
                {
                    dr["事假"] = Convert.ToInt32(Math.Truncate(model.H3.Value));
                }
                if (model.H4.HasValue)
                {
                    dr["产假"] = Convert.ToInt32(Math.Truncate(model.H4.Value));
                }
                if (model.H5.HasValue)
                {
                    dr["哺乳假"] = Convert.ToInt32(Math.Truncate(model.H5.Value));
                }
                if (model.H6.HasValue)
                {
                    dr["探亲假"] = Convert.ToInt32(Math.Truncate(model.H6.Value));
                }
                if (model.H7.HasValue)
                {
                    dr["计生假"] = Convert.ToInt32(Math.Truncate(model.H7.Value));
                }
                if (model.H8.HasValue)
                {
                    dr["婚丧假"] = Convert.ToInt32(Math.Truncate(model.H8.Value));
                }
                if (model.H9.HasValue)
                {
                    dr["脱产读研"] = Convert.ToInt32(Math.Truncate(model.H9.Value));
                }
                if (model.H10.HasValue)
                {
                    dr["因公出国"] = Convert.ToInt32(Math.Truncate(model.H10.Value));
                }
                if (model.H11.HasValue)
                {
                    dr["因私出国"] = Convert.ToInt32(Math.Truncate(model.H11.Value));
                }

                dt.Rows.Add(dr);
            }

            return dt;
        }

        /// <summary>
		/// 获取防保科考勤申报-病例
		/// </summary>
		[HttpGet]
        [LogApi(ApiType.Query, Operate = "获取防保科考勤申报-病例")]
        [Permission(Permissions.AttendanceManage.AttDayOffRecordProphylactic)]
        public BizResult<AttDayOffRecordProphylacticCaseModel> GetAttDayOffRecordProphylacticCase([FromQuery] Guid id)
        {
            var result = new BizResult<AttDayOffRecordProphylacticCaseModel>();

            var entity = this.Repo.Get<AttDayOffRecordProphylacticCase>(id);

            if (entity == null)
            {
                result.Error("防保科考勤申报病例不存在");
            }
            else
            {
                var model = entity.Map<AttDayOffRecordProphylacticCaseModel>();

                model.LeaveDateRange.Add(model.LeaveStartDate);
                model.LeaveDateRange.Add(model.LeaveEndDate);

                var details = this.Repo.GetEntities<AttDayOffRecordProphylacticDetail>(p => p.AttDayOffRecordProphylacticCaseId == id);
                model.Detail = details.Maps<AttDayOffRecordProphylacticDetailModel>().OrderBy(p => p.RecordMonth).ToList();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 获取防保科考勤申报
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取防保科考勤申报")]
        [Permission(Permissions.AttendanceManage.AttDayOffRecordProphylactic)]
        public BizResult<AttDayOffRecordProphylacticDetailModel> GetAttDayOffRecordProphylacticDetail([FromQuery] Guid id)
        {
            var result = new BizResult<AttDayOffRecordProphylacticDetailModel>();

            var entity = this.Repo.Get<AttDayOffRecordProphylacticDetail>(id);

            if (entity == null)
            {
                result.Error("防保科考勤申报不存在");
            }
            else
            {
                var model = entity.Map<AttDayOffRecordProphylacticDetailModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 获取防保科考勤申报主表
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取防保科考勤申报主表")]
        [Permission(Permissions.AttendanceManage.AttDayOffRecordProphylactic)]
        public BizResult<AttDayOffRecordProphylacticModel> GetAttDayOffRecordProphylactic([FromQuery] DateTime recordMonth)
        {
            var result = new BizResult<AttDayOffRecordProphylacticModel>();

            var entity = this.Repo.GetEntity<AttDayOffRecordProphylactic>(p => p.RecordMonth == recordMonth);

            var isAllowSubmit = recordMonth == SysDateTime.Now.Date.AddDays(1 - SysDateTime.Now.Date.Day).AddMonths(-1);
            if (entity != null)
            {
                var model = entity.Map<AttDayOffRecordProphylacticModel>();
                model.IsAllowSubmit = isAllowSubmit;
                result.Data = model;
            }
            else
            {
                result.Data = new AttDayOffRecordProphylacticModel()
                {
                    EnumStatus = AttDayOffRecordProphylacticStatus.Pending,
                    IsAllowSubmit = isAllowSubmit,
                };
            }

            return result;
        }

        /// <summary>
		/// 新增防保科考勤申报
		/// </summary>
		/// <param name="model"></param>
		/// <returns></returns>
		[HttpPost]
        [LogApi(ApiType.Save, Operate = "新增防保科考勤申报")]
        [Permission(Permissions.AttendanceManage.AttDayOffRecordProphylactic)]
        public BizResult<AttDayOffRecordProphylacticCaseModel> AddAttDayOffRecordProphylactic(AttDayOffRecordProphylacticCaseModel model)
        {
            var entity = model.Map<AttDayOffRecordProphylacticCase>();
            var details = model.Detail.Maps<AttDayOffRecordProphylacticDetail>();

            var result = this.Repo.AddAttDayOffRecordProphylactic(entity, details);

            return result.Map<AttDayOffRecordProphylacticCaseModel>();
        }

        /// <summary>
        /// 修改防保科考勤申报
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "修改防保科考勤申报")]
        [Permission(Permissions.AttendanceManage.AttDayOffRecordProphylactic)]
        public BizResult<AttDayOffRecordProphylacticCaseModel> UpdateAttDayOffRecordProphylactic(AttDayOffRecordProphylacticCaseModel model)
        {
            var entity = model.Map<AttDayOffRecordProphylacticCase>();
            var details = model.Detail.Maps<AttDayOffRecordProphylacticDetail>();

            var result = this.Repo.UpdateAttDayOffRecordProphylactic(entity, details);

            return result.Map<AttDayOffRecordProphylacticCaseModel>();
        }

        /// <summary>
        /// 删除防保科考勤申报
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除防保科考勤申报")]
        [Permission(Permissions.AttendanceManage.AttDayOffRecordProphylactic)]
        public BizResult DeleteAttDayOffRecordProphylacticDetail(AttDayOffRecordProphylacticDetailModel model)
        {
            return this.Repo.DeleteAttDayOffRecordProphylacticDetail(model.ID);
        }

        /// <summary>
        /// 提交防保科考勤申报
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "提交防保科考勤申报")]
        [Permission(Permissions.AttendanceManage.AttDayOffRecordProphylactic)]
        public BizResult<AttDayOffRecordProphylacticModel> SubjectAttDayOffRecordProphylactic(AttDayOffRecordProphylacticModel model)
        {
            var entity = model.Map<AttDayOffRecordProphylactic>();

            var result = this.Repo.SubjectAttDayOffRecordProphylactic(entity);

            return result.Map<AttDayOffRecordProphylacticModel>();
        }

        /// <summary>
        /// 查询防保科考勤变动
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询防保科考勤变动")]
        [Permission(Permissions.AttendanceManage.ProphylacticChange)]
        public QueryResult<AttDayOffRecordProphylacticDetailQuery> QueryProphylacticChange([FromQuery] AttDayOffRecordProphylacticDetailFilter filter)
        {
            var datas = GetProphylacticChangeDatas(filter, true, out int count);

            //查询正式明细数据
            var employeeIds = datas.Select(p => p.EmployeeId).Distinct().ToList();
            var strMonth = filter.RecordMonth.ToString("yyyy-MM");
            var attDayOffRecordDetails = this.Repo.GetEntities<AttDayOffRecordDetail>(p => p.RecordMonth == strMonth && p.EnumStatus != AttDayOffRecordDetailStatus.Pending
                                                                && p.EnumStatus != AttDayOffRecordDetailStatus.None && p.EmployeeId.HasValue && employeeIds.Contains(p.EmployeeId.Value));

            datas.ForEach(prophylacticDetail =>
            {
                var attDayOffRecordDetail = attDayOffRecordDetails.FirstOrDefault(p => p.EmployeeId == prophylacticDetail.EmployeeId);
                prophylacticDetail.IsDifference = false;

                if (attDayOffRecordDetail != null)
                {
                    if (prophylacticDetail.H2.GetValueOrDefault() != attDayOffRecordDetail.H2.GetValueOrDefault() || prophylacticDetail.H3.GetValueOrDefault() != attDayOffRecordDetail.H3.GetValueOrDefault() ||
                        prophylacticDetail.H4.GetValueOrDefault() != attDayOffRecordDetail.H4.GetValueOrDefault() || prophylacticDetail.H5.GetValueOrDefault() != attDayOffRecordDetail.H5.GetValueOrDefault() ||
                        prophylacticDetail.H6.GetValueOrDefault() != attDayOffRecordDetail.H6.GetValueOrDefault() || prophylacticDetail.H7.GetValueOrDefault() != attDayOffRecordDetail.H7.GetValueOrDefault() ||
                        prophylacticDetail.H8.GetValueOrDefault() != attDayOffRecordDetail.H8.GetValueOrDefault() || prophylacticDetail.H9.GetValueOrDefault() != attDayOffRecordDetail.H9.GetValueOrDefault() ||
                        prophylacticDetail.H10.GetValueOrDefault() != attDayOffRecordDetail.H10.GetValueOrDefault() || prophylacticDetail.H11.GetValueOrDefault() != attDayOffRecordDetail.H11.GetValueOrDefault())
                    {
                        prophylacticDetail.IsDifference = true;
                        prophylacticDetail.AttDayOffRecordDetailQuery = attDayOffRecordDetail.Map<AttDayOffRecordDetailQuery>();
                    }
                }
                else
                {
                    prophylacticDetail.IsDifference = true;
                }
            });

            return new QueryResult<AttDayOffRecordProphylacticDetailQuery>(datas, count, filter);
        }

        private List<AttDayOffRecordProphylacticDetailQuery> GetProphylacticChangeDatas(AttDayOffRecordProphylacticDetailFilter filter, bool isQuery, out int count)
        {
            var exps = this.NewExps<AttDayOffRecordProphylacticDetail>();
            if (filter.DeptId.HasValue)
            {
                exps.And(p => p.AttDayOffRecord.DeptId == filter.DeptId);
            }

            if (!filter.EmpCode.IsEmpty())
            {
                exps.And(p => p.Employee.EmpCode.Contains(filter.EmpCode!));
            }

            if (!filter.EmpName.IsEmpty())
            {
                exps.And(p => p.Employee.DisplayName.Contains(filter.EmpName!));
            }

            //1、如果查询月份为“已提交” 则修改日期必须大于提交日期
            //2、如果查询月份为没有数据，把修改日期大于查询月份第一天的数据查询出来
            //3、如果查询月份没有“提交”，则把当月数据全部查询出来
            var attDayOffRecordProphylactic = this.Repo.GetEntity<AttDayOffRecordProphylactic>(p => p.RecordMonth == filter.RecordMonth && p.EnumStatus == AttDayOffRecordProphylacticStatus.Submitted);
            if (attDayOffRecordProphylactic != null && attDayOffRecordProphylactic.EnumStatus == AttDayOffRecordProphylacticStatus.Submitted)
            {
                var submitTime = attDayOffRecordProphylactic.SubmitTime;

                exps.And(p => p.UpdateTime > submitTime && p.AttDayOffRecordProphylactic.RecordMonth <= filter.RecordMonth);
            }
            else
            {
                if (filter.RecordMonth < SysDateTime.Now.Date.AddDays(1 - SysDateTime.Now.Date.Day).AddMonths(-1))
                {
                    exps.And(p => ((p.AttDayOffRecordProphylactic.EnumStatus == AttDayOffRecordProphylacticStatus.Pending && p.AttDayOffRecordProphylactic.RecordMonth == filter.RecordMonth)
                    || p.UpdateTime > filter.RecordMonth) && p.AttDayOffRecordProphylactic.RecordMonth <= filter.RecordMonth);
                }
                else
                {
                    exps.And(p => false);
                }
            }

            var entitys = this.Repo.GetEntities<AttDayOffRecordProphylacticDetail>(exps);

            var entitysGroup = entitys.GroupBy(p => new { RecordMonth = p.RecordMonth }).Select(p => new { RecordMonth = p.Key.RecordMonth, EmployeeIds = p.Select(p => p.EmployeeId).ToList() }).ToList();

            var exps2 = this.NewExps<AttDayOffRecordProphylacticDetail>();
            if (entitysGroup.Any())
            {
                var exp = this.NewExp<AttDayOffRecordProphylacticDetail>(p => false);
                foreach (var item in entitysGroup)
                {
                    exp = exp.Or(p => p.RecordMonth == item.RecordMonth && item.EmployeeIds.Contains(p.EmployeeId));
                }
                exps2 = exps2.And(exp);
            }
            else
            {
                exps2 = exps2.And(p => false);
            }

            var attDayOffRecordProphylacticDetails = this.Repo.GetEntities<AttDayOffRecordProphylacticDetail>(exps2).Maps<AttDayOffRecordProphylacticDetailQuery>();

            var groups = attDayOffRecordProphylacticDetails.GroupBy(p => new
            {
                EmpUid = p.EmpUid,
                EmpCode = p.EmpCode,
                EmpName = p.EmpName,
                GenderDesc = p.GenderDesc,
                EmpDept = p.EmpDept,
                RecordMonth = p.RecordMonth,
            }).ToList();

            var quers = new List<AttDayOffRecordProphylacticDetailQuery>();
            foreach (var group in groups)
            {
                var attDayOffRecordProphylacticDetailQuery = group.FirstOrDefault();
                if (attDayOffRecordProphylacticDetailQuery != null)
                {
                    attDayOffRecordProphylacticDetailQuery.H2 = group.Sum(p => p.H2.GetValueOrDefault());
                    attDayOffRecordProphylacticDetailQuery.H3 = group.Sum(p => p.H3.GetValueOrDefault());
                    attDayOffRecordProphylacticDetailQuery.H4 = group.Sum(p => p.H4.GetValueOrDefault());
                    attDayOffRecordProphylacticDetailQuery.H5 = group.Sum(p => p.H5.GetValueOrDefault());
                    attDayOffRecordProphylacticDetailQuery.H6 = group.Sum(p => p.H6.GetValueOrDefault());
                    attDayOffRecordProphylacticDetailQuery.H7 = group.Sum(p => p.H7.GetValueOrDefault());
                    attDayOffRecordProphylacticDetailQuery.H8 = group.Sum(p => p.H8.GetValueOrDefault());
                    attDayOffRecordProphylacticDetailQuery.H9 = group.Sum(p => p.H9.GetValueOrDefault());
                    attDayOffRecordProphylacticDetailQuery.H10 = group.Sum(p => p.H10.GetValueOrDefault());
                    attDayOffRecordProphylacticDetailQuery.H11 = group.Sum(p => p.H11.GetValueOrDefault());
                    attDayOffRecordProphylacticDetailQuery.H2 = attDayOffRecordProphylacticDetailQuery.H2 == 0 ? null : attDayOffRecordProphylacticDetailQuery.H2;
                    attDayOffRecordProphylacticDetailQuery.H3 = attDayOffRecordProphylacticDetailQuery.H3 == 0 ? null : attDayOffRecordProphylacticDetailQuery.H3;
                    attDayOffRecordProphylacticDetailQuery.H4 = attDayOffRecordProphylacticDetailQuery.H4 == 0 ? null : attDayOffRecordProphylacticDetailQuery.H4;
                    attDayOffRecordProphylacticDetailQuery.H5 = attDayOffRecordProphylacticDetailQuery.H5 == 0 ? null : attDayOffRecordProphylacticDetailQuery.H5;
                    attDayOffRecordProphylacticDetailQuery.H6 = attDayOffRecordProphylacticDetailQuery.H6 == 0 ? null : attDayOffRecordProphylacticDetailQuery.H6;
                    attDayOffRecordProphylacticDetailQuery.H7 = attDayOffRecordProphylacticDetailQuery.H7 == 0 ? null : attDayOffRecordProphylacticDetailQuery.H7;
                    attDayOffRecordProphylacticDetailQuery.H8 = attDayOffRecordProphylacticDetailQuery.H8 == 0 ? null : attDayOffRecordProphylacticDetailQuery.H8;
                    attDayOffRecordProphylacticDetailQuery.H9 = attDayOffRecordProphylacticDetailQuery.H9 == 0 ? null : attDayOffRecordProphylacticDetailQuery.H9;
                    attDayOffRecordProphylacticDetailQuery.H10 = attDayOffRecordProphylacticDetailQuery.H10 == 0 ? null : attDayOffRecordProphylacticDetailQuery.H10;
                    attDayOffRecordProphylacticDetailQuery.H11 = attDayOffRecordProphylacticDetailQuery.H11 == 0 ? null : attDayOffRecordProphylacticDetailQuery.H11;
                    quers.Add(attDayOffRecordProphylacticDetailQuery);
                }
            }

            if (!filter.Order.IsEmpty())
            {
                var order = filter.Order.Contains("+") ? "ASC" : "DESC";
                var prop = filter.Order.Replace("+", "").Replace("-", "");

                string orderExpression = string.Format("{0} {1}", prop, order);

                quers = quers.OrderBy(orderExpression).ToList();
            }

            count = quers.Count();

            return isQuery ? quers.Paging(filter.PageIndex, filter.PageSize).ToList() : quers;
        }

        /// <summary>
        /// 导出防保科考勤变动
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "导出防保科考勤变动")]
        [Permission(Permissions.AttendanceManage.ProphylacticChange)]
        public IActionResult ExportProphylacticChange([FromQuery] AttDayOffRecordProphylacticDetailFilter filter)
        {
            var datas = GetProphylacticChangeDatas(filter, false, out int count);

            var tables = this.FormatProphylacticChangeTable(datas);
            var bytes = Excel.WriteToExcelBytes(tables, "防保科考勤变动");
            return this.File(bytes, ConstDefinition.Common.Export_Excel, "防保科考勤变动" + SysDateTime.Now.ToString("yyyy-MM-dd") + ConstDefinition.Common.ExtensionOfHighVersionExcel);
        }

        private DataTable FormatProphylacticChangeTable(List<AttDayOffRecordProphylacticDetailQuery> models)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add(new DataColumn("唯一码", typeof(string)));
            dt.Columns.Add(new DataColumn("工号", typeof(string)));
            dt.Columns.Add(new DataColumn("姓名", typeof(string)));
            dt.Columns.Add(new DataColumn("性别", typeof(string)));
            dt.Columns.Add(new DataColumn("年龄", typeof(int)));
            dt.Columns.Add(new DataColumn("部门", typeof(string)));
            dt.Columns.Add(new DataColumn("月份", typeof(string)));
            dt.Columns.Add(new DataColumn("病假", typeof(decimal)));
            dt.Columns.Add(new DataColumn("事假", typeof(decimal)));
            dt.Columns.Add(new DataColumn("产假", typeof(decimal)));
            dt.Columns.Add(new DataColumn("哺乳假", typeof(decimal)));
            dt.Columns.Add(new DataColumn("探亲假", typeof(decimal)));
            dt.Columns.Add(new DataColumn("计生假", typeof(decimal)));
            dt.Columns.Add(new DataColumn("婚丧假", typeof(decimal)));
            dt.Columns.Add(new DataColumn("脱产读研", typeof(decimal)));
            dt.Columns.Add(new DataColumn("因公出国", typeof(decimal)));
            dt.Columns.Add(new DataColumn("因私出国", typeof(decimal)));

            foreach (var model in models ?? new List<AttDayOffRecordProphylacticDetailQuery>())
            {
                var dr = dt.NewRow();
                dr["唯一码"] = model.EmpUid;
                dr["工号"] = model.EmpCode;
                dr["姓名"] = model.EmpName;
                dr["性别"] = model.GenderDesc;
                dr["年龄"] = model.Age;
                dr["部门"] = model.EmpDept;
                dr["月份"] = model.RecordMonth.ToString("yyyy-MM");

                if (model.H2.HasValue)
                {
                    dr["病假"] = model.H2;
                }
                if (model.H3.HasValue)
                {
                    dr["事假"] = model.H3;
                }
                if (model.H4.HasValue)
                {
                    dr["产假"] = model.H4;
                }
                if (model.H5.HasValue)
                {
                    dr["哺乳假"] = model.H5;
                }
                if (model.H6.HasValue)
                {
                    dr["探亲假"] = model.H6;
                }
                if (model.H7.HasValue)
                {
                    dr["计生假"] = model.H7;
                }
                if (model.H8.HasValue)
                {
                    dr["婚丧假"] = model.H8;
                }
                if (model.H9.HasValue)
                {
                    dr["脱产读研"] = model.H9;
                }
                if (model.H10.HasValue)
                {
                    dr["因公出国"] = model.H10;
                }
                if (model.H11.HasValue)
                {
                    dr["因私出国"] = model.H11;
                }

                dt.Rows.Add(dr);
            }

            return dt;
        }

        #endregion 防保部门考勤填报 AttDayOffRecordProphylacticDetail

        #region 考勤员填报

        /// <summary>
        /// 查询考勤人员填报信息
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询考勤人员填报信息")]
        [Permission(Permissions.AttendanceManage.AttendanceStaging, Permissions.AttendanceManage.AttendanceApply)]
        public QueryResult<AttDayOffRecordFillingQuery> QueryCheckRecordFilling([FromQuery] AttDayOffRecordFillingFilter filter)
        {
            int recoredCount = -1;
            var empEntities = GetAttDayOffRecordEmployee(filter, out recoredCount);
            var attDayOffRecord = this.Repo.Get<AttDayOffRecord>(filter.RecordId);
            var models = new List<AttDayOffRecordFillingQuery>();

            if (empEntities.Any())
            {
                // 如果已提交人事部，则显示
                if (attDayOffRecord != null && attDayOffRecord.EnumStatus == AttDayOffRecordStatus.Committed)
                {
                    models = this.GetCheckAttDayOffRecordDetail(filter, empEntities);
                }
                else
                {
                    // 只查询考勤员填报
                    var attExps = this.NewExps<AttDayOffRecordFilling>(p => p.EnumType == AttDayOffRecordFillingType.Check);
                    var strRecordMonth = filter.RecordMonth.ToString("yyyy-MM");
                    attExps.And(p => p.AttDayOffRecord.RecordMonth == strRecordMonth);
                    if (filter.RecordId.HasValue)
                    {
                        attExps.And(p => p.RecordId == filter.RecordId.Value);
                    }
                    else
                    {
                        attExps.And(p => false);
                    }

                    var attDetailEntites = this.Repo.GetEntities(attExps);

                    empEntities.ForEach(e =>
                    {
                        var att = attDetailEntites.FirstOrDefault(d => d.EmployeeId == e.ID);
                        if (att != null)
                        {
                            var model = att.Map<AttDayOffRecordFillingQuery>();
                            models.Add(model);
                        }
                        else
                        {
                            var model = new AttDayOffRecordFilling()
                            {
                                EmployeeId = e.ID,
                                Employee = e,
                                HistoryH12 = e.EmployeeHR?.GeneralHolidayCalculateTime?.ToString("yyyy-MM") == strRecordMonth ? e.EmployeeHR.GeneralHoliday : null
                            }.Map<AttDayOffRecordFillingQuery>();
                            models.Add(model);
                        }
                    });
                }

                //上月卫生津贴
                if (!filter.RecordMonth.IsEmpty() && filter.DeptId.HasValue)
                {
                    //var preMonth = filter.RecordMonth.AddMonths(-1).ToString("yyyy-MM");
                    // 若上月没提交数据则获取不到，取查询当年的数据，如果当年度都取不到，空白
                    List<string> months = new List<string>();
                    for (int i = 1; i <= 12; i++)
                    {
                        var month = filter.RecordMonth.AddMonths(-i).ToString("yyyy-MM");
                        if (month.Substring(0, 4) == filter.RecordMonth.ToString().Substring(0, 4))
                        {
                            months.Add(month);
                        }
                        else
                        {
                            continue;
                        }
                    }

                    var preAttDayOffRecordDetails = this.Repo.GetEntities<AttDayOffRecordDetail>(p => months.Contains(p.AttDayOffRecord.RecordMonth) && p.AttDayOffRecord.DeptId == filter.DeptId
                                                                                                && p.EnumStatus != AttDayOffRecordDetailStatus.None && p.EnumStatus != AttDayOffRecordDetailStatus.Pending);


                    //foreach (var preAttDayOffRecordDetail in preAttDayOffRecordDetails)
                    //{
                    //    var model = models.FirstOrDefault(m => m.EmployeeId == preAttDayOffRecordDetail.EmployeeId);
                    //    if (model != null)
                    //    {
                    //        model.PreMonthH1 = preAttDayOffRecordDetail.H1;

                    //        if (!model.HistoryH12.HasValue && model.GeneralHolidayCalculateTime?.ToString("yyyy-MM") != filter.RecordMonth.ToString("yyyy-MM"))
                    //        {
                    //            var calculatedValue = preAttDayOffRecordDetail.HistoryH12.GetValueOrDefault() - preAttDayOffRecordDetail.H12.GetValueOrDefault();
                    //            model.HistoryH12 = calculatedValue <= 0 ? null : calculatedValue;
                    //        }

                    //        if (attDayOffRecord == null || (attDayOffRecord.EnumStatus != AttDayOffRecordStatus.Committed && attDayOffRecord.EnumStatus != AttDayOffRecordStatus.UnCommitted))
                    //        {
                    //            model.H1 = model.PreMonthH1;
                    //        }
                    //    }
                    //}

                    // 若上月没提交数据则获取不到，取查询当年的数据，如果当年度都取不到，空白
                    List<Guid> empList = new List<Guid>();
                    foreach (var preAttDayOffRecordDetail in preAttDayOffRecordDetails)
                    {
                        var EmpId = preAttDayOffRecordDetail.EmployeeId.Value;
                        if (empList.IndexOf(EmpId) < 0)
                        {
                            var preAttDetail = preAttDayOffRecordDetails.Where(m => m.EmployeeId == EmpId).OrderByDescending(x => x.RecordMonth).FirstOrDefault();

                            var model = models.FirstOrDefault(m => m.EmployeeId == preAttDetail.EmployeeId);
                            if (model != null)
                            {
                                model.PreMonthH1 = preAttDetail.H1;

                                if (!model.HistoryH12.HasValue && model.GeneralHolidayCalculateTime?.ToString("yyyy-MM") != filter.RecordMonth.ToString("yyyy-MM"))
                                {
                                    var calculatedValue = preAttDetail.HistoryH12.GetValueOrDefault() - preAttDetail.H12.GetValueOrDefault();
                                    model.HistoryH12 = calculatedValue <= 0 ? null : calculatedValue;
                                }

                                if (attDayOffRecord == null || (attDayOffRecord.EnumStatus != AttDayOffRecordStatus.Committed && attDayOffRecord.EnumStatus != AttDayOffRecordStatus.UnCommitted))
                                {
                                    model.H1 = model.PreMonthH1;
                                }
                            }
                            empList.Add(EmpId);
                        }
                    }
                }

            }

            var attDetails = this.QueryResult(models, recoredCount, filter);
            return attDetails;
        }

        private List<AttDayOffRecordFillingQuery> GetCheckAttDayOffRecordDetail(AttDayOffRecordFillingFilter filter, List<Employee> empEntities)
        {
            var models = new List<AttDayOffRecordFillingQuery>();
            var attExps = this.NewExps<AttDayOffRecordDetail>(p => p.EnumStatus != AttDayOffRecordDetailStatus.None);
            var strRecordMonth = filter.RecordMonth.ToString("yyyy-MM");
            attExps.And(p => p.AttDayOffRecord != null && p.AttDayOffRecord.RecordMonth == strRecordMonth);
            if (filter.RecordId.HasValue)
            {
                attExps.And(p => p.RecordId == filter.RecordId.Value);
            }
            else
            {
                attExps.And(p => false);
            }

            var attDetailEntites = this.Repo.GetEntities(attExps);

            // 查询待审批的防保科数据
            var pendingEmployeeIds = attDetailEntites.Where(p => p.EnumStatus == AttDayOffRecordDetailStatus.Pending).Select(p => p.EmployeeId).Distinct().ToList();
            var prophylacticFillings = this.Repo.GetEntities<AttDayOffRecordFilling>(p => p.EnumType == AttDayOffRecordFillingType.Prophylactic
                        && pendingEmployeeIds.Contains(p.EmployeeId) && p.RecordMonth == filter.RecordMonth);

            empEntities.ForEach(e =>
            {
                var att = attDetailEntites.FirstOrDefault(d => d.EmployeeId == e.ID);
                var attQuery = new AttDayOffRecordFillingQuery();
                if (att != null)
                {
                    var attFill = new AttDayOffRecordFilling()
                    {
                        EmployeeId = e.ID,
                        Employee = e,
                        H1 = att.H1,
                        H2 = att.H2,
                        H3 = att.H3,
                        H4 = att.H4,
                        H5 = att.H5,
                        H6 = att.H6,
                        H7 = att.H7,
                        H8 = att.H8,
                        H9 = att.H9,
                        H10 = att.H10,
                        H11 = att.H11,
                        H12 = att.H12,
                        HistoryH12 = att.HistoryH12,
                        Updator = att.Updator
                    };

                    attQuery = attFill.Map<AttDayOffRecordFillingQuery>();
                }
                else
                {
                    var attFill = new AttDayOffRecordFilling()
                    {
                        EmployeeId = e.ID,
                        Employee = e
                    };

                    attQuery = attFill.Map<AttDayOffRecordFillingQuery>();
                }

                if (att != null && att.EnumStatus == AttDayOffRecordDetailStatus.Pending)
                {
                    attQuery.AttDayOffRecordDetailStatus = AttDayOffRecordDetailStatus.Pending;

                    var prophylacticFilling = prophylacticFillings.FirstOrDefault(p => p.EmployeeId == e.ID);
                    if (prophylacticFilling != null)
                    {
                        attQuery.ProphylacticFilling = prophylacticFilling.Map<AttDayOffRecordFillingQuery>();
                    }
                }
                models.Add(attQuery);
            });

            return models;
        }

        /// <summary>
        /// 获取部门员工
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="recoredCount"></param>
        /// <returns></returns>
        private List<Employee> GetAttDayOffRecordEmployee(AttDayOffRecordFillingFilter filter, out int recoredCount)
        {
            DateTime dt = filter.RecordMonth;
            DateTime dt2 = dt.AddMonths(1);
            var empExps = this.NewExps<Entities.Employee>();
            if (!filter.DeptId.IsEmpty())
            {
                Guid deptId = filter.DeptId!.Value;
                var deptIds = this.Repo.GetDeptByCurrentUser(deptId);
                if (deptIds.IndexOf(deptId) >= 0)
                {
                    empExps.And(p => p.DeptId == deptId);
                }
                else
                {
                    empExps.And(p => false);
                }
            }
            else
            {
                empExps.And(p => false);
            }

            var hiredId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Hired)?.ID;
            var leaveId = this.Repo.GetDict(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode, Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Leave)?.ID;
            empExps.And(p => p.EmployeeHR != null && (p.EmployeeHR.EmpStatusId == hiredId
                || p.EmployeeHR.EmpStatusId == leaveId && p.EmployeeHR.LeaveDate.HasValue &&
                (p.EmployeeHR.LeaveDate.Value > dt && p.EmployeeHR.LeaveDate.Value <= dt2 || p.EmployeeHR.LeaveDate.Value > dt2)
                || p.EmployeeHR.EmpStatusId == leaveId && p.EmployeeHR.RetireDate.HasValue &&
                (p.EmployeeHR.RetireDate.Value > dt && p.EmployeeHR.RetireDate.Value <= dt2 || p.EmployeeHR.RetireDate.Value > dt2)));
            //int recoredCount = -1;

            if (filter.Order.IsEmpty())
            {
                filter.Order = "-EmpCode";
            }

            return this.Repo.GetEntities(empExps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize);
        }

        #endregion 考勤员填报

        #region 人事 -- 考勤数据相关

        /// <summary>
        /// 人事查询考勤数据
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "人事查询考勤数据")]
        [Permission(Permissions.AttendanceManage.AttendanceHR)]
        public QueryResult<AttDayOffRecordFillingQuery> QueryPersonnelAttendanceData([FromQuery] AttDayOffRecordFillingFilter filter)
        {
            var models = new List<AttDayOffRecordFillingQuery>();

            int recoredCount = -1;
            var empEntities = GetAttDayOffRecordEmployee(filter, out recoredCount);

            if (empEntities.Any())
            {
                // 只查询已经到正式明细表的数据，如果有审批数据，则给出差异
                models = this.GetCheckAttDayOffRecordDetail(filter, empEntities);
            }

            //上月卫生津贴
            if (!filter.RecordMonth.IsEmpty() && filter.DeptId.HasValue)
            {
                var preMonth = filter.RecordMonth.AddMonths(-1).ToString("yyyy-MM");
                var preAttDayOffRecordDetails = this.Repo.GetEntities<AttDayOffRecordDetail>(p => p.AttDayOffRecord != null && p.AttDayOffRecord.RecordMonth == preMonth && p.AttDayOffRecord.DeptId == filter.DeptId
                                                                                && p.EnumStatus != AttDayOffRecordDetailStatus.None && p.EnumStatus != AttDayOffRecordDetailStatus.Pending);

                foreach (var preAttDayOffRecordDetail in preAttDayOffRecordDetails)
                {
                    var model = models.FirstOrDefault(m => m.EmployeeId == preAttDayOffRecordDetail.EmployeeId);
                    if (model != null)
                    {
                        model.PreMonthH1 = preAttDayOffRecordDetail.H1;
                        // 首次查询数据时默认卫生津贴为上月数据
                        //if (model.ID.IsEmpty())
                        //{
                        //	model.H1 = model.PreMonthH1;
                        //}
                    }
                }
            }

            var attDetails = this.QueryResult(models, recoredCount, filter);
            return attDetails;
        }

        /// <summary>
        /// 人事待审批考勤数据 --- 待审批
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "人事待审批考勤数据")]
        [Permission(Permissions.AttendanceManage.AttendanceHRApproval)]
        public QueryResult<AttDayOffRecordDetailQuery> QueryPersonnelPendingApproval([FromQuery] AttDayOffRecordFillingFilter filter)
        {
            int recoredCount = -1;

            var attExps = this.NewExps<AttDayOffRecordDetail>(p => p.EnumStatus == AttDayOffRecordDetailStatus.Pending);
            if (!filter.RecordMonth.IsEmpty())
            {
                var strRecordMonth = filter.RecordMonth.ToString("yyyy-MM");
                attExps.And(p => p.AttDayOffRecord != null && p.AttDayOffRecord.RecordMonth == strRecordMonth);
            }
            else
            {
                attExps.And(p => false);
            }

            if (filter.DeptId.HasValue)
            {
                attExps.And(p => p.AttDayOffRecord != null && p.AttDayOffRecord.DeptId == filter.DeptId.Value);
            }

            if (!filter.EmpName.IsEmpty())
            {
                attExps.And(p => p.Employee != null && p.Employee.DisplayName.Contains(filter.EmpName!));
            }

            var attDetailEntites = this.Repo.GetEntities<AttDayOffRecordDetail>("Employee", attExps);

            recoredCount = attDetailEntites.Count();

            // 查询待审批的防保科数据
            var pendingEmployeeIds = attDetailEntites.Select(p => p.EmployeeId).Distinct().ToList();
            var prophylacticFillings = this.Repo.GetEntities<AttDayOffRecordFilling>(p => p.EnumType == AttDayOffRecordFillingType.Prophylactic
                        && pendingEmployeeIds.Contains(p.EmployeeId) && p.RecordMonth == filter.RecordMonth);

            var models = new List<AttDayOffRecordDetailQuery>();
            foreach (var attDetailEntite in attDetailEntites)
            {
                var model = new AttDayOffRecordDetail()
                {
                    ID = attDetailEntite.ID,
                    EmployeeId = attDetailEntite.EmployeeId,
                    Employee = attDetailEntite.Employee,
                    H1 = attDetailEntite.H1,
                }.Map<AttDayOffRecordDetailQuery>();

                var prophylacticFilling = prophylacticFillings.FirstOrDefault(p => p.EmployeeId == attDetailEntite.EmployeeId);
                if (prophylacticFilling != null)
                {
                    model.ProphylacticFilling = prophylacticFilling.Map<AttDayOffRecordFillingQuery>();
                }

                model.CheckFilling = new AttDayOffRecordFillingQuery()
                {
                    H1 = attDetailEntite.H1,
                    H2 = attDetailEntite.H2,
                    H3 = attDetailEntite.H3,
                    H4 = attDetailEntite.H4,
                    H5 = attDetailEntite.H5,
                    H6 = attDetailEntite.H6,
                    H7 = attDetailEntite.H7,
                    H8 = attDetailEntite.H8,
                    H9 = attDetailEntite.H9,
                    H10 = attDetailEntite.H10,
                    H11 = attDetailEntite.H11,
                    H12 = attDetailEntite.H12,
                    HistoryH12 = attDetailEntite.HistoryH12,
                    Updator = attDetailEntite.Updator
                };

                models.Add(model);
            }

            //上月卫生津贴
            if (!filter.RecordMonth.IsEmpty() && filter.DeptId.HasValue)
            {
                var preMonth = filter.RecordMonth.AddMonths(-1).ToString("yyyy-MM");
                var preAttDayOffRecordDetails = this.Repo.GetEntities<AttDayOffRecordDetail>(p => p.AttDayOffRecord != null && p.AttDayOffRecord.RecordMonth == preMonth && pendingEmployeeIds.Contains(p.EmployeeId)
                                                                                && p.EnumStatus != AttDayOffRecordDetailStatus.None && p.EnumStatus != AttDayOffRecordDetailStatus.Pending);

                foreach (var preAttDayOffRecordDetail in preAttDayOffRecordDetails)
                {
                    var model = models.FirstOrDefault(m => m.EmployeeId == preAttDayOffRecordDetail.EmployeeId);
                    if (model != null)
                    {
                        model.PreMonthH1 = preAttDayOffRecordDetail.H1;
                    }
                }
            }

            models = models.OrderBy(p => p.EmpCode).ToList();

            var attDetails = this.QueryResult(models, recoredCount, filter);
            return attDetails;
        }

        /// <summary>
        /// 人事审批考勤数据  --    审批
        /// </summary>
        /// <param name="recordModel"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "人事审批考勤数据")]
        [Permission(Permissions.AttendanceManage.AttendanceHRApproval)]
        public BizResult ApproveAttDayOffRecord([FromBody] AttDayOffRecordModel recordModel)
        {
            var details = recordModel.Details ?? new List<AttDayOffRecordDetailModel>();
            return this.Repo.ApproveAttDayOffRecord(details.Maps<AttDayOffRecordDetail>());
        }

        #endregion 人事 -- 考勤数据相关

        #region 员工薪资

        /// <summary>
        /// 获取同部门员工卫生津贴信息
        /// </summary>
        /// <param name="deptId">部门ID</param>
        /// <returns>同部门员工卫生津贴信息</returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取同部门员工卫生津贴信息")]
        public BizResult<List<AttDayOffRecordDetailQuery>> GetSameDeptEmployeeWithHealthAllowance([FromQuery] Guid deptId)
        {
            var result = new BizResult<List<AttDayOffRecordDetailQuery>>();

            var deptIds = this.Repo.GetDeptByCurrentUser(deptId);

            // 获取同部门的员工IDs
            var employees = this.Repo.GetEntities<Employee>(p => deptIds.Contains(p.DeptId.Value));
            var employeeIds = employees.Select(e => e.ID).ToList();

            var attDayOffRecordDetail = this.Repo.GetEntity<AttDayOffRecordDetail>(p=> 
                p.AttDayOffRecord != null && 
                p.EnumStatus != AttDayOffRecordDetailStatus.None && 
                p.EnumStatus != AttDayOffRecordDetailStatus.Pending &&
                p.H1.HasValue && p.H1.Value > 0 &&
                p.EmployeeId.HasValue && employeeIds.Contains(p.EmployeeId.Value)
                );

            // 获取考勤记录详情
            var attDetailExps = this.NewExps<AttDayOffRecordDetail>(); 
            attDetailExps.And(p => p.AttDayOffRecord != null && p.EnumStatus != AttDayOffRecordDetailStatus.None && p.EnumStatus != AttDayOffRecordDetailStatus.Pending);
            attDetailExps.And(p => p.EmployeeId.HasValue && employeeIds.Contains(p.EmployeeId.Value));
            if (attDayOffRecordDetail != null)
            {
                attDetailExps.And(p => p.H1.HasValue && p.H1.Value > 0 && p.RecordMonth == attDayOffRecordDetail.RecordMonth);
            }

            var details = this.Repo.GetEntities(attDetailExps);

            // 创建返回模型
            var resultData = new List<AttDayOffRecordDetailQuery>();
            foreach (var detail in details)
            {
                if (detail.EmployeeId == null || detail.Employee == null || !detail.H1.HasValue) continue;

                resultData.Add(new AttDayOffRecordDetailQuery
                {
                    EmployeeId = detail.EmployeeId.Value,
                    EmpDept = detail.Employee.DisplayName,
                    EmpName = detail.Employee.DisplayName,
                    EmpCode = detail.Employee.EmpCode,
                    EmpUid = detail.Employee.Uid.ToString(),
                    H1 = detail.H1.Value
                });
            }

            result.Data = resultData;
            return result;
        }
        
        #endregion
    }
}