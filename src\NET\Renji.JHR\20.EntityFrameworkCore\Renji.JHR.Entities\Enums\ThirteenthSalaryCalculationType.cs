using System.ComponentModel;

namespace Renji.JHR.Entities
{
    /// <summary>
    /// 十三薪计算类型
    /// </summary>
    public enum ThirteenthSalaryCalculationType
    {
        /// <summary>
        /// 无
        /// </summary>
        [Description("")]
        None = 0,

        /// <summary>
        /// 全额
        /// </summary>
        [Description("全额")]
        Full = 1,

        /// <summary>
        /// 半额
        /// </summary>
        [Description("半额")]
        Half = 2,

        /// <summary>
        /// 无
        /// </summary>
        [Description("无")]
        NotPay = 3,

        /// <summary>
        /// 暂不发放
        /// </summary>
        [Description("暂不发放")]
        Suspend = 4
    }
}
