﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Renji.JHR.Entities
{
    public enum EmployeeSalaryStatus
    {
        [Description("")]
        None = 0,

        /// <summary>
        /// 正常
        /// </summary>
        [Description("正常")]
        Normal = 10,

        /// <summary>
        /// 出国停发工资
        /// </summary>
        [Description("出国停发工资")]
        SuspendSalary = 15,

        /// <summary>
        /// 公派出国
        /// </summary>
        [Description("公派出国")]
        OfficialAbroad = 16,

        /// <summary>
        /// 停工资（不计算）
        /// </summary>
        [Description("停工资（不计算）")]
        SalaryStopped = 17,

        /// <summary>
        /// 长病假
        /// </summary>
        [Description("长病假")]
        LongSickLeave = 18,

        /// <summary>
        /// 终止
        /// </summary>
        [Description("终止")]
        Termination = 20,
    }
}
