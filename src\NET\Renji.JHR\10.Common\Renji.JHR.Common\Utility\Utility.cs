﻿using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.Common;
using System.Reflection;

namespace Renji.JHR.Common.Utility
{
    public static class Utility
    {
        /// <summary>
        /// 获取格式化日期,yyyy-MM-dd
        /// </summary>
        /// <param name="obj">日期</param>
        /// <returns>格式化后的日期</returns>
        public static string FormatDate(this DateTime? obj)
        {
            if (obj == null)
            {
                return string.Empty;
            }
            else
            {
                return Convert.ToDateTime(obj).ToString("yyyy-MM-dd");
            }
        }

        public static string FormatDate(this DateTime obj)
        {
            return Convert.ToDateTime(obj).ToString("yyyy-MM-dd");
        }

        /**/

        /// <summary>
        /// 将泛型集合类转换成DataTable
        /// </summary>
        /// <typeparam name="T">集合项类型</typeparam>
        /// <param name="list">集合</param>
        /// <returns>数据集(表)</returns>
        public static DataTable? ToDataTable<T>(List<T>? list)
        {
            return ListToDataTable<T>(list);
        }

        /// <summary>
        /// 将泛类型集合List类转换成DataTable
        /// </summary>
        /// <param name="entitys">泛类型集合</param>
        /// <returns></returns>
        public static DataTable? ListToDataTable<T>(List<T>? entitys)
        {
            //检查实体集合不能为空
            if (entitys == null || entitys.Count < 1)
            {
                return null;
                //throw new Exception("需转换的集合为空");
            }
            //取出第一个实体的所有Propertie
            //Type entityType = entitys[0].GetType();
            PropertyInfo[] entityProperties = entitys[0]!.GetType().GetProperties();

            //生成DataTable的structure
            //生产代码中，应将生成的DataTable结构Cache起来，此处略
            var dt = new DataTable("temp");
            for (int i = 0; i < entityProperties.Length; i++)
            {
                var desc = entityProperties[i].GetCustomAttribute<System.ComponentModel.DescriptionAttribute>();
                //dt.Columns.Add(entityProperties[i].Name, entityProperties[i].PropertyType);
                var col = new DataColumn(entityProperties[i].Name)
                {
                    Caption = desc?.Description
                };
                dt.Columns.Add(col);
            }
            //将所有entity添加到DataTable中
            foreach (var entity in entitys)
            {
                //检查所有的的实体都为同一类型
                //if (entity.GetType() != entityType)
                //{
                //    throw new Exception("要转换的集合元素类型不一致");
                //}
                object?[] entityValues = new object?[entityProperties.Length];
                for (int i = 0; i < entityProperties.Length; i++)
                {
                    entityValues[i] = entityProperties[i].GetValue(entity, null);
                }
                dt.Rows.Add(entityValues);
            }
            return dt;
        }

        public static string FormatDateTime(this DateTime? obj)
        {
            if (obj == null)
            {
                return string.Empty;
            }
            else
            {
                return Convert.ToDateTime(obj).ToString("yyyy-MM-dd HH:mm:ss");
            }
        }

        public static string FormatDateTime(this DateTime obj)
        {
            return Convert.ToDateTime(obj).ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// DataTable转动态List
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public static List<object> ConvertToList(DataTable data)
        {
            var objectList = new List<object>();
            foreach (DataRow row in data.Rows)
            {
                IDictionary<string, object?> dataItemDic = new System.Dynamic.ExpandoObject();
                //	var dataItemDic = dataItem as IDictionary<string, object>;
                foreach (DataColumn col in data.Columns)
                {
                    var name = col.ColumnName;
                    var val = row[name].ToString();
                    dataItemDic[name] = val;
                }
                objectList.Add(dataItemDic);
            }

            return objectList;
        }

        /// <summary>
        /// DataReader转动态List
        /// </summary>
        /// <param name="dr"></param>
        /// <returns></returns>
        public static List<object> ConvertDataReaderToList(DbDataReader dr)
        {
            var objectList = new List<object>();

            while (dr.Read())
            {
                IDictionary<string, object?> dataItemDic = new System.Dynamic.ExpandoObject();
                for (int i = 0; i < dr.FieldCount; i++)
                {
                    var name = dr.GetName(i);
                    var val = dr.GetValue(i);
                    dataItemDic[name] = val;
                }
                objectList.Add(dataItemDic);
            }

            dr.Close();
            dr.Dispose();
            return objectList;
        }

        public static string GetDescription(this Enum enumValue)
        {
            FieldInfo? field = enumValue.GetType().GetField(enumValue.ToString());
            if (field != null)
            {
                Attribute? attribute = Attribute.GetCustomAttribute(field, typeof(Attribute));
                return attribute == null ? enumValue.ToString() : ((DescriptionAttribute)attribute).Description;
            }
            return enumValue.ToString();
        }
    }
}