﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;
using System.Text.Json.Serialization;
using System.Xml.Serialization;

namespace Renji.JHR.Entities
{
    public partial class Role
    {
        /// <summary>
        /// 删除时是否同时删除角色成员
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public bool ConfirmToDelete { get; set; }
    }
}
