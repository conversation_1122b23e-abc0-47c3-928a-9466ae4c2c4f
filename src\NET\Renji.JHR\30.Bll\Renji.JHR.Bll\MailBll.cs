﻿using Renji.JHR.Common.Configration;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.Hosting;
using Shinsoft.Core.Mail;
using Shinsoft.Core.Tasks;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Renji.JHR.Bll
{
    public class MailBll : BaseMailBll
    {
        #region Constructs

        public MailBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public MailBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public MailBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public MailBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        #region Mail

        private static readonly object sendMailLocker = new object();

        public virtual void CreateMail(string templateCode, string to, IDictionary<string, string> datas, bool isLeader = false
                , string objectType = "", string objectName = "", Guid? objectId = null, string trigger = ""
            , bool save = true)
        {
            var mailTemplate = this.MailRepo.GetEntity<MailTemplate>(p => p.Code == templateCode);

            // 添加邮件
            if (mailTemplate != null && !to.IsEmpty())
            {
                var mail = mailTemplate.ToMail(datas);

                mail.To = to;

                mail.Trigger = trigger;
                mail.ObjectType = objectType;
                mail.ObjectName = objectName;
                mail.ObjectId = objectId;

                mail.EnumStatus = MailStatus.Ready;

                var fileName = isLeader ? Config.LeaderFileName : Config.EmpFileName;
                var file = this.FileRepo.GetEntity<FileIndex>(p => p.FileName == fileName);
                var attachments = new List<MailAttachment>();

                if (file != null)
                {
                    var attachment = new MailAttachment()
                    {
                        ID = CombGuid.NewGuid(),
                        ContentType = file.ContentType,
                        FileName = file.FileName,
                        FileSize = file.FileSize,
                        FileExt = file.FileExt,
                        FileIndexId = file.ID
                    };
                    attachments.Add(attachment);
                }

                mail.Attachments = attachments;

                this.AddMail(mail);
            }
        }

        public BizResult<Mail> AddMail(Mail mail)
        {
            var result = new BizResult<Mail>();

            if (mail.ID.IsEmpty())
            {
                mail.ID = Guid.NewGuid();
            }

            var mailAttachments = new List<MailAttachment>();

            if (mail.Attachments?.Any() == true)
            {
                var fileIndexIds = mail.Attachments.Select(p => p.FileIndexId).ToList();
                var fileIndexs = this.FileRepo.GetEntities<FileIndex>(p => fileIndexIds.Contains(p.ID));
                if (fileIndexs.Any(p => !p.BaseFolder.IsEmpty() && !p.SubPath.IsEmpty()))
                {
                    mail.AttachmentPaths = String.Join(",", fileIndexs.Where(p => !p.BaseFolder.IsEmpty() && !p.SubPath.IsEmpty()).Select(p => p.BaseFolder + p.SubPath));
                }

                foreach (var mailAttachment in mail.Attachments)
                {
                    if (mailAttachment.ID.IsEmpty())
                    {
                        mailAttachment.ID = Guid.NewGuid();
                    }
                    mailAttachment.MailId = mail.ID;

                    var fileIndex = fileIndexs.FirstOrDefault(p => p.ID == mailAttachment.FileIndexId);

                    if (fileIndex == null)
                    {
                        result.Error($"找不到邮件附件[{mailAttachment.FileName}]");
                    }
                    else
                    {
                        mailAttachment.FileStream = this.FileBll.GetFileStream(fileIndex);
                    }

                    mailAttachments.Add(this.Add(mailAttachment, false));
                }
            }

            mail = this.Add(mail, false);

            this.SaveChanges();

            var server = this.MailRepo.GetEntity<MailServer>(MailServer.Inverses.SmtpServer, p => p.Valid);
            if (server != null)
            {
                server.SendMailByHsbtSmtp(mail, mailAttachments);
                this.SaveChanges();
            }

            return result;
        }

        #endregion Mail
    }
}