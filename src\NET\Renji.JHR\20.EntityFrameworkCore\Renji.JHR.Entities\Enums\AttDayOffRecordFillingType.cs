﻿using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Renji.JHR.Entities
{
    /// <summary>
    /// 填报数据类型
    /// </summary>
    public enum AttDayOffRecordFillingType
    {
        None = 0,

        /// <summary>
        /// 考勤填报
        /// </summary>
        [Description("考勤填报")]
        Check = 1,

        /// <summary>
        /// 防保科填报
        /// </summary>
        [Description("防保科填报")]
        Prophylactic = 2,

        /// <summary>
        /// 审批确认
        /// </summary>
        [Description("审批确认")]
        Apprved = 3,
    }
}
