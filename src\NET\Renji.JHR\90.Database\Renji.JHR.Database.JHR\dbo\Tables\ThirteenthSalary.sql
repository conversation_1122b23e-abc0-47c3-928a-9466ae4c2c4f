﻿CREATE TABLE [dbo].[ThirteenthSalary]
(
	[ID] UNIQUEIDENTIFIER NOT NULL,
    [SalaryId] UNIQUEIDENTIFIER NOT NULL,
    [EmployeeId] UNIQUEIDENTIFIER NOT NULL,
    [EnumThirteenthSalaryCalculationType] INT NOT NULL,
    [EnumThirteenthSalaryEmployeeStatus] INT NOT NULL,
    [CalculateAmount] DECIMAL(18, 4) NULL,
    [ActualAmount] DECIMAL(18, 4) NULL,
    [CalculateRemark] NVARCHAR(1000) NULL,
    [Remark] NVARCHAR(500) NULL,

    [Deleted] BIT NOT NULL ,
    [Creator] NVARCHAR(50) NULL,
    [CreateTime] DATETIME NULL,
    [LastEditor] NVARCHAR(50) NULL,
    [LastEditTime] DATETIME NULL,
    CONSTRAINT [PK_ThirteenthSalary] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_ThirteenthSalary_Salary] FOREIGN KEY ([SalaryId]) REFERENCES [dbo].[Salary] ([ID]),
    CONSTRAINT [FK_ThirteenthSalary_Employee] FOREIGN KEY ([EmployeeId]) REFERENCES [dbo].[Employee] ([ID])
)

GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'十三薪',
    @level0type = N'SCHEMA',
    @level0name = N'dbo', 
    @level1type = N'TABLE', 
    @level1name = N'ThirteenthSalary';
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'薪资ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ThirteenthSalary',
    @level2type = N'COLUMN',
    @level2name = N'SalaryId';
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'员工ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ThirteenthSalary',
    @level2type = N'COLUMN',
    @level2name = N'EmployeeId';
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'计算类型：1-全额，2-半额，3-不发，4-暂不发放',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ThirteenthSalary',
    @level2type = N'COLUMN',
    @level2name = N'EnumThirteenthSalaryCalculationType';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'计算金额',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ThirteenthSalary',
    @level2type = N'COLUMN',
    @level2name = N'CalculateAmount';
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'实际金额',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ThirteenthSalary',
    @level2type = N'COLUMN',
    @level2name = N'ActualAmount';
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'计算说明',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ThirteenthSalary',
    @level2type = N'COLUMN',
    @level2name = N'CalculateRemark';
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'员工状态：1-正常，2-退休，3-年薪制，4-有处分，5-离岗培训，6-考核有问题，7-因公出国',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ThirteenthSalary',
    @level2type = N'COLUMN',
    @level2name = N'EnumThirteenthSalaryEmployeeStatus';
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'备注',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ThirteenthSalary',
    @level2type = N'COLUMN',
    @level2name = N'Remark';
GO

