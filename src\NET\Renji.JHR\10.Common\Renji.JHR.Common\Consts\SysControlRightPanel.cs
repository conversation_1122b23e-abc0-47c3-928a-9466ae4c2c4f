﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Renji.JHR.Common
{
    /// <summary>
    /// 人事分页 和 人事信息按钮
    /// </summary>
    public static class SysControlRightPanel
    {
        /// <summary>
        /// 2
        /// </summary>
        public static class Button
        {
            /// <summary>
            /// 查询设置按钮
            /// </summary>
            public const string Code_Query = "Query";

            /// <summary>
            /// 添加按钮
            /// </summary>
            public const string Code_Add = "Add";

            /// <summary>
            /// 删除按钮
            /// </summary>
            public const string Code_Delete = "Delete";

            /// <summary>
            /// 工龄更新
            /// </summary>
            public const string Code_SocAge = "SocAge";

            /// <summary>
            /// 工休更新
            /// </summary>
            public const string Code_GeneralHoliday = "GeneralHoliday";
        }

        /// <summary>
        /// 0
        /// </summary>
        public static class PaginationNavigation
        {

        }
       
    }
}
