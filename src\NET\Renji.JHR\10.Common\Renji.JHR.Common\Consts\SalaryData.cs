﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Renji.JHR.Common.Consts
{
    public static class SalaryData
    {
        /// <summary>
        /// 一值班二值班费
        /// </summary>
        public const string DutyAllowance = "DutyAllowance";

        /// <summary>
        /// 上下班交通费
        /// </summary>
        public const string CommutingAllowance = "CommutingAllowance";

        /// <summary>
        /// 中夜班费24小时班
        /// </summary>
        public const string NightShiftAllowance24Hours = "NightShiftAllowance24Hours";

        /// <summary>
        /// 中夜班费中班
        /// </summary>
        public const string NightShiftAllowanceMidShift = "NightShiftAllowanceMidShift";

        /// <summary>
        /// 中夜班费夜班
        /// </summary>
        public const string NightShiftAllowanceNightShift = "NightShiftAllowanceNightShift";

        /// <summary>
        /// 中夜班费急诊24小时班
        /// </summary>
        public const string NightShiftAllowanceEmergency24Hours = "NightShiftAllowanceEmergency24Hours";

        /// <summary>
        /// 中夜班费急诊中班
        /// </summary>
        public const string NightShiftAllowanceEmergencyMidShift = "NightShiftAllowanceEmergencyMidShift";

        /// <summary>
        /// 中夜班费急诊夜班
        /// </summary>
        public const string NightShiftAllowanceEmergencyNightShift = "NightShiftAllowanceEmergencyNightShift";

        /// <summary>
        /// 中夜班费护理A档24小时班
        /// </summary>
        public const string NightShiftAllowanceNursingGradeA24Hours = "NightShiftAllowanceNursingGradeA24Hours";

        /// <summary>
        /// 中夜班费护理A档中班
        /// </summary>
        public const string NightShiftAllowanceNursingGradeAMidShift = "NightShiftAllowanceNursingGradeAMidShift";

        /// <summary>
        /// 中夜班费护理A档夜班
        /// </summary>
        public const string NightShiftAllowanceNursingGradeANightShift = "NightShiftAllowanceNursingGradeANightShift";

        /// <summary>
        /// 会费比例
        /// </summary>
        public const string MembershipFeePercentage = "MembershipFeePercentage";

        /// <summary>
        /// 停车补贴
        /// </summary>
        public const string ParkingSubsidy = "ParkingSubsidy";

        /// <summary>
        /// 公积金比例
        /// </summary>
        public const string HousingFundPercentage = "HousingFundPercentage";

        /// <summary>
        /// 养老保险比例
        /// </summary>
        public const string PensionInsurancePercentage = "PensionInsurancePercentage";

        /// <summary>
        /// 医疗保险比例
        /// </summary>
        public const string MedicalInsurancePercentage = "MedicalInsurancePercentage";

        /// <summary>
        /// 南院行政值班费日班夜班
        /// </summary>
        public const string SouthCampusAdminDutyAllowanceDayNightShift = "SouthCampusAdminDutyAllowanceDayNightShift";

        /// <summary>
        /// 南院行政值班费节假日
        /// </summary>
        public const string SouthCampusAdminDutyAllowanceHolidays = "SouthCampusAdminDutyAllowanceHolidays";

        /// <summary>
        /// 博士后会费
        /// </summary>
        public const string PostdoctoralMembershipFee = "PostdoctoralMembershipFee";

        /// <summary>
        /// 博士后房贴
        /// </summary>
        public const string PostdoctoralHousingAllowance = "PostdoctoralHousingAllowance";

        /// <summary>
        /// 失业保险比例
        /// </summary>
        public const string UnemploymentInsurancePercentage = "UnemploymentInsurancePercentage";

        /// <summary>
        /// 日工资计算天数
        /// </summary>
        public const string DailyWageCalculationDays = "DailyWageCalculationDays";

        /// <summary>
        /// 最低工资
        /// </summary>
        public const string MinimumWage = "MinimumWage";

        /// <summary>
        /// 最低社保缴费基数
        /// </summary>
        public const string MinimumSocialSecurityPaymentBase = "MinimumSocialSecurityPaymentBase";

        /// <summary>
        /// 最高社保缴费基数
        /// </summary>
        public const string MaximumSocialSecurityPaymentBase = "MaximumSocialSecurityPaymentBase";

        /// <summary>
        /// 独生子女费
        /// </summary>
        public const string OneChildAllowance = "OneChildAllowance";

        /// <summary>
        /// 粮油补贴
        /// </summary>
        public const string FoodAllowance = "FoodAllowance";

        /// <summary>
        /// 职业年金比例
        /// </summary>
        public const string OccupationalAnnuity = "OccupationalAnnuity";

        /// <summary>
        /// 补充公积金比例
        /// </summary>
        public const string SupplementaryHousingFundPercentage = "SupplementaryHousingFundPercentage";
    }
}
