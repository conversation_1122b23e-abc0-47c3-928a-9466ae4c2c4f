﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore.Dm.Internal;
using NPOI.SS.Formula.Functions;
using Renji.JHR.Api.Models;
using Renji.JHR.Api.Utils;
using Renji.JHR.Bll;
using Renji.JHR.Common;
using Renji.JHR.Common.Configration;
using Renji.JHR.Common.Consts;
using Renji.JHR.Common.Utility;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.Hosting;
using Shinsoft.Core.Mvc;
using Shinsoft.Core.NLog;
using StackExchange.Redis;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;


namespace Renji.JHR.Api.Controllers
{
    /// <summary>
    /// 员工
    /// </summary>
    public class HRController : BaseApiController<HRBll>
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public HRController(IHttpClientFactory httpClientFactory, IHttpContextAccessor httpContextAccessor)
        {
            _httpClientFactory = httpClientFactory;
            _httpContextAccessor = httpContextAccessor;
        }

        #region Employee

        /// <summary>
        /// 查询员工-人员信息
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeQuery> QueryEmployee([FromQuery] EmployeeFilter filter)
        {
            var exps = this.NewExps<Entities.Employee>();
            if (filter.DeptId.HasValue)
            {
                if (true == filter.IsContainSubDept)
                {
                    var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                    exps.And(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));
                }
                else
                {
                    exps.And(p => p.DeptId == filter.DeptId.Value);
                }
            }

            //if (filter.EnumStatus.HasValue)
            //{
            //    exps.And(p => p.EnumStatus == (EmployeeStatus)filter.EnumStatus.Value);

            //    if (filter.HireStyleId.HasValue)
            //    {
            //        exps.And(p => p.EmployeeHR.HireStyleId == filter.HireStyleId.Value);
            //    }
            //    if (filter.LeaveStyleId.HasValue)
            //    {
            //        exps.And(p => p.EmployeeHR.LeaveStyleId == filter.LeaveStyleId.Value);
            //    }
            //}

            if (filter.EmpStatusId.HasValue)
            {
                exps.And(p => p.EmployeeHR != null && p.EmployeeHR.EmpStatusId == filter.EmpStatusId.Value);

                if (filter.HireStyleId.HasValue)
                {
                    exps.And(p => p.EmployeeHR != null && p.EmployeeHR.HireStyleId == filter.HireStyleId.Value);
                }
                if (filter.LeaveStyleId.HasValue)
                {
                    exps.And(p => p.EmployeeHR != null && p.EmployeeHR.LeaveStyleId == filter.LeaveStyleId.Value);
                }
            }

            if (!string.IsNullOrEmpty(filter.DisplayName))
            {
                exps.And(p => p.DisplayName.Contains(filter.DisplayName));
            }

            if (filter.OfficialRankId.HasValue)
            {
                exps.And(p => p.EmployeeHR != null && p.EmployeeHR.OfficialRankId == filter.OfficialRankId.Value);
            }

            if (filter.EmpUid != null)
            {
                exps.And(p => p.Uid.Equals( filter.EmpUid));
            }

            if (filter.Order.IsEmpty())
            {
                filter.Order = $"{nameof(Entities.Employee.EmpCode)} {GeneralConsts.OrderByAsc}";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 查询员工-人员部门调整
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "查询员工")]
        [Permission(Permissions.HRManage.EmpOrgModifier, Permissions.HRManage.LogisticsStaffManage, Permissions.HRManage.LogisticsStaffManage)]
        public QueryResult<EmployeeQuery> QueryEmployeeByCommonCondition([FromBody] EmployeeFilter filter)
        {
            var exps = this.NewExps<Entities.Employee>();
            if (filter.DeptId.HasValue)
            {
                if (true == filter.IsContainSubDept)
                {
                    var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                    exps.And(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));
                }
                else
                {
                    exps.And(p => p.DeptId == filter.DeptId.Value);
                }
            }

            if (filter.CommonConditionList != null && filter.CommonConditionList.Any())
            {
                var expression = new Common.DynamicQuery().GetDynamicQuery<Entities.Employee>(filter.CommonConditionList);
                exps.And(expression);
            }

            if (filter.Order.IsEmpty())
            {
                filter.Order = $"{nameof(Entities.Employee.EmpCode)} {GeneralConsts.OrderByAsc}";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 获取员工
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取员工")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeModel> GetEmployee([FromQuery, Required] Guid id)
        {
            var result = this.BizResult<EmployeeModel>();

            var entity = this.Repo.Get<Entities.Employee>(id);

            if (entity == null)
            {
                result.Error("员工不存在");
            }
            else
            {
                var attachment = this.Repo.GetEntity<Entities.Attachment>(p => p.ObjectType == JHR.Common.Consts.Employee.EmployeeTableName && p.ObjectId == entity.ID.ToString());
                if (attachment != null)
                {
                    entity.AttachmentId = attachment.ID;
                }
                var model = entity.Map<EmployeeModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增员工
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增员工")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeModel> AddEmployee([FromBody] EmployeeModel model)
        {
            var entity = model.Map<Entities.Employee>();
            //entity.EnumIdentityType = IdentityType.IdentityCard;
            entity.AttachmentId = model.AttachmentId;
            Guid historyId = Guid.Empty;

            var result = this.Repo.AddEmployee(entity, model.DeptPrincipalID, ref historyId);

            var emp = result.Map<EmployeeModel>();

            if (!historyId.IsEmpty())
            {
                //调接口
                this.GetDeptPrincipInformation(historyId);
            }
            return emp;
        }

        /// <summary>
        /// 更新员工
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新员工")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeModel> UpdateEmployee([FromBody] EmployeeModel model)
        {
            var entity = model.Map<Entities.Employee>();
            Guid historyId = Guid.Empty;

            var result = this.Repo.UpdateEmployee(entity, model.DeptPrincipalID, ref historyId);

            var emp = result.Map<EmployeeModel>();

            if (!historyId.IsEmpty())
            {
                //调接口
                this.GetDeptPrincipInformation(historyId);
            }

            return emp;
        }

        /// <summary>
        /// 删除员工
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除员工")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployee([FromBody] EmployeeModel model)
        {
            var entity = new Entities.Employee
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployee(entity);

            if (result.Data != null)
            {
                GetPersonnelInformation(result.Data.Uid, Renji.JHR.Common.Consts.Employee.EmployeeOAUpdate);
            }
            return new BizResult();
        }

        /// <summary>
        /// 验证员工码是否存在
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "验证员工码是否存在")]
        public BizResult<bool> IsEmpCodeExists([FromQuery] EmployeeModel model)
        {
            var result = this.BizResult<bool>();
            //是否在职
            //null hr  或 离职

            var hr = this.Repo.GetEmpHRByID(model.ID);
            if (hr == null && model.ID != Guid.Empty)
            {
                result.Data = false;
                return result;
            }

            var entity = new Entities.Employee
            {
                ID = model.ID,
                EmpCode = model.EmpCode
            };

            var isExists = this.Repo.IsEmpCodeExists(entity);

            result.Data = isExists;
            return result;
        }

        /// <summary>
        /// 验证身份证是否重复
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "验证身份证是否重复")]
        public BizResult<bool> CheckIdentityNumber([FromQuery] EmployeeModel model)
        {
            var result = this.BizResult<bool>();

            var entity = new Entities.Employee
            {
                ID = model.ID,
                IdentityNumber = model.IdentityNumber
            };

            var repeat = this.Repo.IdentityNumberRepeat(entity);
            result.Data = repeat;
            return result;
        }

        #endregion Employee

        #region 组合查询 人员部门调整,后勤人员管理,护理人员管理

        /// <summary>
        /// 组合查询员工
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "组合查询员工")]
        public QueryResult<ViewEmployeeInfoQuery> QueryEmployeeByCommonConditions([FromBody] EmployeeFilter filter)
        {
            var exps = this.NewExps<Entities.ViewEmployeeInfo>();

            if (filter.DeptId.HasValue)
            {
                if (true == filter.IsContainSubDept)
                {
                    var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                    exps.And(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));
                }
                else
                {
                    exps.And(p => p.DeptId == filter.DeptId.Value);
                }
            }
            else
            {
                bool fullcontrol = this.Repo.GetFullControlDeptByCurrentUser();
                if (!fullcontrol)
                {
                    var deptIds = this.Repo.GetRealDeptByCurrentUser().Select(c => c.ID).ToList();
                    exps.And(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));
                }
            }

            if (!string.IsNullOrEmpty(filter.ModulePart))
            {
                if (filter.ModulePart.Equals(Dicts.ModulePart_Logistics))
                {
                    exps.And(p => p.OfficialRankCode == Dicts.OfficialRankCode_Worker || p.OfficialRankCode == Dicts.OfficialRankCode_Other);
                }
                else if (filter.ModulePart.Equals(Dicts.ModulePart_Nursing))
                {
                    exps.And(p => p.OfficialRankCode == Dicts.OfficialRankCode_Nursing);
                }
            }

            if (filter.CommonConditionList != null && filter.CommonConditionList.Any())
            {
                var expression = new Common.DynamicQuery().GetDynamicQuery<Entities.ViewEmployeeInfo>(filter.CommonConditionList);
                exps.And(expression);
            }

            if (filter.Order.IsEmpty())
            {
                filter.Order = $"{nameof(ViewEmployeeInfo.EmpCode)} {GeneralConsts.OrderByAsc}";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<ViewEmployeeInfoQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        #endregion 组合查询 人员部门调整,后勤人员管理,护理人员管理

        #region 组合查询 人员信息

        /// <summary>
        /// 组合查询员工
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "组合查询员工")]
        public QueryResult<ViewEmployeeInfoQuery> QueryEmployeeByConditions([FromBody] EmployeeFilter filter)
        {
            var exps = this.NewExps<Entities.ViewEmployeeInfo>();

            if (filter.DeptId.HasValue)
            {
                if (true == filter.IsContainSubDept)
                {
                    var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                    exps.And(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));
                }
                else
                {
                    exps.And(p => p.DeptId == filter.DeptId.Value);
                }
            }
            else
            {
                var deptIds = this.Repo.GetRealDeptByCurrentUser().Select(c => c.ID).ToList();
                exps.And(p => deptIds.Contains(p.DeptId.Value));
            }

            if (filter.EmployeeQuerySettingId.HasValue)
            {
                var employeeQuerySettingItems = this.Repo.GetEntities<EmployeeQuerySettingItem>(p => p.EmployeeQuerySettingId == filter.EmployeeQuerySettingId.Value && p.ParentId == null);

                var itemModels = employeeQuerySettingItems.Map<List<EmployeeQuerySettingItemModel>>();

                List<Common.QueryCondition> queryConditions = EmployeeQuerySettingItemModel.ToQueryCondition(itemModels);

                var expression = new Common.DynamicQuery().GetDynamicQuery<Entities.ViewEmployeeInfo>(queryConditions);
                exps.And(expression);
            }
            else
            {
                if (filter.ConditionList != null && filter.ConditionList.Any())
                {
                    List<Common.QueryCondition> queryConditions = EmployeeQuerySettingItemModel.ToQueryCondition(filter.ConditionList);
                    var expression = new Common.DynamicQuery().GetDynamicQuery<Entities.ViewEmployeeInfo>(queryConditions);
                    exps.And(expression);
                }
            }
            if (filter.Order.IsEmpty())
            {
                filter.Order = $"{nameof(ViewEmployeeInfo.EmpCode)} {GeneralConsts.OrderByAsc}";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<ViewEmployeeInfoQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 查询员工字段列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工字段列表")]
        public QueryResult<QuerySettingColumnQuery> QueryEmployeeInfoQuerySettingColumns()
        {
            var entities = this.Repo.QuerySettingColumnsByModuleName(Common.Consts.EmployeeHR.EmployeeInfoQuery);
            var models = entities.Maps<QuerySettingColumnQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询员工字段列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工字段列表")]
        public QueryResult<DataTypeOperationQuery> QueryOperationByColumnType([FromQuery, Required] string columnType)
        {
            var entities = this.Repo.QueryOperationByColumnType(columnType);
            var models = entities.Maps<DataTypeOperationQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 新增员工组合查询
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增员工组合查询")]
        public BizResult<EmployeeQuerySettingModel> AddEmployeeQuerySetting([FromBody] EmployeeQuerySettingModel model)
        {
            var entity = model.Map<Entities.EmployeeQuerySetting>();
            entity.ModuleCode = Common.Consts.EmployeeHR.EmployeeInfoQuery;
            entity.UserId = this.CurrentUser?.ID ?? Guid.Empty;
            var result = this.Repo.AddEmployeeQuerySetting(entity);

            return result.Map<EmployeeQuerySettingModel>();
        }

        /// <summary>
        /// 查询员工组合查询列表
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工组合查询列表")]
        public QueryResult<EmployeeQuerySettingQuery> QueryEmployeeQuerySetting([FromQuery] EmployeeQuerySettingFilter filter)
        {
            var exps = this.NewExps<Entities.EmployeeQuerySetting>();

            var userId = this.CurrentUser?.ID;
            exps.And(p => p.UserId == userId);
            exps.And(p => p.ModuleCode == Common.Consts.EmployeeHR.EmployeeInfoQuery);

            if (filter.Order.IsEmpty())
            {
                filter.Order = "CreateTime desc";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeQuerySettingQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 删除员工组合查询设置
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除员工组合查询设置")]
        public BizResult DeleteEmployeeQuerySetting([FromBody] EmployeeQuerySettingModel model)
        {
            var entity = new Entities.EmployeeQuerySetting
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeQuerySetting(entity);

            return result;
        }

        #endregion 组合查询 人员信息

        #region EmployeeHR

        /// <summary>
        /// 获取员工人事信息
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取员工人事信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeHRModel> GetEmployeeHR([FromQuery, Required] Guid id)
        {
            var result = this.BizResult<EmployeeHRModel>();

            var entity = this.Repo.Get<Entities.EmployeeHR>(id);
            if (entity != null)
            {
                var attachment = this.Repo.GetEntity<Entities.Attachment>(p =>
                     p.ObjectType == JHR.Common.Consts.EmployeeHR.EmployeeHRTableName &&
                     p.ObjectId == entity.ID.ToString());
                if (attachment != null)
                {
                    entity.AttachmentId = attachment.ID;
                    entity.AttachmentName = attachment.FileName;
                }

                var model = entity.Map<EmployeeHRModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 更新员工人事信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新员工人事信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeHRModel> UpdateEmployeeHR([FromBody] EmployeeHRModel model)
        {
            var emp = this.Repo.GetEmpByID(model.ID);
            //是否在职状态
            var empstatus = this.Repo.QueryDictByParentCode(Dicts.EmployeeStatusCode);
            var staff = empstatus.First(s => s.Code == Common.Consts.Dicts.EmpStatusStaffCode);
            if (model.EmpStatusId == staff.ID)
            {
                if (emp != null && this.Repo.IsEmpCodeExists(emp))
                {
                    var r = new BizResult<EmployeeHRModel>();
                    r.Error("已存在相同工号在职状态的人员");
                    return r;
                }
            }

            var entity = model.Map<Entities.EmployeeHR>();

            var isadd = 0;

            var result = this.Repo.SaveEmployeeHR(entity, ref isadd).Map<EmployeeHRModel>();

            var callresult = GetPersonnelInformation(emp?.Uid ?? 0, isadd.ToString());

            if (!callresult.Succeed)
            {
                result.Error(callresult.Messages[0]);
            }

            return result;
        }

        /// <summary>
        /// 工龄年初更新
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "工龄年初更新")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<string> UpdateCompanyAge()
        {
            this.Repo.UpdateCompanyAge();
            return new BizResult<string>();
        }

        /// <summary>
        /// 计算公休
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Save, Operate = "计算公休")]
        public BizResult CalculateGeneralHoliday()
        {
            var result = new BizResult();

            if (!this.Repo.CalculateGeneralHolidays())
            {
                result.Error($"当前年份已计算过公休");
            }

            return result;
        }
        /// <summary>
        /// 编辑政治面貌
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "编辑政治面貌")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeHRModel> EditEmployeeParty(EmployeeHRModel model)
        {
            var entity = model.Map<Entities.EmployeeHR>();

            var result = this.Repo.EditEmployeeParty(entity);

            return result.Map<EmployeeHRModel>();
        }

        #region 所在支部
        /// <summary>
        /// 查询所在支部
        /// </summary>
        [HttpGet]
        [Permission(Permissions.HRManage.StaffInfo)]
        [LogApi(ApiType.Query, Operate = "查询所在支部")]
        public BizResult<List<EmployeeHRAffiliatedBranchQuery>> QueryEmployeeHRAffiliatedBranch([FromQuery] Guid id)
        {
            var exps = this.NewExps<EmployeeHRAffiliatedBranch>(p => p.EmployeeHRId == id);
            var entities = this.Repo.GetEntities<EmployeeHRAffiliatedBranch>(p => p.EmployeeHRId == id);

            var models = entities.Maps<EmployeeHRAffiliatedBranchQuery>().OrderByDescending(p => p.TransferTime).ToList();

            return new BizResult<List<EmployeeHRAffiliatedBranchQuery>>(models);
        }

        /// <summary>
        /// 获取所在支部
        /// </summary>
        [HttpGet]
        [Permission(Permissions.HRManage.StaffInfo)]
        [LogApi(ApiType.Query, Operate = "获取所在支部")]
        public BizResult<EmployeeHRAffiliatedBranchModel> GetEmployeeHRAffiliatedBranch([FromQuery] Guid id)
        {
            var result = new BizResult<EmployeeHRAffiliatedBranchModel>();

            var entity = this.Repo.Get<EmployeeHRAffiliatedBranch>(id);

            if (entity == null)
            {
                result.Error("所在支部不存在");
            }
            else
            {
                var model = entity.Map<EmployeeHRAffiliatedBranchModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增所在支部
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Permission(Permissions.HRManage.StaffInfo)]
        [LogApi(ApiType.Save, Operate = "新增所在支部")]
        public BizResult<EmployeeHRAffiliatedBranchModel> AddEmployeeHRAffiliatedBranch(EmployeeHRAffiliatedBranchModel model)
        {
            var entity = model.Map<EmployeeHRAffiliatedBranch>();

            var result = this.Repo.AddEmployeeHRAffiliatedBranch(entity);

            return result.Map<EmployeeHRAffiliatedBranchModel>();
        }

        /// <summary>
        /// 更新所在支部
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Permission(Permissions.HRManage.StaffInfo)]
        [LogApi(ApiType.Save, Operate = "更新所在支部")]
        public BizResult<EmployeeHRAffiliatedBranchModel> UpdateEmployeeHRAffiliatedBranch(EmployeeHRAffiliatedBranchModel model)
        {
            var entity = model.Map<EmployeeHRAffiliatedBranch>();

            var result = this.Repo.UpdateEmployeeHRAffiliatedBranch(entity);

            return result.Map<EmployeeHRAffiliatedBranchModel>();
        }

        /// <summary>
        /// 删除所在支部
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Permission(Permissions.HRManage.StaffInfo)]
        [LogApi(ApiType.Save, Operate = "删除所在支部")]
        public BizResult DeleteEmployeeHRAffiliatedBranch(EmployeeHRAffiliatedBranchModel model)
        {
            return this.Repo.DeleteEmployeeHRAffiliatedBranch(model.ID);
        }
        #endregion

        #region 党内职务
        /// <summary>
        /// 查询党内职务
        /// </summary>
        [HttpGet]
        [Permission(Permissions.HRManage.StaffInfo)]
        [LogApi(ApiType.Query, Operate = "查询党内职务")]
        public BizResult<List<EmployeeHRDictQuery>> QueryEmployeeHRDict([FromQuery] Guid id)
        {
            var exps = this.NewExps<EmployeeHRDict>(p => p.EmployeeHRId == id);
            var entities = this.Repo.GetEntities<EmployeeHRDict>(p => p.EmployeeHRId == id);

            var models = entities.Maps<EmployeeHRDictQuery>().OrderByDescending(p => p.CreateTime).ToList();

            return new BizResult<List<EmployeeHRDictQuery>>(models);
        }

        /// <summary>
        /// 获取党内职务
        /// </summary>
        [HttpGet]
        [Permission(Permissions.HRManage.StaffInfo)]
        [LogApi(ApiType.Query, Operate = "获取党内职务")]
        public BizResult<EmployeeHRDictModel> GetEmployeeHRDict([FromQuery] Guid id)
        {
            var result = new BizResult<EmployeeHRDictModel>();

            var entity = this.Repo.Get<EmployeeHRDict>(id);

            if (entity == null)
            {
                result.Error("党内职务不存在");
            }
            else
            {
                var model = entity.Map<EmployeeHRDictModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增党内职务
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Permission(Permissions.HRManage.StaffInfo)]
        [LogApi(ApiType.Save, Operate = "新增党内职务")]
        public BizResult<EmployeeHRDictModel> AddEmployeeHRDict(EmployeeHRDictModel model)
        {
            var entity = model.Map<EmployeeHRDict>();

            var result = this.Repo.AddEmployeeHRDict(entity);

            return result.Map<EmployeeHRDictModel>();
        }

        /// <summary>
        /// 更新党内职务
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Permission(Permissions.HRManage.StaffInfo)]
        [LogApi(ApiType.Save, Operate = "更新党内职务")]
        public BizResult<EmployeeHRDictModel> UpdateEmployeeHRDict(EmployeeHRDictModel model)
        {
            var entity = model.Map<EmployeeHRDict>();

            var result = this.Repo.UpdateEmployeeHRDict(entity);

            return result.Map<EmployeeHRDictModel>();
        }

        /// <summary>
        /// 删除党内职务
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Permission(Permissions.HRManage.StaffInfo)]
        [LogApi(ApiType.Save, Operate = "删除党内职务")]
        public BizResult DeleteEmployeeHRDict(EmployeeHRDictModel model)
        {
            return this.Repo.DeleteEmployeeHRDict(model.ID);
        }
        #endregion

        #region 党员奖惩
        /// <summary>
        /// 获取党员奖惩
        /// </summary>
        [HttpGet]
        [Permission(Permissions.HRManage.StaffInfo)]
        [LogApi(ApiType.Query, Operate = "获取党员奖惩")]
        public BizResult<List<EmployeeHRPartyMemberHonorQuery>> QueryEmployeeHRPartyMemberHonor([FromQuery] Guid id)
        {
            var exps = this.NewExps<EmployeeHRPartyMemberHonor>(p => p.EmployeeHRId == id);
            var entities = this.Repo.GetEntities<EmployeeHRPartyMemberHonor>(p => p.EmployeeHRId == id);

            var models = entities.Maps<EmployeeHRPartyMemberHonorQuery>().OrderByDescending(p => p.Time).ToList();

            return new BizResult<List<EmployeeHRPartyMemberHonorQuery>>(models);
        }

        /// <summary>
        /// 获取党员奖惩
        /// </summary>
        [HttpGet]
        [Permission(Permissions.HRManage.StaffInfo)]
        [LogApi(ApiType.Query, Operate = "获取党员奖惩")]
        public BizResult<EmployeeHRPartyMemberHonorModel> GetEmployeeHRPartyMemberHonor([FromQuery] Guid id)
        {
            var result = new BizResult<EmployeeHRPartyMemberHonorModel>();

            var entity = this.Repo.Get<EmployeeHRPartyMemberHonor>(id);

            if (entity == null)
            {
                result.Error("党员奖惩不存在");
            }
            else
            {
                var model = entity.Map<EmployeeHRPartyMemberHonorModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增党员奖惩
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Permission(Permissions.HRManage.StaffInfo)]
        [LogApi(ApiType.Save, Operate = "新增党员奖惩")]
        public BizResult<EmployeeHRPartyMemberHonorModel> AddEmployeeHRPartyMemberHonor(EmployeeHRPartyMemberHonorModel model)
        {
            var entity = model.Map<EmployeeHRPartyMemberHonor>();

            var result = this.Repo.AddEmployeeHRPartyMemberHonor(entity);

            return result.Map<EmployeeHRPartyMemberHonorModel>();
        }

        /// <summary>
        /// 更新党员奖惩
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Permission(Permissions.HRManage.StaffInfo)]
        [LogApi(ApiType.Save, Operate = "更新党员奖惩")]
        public BizResult<EmployeeHRPartyMemberHonorModel> UpdateEmployeeHRPartyMemberHonor(EmployeeHRPartyMemberHonorModel model)
        {
            var entity = model.Map<EmployeeHRPartyMemberHonor>();

            var result = this.Repo.UpdateEmployeeHRPartyMemberHonor(entity);

            return result.Map<EmployeeHRPartyMemberHonorModel>();
        }

        /// <summary>
        /// 删除党员奖惩
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Permission(Permissions.HRManage.StaffInfo)]
        [LogApi(ApiType.Save, Operate = "删除党员奖惩")]
        public BizResult DeleteEmployeeHRPartyMemberHonor(EmployeeHRPartyMemberHonorModel model)
        {
            return this.Repo.DeleteEmployeeHRPartyMemberHonor(model.ID);
        }
        #endregion
        #endregion EmployeeHR

        #region EmployeeSocialInsurance

        /// <summary>
        /// 获取员工社保信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取员工社保信息")]
        public BizResult<EmployeeSocialInsuranceModel> getSocialSecurityInfo([FromQuery, Required] Guid id)
        {
            var result = this.BizResult<EmployeeSocialInsuranceModel>();

            var entity = this.Repo.Get<Entities.EmployeeSocialInsurance>(id);

            var model = entity?.Map<EmployeeSocialInsuranceModel>();
            result.Data = model;

            return result;
        }

        /// <summary>
        /// 更新员工社保信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新员工社保信息")]
        public BizResult<EmployeeSocialInsuranceModel> updateEmployeeSocialSecurity([FromBody] EmployeeSocialInsuranceModel model)
        {
            var entity = model.Map<EmployeeSocialInsurance>();

            var result = this.Repo.UpdateEmployeeSocialSecurity(entity, model.PaySlipNumber);

            return result.Map<EmployeeSocialInsuranceModel>();
        }

        #endregion EmployeeSocialInsurance

        #region EmployeeBenefit

        /// <summary>
        /// 获取员工工资信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取员工工资信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeBenefitModel> getWagesInfo([FromQuery, Required] Guid id)
        {
            var result = this.BizResult<EmployeeBenefitModel>();

            var entity = this.Repo.Get<Entities.EmployeeBenefit>(id);

            var model = entity?.Map<EmployeeBenefitModel>();
            result.Data = model;

            return result;
        }

        /// <summary>
        /// 更新员工工资信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "更新员工工资信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeBenefitModel> updateEmployeeBenefit([FromBody] EmployeeBenefitModel model)
        {
            var entity = model.Map<EmployeeBenefit>();

            var result = this.Repo.UpdateEmployeeBenefit(entity, model.ChangeDate, model.Remark, model.HistoryList);

            return result.Map<EmployeeBenefitModel>();
        }

        #endregion EmployeeBenefit

        #region EmployeePayrollHistory

        /// <summary>
        /// 查询员工工资调整记录
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工工资调整记录")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeePayrollHistoryQuery> QueryWagesHistory([FromQuery] EmployeePayrollHistoryFilter filter)
        {
            var exps = this.NewExps<EmployeePayrollHistory>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeePayrollHistory.Columns.CreateTime + GeneralConsts.OrderByDesc;
            }
            if (!filter.IsViewAll)
            {
                exps.And(p => p.Tag == null);
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeePayrollHistoryQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        #endregion EmployeePayrollHistory

        #region EmployeePayrollPrint

        /// <summary>
        /// 查询打印审核人
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询打印审核人")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeePayrollPrintQuery> GetEmployeePayrollPrint([FromQuery] EmployeePayrollPrintFilter filter)
        {
            var exps = this.NewExps<EmployeePayrollPrint>();

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);
            var model = entities.Maps<EmployeePayrollPrintQuery>();

            return this.QueryResult(model, recoredCount, filter);
        }

        /// <summary>
        /// 更新打印审核人
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "更新打印审核人")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeePayrollPrintModel> UpdateEmployeePayrollPrint([FromBody] EmployeePayrollPrintModel model)
        {
            var entity = model.Map<EmployeePayrollPrint>();

            var result = this.Repo.UpdateEmployeePayrollPrint(entity);

            return result.Map<EmployeePayrollPrintModel>();
        }

        /// <summary>
        /// 删除打印审核人
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "删除打印审核人")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeePayrollPrintModel> DeleteEmployeePayrollPrint([FromBody] EmployeePayrollPrintModel model)
        {
            var entity = model.Map<EmployeePayrollPrint>();

            var result = this.Repo.DeleteEmployeePayrollPrint(entity);

            return result.Map<EmployeePayrollPrintModel>();
        }

        /// <summary>
        /// 查询打印信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询打印信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeePayrollHistoryModel> GetPrintDetailsInfo([FromQuery, Required] Guid id)
        {
            var result = new BizResult<EmployeePayrollHistoryModel>();

            var entity = this.Repo.Get<Entities.EmployeePayrollHistory>(id);

            result.Data = entity?.Map<EmployeePayrollHistoryModel>();
            if (result.Data != null)
            {
                var Query = this.NewExps<EmployeePayrollHistory>();
                Query.Add(x => x.EmployeeId == result.Data.EmployeeId && x.ChangeItem == "薪级工资");

                var xinjiEntity = this.Repo.GetEntities<EmployeePayrollHistory>(Query).ToList().OrderByDescending(x => x.LastEditTime).FirstOrDefault();
                var dataEntity = xinjiEntity?.Map<EmployeePayrollHistoryModel>();
                if (dataEntity != null)
                {
                    var XinjiBeforeAfterInfo = new KeyValueEntity()
                    {
                        BeforeKey = dataEntity.BeforeKey,
                        BeforeValue = dataEntity.BeforeValue,
                        AfterKey = dataEntity.AfterKey,
                        AfterValue = dataEntity.AfterValue
                    };
                    result.Data.XinjiBeforeAfterInfo = XinjiBeforeAfterInfo;
                }
                else
                {
                    //根据工资表查询当前薪级
                    var XinjiAfterEntity = this.Repo.Get<EmployeeBenefit>(result.Data.EmployeeId);
                    var XinjiBeforeAfterInfo = new KeyValueEntity()
                    {
                        AfterKey = XinjiAfterEntity?.PayRollOrgSalaryLevel?.Name,
                        AfterValue = XinjiAfterEntity?.PayRollOrgSalaryLevel?.Value.ToString()
                    };
                    result.Data.XinjiBeforeAfterInfo = XinjiBeforeAfterInfo;
                }
            }
            return result;
        }

        #endregion EmployeePayrollPrint

        #region EmployeeStation

        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询部门负责人")]
        public BizResult<EmployeeModel> QueryDeptPrincipal([FromQuery] Guid deptid)
        {
            var result = new BizResult<EmployeeModel>();

            var entity = this.Repo.GetDeptEmp(deptid);

            result.Data = entity?.Map<EmployeeModel>();

            return result;
        }

        /// <summary>
        /// 查询员工聘任岗位
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工聘任岗位")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeStationQuery> QueryEmployeeStation([FromQuery] EmployeeStationFilter filter)
        {
            var exps = this.NewExps<EmployeeStation>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            exps.And(p => filter.Estype == p.Type);

            /*
            if (filter.ContainRank != null && filter.ContainRank.Any())
            {
                var ranks = this.Repo.QueryDictByCode(filter.ContainRank, Dicts.RankCode);
                var ids = ranks.Select(p => p.ID).ToList();
                exps.And(p => ids.Contains(p.RankId.Value));
            }

            if (filter.NotContainRank != null && filter.NotContainRank.Any())
            {
                var ranks = this.Repo.QueryDictByCode(filter.NotContainRank, Dicts.RankCode);
                var ids = ranks.Select(p => p.ID).ToList();
                exps.And(p => !ids.Contains(p.RankId.Value));
            }
            */

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeStation.Columns.StartDate + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeStationQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增员工聘任岗位
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增员工聘任岗位")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeStationModel> AddEmployeeStation([FromBody] EmployeeStationModel model)
        {
            var entity = model.Map<EmployeeStation>();

            var result = this.Repo.AddEmployeeStation(entity, model.DeptPrincipalID);

            return result.Map<EmployeeStationModel>();
        }

        /// <summary>
        /// 更新员工聘任岗位
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新员工聘任岗位")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeStationModel> UpdateEmployeeStation([FromBody] EmployeeStationModel model)
        {
            var entity = model.Map<EmployeeStation>();

            var result = this.Repo.UpdateEmployeeStation(entity, model.DeptPrincipalID);

            return result.Map<EmployeeStationModel>();
        }

        /// <summary>
        /// 删除员工聘任岗位
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除员工聘任岗位")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeStation([FromBody] EmployeeStationModel model)
        {
            var entity = new EmployeeStation
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeStation(entity);

            return result;
        }

        #endregion EmployeeStation

        #region EmployeeCertify

        /// <summary>
        /// 查询员工职称资格
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工职称资格")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeCertifyQuery> QueryEmployeeCertify([FromQuery] EmployeeCertifyFilter filter)
        {
            var exps = this.NewExps<EmployeeCertify>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeCertify.Columns.EffectDate + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeCertifyQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增员工职称资格
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增员工职称资格")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeCertifyModel> AddEmployeeCertify([FromBody] EmployeeCertifyModel model)
        {
            var entity = model.Map<EmployeeCertify>();

            var result = this.Repo.AddEmployeeCertify(entity);

            return result.Map<EmployeeCertifyModel>();
        }

        /// <summary>
        /// 更新员工职称资格
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新员工职称资格")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeCertifyModel> UpdateEmployeeCertify([FromBody] EmployeeCertifyModel model)
        {
            var entity = model.Map<EmployeeCertify>();

            var result = this.Repo.UpdateEmployeeCertify(entity);

            return result.Map<EmployeeCertifyModel>();
        }

        /// <summary>
        /// 删除员工职称资格
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除员工职称资格")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeCertify([FromBody] EmployeeCertifyModel model)
        {
            var entity = new EmployeeCertify
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeCertify(entity);

            return result;
        }

        #endregion EmployeeCertify

        #region EmployeeEducation

        /// <summary>
        /// 查询员工学习经历
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工学习经历")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeEducationQuery> QueryEmployeeEducation([FromQuery] EmployeeEducationFilter filter)
        {
            var exps = this.NewExps<EmployeeEducation>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeEducation.Columns.BeginDate + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeEducationQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增员工学习经历
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增员工学习经历")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeEducationModel> AddEmployeeEducation([FromBody] EmployeeEducationModel model)
        {
            var entity = model.Map<EmployeeEducation>();

            var result = this.Repo.AddEmployeeEducation(entity);

            return result.Map<EmployeeEducationModel>();
        }

        /// <summary>
        /// 更新员工学习经历
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新员工学习经历")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeEducationModel> UpdateEmployeeEducation([FromBody] EmployeeEducationModel model)
        {
            var entity = model.Map<EmployeeEducation>();

            var result = this.Repo.UpdateEmployeeEducation(entity);

            return result.Map<EmployeeEducationModel>();
        }

        /// <summary>
        /// 删除员工学习经历
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除员工学习经历")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeEducation([FromBody] EmployeeEducationModel model)
        {
            var entity = new EmployeeEducation
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeEducation(entity);

            return result;
        }

        #endregion EmployeeEducation

        #region EmployeeWork

        /// <summary>
        /// 查询员工工作经历
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工工作经历")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeWorkQuery> QueryEmployeeWork([FromQuery] EmployeeWorkFilter filter)
        {
            var exps = this.NewExps<EmployeeWork>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeWork.Columns.StartDate + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeWorkQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增员工工作经历
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增员工工作经历")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeWorkModel> AddEmployeeWork([FromBody] EmployeeWorkModel model)
        {
            var entity = model.Map<EmployeeWork>();

            var result = this.Repo.AddEmployeeWork(entity);

            return result.Map<EmployeeWorkModel>();
        }

        /// <summary>
        /// 更新员工工作经历
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新员工工作经历")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeWorkModel> UpdateEmployeeWork([FromBody] EmployeeWorkModel model)
        {
            var entity = model.Map<EmployeeWork>();

            var result = this.Repo.UpdateEmployeeWork(entity);

            return result.Map<EmployeeWorkModel>();
        }

        /// <summary>
        /// 删除员工工作经历
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除员工工作经历")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeWork([FromBody] EmployeeWorkModel model)
        {
            var entity = new EmployeeWork
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeWork(entity);

            return result;
        }

        #endregion EmployeeWork

        #region EmployeeAbroadInfo

        /// <summary>
        /// 查询员工出国情况
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工出国情况")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeAbroadInfoQuery> QueryEmployeeAbroadInfo([FromQuery] EmployeeAbroadInfoFilter filter)
        {
            var exps = this.NewExps<EmployeeAbroadInfo>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeAbroadInfo.Columns.StartDate + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeAbroadInfoQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增员工出国情况
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增员工出国情况")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeAbroadInfoModel> AddEmployeeAbroadInfo([FromBody] EmployeeAbroadInfoModel model)
        {
            var entity = model.Map<EmployeeAbroadInfo>();

            var result = this.Repo.AddEmployeeAbroadInfo(entity);

            return result.Map<EmployeeAbroadInfoModel>();
        }

        /// <summary>
        /// 更新员工出国情况
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新员工出国情况")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeAbroadInfoModel> UpdateEmployeeAbroadInfo([FromBody] EmployeeAbroadInfoModel model)
        {
            var entity = model.Map<EmployeeAbroadInfo>();

            var result = this.Repo.UpdateEmployeeAbroadInfo(entity);

            return result.Map<EmployeeAbroadInfoModel>();
        }

        /// <summary>
        /// 删除员工出国情况
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除员工出国情况")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeAbroadInfo([FromBody] EmployeeAbroadInfoModel model)
        {
            var entity = new EmployeeAbroadInfo
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeAbroadInfo(entity);

            return result;
        }

        #endregion EmployeeAbroadInfo

        #region EmployeeContract

        /// <summary>
        /// 查询员工合同
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工合同")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeContractQuery> QueryEmployeeContract([FromQuery] EmployeeContractFilter filter)
        {
            var exps = this.NewExps<EmployeeContract>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeContract.Columns.StartDate + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeContractQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 查询待续签员工合同
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询待续签员工合同")]
        [Permission(Permissions.HRManage.ContractManage)]
        public QueryResult<EmployeeContractQuery> QueryRenewEmployeeContract([FromQuery] EmployeeContractFilter filter)
        {
            var exps = this.NewExps<EmployeeContract>();
            var currentDate = SysDateTime.Now.Date;

            exps.And(p => ((p.ProEndDate.HasValue && p.ProEndDate.Value.Date >= currentDate && p.ProEndDate.Value.Date <= currentDate.AddDays(Config.ContractsExpire)) ||
                           (p.EndDate.HasValue && p.EndDate.Value.Date >= currentDate && p.EndDate.Value.Date <= currentDate.AddDays(Config.ContractsExpire) && !p.ProEndDate.HasValue && (!p.IsNextTime.HasValue || !p.IsNextTime.Value)) ||
                           (p.IsNextTime == true && p.ReminderDate <= currentDate.AddDays(Config.ContractsExpire))) && p.IsTheLatest == true && (!p.IsRemind.HasValue || !p.IsRemind.Value));

            if (!filter.EmpCode.IsEmpty())
            {
                exps.And(p => p.Employee.EmpCode.Contains(filter.EmpCode!));
            }

            if (!filter.DisplayName.IsEmpty())
            {
                exps.And(p => p.Employee.DisplayName.Contains(filter.DisplayName!));
            }

            if (!filter.QueryType.IsEmpty())
            {
                if (filter.QueryType == 1)
                {
                    //实习期结束
                    exps.And(p => p.ProEndDate.HasValue && p.ProEndDate.Value.Date <= currentDate.AddDays(Config.ContractsExpire));
                }
                else if (filter.QueryType == 2)
                {
                    //合同结束
                    exps.And(p => p.EndDate.HasValue && p.EndDate.Value.Date <= currentDate.AddDays(Config.ContractsExpire) && !p.ProEndDate.HasValue && (!p.IsNextTime.HasValue || !p.IsNextTime.Value));
                }
                else if (filter.QueryType == 3)
                {
                    //下一次提醒
                    exps.And(p => p.IsNextTime == true && p.ReminderDate <= currentDate.AddDays(Config.ContractsExpire));
                }
            }

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeContract.Columns.StartDate + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeContractQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 下个月提醒
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "下个月提醒")]
        [Permission(Permissions.HRManage.ContractManage)]
        public BizResult<EmployeeContractModel> UpdateNextTimeRemind([FromBody] EmployeeContractModel model)
        {
            var entity = model.Map<EmployeeContract>();

            var result = this.Repo.UpdateNextTimeRemind(entity);

            return result.Map<EmployeeContractModel>();
        }

        /// <summary>
        /// 批量执行下次提醒
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "批量执行下次提醒")]
        [Permission(Permissions.HRManage.ContractManage)]
        public BizResult<EmployeeContractModel> BatchUpdateNextTimeRemind([FromBody] EmployeeContractModel model)
        {
            var entity = model.Map<EmployeeContract>();

            var result = this.Repo.BatchUpdateNextTimeRemind(entity, model.Ids);

            return result.Map<EmployeeContractModel>();
        }

        /// <summary>
        /// 批量发送续签邮件提醒
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "批量发送续签邮件提醒")]
        [Permission(Permissions.HRManage.ContractManage)]
        public BizResult<EmployeeContractModel> BatchSendEmailForRenewRemind([FromBody] EmployeeContractModel model)
        {
            var entity = model.Map<EmployeeContract>();

            var result = this.Repo.BatchSendEmailForRenewRemind(entity, model.Ids);

            return result.Map<EmployeeContractModel>();
        }

        /// <summary>
        /// 发送续签邮件提醒
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "发送续签邮件提醒")]
        [Permission(Permissions.HRManage.ContractManage)]
        public BizResult<EmployeeContractModel> SendEmailForRenewRemind([FromBody] EmployeeContractModel model)
        {
            var entity = model.Map<EmployeeContract>();

            var result = this.Repo.SendEmailForRenewRemind(entity);

            return result.Map<EmployeeContractModel>();
        }

        /// <summary>
        /// 新增员工合同
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增员工合同")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeContractModel> AddEmployeeContract([FromBody] EmployeeContractModel model)
        {
            var entity = model.Map<EmployeeContract>();

            var result = this.Repo.AddEmployeeContract(entity);

            return result.Map<EmployeeContractModel>();
        }

        /// <summary>
        /// 更新员工合同
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新员工合同")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeContractModel> UpdateEmployeeContract([FromBody] EmployeeContractModel model)
        {
            var entity = model.Map<EmployeeContract>();

            var result = this.Repo.UpdateEmployeeContract(entity);

            return result.Map<EmployeeContractModel>();
        }

        /// <summary>
        /// 删除员工合同
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除员工合同")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeContract([FromBody] EmployeeContractModel model)
        {
            var entity = new EmployeeContract
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeContract(entity);

            return result;
        }

        #endregion EmployeeContract

        #region EmployeeTrain

        /// <summary>
        /// 查询员工培养计划
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工培养计划")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeTrainQuery> QueryEmployeeTrain([FromQuery] EmployeeTrainFilter filter)
        {
            var exps = this.NewExps<EmployeeTrain>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeTrain.Columns.StartDate + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeTrainQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增员工培养计划
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增员工培养计划")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeTrainModel> AddEmployeeTrain([FromBody] EmployeeTrainModel model)
        {
            var entity = model.Map<EmployeeTrain>();

            var result = this.Repo.AddEmployeeTrain(entity);

            return result.Map<EmployeeTrainModel>();
        }

        /// <summary>
        /// 更新员工培养计划
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新员工培养计划")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeTrainModel> UpdateEmployeeTrain([FromBody] EmployeeTrainModel model)
        {
            var entity = model.Map<EmployeeTrain>();

            var result = this.Repo.UpdateEmployeeTrain(entity);

            return result.Map<EmployeeTrainModel>();
        }

        /// <summary>
        /// 删除员工培养计划
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除员工培养计划")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeTrain([FromBody] EmployeeTrainModel model)
        {
            var entity = new EmployeeTrain
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeTrain(entity);

            return result;
        }

        #endregion EmployeeTrain

        #region EmployeeAssessment

        /// <summary>
        /// 查询考核
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询考核")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeAssessmentQuery> QueryEmployeeAssessment([FromQuery] EmployeeAssessmentFilter filter)
        {
            var exps = this.NewExps<EmployeeAssessment>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeAssessment.Columns.EvaluateDate + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeAssessmentQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增考核
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增考核")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeAssessmentModel> AddEmployeeAssessment([FromBody] EmployeeAssessmentModel model)
        {
            var entity = model.Map<EmployeeAssessment>();

            var result = this.Repo.AddEmployeeAssessment(entity);

            return result.Map<EmployeeAssessmentModel>();
        }

        /// <summary>
        /// 更新考核
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新考核")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeAssessmentModel> UpdateEmployeeAssessment([FromBody] EmployeeAssessmentModel model)
        {
            var entity = model.Map<EmployeeAssessment>();

            var result = this.Repo.UpdateEmployeeAssessment(entity);

            return result.Map<EmployeeAssessmentModel>();
        }

        /// <summary>
        /// 删除考核
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除考核")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeAssessment([FromBody] EmployeeAssessmentModel model)
        {
            var entity = new EmployeeAssessment
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeAssessment(entity);

            return result;
        }

        #endregion EmployeeAssessment

        #region EmployeeDeduct

        /// <summary>QueryEmployeeDeduct
        /// 查询工龄薪级计算
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询工龄薪级计算")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeDeductQuery> QueryEmployeeDeduct([FromQuery] EmployeeDeductFilter filter)
        {
            var exps = this.NewExps<EmployeeDeduct>();

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeDeduct.Columns.Year + GeneralConsts.OrderByDesc;
            }

            var result = this.Repo.GetDynamicQuery<EmployeeDeduct, EmployeeDeductQuery>(filter, exps);

            return result;

        }


        [HttpGet]
        [LogApi(ApiType.Query, Operate = "导入下载摸板")]
        public IActionResult DownlodaEmployeeDeductTemplate()
        {
            string fileTemplate = "UpdateCompanyAgeSalaryScale.xlsx";


            string filepath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, Common.Consts.Exports.PathTemplate, fileTemplate);
            if (!System.IO.File.Exists(filepath))
            {
                return NotFound();
            }

            using FileStream fs = new FileStream(filepath, FileMode.Open, FileAccess.Read, FileShare.Read);
            using var ms = new MemoryStream();
            fs.CopyTo(ms);

            var contentTypeProvider = new Microsoft.AspNetCore.StaticFiles.FileExtensionContentTypeProvider();
            var contenttype = Common.Consts.Exports.ContentType;
            contentTypeProvider.TryGetContentType(filepath, out contenttype);
            return File(ms.ToArray(), contenttype ?? "", Path.GetFileName(filepath));
        }


        [HttpPost]
        [LogApi(ApiType.Save, Operate = "导入工龄津贴")]
        public BizResult ImportEmployeeDeduct([FromForm] IFormCollection form)
        {
            var result = new BizResult();
            var ms = new MemoryStream();
            try
            {
                var file = form.Files["file"];
                var deductId = form["deductId"].AsString();

                if (file == null)
                {
                    result.Error("请上传文件");
                }
                else
                {
                    var uidList = new List<int>();
                    var deductCalculateList = new List<EmployeeDeductCalculate>();

                    file.CopyTo(ms);
                    var dt = new DataTable();
                    var fileInfo = new FileInfo(file.FileName);
                    dt = Excel.ReadTemplateFromExcelToDataTable(ms, fileInfo.Extension);

                    if (dt.Rows.Count == 0)
                    {
                        result.Error("导入列表不可为空");
                    }
                    else
                    {
                        //检查列头是否符合格式要求
                        var lackColumns = DataTableHelper.CheckColumns(dt, ImportMapping.EmployeeDeductMapping);
                        if (lackColumns.Any())
                        {
                            result.Error($"模板中缺少如下列：{string.Join('、', lackColumns)}。");
                        }
                    }

                    if (result.Succeed)
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            var row = dt.Rows[i];
                            var deductCalculate = new EmployeeDeductCalculate();
                            var errorMessage = string.Empty;

                            // 取值
                            var uid = row["唯一码"].AsString().AsTrim();
                            var empCode = row["工号"].AsString().AsTrim();
                            var name = row["姓名"].AsString().AsTrim();
                            var notCalAge = row["不计算工龄"].AsString().AsTrim();
                            var notCalSalaryScale = row["不计算薪级"].AsString().AsTrim();

                            #region 判断 验证 取值
                            int uidNumber = 0;
                            if (uid.IsEmpty())
                            {
                                errorMessage += "唯一码不能为空;";
                            }
                            else if (!int.TryParse(uid, out uidNumber))
                            {
                                errorMessage += "唯一码不正确，必须为数字;";
                            }
                            else if (uidNumber <= 0)
                            {
                                errorMessage += "唯一码必须大于0;";
                            }

                            if (empCode.IsEmpty())
                            {
                                errorMessage += "工号不能为空;";
                            }
                            

                            if (!uid.IsEmpty() && !empCode.IsEmpty())
                            {
                                if (this.Repo.GetCount<Renji.JHR.Entities.Employee>(x => x.Uid == uidNumber && x.EmpCode == empCode) == 0)
                                {
                                    errorMessage += "唯一码与工号不匹配;";
                                }
                            }

                            if (name.IsEmpty())
                            {
                                errorMessage += "姓名不能为空;";
                            }
                            

                            if (!uid.IsEmpty() && !name.IsEmpty())
                            {
                                if (this.Repo.GetCount<Renji.JHR.Entities.Employee>(x => x.Uid == uidNumber && x.DisplayName == name) == 0)
                                {
                                    errorMessage += "唯一码与姓名不匹配;";
                                }
                            }



                            if (notCalAge.IsEmpty())
                            {
                                errorMessage += "计算工龄不能为空;";
                            }
                            else
                            {
                                if (notCalAge.Equals("0") || notCalAge.ToLower().Equals("false") || notCalAge.ToLower().Equals("否"))
                                    deductCalculate.NotCalAge = false;
                                else if (notCalAge.Equals("1") || notCalAge.ToLower().Equals("true") || notCalAge.ToLower().Equals("是"))
                                    deductCalculate.NotCalAge = true;
                            }

                            if (notCalSalaryScale.IsEmpty())
                            {
                                errorMessage += "计算薪级不能为空;";
                            }
                            else
                            {
                                if (notCalSalaryScale.Equals("0") || notCalSalaryScale.ToLower().Equals("false") || notCalSalaryScale.ToLower().Equals("否"))
                                    deductCalculate.NotCalSalaryScale = false;
                                else if (notCalSalaryScale.Equals("1") || notCalSalaryScale.ToLower().Equals("true") || notCalSalaryScale.ToLower().Equals("是"))
                                    deductCalculate.NotCalSalaryScale = true;
                            }

                            #endregion 判断 验证 取值
                            if (errorMessage.IsEmpty())
                            {
                                uidList.Add(uidNumber);
                                deductCalculateList.Add(deductCalculate);
                            }
                            else
                            {
                                errorMessage = $"第{i + 1}行：{errorMessage}";
                                result.Error(errorMessage);
                            }
                        }
                    }

                    // 数据正确 写数据
                    if (result.Succeed)
                    {
                        result = this.Repo.ImportEmployeeDeductCalculate(Guid.Parse(deductId), uidList, deductCalculateList);
                    }
                }

                return result;
            }
            finally
            {
                ms.Dispose();
            }
        }



        /// <summary>
        /// 新增扣减工龄
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增工龄薪级计算")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeDeductModel> AddEmployeeDeduct([FromBody] EmployeeDeductModel model)
        {
            var entity = model.Map<EmployeeDeduct>();

            var result = this.Repo.AddEmployeeDeduct(entity);

            return result.Map<EmployeeDeductModel>();
        }

        /// <summary>
        /// 更新扣减工龄
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "更新工龄薪级计算")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeDeductModel> UpdateEmployeeDeduct([FromQuery] Guid DeductId)
        {
            var result = this.Repo.UpdateEmployeeDeduct(DeductId);

            return result.Map<EmployeeDeductModel>();
        }

        /// <summary>
        /// 删除扣减工龄
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除工龄薪级计算")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeDeduct([FromBody] EmployeeDeductModel model)
        {
            var entity = new EmployeeDeduct
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeDeduct(entity);

            return result;
        }

        #endregion

        #region EmployeeDeductCalculate

        /// <summary>
        /// 查询扣减工龄
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询扣减工龄")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeDeductCalculateQuery> QueryEmployeeDeductUnCalculate([FromQuery] EmployeeDeductCalculateFilter filter)
        {
            var exps = this.NewExps<EmployeeDeductCalculate>();
            exps.Add(x => x.NotCalAge.HasValue && x.NotCalAge.Value || x.NotCalSalaryScale.HasValue && x.NotCalSalaryScale.Value);

            int uid = 0;
            if (!string.IsNullOrEmpty(filter.Uid) && int.TryParse(filter.Uid.Trim(), out uid) && uid > 0)
            {
                exps.Add(x => x.Employee.Uid == uid);
            }
            if (!string.IsNullOrEmpty(filter.EmpCode))
            {
                exps.Add(x => x.Employee.EmpCode.Contains(filter.EmpCode));
            }
            if (!string.IsNullOrEmpty(filter.DisplayName))
            {
                exps.Add(x => x.Employee.DisplayName.Contains(filter.DisplayName));
            }

            if (filter.Order.IsEmpty())
            {
                filter.Order = $"{nameof(Entities.EmployeeDeductCalculate.CreateTime)}{Common.Consts.GeneralConsts.OrderByAsc}";
            }

            var result = this.Repo.GetDynamicQuery<EmployeeDeductCalculate, EmployeeDeductCalculateQuery>(filter, exps);

            return result;
        }

        /// <summary>
        /// 查询扣减工龄
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询扣减工龄")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeDeductCalculateQuery> QueryEmployeeDeductCalculate([FromQuery] EmployeeDeductCalculateFilter filter)
        {
            var exps = this.NewExps<EmployeeDeductCalculate>();
            exps.Add(x => !(x.NotCalAge.HasValue && x.NotCalAge.Value || x.NotCalSalaryScale.HasValue && x.NotCalSalaryScale.Value));

            int uid = 0;
            if (!string.IsNullOrEmpty(filter.Uid) && int.TryParse(filter.Uid.Trim(), out uid) && uid > 0)
            {
                exps.Add(x => x.Employee.Uid == uid);
            }
            if (!string.IsNullOrEmpty(filter.EmpCode))
            {
                exps.Add(x => x.Employee.EmpCode.Contains(filter.EmpCode));
            }
            if (!string.IsNullOrEmpty(filter.DisplayName))
            {
                exps.Add(x => x.Employee.DisplayName.Contains(filter.DisplayName));
            }

            if (filter.Order.IsEmpty())
            {
                filter.Order = $"{nameof(Entities.EmployeeDeductCalculate.CreateTime)}{Common.Consts.GeneralConsts.OrderByAsc}";
            }

            var result = this.Repo.GetDynamicQuery<EmployeeDeductCalculate, EmployeeDeductCalculateQuery>(filter, exps);

            return result;
        }

        /// <summary>
        /// 获取扣减工龄
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取扣减工龄")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeDeductCalculateModel> GetEmployeeDeductCalculate([FromQuery] Guid id)
        {
            var result = new BizResult<EmployeeDeductCalculateModel>();

            var entity = this.Repo.Get<EmployeeDeductCalculate>(id);

            if (entity == null)
            {
                result.Error("暂未添加员工");
            }
            else
            {
                var model = entity.Map<EmployeeDeductCalculateModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增扣减工龄
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新扣减工龄")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeDeductCalculateModel> UpdateEmployeeDeductCalculate([FromBody] EmployeeDeductCalculateModel model)
        {
            var entity = model.Map<EmployeeDeductCalculate>();

            var result = this.Repo.UpdateEmployeeDeductCalculate(entity);

            return result.Map<EmployeeDeductCalculateModel>();
        }


        /// <summary>
        /// 导出薪级工资计算
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "导出薪级工龄计算")]
        public IActionResult ExportEmployeeDeductCalculate(EmployeeDeductCalculateFilter filter)
        {
            var exps = this.NewExps<EmployeeDeductCalculate>();

            var paths = new List<string>
            {
                EmployeeDeductCalculate.Foreigns.Employee,
                $"{ EmployeeDeductCalculate.Foreigns.Employee }.{Entities.Employee.Inverses.EmployeeSalary}",
            };

            var entities = this.Repo.GetDynamicEntities<EmployeeDeductCalculate>(paths, filter, exps);


            var tables = this.FormatEmployeeSalaryTable(entities);

            var bytes = Excel.WriteToExcelBytes(tables, "工龄薪级");
            return this.File(bytes, ConstDefinition.Common.Export_Excel, "工龄薪级" + SysDateTime.Now.ToString("yyyy-MM-dd") + ConstDefinition.Common.ExtensionOfHighVersionExcel);
        }

        private DataTable FormatEmployeeSalaryTable(List<EmployeeDeductCalculate> employeeDeductCalculates)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add(new DataColumn("唯一码", typeof(string)));
            dt.Columns.Add(new DataColumn("工号", typeof(string)));
            dt.Columns.Add(new DataColumn("姓名", typeof(string)));
            dt.Columns.Add(new DataColumn("不计算工龄", typeof(string)));
            dt.Columns.Add(new DataColumn("实际工龄", typeof(string)));
            dt.Columns.Add(new DataColumn("新实际工龄", typeof(string)));
            dt.Columns.Add(new DataColumn("本院工龄", typeof(string)));
            dt.Columns.Add(new DataColumn("新本院工龄", typeof(string)));
            dt.Columns.Add(new DataColumn("不计算薪资", typeof(string)));
            dt.Columns.Add(new DataColumn("薪资类型", typeof(string)));
            dt.Columns.Add(new DataColumn("岗位类型", typeof(string)));
            dt.Columns.Add(new DataColumn("薪级", typeof(string)));
            dt.Columns.Add(new DataColumn("新薪级", typeof(string)));
            dt.Columns.Add(new DataColumn("薪资状态", typeof(string)));

            if (employeeDeductCalculates == null)
            {
                employeeDeductCalculates = new List<EmployeeDeductCalculate>();
            }

            foreach (var emp in employeeDeductCalculates)
            {
                var dr = dt.NewRow();
                dr["唯一码"] = emp.Employee.Uid;
                dr["工号"] = emp.Employee.EmpCode;
                dr["姓名"] = emp.Employee.DisplayName;
                dr["不计算工龄"] = emp.NotCalAge.HasValue && emp.NotCalAge.Value ? "是" : "否";
                dr["不计算薪资"] = emp.NotCalSalaryScale.HasValue && emp.NotCalSalaryScale.Value ? "是" : "否";
                dr["实际工龄"] = emp.SocietyAge;
                dr["新实际工龄"] = emp.NewSocietyAge;
                dr["本院工龄"] = emp.CompanyAge;
                dr["新本院工龄"] = emp.NewCompanyAge;
                dr["薪资类型"] = emp.Employee.EmployeeSalary?.EnumSalaryTypeDesc;
                dr["岗位类型"] = emp.Employee.EmployeeSalary?.Station?.Name.ToString();
                dr["薪级"] = emp.SalaryScale;
                dr["新薪级"] = emp.NewSalaryScale;
                dr["薪资状态"] = emp.Employee.EmployeeSalary?.EnumEmployeeSalaryStatusDesc;

                dt.Rows.Add(dr);
            }

            return dt;
        }

        #endregion EmployeeDeductCalculate

        #region EmployeeDeductWorkingAge

        /// <summary>
        /// 查询扣减工龄
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询扣减工龄")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeDeductWorkingAgeQuery> QueryEmployeeDeductWorkingAge([FromQuery] EmployeeDeductWorkingAgeFilter filter)
        {
            var exps = this.NewExps<EmployeeDeductWorkingAge>(x => x.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeDeductWorkingAge.Columns.Year + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeDeductWorkingAgeQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 获取扣减工龄
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取扣减工龄")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeDeductWorkingAgeModel> GetEmployeeDeductWorkingAge([FromQuery] Guid id)
        {
            var result = new BizResult<EmployeeDeductWorkingAgeModel>();

            var entity = this.Repo.Get<EmployeeDeductWorkingAge>(id);

            if (entity == null)
            {
                result.Error("暂未添加员工");
            }
            else
            {
                var model = entity.Map<EmployeeDeductWorkingAgeModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增扣减工龄
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增扣减工龄")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeDeductWorkingAgeModel> AddEmployeeDeductWorkingAge([FromBody] EmployeeDeductWorkingAgeModel model)
        {
            var entity = model.Map<EmployeeDeductWorkingAge>();

            var result = this.Repo.AddEmployeeDeductWorkingAge(entity);

            return result.Map<EmployeeDeductWorkingAgeModel>();
        }

        /// <summary>
        /// 更新扣减工龄
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新扣减工龄")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeDeductWorkingAgeModel> UpdateEmployeeDeductWorkingAge([FromBody] EmployeeDeductWorkingAgeModel model)
        {
            var entity = model.Map<EmployeeDeductWorkingAge>();

            var result = this.Repo.UpdateEmployeeDeductWorkingAge(entity);

            return result.Map<EmployeeDeductWorkingAgeModel>();
        }

        /// <summary>
        /// 删除扣减工龄
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除扣减工龄")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeDeductWorkingAge([FromBody] EmployeeDeductWorkingAgeModel model)
        {
            var entity = new EmployeeDeductWorkingAge
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeDeductWorkingAge(entity);

            return result;
        }

        #endregion EmployeeDeductWorkingAge

        #region EmployeeIncentive

        /// <summary>
        /// 查询奖惩
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询奖惩")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeIncentiveQuery> QueryEmployeeIncentive([FromQuery] EmployeeIncentiveFilter filter)
        {
            var exps = this.NewExps<EmployeeIncentive>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeIncentive.Columns.Date + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeIncentiveQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增奖惩
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增奖惩")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeIncentiveModel> AddEmployeeIncentive([FromBody] EmployeeIncentiveModel model)
        {
            var entity = model.Map<EmployeeIncentive>();

            var result = this.Repo.AddEmployeeIncentive(entity);

            return result.Map<EmployeeIncentiveModel>();
        }

        /// <summary>
        /// 更新奖惩
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新奖惩")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeIncentiveModel> UpdateEmployeeIncentive([FromBody] EmployeeIncentiveModel model)
        {
            var entity = model.Map<EmployeeIncentive>();

            var result = this.Repo.UpdateEmployeeIncentive(entity);

            return result.Map<EmployeeIncentiveModel>();
        }

        /// <summary>
        /// 删除奖惩
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除奖惩")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeIncentive([FromBody] EmployeeIncentiveModel model)
        {
            var entity = new EmployeeIncentive
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeIncentive(entity);

            return result;
        }

        #endregion EmployeeIncentive

        #region EmployeeHealth

        /// <summary>
        /// 获取员工健康情况
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取员工健康情况")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeHealthModel> GetEmployeeHealth([FromQuery, Required] Guid id)
        {
            var result = this.BizResult<EmployeeHealthModel>();

            var entity = this.Repo.Get<EmployeeHealth>(id);

            var model = entity?.Map<EmployeeHealthModel>();

            result.Data = model;

            return result;
        }

        /// <summary>
        /// 更新员工健康情况
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新员工健康情况")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeHealthModel> UpdateEmployeeHealth([FromBody] EmployeeHealthModel model)
        {
            var entity = model.Map<EmployeeHealth>();

            var result = this.Repo.UpdateEmployeeHealth(entity);

            return result.Map<EmployeeHealthModel>();
        }

        #endregion EmployeeHealth

        #region EmployeeAccident

        /// <summary>
        /// 查询医疗事故
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询医疗事故")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeAccidentQuery> QueryEmployeeAccident([FromQuery] EmployeeAccidentFilter filter)
        {
            var exps = this.NewExps<EmployeeAccident>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeAccident.Columns.Date + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeAccidentQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增医疗事故
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增医疗事故")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeAccidentModel> AddEmployeeAccident([FromBody] EmployeeAccidentModel model)
        {
            var entity = model.Map<EmployeeAccident>();

            var result = this.Repo.AddEmployeeAccident(entity);

            return result.Map<EmployeeAccidentModel>();
        }

        /// <summary>
        /// 更新医疗事故
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新医疗事故")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeAccidentModel> UpdateEmployeeAccident([FromBody] EmployeeAccidentModel model)
        {
            var entity = model.Map<EmployeeAccident>();

            var result = this.Repo.UpdateEmployeeAccident(entity);

            return result.Map<EmployeeAccidentModel>();
        }

        /// <summary>
        /// 删除医疗事故
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除医疗事故")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeAccident([FromBody] EmployeeAccidentModel model)
        {
            var entity = new EmployeeAccident
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeAccident(entity);

            return result;
        }

        #endregion EmployeeAccident

        #region EmployeeTeach

        /// <summary>
        /// 查询教学信息
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询教学信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeTeachQuery> QueryEmployeeTeach([FromQuery] EmployeeTeachFilter filter)
        {
            var exps = this.NewExps<EmployeeTeach>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeTeach.Columns.Year + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeTeachQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增教学信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增教学信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeTeachModel> AddEmployeeTeach([FromBody] EmployeeTeachModel model)
        {
            var entity = model.Map<EmployeeTeach>();

            var result = this.Repo.AddEmployeeTeach(entity);

            return result.Map<EmployeeTeachModel>();
        }

        /// <summary>
        /// 更新教学信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新教学信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeTeachModel> UpdateEmployeeTeach([FromBody] EmployeeTeachModel model)
        {
            var entity = model.Map<EmployeeTeach>();

            var result = this.Repo.UpdateEmployeeTeach(entity);

            return result.Map<EmployeeTeachModel>();
        }

        /// <summary>
        /// 删除教学信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除教学信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeTeach([FromBody] EmployeeTeachModel model)
        {
            var entity = new EmployeeTeach
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeTeach(entity);

            return result;
        }

        #endregion EmployeeTeach

        #region EmployeeRelation

        /// <summary>
        /// 查询社会关系
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询社会关系")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeRelationQuery> QueryEmployeeRelation([FromQuery] EmployeeRelationFilter filter)
        {
            var exps = this.NewExps<EmployeeRelation>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeRelation.Columns.CreateTime + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeRelationQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增社会关系
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增社会关系")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeRelationModel> AddEmployeeRelation([FromBody] EmployeeRelationModel model)
        {
            var entity = model.Map<EmployeeRelation>();

            var result = this.Repo.AddEmployeeRelation(entity);

            return result.Map<EmployeeRelationModel>();
        }

        /// <summary>
        /// 更新社会关系
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新社会关系")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeRelationModel> UpdateEmployeeRelation([FromBody] EmployeeRelationModel model)
        {
            var entity = model.Map<EmployeeRelation>();

            var result = this.Repo.UpdateEmployeeRelation(entity);

            return result.Map<EmployeeRelationModel>();
        }

        /// <summary>
        /// 删除社会关系
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除社会关系")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeRelation([FromBody] EmployeeRelationModel model)
        {
            var entity = new EmployeeRelation
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeRelation(entity);

            return result;
        }

        #endregion EmployeeRelation

        #region EmployeeArticle

        /// <summary>
        /// 查询论文信息
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询论文信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeArticleQuery> QueryEmployeeArticle([FromQuery] EmployeeArticleFilter filter)
        {
            var exps = this.NewExps<EmployeeArticle>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeArticle.Columns.CreateTime + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeArticleQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增论文信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增论文信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeArticleModel> AddEmployeeArticle([FromBody] EmployeeArticleModel model)
        {
            var entity = model.Map<EmployeeArticle>();

            var result = this.Repo.AddEmployeeArticle(entity);

            return result.Map<EmployeeArticleModel>();
        }

        /// <summary>
        /// 更新论文信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新论文信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeArticleModel> UpdateEmployeeArticle([FromBody] EmployeeArticleModel model)
        {
            var entity = model.Map<EmployeeArticle>();

            var result = this.Repo.UpdateEmployeeArticle(entity);

            return result.Map<EmployeeArticleModel>();
        }

        /// <summary>
        /// 删除论文信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除论文信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeArticle([FromBody] EmployeeArticleModel model)
        {
            var entity = new EmployeeArticle
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeArticle(entity);

            return result;
        }

        #endregion EmployeeArticle

        #region EmployeeClass

        /// <summary>
        /// 查询课题信息
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询课题信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeClassQuery> QueryEmployeeClass([FromQuery] EmployeeClassFilter filter)
        {
            var exps = this.NewExps<EmployeeClass>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeClass.Columns.CreateTime + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeClassQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增课题信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增课题信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeClassModel> AddEmployeeClass([FromBody] EmployeeClassModel model)
        {
            var entity = model.Map<EmployeeClass>();

            var result = this.Repo.AddEmployeeClass(entity);

            return result.Map<EmployeeClassModel>();
        }

        /// <summary>
        /// 更新课题信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新课题信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeClassModel> UpdateEmployeeClass([FromBody] EmployeeClassModel model)
        {
            var entity = model.Map<EmployeeClass>();

            var result = this.Repo.UpdateEmployeeClass(entity);

            return result.Map<EmployeeClassModel>();
        }

        /// <summary>
        /// 删除课题信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除课题信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeClass([FromBody] EmployeeClassModel model)
        {
            var entity = new EmployeeClass
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeClass(entity);

            return result;
        }

        #endregion EmployeeClass

        #region EmployeePatent

        /// <summary>
        /// 查询专利信息
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询专利信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeePatentQuery> QueryEmployeePatent([FromQuery] EmployeePatentFilter filter)
        {
            var exps = this.NewExps<EmployeePatent>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeePatent.Columns.CreateTime + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeePatentQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增专利信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增专利信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeePatentModel> AddEmployeePatent([FromBody] EmployeePatentModel model)
        {
            var entity = model.Map<EmployeePatent>();

            var result = this.Repo.AddEmployeePatent(entity);

            return result.Map<EmployeePatentModel>();
        }

        /// <summary>
        /// 更新专利信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新专利信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeePatentModel> UpdateEmployeePatent([FromBody] EmployeePatentModel model)
        {
            var entity = model.Map<EmployeePatent>();

            var result = this.Repo.UpdateEmployeePatent(entity);

            return result.Map<EmployeePatentModel>();
        }

        /// <summary>
        /// 删除专利信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除专利信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeePatent([FromBody] EmployeePatentModel model)
        {
            var entity = new EmployeePatent
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeePatent(entity);

            return result;
        }

        #endregion EmployeePatent

        #region EmployeeTeacher

        /// <summary>
        /// 查询导师信息
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询导师信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeTeacherQuery> QueryEmployeeTeacher([FromQuery] EmployeeTeacherFilter filter)
        {
            var exps = this.NewExps<EmployeeTeacher>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeTeacher.Columns.CreateTime + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeTeacherQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增导师信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增导师信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeTeacherModel> AddEmployeeTeacher([FromBody] EmployeeTeacherModel model)
        {
            var entity = model.Map<EmployeeTeacher>();

            var result = this.Repo.AddEmployeeTeacher(entity);

            return result.Map<EmployeeTeacherModel>();
        }

        /// <summary>
        /// 更新导师信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新导师信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeTeacherModel> UpdateEmployeeTeacher([FromBody] EmployeeTeacherModel model)
        {
            var entity = model.Map<EmployeeTeacher>();

            var result = this.Repo.UpdateEmployeeTeacher(entity);

            return result.Map<EmployeeTeacherModel>();
        }

        /// <summary>
        /// 删除导师信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除导师信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeTeacher([FromBody] EmployeeTeacherModel model)
        {
            var entity = new EmployeeTeacher
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeTeacher(entity);

            return result;
        }

        #endregion EmployeeTeacher

        #region EmployeeAward

        /// <summary>
        /// 查询获奖信息
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询获奖信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<EmployeeAwardQuery> QueryEmployeeAward([FromQuery] EmployeeAwardFilter filter)
        {
            var exps = this.NewExps<EmployeeAward>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeAward.Columns.CreateTime + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeAwardQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 新增获奖信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增获奖信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeAwardModel> AddEmployeeAward([FromBody] EmployeeAwardModel model)
        {
            var entity = model.Map<EmployeeAward>();

            var result = this.Repo.AddEmployeeAward(entity);

            return result.Map<EmployeeAwardModel>();
        }

        /// <summary>
        /// 更新获奖信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新获奖信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<EmployeeAwardModel> UpdateEmployeeAward([FromBody] EmployeeAwardModel model)
        {
            var entity = model.Map<EmployeeAward>();

            var result = this.Repo.UpdateEmployeeAward(entity);

            return result.Map<EmployeeAwardModel>();
        }

        /// <summary>
        /// 删除获奖信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除获奖信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeAward([FromBody] EmployeeAwardModel model)
        {
            var entity = new EmployeeAward
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeAward(entity);

            return result;
        }

        #endregion EmployeeAward

        #region 下拉列表

        /// <summary>
        /// 查询员工状态
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工状态")]
        public QueryResult<DictQuery> QueryEmployeeStatus()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.EmployeeStatusCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询员工聘任职别
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工聘任职别")]
        public QueryResult<DictQuery> QueryRank()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.RankCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询员工职别
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工职别")]
        public QueryResult<DictQuery> QueryOfficialRank()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.OfficialRankCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询员工在职方式
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工在职方式")]
        public QueryResult<DictQuery> QueryHireStyle()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.HireStyleCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询员工离职方式
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工离职方式")]
        public QueryResult<DictQuery> QueryLeaveStyle()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.LeaveStyleCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询婚姻情况
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询婚姻情况")]
        public QueryResult<DictQuery> QueryMarryList()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.MarryCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询民族
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询民族")]
        public QueryResult<DictQuery> QueryNationality()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.NationalityCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询户口类型
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询户口类型")]
        public QueryResult<DictQuery> QueryRegisterType()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.RegisterTypeCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询学位
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询学位")]
        public QueryResult<DictQuery> QueryDegrees()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.DegreesCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询社保
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询社保")]
        public QueryResult<DictQuery> QuerySocialSecurity()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.SocialSecurityTypeCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询学历
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询学历")]
        public QueryResult<DictQuery> QueryEducation()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.EducationCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询毕业情况
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询毕业情况")]
        public QueryResult<DictQuery> QueryGraduation()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.GraduationCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询学习方式
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询学习方式")]
        public QueryResult<DictQuery> QueryLearnWay()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.LearnWayCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询政治面貌
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询政治面貌")]
        public QueryResult<DictQuery> QueryParty()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.PartyCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询招募来源
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询招募来源")]
        public QueryResult<DictQuery> QueryRecruitmentCategory()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.RecruitmentCategoryCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询招募公司
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询招募公司")]
        public QueryResult<DictQuery> QueryRecruitmentCompany()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.RecruitmentCompanyCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询职称级别
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询职称级别")]
        public QueryResult<DictQuery> QueryLevel()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.LevelCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询出国类别
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询出国类别")]
        public QueryResult<DictQuery> QueryAbroadType()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.AbroadTypeCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询合同类型
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询合同类型")]
        public QueryResult<DictQuery> QueryContractType()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.ContractTypeCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询培养计划级别
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询培养计划级别")]
        public QueryResult<DictQuery> QueryTrainLevel()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.TrainLevelCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询考核结果
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询考核结果")]
        public QueryResult<DictQuery> QueryEvaluateResult()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.EvaluateResultCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询考核年度
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询考核年度")]
        public QueryResult<DictQuery> QueryYearList()
        {
            List<DictQuery> list = new List<DictQuery>();
            for (int year = Dicts.StartYear; year <= Dicts.EndYear; year++)
            {
                DictQuery dict = new DictQuery() { ID = Guid.NewGuid(), Code = year.ToString(), Name = year.ToString() };
                list.Add(dict);
            }
            var result = this.QueryResult(list);
            return result;
        }

        /// <summary>
        /// 查询奖惩类型
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询奖惩类型")]
        public QueryResult<DictQuery> QueryIncentType()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.IncentTypeCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询奖惩级别
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询奖惩级别")]
        public QueryResult<DictQuery> QueryIncentLevel()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.IncentLevelCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询医疗事故性质
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询医疗事故性质")]
        public QueryResult<DictQuery> QueryAccidentType()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.AccidentTypeCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询论文收录情况
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询论文收录情况")]
        public QueryResult<DictQuery> QueryIncomeType()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.IncomeTypeCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询课题级别
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询课题级别")]
        public QueryResult<DictQuery> QueryClassLevel()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.ClassLevelCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询博硕导
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询博硕导")]
        public QueryResult<DictQuery> QueryTeacherType()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.TeacherTypeCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询获奖级别
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询获奖级别")]
        public QueryResult<DictQuery> QueryAwardLevel()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.AwardLevelCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 根据ParentCode 查询字典
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "根据ParentCode 查询字典")]
        public QueryResult<DictQuery> QueryDictByParentCode([FromQuery, Required] string code)
        {
            var entities = this.Repo.QueryDictByParentCode(code);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询职位
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询职位")]
        public BizResult<List<StationQuery>> QueryStation([FromQuery] int type)
        {
            //// 职务=1/职称=2
            var entities = this.Repo.GetEntities<Station>();

            var all = entities.Maps<StationQuery>();

            foreach (var model in all)
            {
                var childern = all.Where(p => p.ParentId == model.ID).OrderBy(p => p.Number).ToList();
                if (childern != null && childern.Any())
                {
                    model.Children = childern;
                }
            }

            var models = all
                .Where(p => !p.ParentId.HasValue && ((type == 1 && true == p.IsPost) || (type == 2 && true == p.IsTitle)))
                .OrderBy(p => p.Number)
                .ToList();

            return this.BizResult(models);
        }

        /// <summary>
        /// 查询岗位
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询岗位")]
        public BizResult<List<PositionStationQuery>> QueryPositionStation([FromQuery, Required] Guid stationID)
        {
            var entities = this.Repo.GetEntities<PositionStation>(p => p.StationId == stationID);

            var all = entities.Maps<PositionStationQuery>();

            var models = all
                .OrderBy(p => p.Ordinal)
                .ToList();

            return this.BizResult(models);
        }

        /// <summary>
        /// 查询工资标准类型
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询工资标准类型")]
        public BizResult<List<PayRollOrgClassQuery>> QueryPayRollOrgClass()
        {
            var entity = this.Repo.GetEntities<PayRollOrgClass>();
            var all = entity.Maps<PayRollOrgClassQuery>();

            return this.BizResult(all);
        }

        /// <summary>
        /// 查询工资组
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询工资组")]
        public BizResult<List<PayRollCompGroupQuery>> QueryPayRollCompGroup()
        {
            var entity = this.Repo.GetEntities<PayRollCompGroup>();
            var all = entity.Maps<PayRollCompGroupQuery>();

            return this.BizResult(all);
        }

        /// <summary>
        /// 查询岗位工资级别
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询岗位工资级别")]
        public BizResult<List<PayRollOrgSalaryQuery>> QueryPayRollOrgSalary([FromQuery] Guid condition)
        {
            var entity = new List<PayRollOrgSalary>();
            if (condition == Guid.Empty)
            {
                entity = this.Repo.GetEntities<PayRollOrgSalary>();
            }
            else
            {
                entity = this.Repo.GetEntities<PayRollOrgSalary>(x => x.OrgClassId == condition);
            }

            var all = entity.Maps<PayRollOrgSalaryQuery>();
            return this.BizResult(all);
        }

        /// <summary>
        /// 查询薪级工资级别
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询薪级工资级别")]
        public BizResult<List<PayRollOrgSalaryLevelQuery>> QueryPayRollOrgSalaryLevel([FromQuery] Guid condition)
        {
            var entity = new List<PayRollOrgSalaryLevel>();
            if (condition == Guid.Empty)
            {
                entity = this.Repo.GetEntities<PayRollOrgSalaryLevel>();
            }
            else
            {
                entity = this.Repo.GetEntities<PayRollOrgSalaryLevel>(x => x.OrgClassId == condition);
            }

            var all = entity.Maps<PayRollOrgSalaryLevelQuery>();

            return this.BizResult(all);
        }

        /// <summary>
        /// 查询职务工资级别
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询职务工资级别")]
        public BizResult<List<PayRollOrgPositionSalarysQuery>> QueryPayRollOrgPositionSalarys([FromQuery] Guid condition)
        {
            var entity = new List<PayRollOrgPositionSalarys>();
            if (condition == Guid.Empty)
            {
                entity = this.Repo.GetEntities<PayRollOrgPositionSalarys>();
            }
            else
            {
                entity = this.Repo.GetEntities<PayRollOrgPositionSalarys>(x => x.OrgClassId == condition);
            }

            var all = entity.Maps<PayRollOrgPositionSalarysQuery>();

            return this.BizResult(all);
        }

        /// <summary>
        /// 查询出勤状态
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询出勤状态")]
        public QueryResult<DictQuery> QueryWorkState()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.WorkState);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询证件类型
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询证件类型")]
        public QueryResult<DictQuery> QueryDocumentType()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.DocumentType);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询高层次人才类别
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询高层次人才类别")]
        public QueryResult<DictQuery> QueryHighTalent()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.HighTalentType);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询行政职务
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询行政职务")]
        public QueryResult<DictQuery> QueryAdministrativePosition()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.AdministrativePosition);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        #endregion 下拉列表

        #region AddEmployeeHighTalent

        /// <summary>
        /// 新增高层次人才信息
        /// </summary>
        /// <param name="model"></param>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增高层次人才信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult AddEmployeeHighTalent([FromBody] EmployeeHighTalentModel model)
        {
            var entity = model.Map<EmployeeHighTalent>();

            var result = this.Repo.AddEmployeeHighTalent(entity);

            return result.Map<EmployeeHighTalentModel>();
        }

        /// <summary>
        /// 查询高层次人才信息
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询高层次人才信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult queryEmployeeHighTalent([FromQuery] EmployeeHighTalentFilter filter)
        {
            var exps = this.NewExps<EmployeeHighTalent>();
            exps.And(x => x.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = EmployeeHighTalent.Columns.CreateTime + GeneralConsts.OrderByDesc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeHighTalentQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 删除高层次人才信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "删除高层次人才信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult DeleteEmployeeHighTalent([FromBody] EmployeeHighTalentModel model)
        {
            var entity = new EmployeeHighTalent { ID = model.ID };

            var result = this.Repo.DeleteEmployeeHighTalent(entity);

            return result;
        }

        /// <summary>
        /// 修改高层次人才信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "修改高层次人才信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult UpdateEmployeeHighTalent([FromBody] EmployeeHighTalentModel model)
        {
            var entity = model.Map<EmployeeHighTalent>();

            var result = this.Repo.updateEmployeeHighTalent(entity);

            return result.Map<EmployeeHighTalentModel>();
        }

        #endregion AddEmployeeHighTalent

        #region MajorTechnical

        /// <summary>
        /// 查询专业技术职称
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询专业技术职称")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public BizResult<List<MajorTechnicalQuery>> QueryMajorTechnical()
        {
            var entities = this.Repo.GetEntities<MajorTechnical>();

            var all = entities.Maps<MajorTechnicalQuery>();

            foreach (var model in all)
            {
                var childern = all.Where(p => p.ParentId == model.ID).OrderBy(p => p.Ordinal).ToList();
                if (childern != null && childern.Any())
                {
                    model.Children = childern;
                }
            }

            var models = all
                .Where(p => !p.ParentId.HasValue)
                .OrderBy(p => p.Ordinal)
                .ToList();

            return this.BizResult(models);
        }

        #endregion MajorTechnical

        #region EmployeeDeptHistory

        /// <summary>
        /// 查询部门变更信息
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询部门变更信息")]
        public QueryResult<EmployeeDeptHistoryQuery> QueryEmployeeDeptHistory([FromQuery] EmployeeDeptHistoryFilter filter)
        {
            var exps = this.NewExps<Entities.EmployeeDeptHistory>();
            exps.And(p => p.EmployeeId == filter.EmployeeId);

            if (filter.Order.IsEmpty())
            {
                filter.Order = Entities.EmployeeDeptHistory.Columns.CreateTime + GeneralConsts.OrderByAsc;
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeDeptHistoryQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 更新员工部门变更
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新员工部门变更")]
        public BizResult<EmployeeDeptHistoryModel> UpdateEmployeeDept([FromBody] EmployeeDeptHistoryModel model)
        {
            var entity = model.Map<Entities.EmployeeDeptHistory>();

            var result = this.Repo.UpdateEmployeeDept(entity);

            return result.Map<EmployeeDeptHistoryModel>();
        }

        /// <summary>
        /// 批量更新员工部门
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "批量更新员工部门")]
        public BizResult<EmployeeDeptHistoryModel> BatchUpdateEmployeeDept([FromBody] EmployeeDeptHistoryModel model)
        {
            var entity = model.Map<Entities.EmployeeDeptHistory>();
            List<int> uids = new List<int>();

            var result = this.Repo.BatchUpdateEmployeeDept(entity, model.Ids, ref uids);

            if (result.Succeed)
            {
                ////同步更新oa接口
                foreach (var uid in uids)
                {
                    this.GetPersonnelInformation(uid);
                }
            }

            return result.Map<EmployeeDeptHistoryModel>();
        }

        #endregion EmployeeDeptHistory

        #region EmployeeList

        /// <summary>
        /// 查询高级查询树
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询高级查询树")]
        [Permission(Permissions.HRManage.EmpRoster)]
        public BizResult<List<EmployeeListQuery>> QueryEmployeeListTree()
        {
            var entities = this.Repo.GetEntities<EmployeeList>();

            var all = entities.Maps<EmployeeListQuery>();

            var queryTypes = this.Repo.QueryDictByParentCode(Dicts.AdvancedQueryTypeCode);

            List<EmployeeListQuery> tree = new List<EmployeeListQuery>();

            EmployeeListQuery root = new EmployeeListQuery();
            root.ID = Guid.NewGuid();
            root.ListName = Dicts.AdvancedQueryTypeRootName;
            root.Children = new List<EmployeeListQuery>();

            foreach (var item in queryTypes)
            {
                EmployeeListQuery type = new EmployeeListQuery();
                type.ID = item.ID;
                type.ParentId = root.ID;
                type.ListName = item.Name;
                var childern = all.Where(p => p.QueryTypeId == item.ID).ToList();
                if (childern != null && childern.Any())
                {
                    type.Children = childern;
                    foreach (var model in childern)
                    {
                        model.ParentId = item.ID;
                    }
                }
                root.Children.Add(type);
            }
            tree.Add(root);

            return this.BizResult(tree);
        }

        /// <summary>
        /// 查询员工高级查询
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "查询员工高级查询")]
        [Permission(Permissions.HRManage.EmpRoster)]
        public EmployeeAdvancedModel QueryEmployeeList([FromBody] EmployeeListFilter filter)
        {
            var result = new EmployeeAdvancedModel();

            var dbEntity = this.Repo.Get<EmployeeList>(filter.ID);

            if (dbEntity == null)
            {
                result.Success = false;
                result.Message = "查询记录不存在";
            }
            else
            {
                string sql = Config.IsDM ? dbEntity.DMExpression ?? "" : dbEntity.Expression ?? "";
                if (filter.ConditionList != null && filter.ConditionList.Any())
                {
                    var condition = new Common.DynamicQuery().GetSqlQuery(filter.ConditionList);
                    if (!string.IsNullOrEmpty(condition))
                    {
                        sql += condition;
                    }
                }

                //var reader = this.Repo.QueryEmployeeListReader(sql);
                //var listdata = Utility.ConvertDataReaderToList(reader);

                var data = this.Repo.QueryEmployeeList(sql).Tables[0];
                var listdata = Utility.ConvertToList(data);
                result.RecordCount = listdata.Count;
                result.PageIndex = filter.PageIndex;
                result.PageSize = filter.PageSize;
                if (filter.PageIndex > 0 && listdata.Count > filter.PageSize)
                    listdata = listdata.AsQueryable().Skip((filter.PageIndex - 1) * filter.PageSize).Take(filter.PageSize).ToList();
                result.TableData = listdata;
                result.TableHead = GetHeadData(dbEntity.Fields ?? "", dbEntity.FieldsName ?? "");
                result.DataColumns = GetDataColumns(dbEntity.Fields ?? "", dbEntity.FieldsName ?? "", dbEntity.FieldsType ?? "");
                result.Success = true;
            }

            return result;
        }

        /// <summary>
        /// 导出员工高级查询
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [Permission(Permissions.HRManage.EmpRoster)]
        public IActionResult GetEmployeeListExcel([FromQuery] EmployeeListFilter filter)
        {
            var result = new EmployeeAdvancedModel();

            var dbEntity = this.Repo.Get<EmployeeList>(filter.ID);

            string sql = Config.IsDM ? dbEntity?.DMExpression ?? "" : dbEntity?.Expression ?? "";

            if (filter.ConditionList != null && filter.ConditionList.Any())
            {
                var condition = new Common.DynamicQuery().GetSqlQuery(filter.ConditionList);
                if (!string.IsNullOrEmpty(condition))
                {
                    sql += condition;
                }
            }
            var dt = this.Repo.QueryEmployeeList(sql).Tables[0];
            var fieldNames = (dbEntity?.FieldsName).GetValueOrDefault().Split(EmployeeLists.Tag_Split);
            var fieldtypes = (dbEntity?.FieldsType).GetValueOrDefault().Split(EmployeeLists.Tag_Split);

            if (dt.Columns.Count > fieldNames.Length)
            {
                dt.Columns.RemoveAt(0);
            }
            for (int i = 0; i < fieldNames.Length; i++)
            {
                dt.Columns[i].Caption = fieldNames[i];
                dt.Columns[i].Namespace = fieldtypes[i];
            }

            string exportFileName = $"{AppDomain.CurrentDomain.BaseDirectory}" + string.Format(Common.Consts.Exports.FileExcel, Common.Consts.Exports.FileAdvancedSearch);//生成后存放的地址 和文件名

            var _NpoiExcelUtility = new NpoiExcelUtility();
            if (dt != null)
            {
                _NpoiExcelUtility.CreatExcelSheet(Common.Consts.Exports.FileAdvancedSearch, dt);//生成数据   这里需要注意  如果datatable没值会报错  所以要判断
            }
            var bytes = _NpoiExcelUtility.SaveExcelBytes();
            return File(bytes, Common.Consts.Exports.ContentType, Path.GetFileName(exportFileName));
        }

        /// <summary>
        /// 查询字典设置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询字典设置")]
        [Permission(Permissions.HRManage.EmpRoster)]
        public EmployeeAdvancedModel QueryDictsSetting()
        {
            var entities = this.Repo.GetEntities<Dict>();

            var result = new EmployeeAdvancedModel();

            //性别
            var items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.Sex);
            result.Gender = new List<DictSetting>();
            foreach (var item in items)
            {
                result.Gender.Add(new DictSetting()
                {
                    Id = item.Code.ToString(),
                    Name = item.Name
                });
            }

            //民族
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.NationalityCode);
            result.Nationality = new List<DictSetting>();
            foreach (var item in items)
            {
                result.Nationality.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //婚姻情况
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.MarryCode);
            result.Marry = new List<DictSetting>();
            foreach (var item in items)
            {
                result.Marry.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //户口类型
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.RegisterTypeCode);
            result.RegisterType = new List<DictSetting>();
            foreach (var item in items)
            {
                result.RegisterType.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //最高学位 学位
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.DegreesCode);
            result.Degree = new List<DictSetting>();
            foreach (var item in items)
            {
                result.Degree.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //最高学历 学历
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.EducationCode);
            result.Education = new List<DictSetting>();
            foreach (var item in items)
            {
                result.Education.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //政治面貌
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.PartyCode);
            result.Party = new List<DictSetting>();
            foreach (var item in items)
            {
                result.Party.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //员工状态
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.EmployeeStatusCode);
            result.EmployeeStatus = new List<DictSetting>();
            foreach (var item in items)
            {
                result.EmployeeStatus.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //在职方式
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.HireStyleCode);
            result.HireStyle = new List<DictSetting>();
            foreach (var item in items)
            {
                result.HireStyle.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //离职方式
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.LeaveStyleCode);
            result.LeaveStyle = new List<DictSetting>();
            foreach (var item in items)
            {
                result.LeaveStyle.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //招募类别
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.RecruitmentCategoryCode);
            result.RecruitmentCategory = new List<DictSetting>();
            foreach (var item in items)
            {
                result.RecruitmentCategory.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //招募公司
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.RecruitmentCompanyCode);
            result.RecruitmentCompany = new List<DictSetting>();
            foreach (var item in items)
            {
                result.RecruitmentCompany.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //职别
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.OfficialRankCode);
            result.OfficialRank = new List<DictSetting>();
            foreach (var item in items)
            {
                result.OfficialRank.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //聘任职别
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.RankCode);
            result.Rank = new List<DictSetting>();
            foreach (var item in items)
            {
                result.Rank.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //职称资格级别
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.LevelCode);
            result.Level = new List<DictSetting>();
            foreach (var item in items)
            {
                result.Level.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //毕业情况
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.GraduationCode);
            result.Graduation = new List<DictSetting>();
            foreach (var item in items)
            {
                result.Graduation.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //学习形式
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.LearnWayCode);
            result.LearnWay = new List<DictSetting>();
            foreach (var item in items)
            {
                result.LearnWay.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //出国类别
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.AbroadTypeCode);
            result.AbroadType = new List<DictSetting>();
            foreach (var item in items)
            {
                result.AbroadType.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //合同类型
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.ContractTypeCode);
            result.ContractType = new List<DictSetting>();
            foreach (var item in items)
            {
                result.ContractType.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //培养计划级别
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.TrainLevelCode);
            result.TrainLevel = new List<DictSetting>();
            foreach (var item in items)
            {
                result.TrainLevel.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //考核结果
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.EvaluateResultCode);
            result.EvaluateResult = new List<DictSetting>();
            foreach (var item in items)
            {
                result.EvaluateResult.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //奖惩级别
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.IncentLevelCode);
            result.IncentLevel = new List<DictSetting>();
            foreach (var item in items)
            {
                result.IncentLevel.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //奖惩类别
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.IncentTypeCode);
            result.IncentType = new List<DictSetting>();
            foreach (var item in items)
            {
                result.IncentType.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //医疗事故性质
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.AccidentTypeCode);
            result.AccidentType = new List<DictSetting>();
            foreach (var item in items)
            {
                result.AccidentType.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //收录情况
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.IncomeTypeCode);
            result.IncomeType = new List<DictSetting>();
            foreach (var item in items)
            {
                result.IncomeType.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //课题级别
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.ClassLevelCode);
            result.ClassLevel = new List<DictSetting>();
            foreach (var item in items)
            {
                result.ClassLevel.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //博硕导
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.TeacherTypeCode);
            result.TeacherType = new List<DictSetting>();
            foreach (var item in items)
            {
                result.TeacherType.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //获奖级别
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.AwardLevelCode);
            result.AwardLevel = new List<DictSetting>();
            foreach (var item in items)
            {
                result.AwardLevel.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            //社保类型
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.SocialSecurityTypeCode);
            result.SocialSecurityType = new List<DictSetting>();
            foreach (var item in items)
            {
                result.SocialSecurityType.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                }); ;
            }

            //出勤状态
            items = entities.Where(p => p.Parent != null && p.Parent.Code == Dicts.WorkState);
            result.WorkState = new List<DictSetting>();
            foreach (var item in items)
            {
                result.WorkState.Add(new DictSetting()
                {
                    Id = item.ID.ToString(),
                    Name = item.Name
                });
            }

            return result;
        }

        private List<QueryColumn> GetDataColumns(string field, string fieldsName, string fieldsType)
        {
            List<QueryColumn> dataColumns = new List<QueryColumn>();

            var fields = field.Split(EmployeeLists.Tag_Split);
            var fieldNames = fieldsName.Split(EmployeeLists.Tag_Split);
            var fieldTypes = fieldsType.Split(EmployeeLists.Tag_Split);

            for (int i = 0; i < fields.Length; i++)
            {
                if (!string.IsNullOrEmpty(fields[i]))
                {
                    dataColumns.Add(new QueryColumn()
                    {
                        Label = fieldNames[i],
                        Type = fieldTypes[i],
                        Value = fields[i],
                        Field = fields[i].Substring(fields[i].IndexOf(EmployeeLists.Tag_Spot) + 1)
                    });
                }
            }
            return dataColumns;
        }

        private List<object> GetHeadData(string field, string fieldsName)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add(new DataColumn("displayName"));
            dt.Columns.Add(new DataColumn("propName"));

            var fields = field.Split(EmployeeLists.Tag_Split);
            var fieldNames = fieldsName.Split(EmployeeLists.Tag_Split);

            for (int i = 0; i < fields.Length; i++)
            {
                if (!string.IsNullOrEmpty(fields[i]))
                {
                    var newrow1 = dt.NewRow();
                    newrow1[0] = fieldNames[i];
                    newrow1[1] = fields[i].Substring(fields[i].IndexOf(EmployeeLists.Tag_Spot) + 1);
                    dt.Rows.Add(newrow1);
                }
            }
            return Utility.ConvertToList(dt);
        }

        /// <summary>
        /// 查询高级设置查询树
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询高级设置查询树")]
        [Permission(Permissions.HRManage.EmpRoster)]
        public BizResult<List<QueryColumnInfoQuery>> QueryEmployeeListSettingTree()
        {
            var entities = this.Repo.GetEntities<QueryColumnInfo>();

            var all = entities.Maps<QueryColumnInfoQuery>();

            var tables = this.Repo.GetEntities<QueryTableInfo>("Sort");

            List<QueryColumnInfoQuery> tree = new List<QueryColumnInfoQuery>();

            foreach (var table in tables)
            {
                QueryColumnInfoQuery type = new QueryColumnInfoQuery();
                type.ID = table.ID;
                type.TableName = table.TableName;
                type.ColumnDesc = table.TableDesc;
                var childern = all.Where(p => p.TableId == table.ID && p.IsPKey == false).OrderBy(c => c.Sort).ToList();
                if (childern != null && childern.Any())
                {
                    type.Children = childern;
                    foreach (var model in childern)
                    {
                        model.ParentId = table.ID;
                        model.TableDesc = table.TableDesc.GetValueOrDefault();
                    }
                }
                tree.Add(type);
            }

            return this.BizResult(tree);
        }

        /// <summary>
        /// 查询高级查询类型
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询高级查询类型")]
        [Permission(Permissions.HRManage.EmpRoster)]
        public QueryResult<DictQuery> QueryAdvancedQueryType()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.AdvancedQueryTypeCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 保存高级查询信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "保存高级查询信息")]
        [Permission(Permissions.HRManage.EmpRoster)]
        public BizResult<EmployeeListModel> SaveEmployeeList([FromBody] EmployeeListModel model)
        {
            var entity = CreateEmployeeListByModel(model);
            var result = this.Repo.SaveEmployeeList(entity);

            return result.Map<EmployeeListModel>();
        }

        private EmployeeList CreateEmployeeListByModel(EmployeeListModel model)
        {
            EmployeeList entity = new EmployeeList();
            entity.ID = model.ID;
            entity.ListName = model.ListName;
            entity.QueryTypeId = model.QueryTypeId;

            List<string> tables = new List<string>();
            //exp sqlserver;dmExp 达梦
            string fields = "", DMfields = "", fieldsname = "", fieldstype = "", exp = EmployeeLists.SQL_Select, dmExp = EmployeeLists.SQL_Select;
            List<Guid> columnIds = new List<Guid>();
            var dictTuples = new List<Tuple<string?, string, string?>>();

            var dictByTextAlias = "dbt";
            var dbtNum = 0;
            var dictByCodeAlias = "dbc";
            var dbcNum = 0;
            var deptNameAlias = "dn";
            var dnNum = 0;
            var compGroupNameAlias = "cgn";
            var cgnNum = 0;
            var orgClassNameAlias = "ocn";
            var ocnNum = 0;
            var orgSalaryNameAlias = "osn";
            var osnNum = 0;
            var orgSalaryLevelNameAlias = "osln";
            var oslnNum = 0;
            var orgPositionSalaryNameAlias = "opsn";
            var opsnNum = 0;

            //代码检测时，注释下面这段代码

            foreach (var column in model.ColumnInfos)
            {
                if (columnIds.IndexOf(column.ID) >= 0)
                {
                    continue;
                }
                columnIds.Add(column.ID);

                //employee id
                if (column.TableName?.Equals(EmployeeLists.Employee_Table_Name, StringComparison.OrdinalIgnoreCase) == true && !dmExp.Contains(EmployeeLists.DM_Employee_PK_Name))
                {
                    dmExp += EmployeeLists.DM_Employee_PK_Name + EmployeeLists.Tag_Comma;
                }
                if (column.TableName?.Equals(EmployeeLists.Employee_Table_Name, StringComparison.OrdinalIgnoreCase) == true && !exp.Contains(EmployeeLists.Employee_PK_Name))
                {
                    exp += EmployeeLists.Employee_PK_Name + EmployeeLists.Tag_Comma;
                }

                //sqlserver字段
                fields += column.TableName + EmployeeLists.Tag_Spot + column.ColumnName + EmployeeLists.Tag_Split;
                //达梦字段
                DMfields += EmployeeLists.DM_Tag_QuotationMark + column.TableName + EmployeeLists.DM_Tag_QuotationMark + EmployeeLists.Tag_Spot + EmployeeLists.DM_Tag_QuotationMark + column.ColumnName + EmployeeLists.DM_Tag_QuotationMark + EmployeeLists.Tag_Split;
                fieldsname += column.ColumnDesc + EmployeeLists.Tag_Split;
                fieldstype += $"{column.DataType}" + EmployeeLists.Tag_Split;


                int idataType = int.Parse(column.DataType ?? "");
                if ((FieldType)idataType == FieldType.Date)
                {
                    exp += string.Format(EmployeeLists.DateFormat, column.TableName, column.ColumnName) + EmployeeLists.Tag_Comma;
                    dmExp += string.Format(EmployeeLists.DM_DateFormat, column.TableName, column.ColumnName) + EmployeeLists.Tag_Comma;
                }
                else if ((FieldType)idataType == FieldType.Dict)
                {
                    if (column.ColumnName.AsTrim() == EmployeeLists.Column_Gender.Trim())
                    {
                        exp += string.Format(EmployeeLists.DictFormat_Gender, column.TableName, column.ColumnName) + EmployeeLists.Tag_Comma;

                        //达梦函数改为联表查询
                        dbcNum++;
                        dictTuples.Add(Tuple.Create(column.TableName, dictByCodeAlias + dbcNum.ToString(), column.ColumnName));
                        dmExp += dictByCodeAlias + dbcNum.ToString() + EmployeeLists.Tag_Spot + EmployeeLists.DM_Column_Name + " as \"" + column.ColumnName + "\"" + EmployeeLists.Tag_Comma;
                    }
                    else
                    {
                        exp += string.Format(EmployeeLists.DictFormat, column.TableName, column.ColumnName) + EmployeeLists.Tag_Comma;

                        dbtNum++;
                        dictTuples.Add(Tuple.Create(column.TableName, dictByTextAlias + dbtNum.ToString(), column.ColumnName));
                        dmExp += dictByTextAlias + dbtNum.ToString() + EmployeeLists.Tag_Spot + EmployeeLists.DM_Column_Name + " as \"" + column.ColumnName + "\"" + EmployeeLists.Tag_Comma;
                    }
                }
                else if ((FieldType)idataType == FieldType.Dept)
                {
                    exp += string.Format(EmployeeLists.DeptFormat, column.TableName, column.ColumnName) + EmployeeLists.Tag_Comma;

                    dnNum++;
                    dictTuples.Add(Tuple.Create(column.TableName, deptNameAlias + dnNum.ToString(), column.ColumnName));
                    dmExp += deptNameAlias + dnNum.ToString() + EmployeeLists.Tag_Spot + EmployeeLists.DM_Column_Name + " as \"" + column.ColumnName + "\"" + EmployeeLists.Tag_Comma;
                }
                else if ((FieldType)idataType == FieldType.OrgClass)
                {
                    exp += string.Format(EmployeeLists.OrgClassFormat, column.TableName, column.ColumnName) + EmployeeLists.Tag_Comma;

                    ocnNum++;
                    dictTuples.Add(Tuple.Create(column.TableName, orgClassNameAlias + ocnNum.ToString(), column.ColumnName));
                    dmExp += orgClassNameAlias + ocnNum.ToString() + EmployeeLists.Tag_Spot + EmployeeLists.DM_Column_Name + " as \"" + column.ColumnName + "\"" + EmployeeLists.Tag_Comma;
                }
                else if ((FieldType)idataType == FieldType.CompGroup)
                {
                    exp += string.Format(EmployeeLists.CompGroupFormat, column.TableName, column.ColumnName) + EmployeeLists.Tag_Comma;

                    cgnNum++;
                    dictTuples.Add(Tuple.Create(column.TableName, compGroupNameAlias + cgnNum.ToString(), column.ColumnName));
                    dmExp += compGroupNameAlias + cgnNum.ToString() + EmployeeLists.Tag_Spot + "\"CompGroupName\"" + EmployeeLists.Tag_Comma;
                }
                else if ((FieldType)idataType == FieldType.OrgSalary)
                {
                    exp += string.Format(EmployeeLists.OrgSalaryFormat, column.TableName, column.ColumnName) + EmployeeLists.Tag_Comma;

                    osnNum++;
                    dictTuples.Add(Tuple.Create(column.TableName, orgSalaryNameAlias + osnNum.ToString(), column.ColumnName));
                    dmExp += orgSalaryNameAlias + osnNum.ToString() + EmployeeLists.Tag_Spot + EmployeeLists.DM_Column_Name + " as \"" + column.ColumnName + "\"" + EmployeeLists.Tag_Comma;
                }
                else if ((FieldType)idataType == FieldType.OrgSalaryLevel)
                {
                    exp += string.Format(EmployeeLists.OrgSalaryLevelFormat, column.TableName, column.ColumnName) + EmployeeLists.Tag_Comma;

                    oslnNum++;
                    dictTuples.Add(Tuple.Create(column.TableName, orgSalaryLevelNameAlias + oslnNum.ToString(), column.ColumnName));
                    dmExp += orgSalaryLevelNameAlias + oslnNum.ToString() + EmployeeLists.Tag_Spot + EmployeeLists.DM_Column_Name + " as \"" + column.ColumnName + "\"" + EmployeeLists.Tag_Comma;
                }
                else if ((FieldType)idataType == FieldType.OrgPositionSalary)
                {
                    exp += string.Format(EmployeeLists.OrgPositionSalaryFormat, column.TableName, column.ColumnName) + EmployeeLists.Tag_Comma;

                    opsnNum++;
                    dictTuples.Add(Tuple.Create(column.TableName, orgPositionSalaryNameAlias + opsnNum.ToString(), column.ColumnName));
                    dmExp += orgPositionSalaryNameAlias + opsnNum.ToString() + EmployeeLists.Tag_Spot + EmployeeLists.DM_Column_Name + " as \"" + column.ColumnName + "\"" + EmployeeLists.Tag_Comma;
                }
                else
                {
                    exp += column.TableName + EmployeeLists.Tag_Spot + column.ColumnName + EmployeeLists.Tag_Comma;
                    dmExp += EmployeeLists.DM_SQL_Schema + EmployeeLists.DM_Tag_QuotationMark + column.TableName + EmployeeLists.DM_Tag_QuotationMark + EmployeeLists.Tag_Spot + EmployeeLists.DM_Tag_QuotationMark + column.ColumnName + EmployeeLists.DM_Tag_QuotationMark + EmployeeLists.Tag_Comma;
                }


                if (tables.IndexOf(column.TableName ?? "") < 0)
                    tables.Add(column.TableName ?? "");
            }
            if (fields.Length > 0)
                fields = fields.Substring(0, fields.Length - 1);
            if (DMfields.Length > 0)
                DMfields = DMfields.Substring(0, DMfields.Length - 1);
            if (fieldsname.Length > 0)
                fieldsname = fieldsname.Substring(0, fieldsname.Length - 1);
            if (fieldstype.Length > 0)
                fieldstype = fieldstype.Substring(0, fieldstype.Length - 1);
            if (exp.Length > 0)
                exp = exp.Substring(0, exp.Length - 1);
            if (dmExp.Length > 0)
                dmExp = dmExp.Substring(0, dmExp.Length - 1);

            entity.Fields = fields;
            entity.DMFields = DMfields;
            entity.FieldsName = fieldsname;
            entity.FieldsType = fieldstype;

            var columninfos = this.Repo.GetEntities<QueryColumnInfo>();
            var tableinfos = this.Repo.GetEntities<QueryTableInfo>("Sort");
            var dicts = this.Repo.GetEntities<Dict>();
            var depts = this.Repo.GetEntities<Department>();

            var strCon = "";
            var dmStrCon = "";
            if (!string.IsNullOrEmpty(model.Condition))
            {
                entity.Condition = model.Condition;
                strCon = model.Condition;
                foreach (var tab in tableinfos)
                {
                    List<QueryColumnInfo> tabColumnsInfo = columninfos.Where(c => c.TableId == tab.ID && c.IsPKey == false).OrderByDescending(c => c.Sort).ToList();
                    var strCons = BuildCondition(strCon, dmStrCon, tab, tabColumnsInfo, dicts, tables, depts);
                    strCon = strCons.Item1;
                    dmStrCon = strCons.Item2;
                }
            }
            else
            {
                entity.Condition = "";
            }

            exp += EmployeeLists.SQL_From;
            dmExp += EmployeeLists.SQL_From;
            string table1 = "", table1Key = "", table2 = "", table2Key = "";
            string relation = "";
            string validstr = "";
            string dmValidstr = "";
            for (int i = 0; i <= tables.Count - 1; i++)
            {
                var table = tables[i];

                if (i == 0)
                {
                    exp += table;
                    dmExp += EmployeeLists.DM_SQL_Schema + EmployeeLists.DM_Tag_QuotationMark + table + EmployeeLists.DM_Tag_QuotationMark;

                    table1 = table;
                    var tablePk = columninfos.Where(c => c.TableName == table1 && c.IsPKey == true).FirstOrDefault();
                    if (tablePk != null)
                    {
                        table1Key = tablePk.ColumnName ?? "";
                    }
                }
                else
                {
                    table2 = table;
                    var tablePk = columninfos.Where(c => c.TableName == table2 && c.IsPKey == true).FirstOrDefault();
                    if (tablePk != null)
                    {
                        table2Key = tablePk.ColumnName ?? "";
                    }
                    if (!string.IsNullOrEmpty(table1Key) && !string.IsNullOrEmpty(table2Key))
                    {
                        if (table == "ViewEmployeeHRDict")
                        {
                            exp += EmployeeLists.SQL_LEFTJOIN + table + " on" + string.Format(EmployeeLists.RelationFormat, table1, table1Key, table2, table2Key);
                            dmExp += EmployeeLists.SQL_LEFTJOIN + EmployeeLists.DM_SQL_Schema + EmployeeLists.DM_Tag_QuotationMark + table + EmployeeLists.DM_Tag_QuotationMark + " on" + string.Format(EmployeeLists.DM_RelationFormat, table1, table1Key, table2, table2Key);
                        }
                        else
                        {
                            exp += EmployeeLists.SQL_INNERJOIN + table + " on" + string.Format(EmployeeLists.RelationFormat, table1, table1Key, table2, table2Key);
                            dmExp += EmployeeLists.SQL_INNERJOIN + EmployeeLists.DM_SQL_Schema + EmployeeLists.DM_Tag_QuotationMark + table + EmployeeLists.DM_Tag_QuotationMark + " on" + string.Format(EmployeeLists.DM_RelationFormat, table1, table1Key, table2, table2Key);
                        }
                    }
                }

                if (!table.StartsWith(EmployeeLists.SQL_View))
                {
                    validstr += EmployeeLists.SQL_And;
                    dmValidstr += EmployeeLists.SQL_And;

                    validstr += string.Format(EmployeeLists.ValidFormat, table);
                    dmValidstr += string.Format(EmployeeLists.DM_ValidFormat, table);
                }
            }

            foreach (var dict in dictTuples)
            {
                if (dict.Item2.StartsWith(dictByTextAlias))
                {
                    dmExp += $@"LEFT JOIN ""JHR"".""Dict"" {dict.Item2} on {dict.Item2}.ID = {string.Format(EmployeeLists.DM_SQL_Table_Column, dict.Item1, dict.Item3)} and {dict.Item2}.""Deleted"" = 0 ";
                }
                else if (dict.Item2.StartsWith(dictByCodeAlias))
                {
                    dmExp += $@"LEFT JOIN (SELECT d1.ID, d1.""Code"",d1.""Name"" FROM ""JHR"".""Dict"" d1 JOIN ""JHR"".""Dict"" dp ON dp.ID = d1.""ParentId"" and d1.""Deleted"" = 0  and  dp.""Deleted"" = 0 WHERE dp.""Code"" = '00019') as {dict.Item2}
on {dict.Item2}.""Code"" = CAST(""JHR"".""Employee"".""EnumGender"" as VARCHAR)";
                }
                else if (dict.Item2.StartsWith(deptNameAlias))
                {
                    dmExp += $@"LEFT JOIN ""JHR"".""Department"" {dict.Item2} on {dict.Item2}.ID = {string.Format(EmployeeLists.DM_SQL_Table_Column, dict.Item1, dict.Item3)} and {dict.Item2}.""Deleted"" = 0 ";
                }
                else if (dict.Item2.StartsWith(compGroupNameAlias))
                {
                    dmExp += $@"LEFT JOIN ""JHR"".""PayRollCompGroup"" {dict.Item2} on {dict.Item2}.ID = {string.Format(EmployeeLists.DM_SQL_Table_Column, dict.Item1, dict.Item3)} and {dict.Item2}.""Deleted"" = 0 ";
                }
                else if (dict.Item2.StartsWith(orgClassNameAlias))
                {
                    dmExp += $@"LEFT JOIN ""JHR"".""PayRollOrgClass"" {dict.Item2} on {dict.Item2}.ID = {string.Format(EmployeeLists.DM_SQL_Table_Column, dict.Item1, dict.Item3)} and {dict.Item2}.""Deleted"" = 0 ";
                }
                else if (dict.Item2.StartsWith(orgSalaryNameAlias))
                {
                    dmExp += $@"LEFT JOIN ""JHR"".""PayRollOrgSalary"" {dict.Item2} on {dict.Item2}.ID = {string.Format(EmployeeLists.DM_SQL_Table_Column, dict.Item1, dict.Item3)} and {dict.Item2}.""Deleted"" = 0 ";
                }
                else if (dict.Item2.StartsWith(orgSalaryLevelNameAlias))
                {
                    dmExp += $@"LEFT JOIN ""JHR"".""PayRollOrgSalaryLevel"" {dict.Item2} on {dict.Item2}.ID = {string.Format(EmployeeLists.DM_SQL_Table_Column, dict.Item1, dict.Item3)} and {dict.Item2}.""Deleted"" = 0 ";
                }
                else if (dict.Item2.StartsWith(orgPositionSalaryNameAlias))
                {
                    dmExp += $@"LEFT JOIN ""JHR"".""PayRollOrgPositionSalarys"" {dict.Item2} on {dict.Item2}.ID = {string.Format(EmployeeLists.DM_SQL_Table_Column, dict.Item1, dict.Item3)} and {dict.Item2}.""Deleted"" = 0 ";
                }
            }

            exp += EmployeeLists.SQL_Where + EmployeeLists.SQL_WhereTrue;
            dmExp += EmployeeLists.SQL_Where + EmployeeLists.SQL_WhereTrue;
            if (relation.Length > EmployeeLists.SQL_And.Length)
            {
                exp += relation;
                dmExp += relation;
            }

            if (validstr.Length > EmployeeLists.SQL_And.Length)
            {
                exp += validstr;
                dmExp += dmValidstr;
            }
            if (!string.IsNullOrEmpty(model.Condition))
            {
                exp += strCon;
                dmExp += dmStrCon;
            }

            entity.DMExpression = dmExp;
            entity.Expression = exp;

            return entity;
        }

        private int SubstringCount(string str, string substring)
        {
            if (str.Contains(substring))
            {
                string strReplaced = str.Replace(substring, "");
                return (str.Length - strReplaced.Length) / substring.Length;
            }

            return 0;
        }

        private Tuple<string, string> BuildCondition(string strCon, string dmStrCon, QueryTableInfo tab, List<QueryColumnInfo> columnInfos, List<Dict> dicts, List<string> tables, IList<Department> depts)
        {
            //替换表名.列名和对应值
            string result = strCon;
            string dmResult = dmStrCon;

            foreach (var col in columnInfos.OrderByDescending(c => c.Sort))
            {
                if (col.IsPKey == true)
                {
                    continue;
                }
                string keyword = tab.TableDesc + EmployeeLists.Tag_Spot + col.ColumnDesc;

                var count = SubstringCount(result, keyword);

                for (int i = 0; i < count; i++)
                {
                    if (result.Contains(keyword))
                    {
                        int columnStart = result.IndexOf(keyword);
                        string part1 = result.Substring(0, columnStart);

                        string part2 = result.Substring(columnStart + keyword.Length);
                        string dmPart2 = "";
                        if (dmResult.IsEmpty())
                        {
                            dmPart2 = part2;
                        }
                        else
                        {
                            int columnDmStart = dmResult.IndexOf(keyword);
                            dmPart2 = dmResult.Substring(columnDmStart + keyword.Length);
                        }
                        if (col.DataType?.Equals(((int)FieldType.Bool).ToString()) == true || col.DataType?.Equals(((int)FieldType.Dict).ToString()) == true)
                        {
                            int columnendmax = part2.IndexOf("'");
                            string part25 = part2.Substring(0, columnendmax);
                            string part3 = part2.Substring(columnendmax);
                            string dmPart3 = dmPart2.Substring(columnendmax);

                            int valStart = part3.IndexOf("[");
                            int valEnd = part3.IndexOf("]");
                            int dmValStart = dmPart3.IndexOf("[");
                            int dmValEnd = dmPart3.IndexOf("]");

                            string sIn = part3.Substring(valStart + 1, valEnd - valStart - 1);
                            string sInVal = "";

                            if (col.DataType.Equals(((int)FieldType.Bool).ToString()))
                            {
                                if (sIn.Equals(EmployeeLists.No_Text))
                                {
                                    sInVal = EmployeeLists.No_Value;
                                }
                                else
                                {
                                    sInVal = EmployeeLists.Yes_Value;
                                }
                            }
                            else
                            {
                                var items = dicts.Where(p => p.Parent != null && p.Parent.Code == col.SqlAddition1 && p.Name == sIn).FirstOrDefault();
                                sInVal = col.SqlAddition1 == Dicts.Sex ? items?.Code ?? "" : items?.ID.ToString() ?? "";
                                //sInVal = items?.ID.ToString();
                            }

                            string part4 = part3.Substring(0, valStart);
                            string part5 = part3.Substring(valEnd + 1);
                            string dmPart4 = dmPart3.Substring(0, dmValStart);
                            string dmPart5 = dmPart3.Substring(dmValEnd + 1);

                            dmResult = part1 + EmployeeLists.DM_SQL_Schema + EmployeeLists.DM_Tag_QuotationMark + tab.TableName + EmployeeLists.DM_Tag_QuotationMark + EmployeeLists.Tag_Spot + EmployeeLists.DM_Tag_QuotationMark + col.ColumnName + EmployeeLists.DM_Tag_QuotationMark + part25 + dmPart4 + sInVal + dmPart5;
                            result = part1 + tab.TableName + EmployeeLists.Tag_Spot + col.ColumnName + part25 + part4 + sInVal + part5;

                        }
                        else if (col.DataType.AsTrim().Equals(((int)FieldType.Dept).ToString()))
                        {
                            //部门
                            int columnendmax = part2.IndexOf("'");
                            string part25 = part2.Substring(0, columnendmax);
                            string part3 = part2.Substring(columnendmax);
                            string dmPart3 = dmPart2.Substring(columnendmax);

                            int valStart = part3.IndexOf("](");
                            int valStart2 = part3.IndexOf("[");
                            int valEnd = part3.IndexOf(")");
                            int dmValStart = dmPart3.IndexOf("[");
                            int dmValEnd = dmPart3.IndexOf(")");

                            string sIn = part3.Substring(valStart + 2, valEnd - valStart - 2);

                            var dept = depts.Where(s => s.Uid.ToString() == sIn).FirstOrDefault();
                            var sInVal = dept?.ID.ToString() ?? "";

                            string part4 = part3.Substring(0, valStart2);
                            string part5 = part3.Substring(valEnd + 1);
                            string dmPart4 = dmPart3.Substring(0, dmValStart);
                            string dmPart5 = dmPart3.Substring(dmValEnd + 1);

                            dmResult = part1 + EmployeeLists.DM_SQL_Schema + EmployeeLists.DM_Tag_QuotationMark + tab.TableName + EmployeeLists.DM_Tag_QuotationMark + EmployeeLists.Tag_Spot + EmployeeLists.DM_Tag_QuotationMark + col.ColumnName + EmployeeLists.DM_Tag_QuotationMark + part25 + dmPart4 + sInVal + dmPart5;
                            result = part1 + tab.TableName + EmployeeLists.Tag_Spot + col.ColumnName + part25 + part4 + sInVal + part5;
                        }
                        else
                        {
                            //增加，党内职务
                            if ((col.TableName + EmployeeLists.Tag_Spot + col.ColumnName) == "ViewEmployeeHRDict.PartyPositionName")
                            {
                                List<string> dictIds = new List<string>();
                                var temp = part2.Trim().Replace("'[", string.Empty).Replace("]'", string.Empty);
                                var regexPostions = @"(?<=\()(.+?)(?=\))";
                                var matchs = Regex.Matches(temp, regexPostions);
                                if (matchs.Count > 0)
                                {
                                    if (matchs[0].Value.Contains(","))
                                    {
                                        foreach (var positionName in matchs[0].Value.Split(','))
                                        {
                                            var dictEneity = dicts.Where(x => x.Name == positionName.Trim()).FirstOrDefault();
                                            if (dictEneity != null)
                                            {
                                                dictIds.Add("'" + dictEneity.ID.ToString() + "'");
                                            }
                                        }
                                    }
                                    else
                                    {
                                        var dictEneity = dicts.Where(x => x.Name == matchs[0].Value.Trim()).FirstOrDefault();
                                        if (dictEneity != null)
                                        {
                                            dictIds.Add("'" + dictEneity.ID.ToString() + "'");
                                        }
                                    }
                                }

                                if (temp.StartsWith("any"))
                                {
                                    dmResult = part1 + $" EXISTS(SELECT \"DictId\" FROM \"JHR\".\"EmployeeHRDict\" WHERE \"EmployeeHRId\" = \"JHR\".\"EmployeeHR\".ID and \"DictId\" in({string.Join(',', dictIds)}))";
                                    result = part1 + $" EXISTS(SELECT DictId FROM EmployeeHRDict WHERE EmployeeHRId = EmployeeHR.ID and DictId in({string.Join(',', dictIds)}))";
                                }
                                else if (temp.StartsWith("all"))
                                {
                                    var sqlStr = "";
                                    var dmSqlStr = "";
                                    foreach (var dictId in dictIds)
                                    {
                                        sqlStr += $" EXISTS(SELECT DictId FROM EmployeeHRDict WHERE EmployeeHRId = EmployeeHR.ID and DictId ={dictId}) And";
                                        dmSqlStr += $" EXISTS(SELECT \"DictId\" FROM \"JHR\".\"EmployeeHRDict\" WHERE \"EmployeeHRId\" = \"JHR\".\"EmployeeHR\".ID and \"DictId\" ={dictId}) And";
                                    }
                                    sqlStr = sqlStr.Substring(0, sqlStr.Length - 3);
                                    dmSqlStr = dmSqlStr.Substring(0, dmSqlStr.Length - 3);

                                    result = part1 + sqlStr;
                                    dmResult = part1 + dmSqlStr;
                                }
                            }
                            else
                            {
                                result = part1 + tab.TableName + EmployeeLists.Tag_Spot + col.ColumnName + part2;
                                dmResult = part1 + EmployeeLists.DM_SQL_Schema + tab.TableName + EmployeeLists.Tag_Spot + col.ColumnName + part2;
                            }
                        }

                        if (tables.IndexOf(tab.TableName ?? "") < 0)
                            tables.Add(tab.TableName ?? "");
                    }
                }
            }
            return Tuple.Create(result, dmResult);
        }

        /// <summary>
        /// 删除高级查询
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除高级查询")]
        [Permission(Permissions.HRManage.EmpRoster)]
        public BizResult DeleteEmployeeList([FromBody] EmployeeListModel model)
        {
            var entity = new EmployeeList
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteEmployeeList(entity);

            return result;
        }

        /// <summary>
        /// 获取高级查询
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取高级查询")]
        [Permission(Permissions.HRManage.EmpRoster)]
        public BizResult<EmployeeListModel> GetEmployeeList([FromQuery, Required] Guid id)
        {
            var result = this.BizResult<EmployeeListModel>();
            var entity = this.Repo.Get<Entities.EmployeeList>(id);

            if (entity == null)
            {
                result.Error("对象为空");
            }
            else
            {
                var model = entity.Map<EmployeeListModel>();

                var columns = this.Repo.GetEntities<QueryColumnInfo>();
                var columnsInfo = columns.Maps<QueryColumnInfoQuery>();

                model.ColumnInfos = new List<QueryColumnInfoQuery>();

                var fields = model.Fields.GetValueOrDefault().Split(EmployeeLists.Tag_Split);
                foreach (var field in fields)
                {
                    var fieldsArea = field.Split(EmployeeLists.Tag_Spot);
                    var column = columnsInfo.Where(c => c.TableName == fieldsArea[0] && c.ColumnName == fieldsArea[1]).FirstOrDefault();
                    if (column != null)
                    {
                        model.ColumnInfos.Add(column);
                    }
                }

                result.Data = model;
            }

            return result;
        }

        #endregion EmployeeList

        #region otherEmpManage

        /// <summary>
        /// 查询其他人员类型
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询其他人员类型")]
        [Permission(Permissions.HRManage.OtherEmpManage)]
        public QueryResult<DictQuery> QueryOtherEmpTypes()
        {
            var entities = this.Repo.QueryDictByParentCode(Dicts.OtherEmpTypeCode);
            var models = entities.Maps<DictQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        [HttpPost]
        [LogApi(ApiType.Query, Operate = "查询其他人员库信息")]
        [Permission(Permissions.HRManage.OtherEmpManage)]
        public QueryResult<OtherEmployeeInfoQuery> QueryOtherEmployeeInfo([FromBody] OtherEmpManageFilter filter)
        {
            var exps = this.NewExps<OtherEmployeeInfo>();

            if (filter.ConditionList != null && filter.ConditionList.Any())
            {
                var expression = new Common.DynamicQuery().GetDynamicQuery<OtherEmployeeInfo>(filter.ConditionList);
                exps.And(expression);
            }
            if (filter.DeptID.HasValue)
            {
                exps.And(s => s.DepartmentID == filter.DeptID.Value);
            }
            if (filter.EnumType.HasValue)
            {
                exps.And(s => s.OtherEmpTypeId == filter.EnumType.Value);
            }
            if (filter.Status.HasValue)
            {
                exps.And(s => s.EmpStatusId == filter.Status.Value);
            }

            if (filter.Order.IsEmpty())
            {
                filter.Order = $"{nameof(OtherEmployeeInfo.CreateTime)} {GeneralConsts.OrderByDesc}";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<OtherEmployeeInfoQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "维护其他人员信息")]
        [Permission(Permissions.HRManage.OtherEmpManage)]
        public BizResult SaveOtherEmployeeInfo([FromBody] OtherEmployeeInfoModel model)
        {
            var result = new BizResult();
            /*
            //已经存在该编号
            if (model.Code.IsEmpty())
            {
                result.Error("[用户组编号] 是必填项！");
                return result;
            }
            if (model.Name.IsEmpty())
            {
                result.Error("[用户组名称] 是必填项！");
                return result;
            }
            var codeRepeat = this.Repo.GetUserGroupByCode(model.Code);
            if (codeRepeat != null && codeRepeat.ID != model.ID)
            {
                result.Error("已经存在该编号");
                return result;
            }
            */

            var entity = model.Map<OtherEmployeeInfo>();
            this.Repo.SaveOtherEmp(entity);

            return result;
        }

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除其他人员信息")]
        [Permission(Permissions.HRManage.OtherEmpManage)]
        public BizResult DeleteOtherEmployeeInfo([FromBody] OtherEmployeeInfoModel model)
        {
            var result = new BizResult();
            var e = this.Repo.Find<OtherEmployeeInfo>(model.ID);
            if (e != null)
                this.Repo.DelOtherEmp(e);
            else
                result.Error("未找到人员信息");

            return result;
        }

        #endregion otherEmpManage

        #region 导入

        [HttpGet]
        [LogApi(ApiType.Query, Operate = "导入下载摸板")]
        public IActionResult DownlodaImportExcelTemplate(string type)
        {
            if (!ImportEmployee.ImportEmp.ContainsKey(type))
            {
                return NotFound();
            }
            var tp = ImportEmployee.ImportEmp[type];

            string filepath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, Common.Consts.Exports.PathTemplate, tp.Item3);
            if (!System.IO.File.Exists(filepath))
            {
                return NotFound();
            }

            using FileStream fs = new FileStream(filepath, FileMode.Open, FileAccess.Read, FileShare.Read);
            using var ms = new MemoryStream();
            fs.CopyTo(ms);

            var contentTypeProvider = new Microsoft.AspNetCore.StaticFiles.FileExtensionContentTypeProvider();
            var contenttype = Common.Consts.Exports.ContentType;
            contentTypeProvider.TryGetContentType(filepath, out contenttype);
            return File(ms.ToArray(), contenttype ?? "", Path.GetFileName(filepath));
        }

        [HttpPost]
        [LogApi(ApiType.File, Operate = "导入")]
        public BizResult<string> ImportExcel([FromForm] IFormCollection form)
        {
            var result = this.BizResult<string>(); //new BizResult();
            var ms = new MemoryStream();

            var file = form.Files["file"];

            if (file == null)
            {
                result.Error("没有获取到上传的文件");
            }
            else
            {
                file.CopyTo(ms);
                ms.Position = 0;

                var emptype = form["type"].AsString();

                var tp = ImportEmployee.ImportEmp[emptype];

                if (tp != null)
                {
                    var groupid = CombGuid.NewGuid();
                    var dt = this.Repo.GetDataTable(tp.Item1, groupid);

                    ExcelHelper.ExcelToDataTable(ms, null, true, dt);

                    if (dt.Rows.Count != 0)
                    {
                        (bool isok, string msg) r = new(false, string.Empty);
                        if (tp.Item2.Trim().Length > 0)
                        {
                            if (Config.IsDM)
                            {
                                r = this.Repo.DmBulkCopyProc(dt, tp.Item2, groupid);
                            }
                            else
                            {
                                r = this.Repo.SqlBulkCopyProc(dt, tp.Item2, groupid);
                            }
                        }
                        else
                        {
                            r = this.Repo.ImportEmployeeSocialInsurance(dt, CurrentUser?.DisplayName);
                        }

                        if (r.isok)
                            result.Data = r.msg;
                        else
                            result.Error(r.msg);
                    }
                    else
                        result.Error("模板错误或无数据");
                }
                else
                {
                    result.Error("参数错误");
                }
            }

            return result;
        }

        #endregion 导入

        #region 提取数据写入第三方

        [HttpGet]
        [AllowAnonymous]
        [LogApi(ApiType.Interface, Operate = "提取数据写入一卡通")]
        public BizResult PostAttDayOffRecordToOneCard()
        {
            var bizresult = new BizResult();
            try
            {
                var year = SysDateTime.Now.Year;
                var month = SysDateTime.Now.Month;
                month--;
                if (month == 0)
                {
                    year--;
                    month = 12;
                }
                string recordMonth = string.Format("{0}-{1}", year, month.ToString().PadLeft(2, '0'));

                var model = this.Repo.GetEntity<AttDayOffRecordPost>(p => p.RecordMonth == recordMonth);
                if (model != null)
                {
                    bizresult.Error("该月数据已写入一卡通");
                    return bizresult;
                }

                var url = "http://172.17.11.14/Shell/OpenAjax.aspx?biz=a_form_record.add&FormGID=bee0ab71f09746b190cd2f4e02188444";

                //获取考勤记录
                var entities = this.Repo.GetEntities<AttDayOffRecord>(p => (p.EnumStatus == AttDayOffRecordStatus.Committed || p.EnumProphylacticStatus == ProphylacticStatus.Submitted)
                         && p.RecordMonth == recordMonth);
                if (entities.Count > 0)
                {
                    var ids = entities.Select(p => p.ID).ToList();
                    var details = this.Repo.GetEntities<AttDayOffRecordDetail>(p => p.RecordId.HasValue && ids.Contains(p.RecordId.Value));

                    foreach (var detail in details)
                    {
                        decimal lostDay = 0;
                        if (detail.H2.HasValue && detail.H2.Value > 0)
                            lostDay += detail.H2.Value;
                        if (detail.H3.HasValue && detail.H3.Value > 0)
                            lostDay += detail.H3.Value;
                        if (detail.H4.HasValue && detail.H4.Value > 0)
                            lostDay += detail.H4.Value;
                        if (detail.H5.HasValue && detail.H5.Value > 0)
                            lostDay += detail.H5.Value;
                        if (detail.H6.HasValue && detail.H6.Value > 0)
                            lostDay += detail.H6.Value;
                        if (detail.H7.HasValue && detail.H7.Value > 0)
                            lostDay += detail.H7.Value;
                        if (detail.H8.HasValue && detail.H8.Value > 0)
                            lostDay += detail.H8.Value;
                        if (detail.H9.HasValue && detail.H9.Value > 0)
                            lostDay += detail.H9.Value;
                        if (detail.H10.HasValue && detail.H10.Value > 0)
                            lostDay += detail.H10.Value;
                        if (detail.H11.HasValue && detail.H11.Value > 0)
                            lostDay += detail.H11.Value;

                        if (lostDay == 0)
                            continue;

                        var id = detail.ID.ToString().ToLower();
                        var uuid = detail.Employee?.UUID?.ToLower() ?? "";

                        //http://172.17.11.14/Shell/OpenAjax.aspx?biz=a_form_record.add&FormGID=bee0ab71f09746b190cd2f4e02188444

                        //< A_Form_Record xmlns: xsi = "http://www.w3.org/2001/XMLSchema-instance" xmlns: xsd = "http://www.w3.org/2001/XMLSchema" FGID = "" SGID = "" >
                        //     < Field >< Key > 7108458af11c4e8e91846a1ee119ba02 </ Key >< Value >< !--ID-- ></ Value ></ Field >
                        //     < Field >< Key > d9c35e95c19744babf054ede29e83dd8 </ Key >< Value >< !--年月-- ></ Value ></ Field >
                        //     < Field >< Key > b9bc722bf3ec496e85ae466bf5ed1f92 </ Key >< Value >< !--人员ID-- ></ Value ></ Field >
                        //     < Field >< Key > 88ef70d58be84306b5e9e7eff1a9d2b9 </ Key >< Value >< !--缺勤天数-- ></ Value ></ Field >
                        //     < Field >< Key > 15d7a0314131493f81445afa78583e41 </ Key >< Value >< !--数据接收时间-- ></ Value ></ Field >
                        //     < Field >< Key > 889939aa1bba4f9288ac01cfbd2d4414 </ Key >< Value >< !--考勤处理状态-- ></ Value ></ Field >
                        //     < Field >< Key > bd6f278e4a2b4d34b3f1d3258330e427 </ Key >< Value >< !--考勤处理时间-- ></ Value ></ Field >
                        //     < Field >< Key > d721030a62b24780a8f04153c578959c </ Key >< Value >< !--备注-- ></ Value ></ Field >
                        //</ A_Form_Record >

                        Hashtable ht = new Hashtable();
                        ht["7108458af11c4e8e91846a1ee119ba02"] = id;
                        ht["d9c35e95c19744babf054ede29e83dd8"] = recordMonth.Replace("-", ""); ;
                        ht["b9bc722bf3ec496e85ae466bf5ed1f92"] = uuid;
                        ht["88ef70d58be84306b5e9e7eff1a9d2b9"] = lostDay.ToString();
                        //ht["d721030a62b24780a8f04153c578959c"] = "test";

                        var result = WebServiceCaller.QueryWebService(url, ht);
                        if (result.Contains("\"result\":true,"))
                        {
                            Information info = new Information()
                            {
                                Key1 = "GetUUIDInformation",
                                Remark = $"{id},{recordMonth},{uuid},{lostDay.ToString()}",
                            };
                            this.Repo.AddInformation(info);
                        }
                    }

                    AttDayOffRecordPost post = new AttDayOffRecordPost()
                    {
                        RecordMonth = recordMonth
                    };
                    this.Repo.AddAttDayOffRecordPost(post);
                }
            }
            catch (Exception)
            {
                bizresult.Error("上传一卡通信息异常");
            }
            return bizresult;
        }


        [HttpGet]
        [AllowAnonymous]
        [LogApi(ApiType.Interface, Operate = "重新提交人员数据写入第三方")]
        public BizResult PostDataByEmployeeStationNotice()
        {
            var bizresult = new BizResult();
            try
            {
                DateTime dt = DateTime.Now.AddDays(-1);
                var entities = this.Repo.GetEntities<EmployeeStationNotice>(p => (p.IsNotice.HasValue && p.IsNotice.Value == false)
                    && p.EndDate < dt);
                List<int> uidList = new List<int>();
                if (entities.Count > 0)
                {
                    foreach (EmployeeStationNotice entity in entities)
                    {
                        int uid = entity.EmployeeUid ?? 0;
                        if (uidList.IndexOf(uid) < 0)
                        {
                            GetPersonnelInformation(uid);
                            uidList.Add(uid);
                        }

                        entity.IsNotice = true;
                        entity.LastEditor = "admin";
                        entity.LastEditTime = DateTime.Now;

                        this.Repo.UpdateEmployeeStationNotice(entity);
                    }
                }

                Information info = new Information()
                {
                    Key1 = "PostDataByEmployeeStationNotice",
                    Remark = "PostDataByEmployeeStationNotice done",
                };
                this.Repo.AddInformation(info);
            }
            catch (Exception ex)
            {
                Information info = new Information()
                {
                    Key1 = "PostDataByEmployeeStationNotice",
                    Remark = ex.Message
                };
                this.Repo.AddInformation(info);
                bizresult.Error("重新提交人员数据写入第三方");
            }
            return bizresult;
        }

        [HttpGet]
        [LogApi(ApiType.Interface, Operate = "提取部门负责人数据写入第三方")]
        public BizResult GetDeptPrincipInformation([FromQuery, Required] Guid ID)
        {
            var bizresult = new BizResult();
            string log = "";
            try
            {

                //var exps = this.NewExps<Entities.ViewDepartmentEmployeeHistory>();
                if (!ID.IsEmpty())
                {
                    //exps.Add(x => x.ID == ID);

                    var model = this.Repo.Get<ViewDepartmentEmployeeHistory>(ID); ;
                    if (model == null)
                    {
                        bizresult.Error("未找到相关人部门负责人信息");
                        return bizresult;
                    }

                    Hashtable ht = ObjectToHashtable(model);

                    log = GetStingFromTable(ht);

                    var result = WebServiceCaller.QuerySoapWebService("http://************:1413/services/HSBHR", "HeadOfDepartment", ht);
                    Information info = new Information()
                    {
                        Key1 = "GetDeptPrincipInformation" + GetClientIP(),
                        Key2 = $"ID:{ID}",
                        Key3 = result.InnerText,
                        Remark = log
                    };
                    this.Repo.AddInformation(info);

                    //var result = "<ResultCode>0</ResultCode>"; /// for test
                    //if (!result.Contains("<ResultCode>1</ResultCode>"))  /// for test
                    //if (!result.InnerText.Contains("<ResultCode>1</ResultCode>"))
                    //{
                    //    bizresult.Error("上传部门负责人信息失败：" + result.InnerText);
                    //}
                    return bizresult;

                }

            }
            catch (Exception ex)
            {
                Information info = new Information()
                {
                    Key1 = "GetDeptPrincipInformation" + GetClientIP(),
                    Key2 = $"ID:{ID}",
                    Key3 = ex.Message,
                    Remark = log
                };
                this.Repo.AddInformation(info);
                bizresult.Error("上传部门负责人信息异常");
            }
            return bizresult;
        }


        [HttpGet]
        [LogApi(ApiType.Interface, Operate = "提取数据写入第三方")]
        public BizResult GetPersonnelInformation([FromQuery, Required] int condition, string addnewFlag = "2")
        {
            var bizresult = new BizResult();
            string log = "";
            try
            {
                var notEmpUids = Config.NotUploadEmployeeUids;
                if (!string.IsNullOrEmpty(notEmpUids))
                {
                    foreach (var uid in notEmpUids.Split(','))
                    {
                        if (uid == condition.ToString())
                        {
                            return bizresult;
                        }
                    }
                }

                var exps = this.NewExps<Entities.ViewPersonnelInformationExport>();
                if (condition > 0)
                {
                    exps.Add(x => x.UserID == condition);
                }
                var model = this.Repo.GetEntity(exps);
                if (model == null)
                {
                    bizresult.Error("未找到相关人员信息");
                    return bizresult;
                }
                if (model.UserType == null)
                {
                    return bizresult;
                }

                Hashtable ht = ObjectToHashtable(model);
                EmployeeOAInsertFail? oaInsertFail = this.Repo.Get<Entities.EmployeeOAInsertFail>(model.ID);
                if (oaInsertFail != null)
                {
                    addnewFlag = Renji.JHR.Common.Consts.Employee.EmployeeOAInsert;
                }

                ht["AddnewFlag"] = addnewFlag;
                var qua = ht["Qualification"]?.ToString();

                var hrbll = new HRBll();
                var quaCurr = this.Repo.Get<Entities.EmployeeStationCurrent>(model.ID);
                if (quaCurr == null)
                {
                    ht["QualificationUpdate"] = Renji.JHR.Common.Consts.Employee.EmployeeQualificationUpdate;
                    quaCurr = new EmployeeStationCurrent()
                    {
                        ID = model.ID,
                        Type = 2,
                        PositionName = qua
                    };
                    hrbll.AddEmployeeStationCurrent(quaCurr);
                }
                else
                {
                    if (quaCurr.PositionName == qua)
                    {
                        ht["QualificationUpdate"] = Renji.JHR.Common.Consts.Employee.EmployeeQualificationUpdateNone;
                    }
                    else
                    {
                        ht["QualificationUpdate"] = Renji.JHR.Common.Consts.Employee.EmployeeQualificationUpdate;
                        quaCurr.PositionName = qua;
                        hrbll.UpdateEmployeeStationCurrent(quaCurr);
                    }
                }

                var dept = this.Repo.Find<Entities.Employee>(model.ID)?.Department;
                if (dept != null && string.IsNullOrEmpty(dept.OrgSerial) && dept.Parent != null)
                {
                    var orgbll = new OrganizationBll();
                    dept.OrgSerial = orgbll.MakeOrgSerial(dept.Parent);
                    orgbll.UpdateDepartment(dept);
                    if (dept.Uid > 0)
                    {
                        this.PostAddDocumentInformation(dept.Uid);
                    }
                }

                log = GetStingFromTable(ht);

                var result = WebServiceCaller.QuerySoapWebService("http://************:1413/services/HSBHR", "SaveStaffInfoNew", ht);
                Information info = new Information()
                {
                    Key1 = "GetPersonnelInformation" + GetClientIP(),
                    Key2 = $"{condition},{addnewFlag}",
                    Key3 = result.InnerText,
                    Remark = log
                };
                this.Repo.AddInformation(info);

                //var result = "<ResultCode>0</ResultCode>"; /// for test
                //if (!result.Contains("<ResultCode>1</ResultCode>"))  /// for test
                if (!result.InnerText.Contains("<ResultCode>1</ResultCode>"))
                {
                    bizresult.Error("推送OA失败：" + result.InnerText);
                    if (addnewFlag == Renji.JHR.Common.Consts.Employee.EmployeeOAInsert && oaInsertFail == null)
                    {
                        oaInsertFail = new EmployeeOAInsertFail();
                        oaInsertFail.ID = model.ID;
                        oaInsertFail.Uid = model.UserID;
                        hrbll.AddEmployeeOAInsertFail(oaInsertFail);
                    }
                }
                else
                {
                    if (oaInsertFail != null)
                    {
                        hrbll.DeleteEmployeeOAInsertFail(oaInsertFail);
                    }
                }
                return bizresult;

                //if (!string.IsNullOrEmpty(model.DepartmentCode))
                //{
                //    //var res = WebServiceCaller.QuerySoapWebService("http://www.webxml.com.cn/WebServices/ValidateEmailWebService.asmx", "ValidateEmailAddress", ht);
                //    //return bizresult;
                //}
                //else
                //{
                //    bizresult.Error("上传人员信息失败");
                //    return bizresult;
                //}
            }
            catch (Exception ex)
            {
                Information info = new Information()
                {
                    Key1 = "GetPersonnelInformation" + GetClientIP(),
                    Key2 = $"{condition},{addnewFlag}",
                    Key3 = ex.Message,
                    Remark = log
                };
                this.Repo.AddInformation(info);
                bizresult.Error("上传人员信息异常");
            }
            return bizresult;
        }

        private string GetStingFromTable(Hashtable ht)
        {
            StringBuilder builder = new StringBuilder();
            foreach (string k in ht.Keys)
            {
                builder.Append(string.Format("{0}:{1},", k, ht[k]));
            }
            return builder.ToString();
        }

        [HttpGet]
        [LogApi(ApiType.Interface, Operate = "查询OA部门数据")]
        public BizResult PostQueryDocumentInformation([FromQuery, Required] int condition)
        {
            var bizresult = new BizResult();
            try
            {
                //string orgserial;
                var exps = this.NewExps<Entities.Department>();
                if (condition > 0)
                {
                    exps.Add(x => x.Uid == condition);
                }
                var model = this.Repo.GetEntity(exps);
                if (model != null && !string.IsNullOrEmpty(model.OrgSerial))
                {
                    //地址：http://************:1413/services/HSBHR?wsdl   方法名：getOrgInfoByOrgCode
                    Hashtable ht = new Hashtable();

                    ht.Add("key", "ezOFFICE12.4".ToMD5());
                    ht.Add("domain", "0");
                    //ht.Add("serviceKey", "webappKey");
                    //ht.Add("verificationType", "0");
                    //ht.Add("userKey", "sys");
                    //ht.Add("userKeyType", "0");
                    //var time = SysDateTime.Now.TimeOfDay.Milliseconds;
                    //ht.Add("time", time);
                    //ht.Add("md5key", ("webappKey0sys0" + time.ToString() + "webapp").ToMD5());
                    ht.Add("cmd", "getOrgInfoByOrgCode");
                    ht.Add("orgCode", model.OrgSerial);

                    var result = WebServiceCaller.QuerySoapWebService("http://************:1413/services/HSBHR", "SaveOrganization", ht, true);
                    Information info = new Information()
                    {
                        Key1 = "PostQueryDocumentInformation" + GetClientIP(),
                        Key2 = $"{condition}",
                        Remark = result.InnerText
                    };
                    this.Repo.AddInformation(info);
                    //<ResultCode>1</ResultCode>
                    if (!result.InnerText.Contains("<ResultCode>1</ResultCode>"))
                    {
                        bizresult.Error("查询OA部门信息失败");
                        return bizresult;
                    }
                }
            }
            catch (Exception ex)
            {
                Information info = new Information()
                {
                    Key1 = "PostQueryDocumentInformation" + GetClientIP(),
                    Key2 = $"{condition}",
                    Key3 = ex.Message
                };
                this.Repo.AddInformation(info);
                bizresult.Error("查询OA部门信息失败");
            }
            return bizresult;
        }

        [HttpGet]
        [LogApi(ApiType.Interface, Operate = "添加OA部门数据")]
        public BizResult PostAddDocumentInformation([FromQuery, Required] int condition)
        {
            var bizresult = new BizResult();
            string log = "";

            try
            {
                //string deptName, string orgserial, string parentorgserial, string hostitalArea
                var exps = this.NewExps<Entities.Department>();
                if (condition > 0)
                {
                    exps.Add(x => x.Uid == condition);
                }
                var model = this.Repo.GetEntity(exps);
                if (model != null && !string.IsNullOrEmpty(model.OrgSerial))
                {
                    if (model.ParentId.HasValue && !model.ParentId.Value.Equals(Guid.Empty))
                    {
                        var parent = this.Repo.Get<Entities.Department>(model.ParentId);
                        if (parent != null && !string.IsNullOrEmpty(parent.OrgSerial))
                        {
                            var dict = this.Repo.Get<Entities.Dict>(model.HospitalAreaId);
                            string hostitalArea;
                            if (dict != null)
                            {
                                hostitalArea = dict.Name;
                            }
                            else
                            {
                                hostitalArea = "东院";
                            }

                            //地址：http://************:1413/services/HSBHR?wsdl   方法名：SaveOrganization
                            Hashtable ht = new Hashtable();
                            ht.Add("key", "ezOFFICE12.4".ToMD5());
                            ht.Add("domain", "0");
                            ht.Add("serviceKey", "webappKey");
                            ht.Add("verificationType", "0");
                            ht.Add("userKey", "sys");
                            ht.Add("userKeyType", "0");
                            var time = SysDateTime.Now.TimeOfDay.Milliseconds;
                            ht.Add("time", time);
                            ht.Add("md5key", ("webappKey0sys0" + time.ToString() + "webapp").ToMD5());
                            ht.Add("cmd", "addOrganizationNew");
                            ht.Add("name", model.Name);
                            ht.Add("orgsimplename", model.Name);
                            ht.Add("orgserial", model.OrgSerial);
                            ht.Add("orgordercode", "500000");
                            ht.Add("orgtype", "1");
                            ht.Add("hospitalArea", hostitalArea);
                            ht.Add("description", "");
                            ht.Add("parentorgserial", parent.OrgSerial);
                            ht.Add("targetOrgserial", "");
                            ht.Add("sort", "0");
                            ht.Add("chargeLeaderAccounts", "");
                            ht.Add("deptLeaderAccounts", "");

                            log = GetStingFromTable(ht);

                            var result = WebServiceCaller.QuerySoapWebService("http://************:1413/services/HSBHR", "SaveOrganization", ht, true);
                            Information info = new Information()
                            {
                                Key1 = "PostAddDocumentInformation" + GetClientIP(),
                                Key2 = $"{condition}",
                                Key3 = result.InnerText,
                                Remark = log
                            };
                            this.Repo.AddInformation(info);
                            if (!result.InnerText.Contains("<ResultCode>1</ResultCode>"))
                            {
                                bizresult.Error("上传部门信息失败");
                                return bizresult;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Information info = new Information()
                {
                    Key1 = "PostAddDocumentInformation" + GetClientIP(),
                    Key2 = $"{condition}",
                    Key3 = ex.Message,
                    Remark = log
                };
                this.Repo.AddInformation(info);
                bizresult.Error("上传部门信息异常");
            }
            return bizresult;
        }

        [HttpGet]
        [LogApi(ApiType.Interface, Operate = "更新OA部门数据")]
        public BizResult PostUpdateDocumentInformation([FromQuery, Required] int condition)
        {
            var bizresult = new BizResult();
            string log = "";

            try
            {
                //string deptName, string orgserial, string parentorgserial, string hostitalArea

                var exps = this.NewExps<Entities.Department>();
                if (condition > 0)
                {
                    exps.Add(x => x.Uid == condition);
                }
                var model = this.Repo.GetEntity(exps);
                if (model != null && !string.IsNullOrEmpty(model.OrgSerial))
                {
                    if (model.ParentId.HasValue && !model.ParentId.Value.Equals(Guid.Empty))
                    {
                        var parent = this.Repo.Get<Entities.Department>(model.ParentId);
                        if (parent != null && !string.IsNullOrEmpty(parent.OrgSerial))
                        {
                            var dict = this.Repo.Get<Entities.Dict>(model.HospitalAreaId);
                            string hostitalArea;
                            if (dict != null)
                            {
                                hostitalArea = dict.Name;
                            }
                            else
                            {
                                hostitalArea = "东院";
                            }

                            //地址：http://************:1413/services/HSBHR?wsdl   方法名：SaveOrganization
                            Hashtable ht = new Hashtable();
                            ht.Add("key", "ezOFFICE12.4".ToMD5());
                            ht.Add("domain", "0");
                            ht.Add("serviceKey", "webappKey");
                            ht.Add("verificationType", "0");
                            ht.Add("userKey", "sys");
                            ht.Add("userKeyType", "0");
                            var time = SysDateTime.Now.TimeOfDay.Milliseconds;
                            ht.Add("time", time);
                            ht.Add("md5key", ("webappKey0sys0" + time.ToString() + "webapp").ToMD5());
                            ht.Add("cmd", "updateOrganizationNew");
                            ht.Add("name", model.Name);
                            ht.Add("orgsimplename", model.Name);
                            ht.Add("orgserial", model.OrgSerial);
                            ht.Add("description", "");
                            ht.Add("orgordercode", "500000");
                            ht.Add("orgtype", "1");
                            ht.Add("hasChanged", "0");
                            ht.Add("parentorgserial", parent.OrgSerial);
                            ht.Add("targetOrgserial", "");
                            ht.Add("sort", "0");
                            ht.Add("chargeLeaderAccounts", "");
                            ht.Add("deptLeaderAccounts", "");
                            ht.Add("hospitalArea", hostitalArea);
                            log = GetStingFromTable(ht);

                            var result = WebServiceCaller.QuerySoapWebService("http://************:1413/services/HSBHR", "SaveOrganization", ht, true);
                            Information info = new Information()
                            {
                                Key1 = "PostUpdateDocumentInformation" + GetClientIP(),
                                Key2 = $"{condition}",
                                Key3 = result.InnerText,
                                Remark = log
                            };
                            this.Repo.AddInformation(info);
                            if (!result.InnerText.Contains("<ResultCode>1</ResultCode>"))
                            {
                                bizresult.Error("上传部门信息失败");
                                return bizresult;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Information info = new Information()
                {
                    Key1 = "PostUpdateDocumentInformation" + GetClientIP(),
                    Key2 = $"{condition}",
                    Key3 = ex.Message,
                    Remark = log
                };
                this.Repo.AddInformation(info);
                bizresult.Error("上传部门信息异常");
            }
            return bizresult;
        }

        [HttpGet]
        [LogApi(ApiType.Interface, Operate = "删除OA部门数据")]
        public BizResult PostDeleteDocumentInformation([FromQuery, Required] int condition)
        {
            var bizresult = new BizResult();
            string log = "";

            try
            {
                //string orgserial;
                var exps = this.NewExps<Entities.Department>();
                if (condition > 0)
                {
                    exps.Add(x => x.Uid == condition);
                }
                var model = this.Repo.GetEntity(exps);
                if (model != null && !string.IsNullOrEmpty(model.OrgSerial))
                {
                    //地址：http://************:1413/services/HSBHR?wsdl   方法名：SaveOrganization
                    Hashtable ht = new Hashtable();

                    ht.Add("key", "ezOFFICE12.4".ToMD5());
                    ht.Add("domain", "0");
                    ht.Add("serviceKey", "webappKey");
                    ht.Add("verificationType", "0");
                    ht.Add("userKey", "sys");
                    ht.Add("userKeyType", "0");
                    var time = SysDateTime.Now.TimeOfDay.Milliseconds;
                    ht.Add("time", time);
                    ht.Add("md5key", ("webappKey0sys0" + time.ToString() + "webapp").ToMD5());
                    ht.Add("cmd", "deleteOrganizationByOrgIdNew");
                    ht.Add("orgserial", model.OrgSerial);
                    log = GetStingFromTable(ht);

                    var result = WebServiceCaller.QuerySoapWebService("http://************:1413/services/HSBHR", "SaveOrganization", ht, true);
                    Information info = new Information()
                    {
                        Key1 = "PostDeleteDocumentInformation" + GetClientIP(),
                        Key2 = $"{condition}",
                        Key3 = result.InnerText,
                        Remark = log
                    };
                    this.Repo.AddInformation(info);
                    if (!result.InnerText.Contains("<ResultCode>1</ResultCode>"))
                    {
                        bizresult.Error("上传部门信息失败");
                        return bizresult;
                    }
                }
            }
            catch (Exception ex)
            {
                Information info = new Information()
                {
                    Key1 = "PostDeleteDocumentInformation" + GetClientIP(),
                    Key2 = $"{condition}",
                    Key3 = ex.Message,
                    Remark = log
                };
                this.Repo.AddInformation(info);
                bizresult.Error("上传部门信息异常");
            }
            return bizresult;
        }

        private async Task<string> WebServiceHelper(string url, HttpContent content)
        {
            var result = string.Empty;
            try
            {
                using (var client = _httpClientFactory.CreateClient())
                using (var response = await client.PostAsync(url, content))
                {
                    if (response.StatusCode == System.Net.HttpStatusCode.OK)
                    {
                        result = await response.Content.ReadAsStringAsync();
                        System.Xml.XmlDocument doc = new System.Xml.XmlDocument();
                        doc.LoadXml(result);
                        result = doc.InnerText;
                    }
                }
            }
            catch (Exception ex)
            {
                result = ex.Message;
            }
            return result;
        }

        public Hashtable ObjectToHashtable<T>(T obj)
        {
            Hashtable hash = new Hashtable();

            if (obj != null)
            {
                System.Reflection.PropertyInfo[] ps = obj.GetType().GetProperties(System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Public);
                foreach (System.Reflection.PropertyInfo p in ps)
                {
                    hash.Add(p.Name, p.GetValue(obj));
                }
                hash.Remove("ID");
            }

            return hash;
        }

        #endregion 提取数据写入第三方

        #region 查询信息接口

        /// <summary>
        /// 查询科室信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询科室信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<ViewDepartmentApiQuery> GetDepartmentInfo([FromQuery] DeptApiFilter filter)
        {
            var exps = this.NewExps<ViewDepartmentApi>();

            if (!filter.Code.IsEmpty())
            {
                exps.And(x => x.Code.Contains(filter.Code!));
            }

            if (!filter.Name.IsEmpty())
            {
                exps.And(x => x.Name.Contains(filter.Name!));
            }

            if (filter.ParentId.HasValue)
            {
                exps.And(x => x.ParentId == filter.ParentId);
            }

            var entities = this.Repo.GetEntities(exps, "Code asc");
            var models = entities.Maps<ViewDepartmentApiQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询人员信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询人员信息")]
        public QueryResult<ViewEmployeeApiQuery> GetEmployeeInfo([FromQuery] EmployeeApiFilter filter)
        {
            var exps = this.NewExps<ViewEmployeeApi>();

            if (!filter.EmployeeCode.IsEmpty())
            {
                exps.And(x => x.EmpCode.Contains(filter.EmployeeCode!));
            }

            if (!filter.EmployeeName.IsEmpty())
            {
                exps.And(x => x.DisplayName.Contains(filter.EmployeeName!));
            }

            if (filter.DeptId.HasValue)
            {
                exps.And(x => x.DeptId == filter.DeptId);
            }

            var entities = this.Repo.GetEntities(exps);
            var models = entities.Maps<ViewEmployeeApiQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询学位信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询学位信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<ViewDegreeApiQuery> GetDegreeInfo()
        {
            var exps = this.NewExps<ViewDegreeApi>();

            var entities = this.Repo.GetEntities(exps, "Code asc");
            var models = entities.Maps<ViewDegreeApiQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询学历信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询学历信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<ViewEducationApiQuery> GetEducationInfo()
        {
            var exps = this.NewExps<ViewEducationApi>();

            var entities = this.Repo.GetEntities(exps, "Code asc");
            var models = entities.Maps<ViewEducationApiQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询职称信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询职称信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<ViewJobTitleApiQuery> GetJobTitleInfo()
        {
            var exps = this.NewExps<ViewJobTitleApi>();

            var entities = this.Repo.GetEntities(exps, "Code asc");
            var models = entities.Maps<ViewJobTitleApiQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询课题级别信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询课题级别信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<ViewLevelApiQuery> GetLevelInfo()
        {
            var exps = this.NewExps<ViewLevelApi>();

            var entities = this.Repo.GetEntities(exps, "Code asc");
            var models = entities.Maps<ViewLevelApiQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询员工学历信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工学历信息")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<ViewEmployeeEducationApiQuery> GetEmployeeEducationInfo([FromQuery] EmployeeEducationApiFilter filter)
        {
            var exps = this.NewExps<ViewEmployeeEducationApi>();

            if (filter.EmployeeId.HasValue)
            {
                exps.And(x => x.EmployeeId == filter.EmployeeId);
            }

            var entities = this.Repo.GetEntities(exps);
            var models = entities.Maps<ViewEmployeeEducationApiQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询员工出国经历
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工出国经历")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<ViewEmployeeAbroadInfoApiQuery> GetEmployeeAbroadInfo([FromQuery] EmployeeAbroadInfoApiFilter filter)
        {
            var exps = this.NewExps<ViewEmployeeAbroadInfoApi>();

            if (!filter.EmployeeCode.IsEmpty())
            {
                exps.And(x => x.EmpCode.Contains(filter.EmployeeCode!));
            }

            var entities = this.Repo.GetEntities(exps);
            var models = entities.Maps<ViewEmployeeAbroadInfoApiQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        /// <summary>
        /// 查询人才计划项目
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询人才计划项目")]
        [Permission(Permissions.HRManage.StaffInfo)]
        public QueryResult<ViewEmployeeClassApiQuery> GetEmployeeClassInfo([FromQuery] EmployeeClassApiFilter filter)
        {
            var exps = this.NewExps<ViewEmployeeClassApi>();

            if (!filter.Name.IsEmpty())
            {
                exps.And(x => x.Name != null && x.Name.Contains(filter.Name!));
            }

            var entities = this.Repo.GetEntities(exps);
            var models = entities.Maps<ViewEmployeeClassApiQuery>();

            var result = this.QueryResult(models);
            return result;
        }

        #endregion 查询信息接口

        #region 市级医院人力资源数据统计报表

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "人力资源数据统计报表")]
        //[AllowAnonymous]
        public BizResult HRStatisticsReport()
        {
            var result = this.Repo.HRStatisticsReport();

            return result.Map<EmployeeHighTalentModel>();
        }

        #endregion

        #region 党建

        [HttpPost]
        [AllowAnonymous]
        [LogApi(ApiType.Interface, Operate = "上传政治面貌信息")]
        public BizResult PostPartyInformation([FromBody] PartyInformationModel partyInfo)
        {
            var result = base.AnonymousValidation(partyInfo);

            if (!int.TryParse(partyInfo.UserID, out var userID))
            {
                result.Error("唯一码格式不正确");
            }

            var empHR = this.Repo.GetEntity<Entities.EmployeeHR>(e => e.Employee.Uid == userID);
            if (empHR == null)
            {
                result.Error("未找到对应的员工信息");
            }

            DateTime? joinDate = null;
            DateTime? probationDate = null;

            if (!string.IsNullOrEmpty(partyInfo.JoinDate))
            {
                if (DateTime.TryParse(partyInfo.JoinDate, out var tempJoinDate))
                {
                    joinDate = tempJoinDate;
                }
                else
                {
                    result.Error("入党时间日期格式不正确");
                }
            }

            if (!string.IsNullOrEmpty(partyInfo.ProbationDate))
            {
                if (DateTime.TryParse(partyInfo.ProbationDate, out var tempProbationDate))
                {
                    probationDate = tempProbationDate;
                }
                else
                {
                    result.Error("转正时间日期格式不正确");
                }
            }

            if (result.Succeed)
            {
                empHR!.PartyJoiningTime = joinDate; // 入党时间
                empHR.ProbationTime = probationDate; // 转正时间
                empHR.Organization = partyInfo.Organization; // 组织关系

                var affiliatedBranchs = partyInfo.AffiliatedBranch?.Split(',').ToList();
                var partyOrganizations = partyInfo.PartyOrganization?.Split(',').ToList();
                var partyPositions = partyInfo.PartyPosition?.Split(',').ToList();

                //if (partyOrganizations.Count != partyPositions.Count)
                //{
                //    result.Error("当前组织和党内职务数量不匹配");
                //}
                //else
                //{
                    Information info = new Information()
                    {
                        Key1 = "PostPartyInformation",
                        Key2 = partyInfo.UserID,
                        Remark = JsonSerializer.Serialize(partyInfo)
                    };
                    this.Repo.AddInformation(info);

                    result = this.Repo.PostPartyInformation(empHR, affiliatedBranchs,
                        partyOrganizations, partyPositions, partyInfo.PartyCode);
                //}
            }

            return result;
        }

        #endregion

        //[JwtIgnore]
        //[HttpPost]
        //public string Test()
        //{
        //    return GetClientIP();
        //    //var clientIp = Request.Headers["X-Forwarded-For"].FirstOrDefault(); // 获取 X-Forwarded-For 头部字段值作为客户端 IP

        //    //if (string.IsNullOrEmpty(clientIp))
        //    //    clientIp = HttpContext.Connection.RemoteIpAddress?.ToString(); // 获取连接远程 IP 地址

        //    //return clientIp;
        //}
    }
}