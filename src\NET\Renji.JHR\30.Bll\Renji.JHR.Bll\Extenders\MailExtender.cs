﻿using MailKit.Net.Smtp;
using MailKit.Security;
using MimeKit;
using Shinsoft.Core;
using Shinsoft.Core.Configuration;
using Shinsoft.Core.Json;
using Shinsoft.Core.Mail;
using Shinsoft.Core.NLog;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Security.Authentication;
using System.Text;

namespace Renji.JHR.Bll
{
    /// <summary>
    /// 邮件扩展类
    /// </summary>
    public static class Extender
    {
        public static (string name, string address) GetMailAddress(this string mailAddress)
        {
            mailAddress = mailAddress.Trim();

            string name, address;

            if (mailAddress.IsMatchRegex("^.*<.*>$"))
            {
                var array = mailAddress.Split('<', '>');
                name = array[0].Trim().Trim('"');
                address = array[1].Trim();
            }
            else
            {
                name = string.Empty;
                address = mailAddress;
            }

            return (name, address);
        }

        public static MailboxAddress ToMailboxAddress(this string mailAddress)
        {
            var (name, address) = mailAddress.GetMailAddress();

            return new MailboxAddress(name, address);
        }

        public static void Fill<T>([NotNull] this T mail, [NotNull] IDictionary<string, string> datas)
            where T : class, IMail
        {
            if (datas.Count > 0)
            {
                var subject = new StringBuilder(mail.Subject);
                var content = new StringBuilder(mail.Content);

                foreach (var data in datas)
                {
                    if (!data.Key.IsEmpty())
                    {
                        var key = new StringBuilder();

                        if (!data.Key.StartsWith("{#"))
                        {
                            key.Append("{#");
                        }

                        key.Append(data.Key);

                        if (!data.Key.EndsWith("#}"))
                        {
                            key.Append("#}");
                        }

                        subject.Replace(key.ToString(), data.Value);
                        content.Replace(key.ToString(), data.Value);
                    }
                }

                mail.Subject = subject.ToString();
                mail.Content = content.ToString();
            }
        }
    }
}
