﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Renji.JHR.Entities
{
    /// <summary>
    /// 工资计算情况
    /// </summary>
    public enum AttDayOffRecordDetailWagesStatus
    {
        None = 0,

        /// <summary>
        /// 工资已算
        /// </summary>
        [Description("工资已算")]
        CalculatedSalary = 1,

        /// <summary>
        /// 被修改
        /// </summary>
        [Description("被修改")]
        Modified = 10,
    }
}
