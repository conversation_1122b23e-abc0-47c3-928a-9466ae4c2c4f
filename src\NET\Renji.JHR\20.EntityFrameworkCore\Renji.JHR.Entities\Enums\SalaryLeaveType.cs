﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Renji.JHR.Entities
{
    public enum SalaryLeaveType
    {
        /// <summary>
        /// 未知
        /// </summary>
        [Description("未知")]
        None = 0,

        /// <summary>
        /// 产假A
        /// </summary>
        [Description("产假A")]
        MaternityLeaveA = 1,

        /// <summary>
        /// 产假B
        /// </summary>
        [Description("产假B")]
        MaternityLeaveB = 2,

        /// <summary>
        /// 产假C
        /// </summary>
        [Description("产假C")]
        MaternityLeaveC = 3,

        /// <summary>
        /// 产假D
        /// </summary>
        [Description("产假D")]
        MaternityLeaveD = 4,

        /// <summary>
        /// 产假E
        /// </summary>
        [Description("产假E")]
        MaternityLeaveE = 5,

        /// <summary>
        /// 产假F
        /// </summary>
        [Description("产假F")]
        MaternityLeaveF = 6,

        /// <summary>
        /// 病假类型一（病假<=62天）
        /// </summary>
        [Description("病假类型一")]
        SickLeave1 = 7,

        /// <summary>
        /// 病假类型二（病假63~180天工龄10年以下）
        /// </summary>
        [Description("病假类型二(十年工龄以下)")]
        SickLeave2A = 8,

        /// <summary>
        /// 病假类型二（病假63~180天工龄10年及以上）
        /// </summary>
        [Description("病假类型二(十年工龄以上)")]
        SickLeave2B = 9,

        /// <summary>
        /// 病假类型三（病假>=181天工龄10年以下）
        /// </summary>
        [Description("病假类型三(十年工龄以下)")]
        SickLeave3A = 10,

        /// <summary>
        /// 病假类型三（病假>=181天工龄10年及以上）
        /// </summary>
        [Description("病假类型三(十年工龄以上)")]
        SickLeave3B = 11,

        /// <summary>
        /// 哺乳假
        /// </summary>
        [Description("哺乳假")]
        BreastfeedingLeave = 12,

        /// <summary>
        /// 公假
        /// </summary>
        [Description("公假")]
        PublicLeave = 13,

        /// <summary>
        /// 事假（累计<=20天或连续<=10）
        /// </summary>
        [Description("事假一")]
        PersonalLeave1 = 14,

        /// <summary>
        /// 事假（连续>10天或累计>20天）
        /// </summary>
        [Description("事假二")]
        PersonalLeave2 = 15,

        /// <summary>
        /// 事假（累计>30天）
        /// </summary>
        [Description("事假三")]
        PersonalLeave3 = 16,

        /// <summary>
        /// 事假（累计>60天）
        /// </summary>
        [Description("事假四")]
        PersonalLeave4 = 17
    }
}
