﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Renji.JHR.Common.Consts
{
    public static class Dicts
    {
        /// <summary>
        /// 员工状态
        /// </summary>
        public static string EmployeeStatusCode = "00012"; // 00012	员工状态

        /// <summary>
        /// 员工状态  在职
        /// </summary>
        public static string EmployeeStatusCode_Hired = "1"; // 00012	在职

        /// <summary>
        /// 员工状态  离职
        /// </summary>
        public static string EmployeeStatusCode_Leave = "2"; // 00012	离职

        /// <summary>
        /// 职别
        /// </summary>
        public static string OfficialRankCode = "00033";  // 00033	职别

        /// <summary>
        /// 职别  护理
        /// </summary>
        public static string OfficialRankCode_Nursing = "2";  // 00033	职别  护理
        /// <summary>
        /// 职别  工人
        /// </summary>
        public static string OfficialRankCode_Worker = "5";  // 00033	职别  工人
        /// <summary>
        /// 职别  其他业务技术
        /// </summary>
        public static string OfficialRankCode_Other = "6";  // 00033	职别  其他业务技术
        /// <summary>
        /// 职别  后勤人员
        /// </summary>
        public static string ModulePart_Logistics = "Logistics";  // 后勤人员  00033	职别 （value: 5,6）
        /// <summary>
        /// 职别  护理人员
        /// </summary>
        public static string ModulePart_Nursing = "Nursing";  // 护理人员  00033	职别 （value:2）

        /// <summary>
        /// 聘任职别
        /// </summary>
        public static string RankCode = "00072";  // 00072	聘任职别
        /// <summary>
        /// 在职方式
        /// </summary>
        public static string HireStyleCode = "00024";  // 00024	在职方式

        #region 在职方式

        /// <summary>
        /// 在职方式 正式_在编
        /// </summary>
        public static string HireStyleCode_OfficialOnStaff = "01";  // 00001 正式_在编

        /// <summary>
        /// 在职方式 正式_非在编
        /// </summary>
        public static string HireStyleCode_OfficialOffStaff = "02";  // 00002 正式_非在编

        /// <summary>
        /// 在职方式 正式_二医编制
        /// </summary>
        public static string HireStyleCode_OfficialSecondMedicalStaff = "03";  // 00003 正式_二医编制

        /// <summary>
        /// 在职方式 正式_非在编自主择业
        /// </summary>
        public static string HireStyleCode_OfficialOffStaffSelfEmployed = "05";  // 00005 正式_非在编自主择业

        /// <summary>
        /// 在职方式 正式_待退休
        /// </summary>
        public static string HireStyleCode_OfficialRetirementPending = "06";  // 00006 正式_待退休

        /// <summary>
        /// 在职方式 正式_在编未办
        /// </summary>
        public static string HireStyleCode_OfficialOnStaffNotHandled = "07";  // 00007 正式_在编未办

        /// <summary>
        /// 在职方式 正式_在编南院
        /// </summary>
        public static string HireStyleCode_OfficialOnStaffSouthCampus = "08";  // 00008 正式_在编南院

        /// <summary>
        /// 在职方式 正式_残疾不在岗及待岗
        /// </summary>
        public static string HireStyleCode_OfficialDisabledNotOnDuty = "10";  // 00010 正式_残疾不在岗及待岗

        /// <summary>
        /// 在职方式 正式_非在编南院
        /// </summary>
        public static string HireStyleCode_OfficialOffStaffSouthCampus = "11";  // 00011 正式_非在编南院

        /// <summary>
        /// 在职方式 正式_南院在编未办
        /// </summary>
        public static string HireStyleCode_OfficialSouthCampusOnStaffNotHandled = "13";  // 00013 正式_南院在编未办

        /// <summary>
        /// 在职方式 非正式_派遣用工
        /// </summary>
        public static string HireStyleCode_UnofficialDispatchedLabor = "20";  // 00020 非正式_派遣用工

        /// <summary>
        /// 在职方式 非正式_非正规就业
        /// </summary>
        public static string HireStyleCode_UnofficialInformalEmployment = "21";  // 00021 非正式_非正规就业

        /// <summary>
        /// 在职方式 非正式_外单位退休及其他
        /// </summary>
        public static string HireStyleCode_UnofficialExternalRetiredAndOther = "23";  // 00023 非正式_外单位退休及其他

        /// <summary>
        /// 在职方式 非正式_退休返聘
        /// </summary>
        public static string HireStyleCode_UnofficialRetiredRehired = "24";  // 00024 非正式_退休返聘

        /// <summary>
        /// 在职方式 非正式_住院医师培训
        /// </summary>
        public static string HireStyleCode_UnofficialResidentDoctorTraining = "25";  // 00025 非正式_住院医师培训

        /// <summary>
        /// 在职方式 非正式_南院住院医师培训
        /// </summary>
        public static string HireStyleCode_UnofficialSouthCampusResidentDoctorTraining = "26";  // 00026 非正式_南院住院医师培训

        /// <summary>
        /// 在职方式 非正式_进站博士后
        /// </summary>
        public static string HireStyleCode_UnofficialPostdoc = "37";  // 00037 非正式_进站博士后

        /// <summary>
        /// 在职方式 正式_非在编派遣
        /// </summary>
        public static string HireStyleCode_OfficialOffStaffDispatched = "38";  // 00038 正式_非在编派遣

        /// <summary>
        /// 在职方式 正式_非在编南院派遣
        /// </summary>
        public static string HireStyleCode_OfficialOffStaffSouthCampusDispatched = "39";  // 00039 正式_非在编南院派遣

        /// <summary>
        /// 在职方式 非正式_兼职
        /// </summary>
        public static string HireStyleCode_UnofficialPartTime = "40";  // 00040 非正式_兼职

        /// <summary>
        /// 在职方式 科室自聘
        /// </summary>
        public static string HireStyleCode_DepartmentSelfHired = "50";  // 00050 科室自聘

        /// <summary>
        /// 在职方式 非正式_外包人员
        /// </summary>
        public static string HireStyleCode_UnofficialOutsourcedStaff = "60";  // 00060 非正式_外包人员

        #endregion

        /// <summary>
        /// 离职方式
        /// </summary>
        public static string LeaveStyleCode = "00025";  // 00025	离职方式
        /// <summary>
        /// 离职方式 已故
        /// </summary>
        public static string LeaveStyleCode_Deceased = "10";  // 00025	离职方式
        /// <summary>
        /// 离职方式 退休
        /// </summary>
        public static string LeaveStyleCode_Retire = "4";
        /// <summary>
        /// 离职方式 辞职
        /// </summary>
        public static string LeaveStyleCode_Resign = "1";
        /// <summary>
        /// 离职方式 返聘终止
        /// </summary>
        public static string LeaveStyleCode_RetiredRehiredEnd = "9";
        /// <summary>
        /// 离职方式 培训离站
        /// </summary>
        public static string LeaveStyleCode_Outbound = "11";
        /// <summary>
        /// 婚姻情况
        /// </summary>
        public static string MarryCode = "00001";  // 00001	婚姻情况
        /// <summary>
        /// 民族
        /// </summary>
        public static string NationalityCode = "00028";  // 00028	民族
        /// <summary>
        /// 户口类型
        /// </summary>
        public static string RegisterTypeCode = "00047";  // 00047	户口类型
        /// <summary>
        /// 学位
        /// </summary>
        public static string DegreesCode = "00030";  // 00030	学位
        /// <summary>
        /// 学历
        /// </summary>
        public static string EducationCode = "00008";  // 00008	学历
        /// <summary>
        /// 政治面貌
        /// </summary>
        public static string PartyCode = "00017";  // 00017	政治面貌
        /// <summary>
        /// 招募来源
        /// </summary>
        public static string RecruitmentCategoryCode = "00023";  // 00023	招募来源
        /// <summary>
        /// 招募公司
        /// </summary>
        public static string RecruitmentCompanyCode = "00054";  // 00054	招募公司
        /// <summary>
        /// 职称级别
        /// </summary>
        public static string LevelCode = "00026";  // 00026	职称级别
        /// <summary>
        /// 毕业情况
        /// </summary>
        public static string GraduationCode = "00046";  // 00046	毕业情况
        /// <summary>
        /// 学习方式
        /// </summary>
        public static string LearnWayCode = "00051";  // 00051	学习方式
        /// <summary>
        /// 出国类别
        /// </summary>
        public static string AbroadTypeCode = "00040";  // 00040	出国类别
        /// <summary>
        /// 合同类型
        /// </summary>
        public static string ContractTypeCode = "00049";  // 00049	合同类型
        /// <summary>
        /// 培养计划级别
        /// </summary>
        public static string TrainLevelCode = "00038";  // 00038	培养计划级别
        /// <summary>
        /// 考核结果
        /// </summary>
        public static string EvaluateResultCode = "00036";  // 00036	考核结果
        /// <summary>
        /// 考核年度 开始年份
        /// </summary>
        public static int StartYear = 2006;
        /// <summary>
        /// 考核年度 结束年份
        /// </summary>
        public static int EndYear = 2036;
        /// <summary>
        /// 奖惩类型
        /// </summary>
        public static string IncentTypeCode = "00018";  // 00018	奖惩类型
        /// <summary>
        /// 奖惩级别
        /// </summary>
        public static string IncentLevelCode = "00052";  // 00052	奖惩级别
        /// <summary>
        /// 医疗事故性质
        /// </summary>
        public static string AccidentTypeCode = "00035";  // 00035	医疗事故性质
        /// <summary>
        /// 论文收录情况
        /// </summary>
        public static string IncomeTypeCode = "00042";  // 00042	论文收录情况
        /// <summary>
        /// 课题级别
        /// </summary>
        public static string ClassLevelCode = "00043";  // 00043	课题级别
        /// <summary>
        /// 博硕导
        /// </summary>
        public static string TeacherTypeCode = "00045";  // 00045	博硕导
        /// <summary>
        /// 获奖级别
        /// </summary>
        public static string AwardLevelCode = "00044";  // 00044	获奖级别
        /// <summary>
        /// 出勤状态
        /// </summary>
        public static string WorkState = "00067"; //00067    出勤状态

        /// <summary>
        /// 高级查询类型
        /// </summary>
        public static string AdvancedQueryTypeCode = "00068";  // 00068	高级查询类型

        public static string AdvancedQueryTypeRootCode = "000"; // 简易花名册

        public static string AdvancedQueryTypeRootName = "简易花名册"; // 简易花名册


        /// <summary>
        /// 性别
        /// </summary>
        public static string Sex = "00019";  // 00019	性别


        /// <summary>
        /// 党内职务
        /// </summary>
        public static string PartyPositionsCode = "00081"; //	党内职务

        /// <summary>
        /// 组织关系
        /// </summary>
        public static string OrganizationCode = "00082"; //	组织关系

        /// <summary>
        /// 人员类型 
        /// </summary>
        public static string OtherEmpTypeCode = "00073"; // 00073	 

        /// <summary>
        /// 员工状态 在职状态
        /// </summary>
        public static string EmpStatusStaffCode = "1";

        public static string EmpStatusNoStaffCode = "2";

        /// <summary>
        /// 社保
        /// </summary>
        public static string SocialSecurityTypeCode = "00031"; //00031

        /// <summary>
        /// 证件类型
        /// </summary>
        public static string DocumentType = "00074";

        public static string DocumentTypeIDNum = "1";

        /// <summary>
        /// 行政职务
        /// </summary>
        public static string AdministrativePosition = "00075";

        /// <summary>
        /// 高层次人才类别
        /// </summary>
        public static string HighTalentType = "00076";

        /// <summary>
        /// 年龄段
        /// </summary>
        public static string AgeRange = "00078";

        /// <summary>
        /// 党内职务
        /// </summary>
        public static string PartyPosition_None = "05BCDB79-DDBD-4A64-A8DF-769679CA13C7";

        /// <summary>
        /// 援外类型
        /// </summary>
        public static string AssistanceForeignType = "00084";
    }
}
