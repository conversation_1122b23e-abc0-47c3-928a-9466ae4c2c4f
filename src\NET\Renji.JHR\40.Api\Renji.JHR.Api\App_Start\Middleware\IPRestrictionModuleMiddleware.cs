﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Renji.JHR.Api
{
    public class IPRestrictionModuleMiddleware
    {
        private RequestDelegate _next;
        public IPRestrictionModuleMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            //过滤掉 Response Headers 的banner ,以隐藏服务器和平台信息
            context.Response.Headers.Remove("Server");
            context.Response.Headers.Remove("X-AspNet-Version");
            context.Response.Headers.Remove("X-Frame-Options");

            //await context.Response.WriteAsync("<p>begin</p>");

            await _next.Invoke(context);

            //await context.Response.WriteAsync("<p>begin</p>");
        }
    }
}
