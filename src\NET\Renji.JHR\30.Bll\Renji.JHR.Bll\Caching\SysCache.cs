﻿using Shinsoft.Core.Caching;
using Shinsoft.Core.Caching.Memory;
using Shinsoft.Core.Caching.Redis;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Renji.JHR.Bll.Caching
{
    public class SysCache : BaseEntityCacheClient<SysBll>
    {
        #region override

        protected override CacheType CurrentType => Config.SysRedis.IsEmpty() ? CacheType.Memory : CacheType.Redis;

        private MemoryCacheClient? _memoryCache;

        protected override MemoryCacheClient MemoryCache => _memoryCache ??= new MemoryCacheClient();

        protected override void FlushMemoryCache()
        {
            _memoryCache?.Flush();
            _memoryCache = null;
        }

        private RedisClient? _redisCache;

        protected override RedisClient RedisCache => _redisCache ??= new RedisClient(Config.SysRedis);

        protected override void FlushRedisCache()
        {
            _redisCache?.Flush();
            _redisCache = null;
        }

        protected override void DisposeRedisClinet()
        {
            _redisCache?.Dispose();
        }

        #endregion override

        #region 登录相关缓存，存放于内存中

        #region Permission

        public List<Permission> Permissions => this.GetMemoryCaches<Permission>();

        public Permission GetPermission(Guid id)
        {
            var entity = this.Permissions.FirstOrDefault(p => p.ID == id);
            if (entity == null)
            {
                throw new InvalidOperationException($"{nameof(SysCache)}:未找到ID为【{id}】的权限");
            }

            return entity;
        }

        public Permission? GetPermission(string code)
        {
            return this.Permissions.FirstOrDefault(p => p.Code == code);
        }

        public List<Permission> GetPermissions(List<Guid> ids)
        {
            return this.Permissions.Where(p => ids.Contains(p.ID)).ToList();
        }

        public List<Permission> GetPermissionsByPerant(Guid parentId)
        {
            return this.Permissions.Where(p => p.ParentId == parentId).ToList();
        }

        public List<Permission> GetPermissionsByParent(string parentCode)
        {
            return this.Permissions.Where(p => p.Parent?.Code == parentCode).ToList();
        }

        #endregion Permission

        #endregion 登录相关缓存，存放于内存中

        #region SysSetting

        public List<SysSetting> SysSettings => this.GetCaches<SysSetting>();

        public SysSetting? GetSysSetting(string key)
        {
            return this.GetHashCache<SysSetting>(key, (field) => this.Repo.GetEntity<SysSetting>(p => p.Key == field));
        }

        #endregion SysSetting

        #region Dict

        public List<Dict> Dicts => this.GetCaches<Dict>();

        public Dict? GetDict(Guid id)
        {
            return this.Dicts.FirstOrDefault(p => p.ID == id);
        }

        public Dict? GetDict(string code)
        {
            return this.Dicts.FirstOrDefault(p => p.Code == code);
        }

        public Dict? GetDict(string parentCode, string code)
        {
            return this.Dicts.FirstOrDefault(p => p.Code == code && p.Parent != null && p.Parent.Code == parentCode);
        }

        public List<Dict> GetDicts(Guid parentId)
        {
            return this.Dicts.Where(p => p.ParentId == parentId).ToList();
        }

        public List<Dict> GetDicts(string parentCode)
        {
            return this.Dicts.Where(p => p.Parent?.Code == parentCode).ToList();
        }

        #endregion Dict

        #region SalaryParameter

        public List<SalaryParameter> SalaryParameters => this.GetCaches<SalaryParameter>();

        public SalaryParameter? GetSalaryParameter(Guid id)
        {
            return this.SalaryParameters.FirstOrDefault(p => p.ID == id);
        }

        #endregion SalaryParameter

        #region Station

        public List<Station> Stations => this.GetCaches<Station>();

        public Station? GetStation(Guid id)
        {
            return this.Stations.FirstOrDefault(p => p.ID == id);
        }

        #endregion Station

        #region SalaryScale

        public List<SalaryScale> SalaryScales => this.GetCaches<SalaryScale>();

        public SalaryScale? GetSalaryScale(Guid id)
        {
            return this.SalaryScales.FirstOrDefault(p => p.ID == id);
        }

        #endregion SalaryScale

        #region Seniority

        public List<Seniority> Senioritys => this.GetCaches<Seniority>();

        public Seniority? GetSeniority(Guid id)
        {
            return this.Senioritys.FirstOrDefault(p => p.ID == id);
        }

        public Seniority? GetSeniorityByWorkAge(int workAge)
        {
            return this.Senioritys.FirstOrDefault(p => p.WorkAge == workAge);
        }

        #endregion Seniority

        #region StationAllowance

        public List<StationAllowance> StationAllowances => this.GetCaches<StationAllowance>();

        public StationAllowance? GetStationAllowance(Guid id)
        {
            return this.StationAllowances.FirstOrDefault(p => p.ID == id);
        }

        public StationAllowance? GetStationAllowanceByStatoinId(Guid statioinId, int workAge)
        {
            return this.StationAllowances.FirstOrDefault(p => p.StationId == statioinId && p.WorkAge == workAge);
        }

        #endregion StationAllowance

        #region TelephoneFee

        public List<TelephoneFee> TelephoneFees => this.GetCaches<TelephoneFee>();

        public TelephoneFee? GetTelephoneFee(Guid id)
        {
            return this.TelephoneFees.FirstOrDefault(p => p.ID == id);
        }

        #endregion TelephoneFee

        #region CarSubsidy

        public List<CarSubsidy> CarSubsidys => this.GetCaches<CarSubsidy>();

        public CarSubsidy? GetCarSubsidy(Guid id)
        {
            return this.CarSubsidys.FirstOrDefault(p => p.ID == id);
        }

        #endregion CarSubsidy

        #region SalaryData

        public List<SalaryData> SalaryDatas => this.GetCaches<SalaryData>();

        public SalaryData? GetSalaryData(Guid id)
        {
            return this.SalaryDatas.FirstOrDefault(p => p.ID == id);
        }

        public SalaryData? GetSalaryData(string code)
        {
            return this.SalaryDatas.FirstOrDefault(p => p.Code == code);
        }

        public IEnumerable<SalaryData>? GetSalaryDatas(List<string> codes)
        {
            return this.SalaryDatas.Where(p => codes.Contains(p.Code));
        }

        #endregion SalaryData

        #region VerificationCode

        private static readonly object _verificationCodeLocker = new();

        public string GetVerificationCode(Guid keyId)
        {
            string? code = string.Empty;

            const string key = "VerificationCode";
            lock (_verificationCodeLocker)
            {
                ConcurrentDictionary<Guid, string>? dict = null;

                if (this.MemoryCache.Exists(key))
                {
                    dict = this.MemoryCache.Get<ConcurrentDictionary<Guid, string>>(key);
                }

                dict ??= new();

                if (!dict.TryGetValue(keyId, out code))
                {
                    Random random = new Random();
                    code = random.Next(1000, 9999).AsString();
                    dict.TryAdd(keyId, code);

                    //一分钟过期
                    TimeSpan expirationTime = TimeSpan.FromMinutes(1);
                    this.MemoryCache.Set(key, dict, expirationTime);
                }
            }

            return code;
        }

        public void RemoveVerificationCode(Guid keyId)
        {
            const string key = "VerificationCode";

            lock (_verificationCodeLocker)
            {
                ConcurrentDictionary<Guid, string>? dict = null;

                if (this.MemoryCache.Exists(key))
                {
                    dict = this.MemoryCache.Get<ConcurrentDictionary<Guid, string>>(key);

                    dict?.TryRemove(keyId, out string? removeCode);

                    //一分钟过期
                    TimeSpan expirationTime = TimeSpan.FromMinutes(1);
                    this.MemoryCache.Set(key, dict, expirationTime);
                }
            }
        }

        #endregion VerificationCode
    }
}