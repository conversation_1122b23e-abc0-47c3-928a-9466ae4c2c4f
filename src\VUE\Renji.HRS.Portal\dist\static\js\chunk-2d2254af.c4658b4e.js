(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d2254af"],{e47a:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.headModel}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"发薪月份"}},[a("el-date-picker",{attrs:{type:"month",placeholder:"请选择月份",editable:!1,clearable:!1,"value-format":"yyyy-MM"},model:{value:e.headModel.recordMonth,callback:function(t){e.$set(e.headModel,"recordMonth",t)},expression:"headModel.recordMonth"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"部门"}},[a("c-select-tree",{attrs:{options:e.treeData,"tree-props":e.treeProps},model:{value:e.headModel.dept,callback:function(t){e.$set(e.headModel,"dept",t)},expression:"headModel.dept"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"工号"}},[a("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.headModel.empCode,callback:function(t){e.$set(e.headModel,"empCode",t)},expression:"headModel.empCode"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"员工姓名"}},[a("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.headModel.empName,callback:function(t){e.$set(e.headModel,"empName",t)},expression:"headModel.empName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:""}},[a("el-button",{attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")]),a("el-button",{attrs:{type:"primary"},on:{click:e.exportData}},[e._v("导出")])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"显示值大于0的项"}},[a("el-checkbox",{model:{value:e.headModel.gtZeroValue,callback:function(t){e.$set(e.headModel,"gtZeroValue",t)},expression:"headModel.gtZeroValue"}})],1)],1)],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"}},[a("el-table-column",{attrs:{prop:"date",label:"唯一码",align:"center",width:"120",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.empUid))])]}}])}),a("el-table-column",{attrs:{prop:"date",label:"工号",align:"center",width:"120",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.empCode))])]}}])}),a("el-table-column",{attrs:{prop:"date",label:"姓名",align:"center",width:"150",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.empName))])]}}])}),a("el-table-column",{attrs:{prop:"date",label:"部门",align:"center",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.empDept))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"行政值班费（元）",align:"center",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[l.edit?a("el-input-number",{staticClass:"edit-input",attrs:{min:0,size:"small"},on:{input:function(e){return l.h2=Math.abs(l.h2)}},model:{value:l.h2,callback:function(t){e.$set(l,"h2",t)},expression:"row.h2"}}):a("span",[e._v(e._s(l.h2))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[l.edit?a("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(t){return e.confirmEdit(l)}}},[e._v(" 更新 ")]):e._e(),l.edit?a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.cancelEdit(l)}}},[e._v(" 取消 ")]):e._e(),l.edit?e._e():a("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(t){return e.Edit(l)}}},[e._v(" 编辑 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[20,50,100],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.paginationChanged}})]},proxy:!0}])})],1)},o=[],n=(a("99af"),a("d81d"),a("d3b7"),a("25f0"),a("3ca3"),a("4d90"),a("ddb0"),a("2b3d"),a("d368")),r=a("cfe3"),i="PayrollSet",d=new r["a"](i),s={searchEmployeePayRollSalaryAddModel:function(e){return d.get("SearchEmployeePayRollSalaryAddModel",e)},updateEmployeePayRollSalaryAdd:function(e){return d.post("UpdateEmployeePayRollSalaryAdd",e)},getEmployeePayRollSalaryAddExcel:function(e){return d.getFile("GetEmployeePayRollSalaryAddExcel",e)}},c={components:{},data:function(){return{headModel:{recordMonth:this.getNowTime(),gtZeroValue:!0},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeLoading:!1,treeExpandedKeys:[],currentNode:null,total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,tableData:[],editOringinData:{}}},created:function(){this.loadTree()},methods:{getNowTime:function(){var e=new Date,t=e.getFullYear(),a=e.getMonth();a=a.toString().padStart(2,"0"),"00"===a&&(t-=1,a="12");var l="".concat(t,"-").concat(a);return l},loadTree:function(){var e=this;n["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data,e.treeExpandedKeys.push(t.data[0].id)})).catch((function(e){console.log(e)})),this.resertCurrentNode()},resertCurrentNode:function(){this.currentNode=null},treeNodeClick:function(e){},Edit:function(e){e.edit=!e.edit},cancelEdit:function(e){e.edit=!1,e.h2=e.originalH2},confirmEdit:function(e){var t=this;isNaN(e.h2)?this.$message.error("请输入数值"):(e.edit=!1,e.recordMonth=this.headModel.recordMonth,s.updateEmployeePayRollSalaryAdd(e).then((function(a){if(a.succeed)var l=a.data;else t.$notice.resultTip(a);e.id=l.id,e.lastEditor=l.lastEditor,e.lastEditTime=l.lastEditTime,e.originalH2=l.h2})).catch((function(e){console.log(e)})))},search:function(){this.listQuery.pageIndex=1,this.getSearchResult()},getSearchResult:function(){var e=this;this.listLoading=!0;var t={RecordMonth:this.headModel.recordMonth,DeptId:this.headModel.dept,EmpCode:this.headModel.empCode,EmpName:this.headModel.empName,GtZeroValue:this.headModel.gtZeroValue,PageIndex:this.listQuery.pageIndex,PageSize:this.listQuery.pageSize};s.searchEmployeePayRollSalaryAddModel(t).then((function(t){t.succeed?(e.total=t.data.recordCount,e.tableData=t.data.datas.map((function(t){return e.$set(t,"edit",!1),t.originalH2=t.h2,t}))):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1}))},paginationChanged:function(){this.getSearchResult()},exportData:function(){var e={RecordMonth:this.headModel.recordMonth,DeptId:this.headModel.dept,EmpCode:this.headModel.empCode,EmpName:this.headModel.empName,GtZeroValue:this.headModel.gtZeroValue,PageIndex:this.listQuery.pageIndex,PageSize:this.listQuery.pageSize};s.getEmployeePayRollSalaryAddExcel(e).then((function(e){var t=new Blob([e],{type:e.type}),a="行政值班费.xlsx";if(window.navigator&&window.navigator.msSaveOrOpenBlob)window.navigator.msSaveOrOpenBlob(t,a);else{var l=document.createElement("a"),o=window.URL.createObjectURL(t);l.href=o,l.download=a,document.body.appendChild(l),l.click(),document.body.removeChild(l),window.URL.revokeObjectURL(o)}}))}}},u=c,p=a("2877"),h=Object(p["a"])(u,l,o,!1,null,null,null);t["default"]=h.exports}}]);