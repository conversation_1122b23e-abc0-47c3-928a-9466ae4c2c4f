﻿CREATE TABLE [dbo].[DepartmentEmployeeHistory]
(
	[ID] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY, 
    [DepartmentId] UNIQUEIDENTIFIER NOT NULL, 
    [PreEmployeeId] UNIQUEIDENTIFIER NULL, 
    [NewEmployeeId] UNIQUEIDENTIFIER NULL, 
    [Creator]      NVARCHAR (50)    NULL,
    [CreateTime]   DATETIME         NULL,
    [LastEditor]   NVARCHAR (50)    NULL,
    [LastEditTime] DATETIME         NULL, 
    CONSTRAINT [FK_DepartmentEmployeeHistory_Department] FOREIGN KEY ([DepartmentId]) REFERENCES [Department]([ID]), 
    CONSTRAINT [FK_DepartmentEmployeeHistory_Employee_00_Pre] FOREIGN KEY ([PreEmployeeId]) REFERENCES [dbo].[Employee]([ID]),
    CONSTRAINT [FK_DepartmentEmployeeHistory_Employee_01_New] FOREIGN KEY ([NewEmployeeId]) REFERENCES [dbo].[Employee]([ID])
)
