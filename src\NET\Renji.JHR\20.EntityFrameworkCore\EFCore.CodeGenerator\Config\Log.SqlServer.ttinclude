﻿<#@ import namespace="System.Collections.Generic" #>
<#+
	const string connectionString = "Server=xl7.corp.shinsoft.net,9210;Database=Renji_JHR_Log; uid=*****;pwd=*****;";

	const string database = "Renji_JHR_Log";

	const string entityNamespace = "Renji.JHR.Entities";
	const string entityPrefix = "";

	const string dalNamespace = "Renji.JHR.Dal";
	const string dbContextName = "LogDbContext";

	const string companyIdTypeName = "Guid";
	const string companyTypeName = "";

	string[] entitySchemas = new string[]
	{
		"log",
	};

	string[] entityTables = new string[]
	{
	};

	string[] entityViews = new string[]
	{
	};

	string[] entityViewPrefixes = new string[]
	{
		"Vw"
	};

	string[] enumNamespaces = new string[]
	{
	};
#>