﻿using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Xml.Serialization;

namespace Renji.JHR.Entities
{
    public partial class MajorTechnical : IParent<MajorTechnical>
    {
        #region IParent

        IComparable IParent.ID => this.ID;

        IComparable? IParent.ParentId => this.ParentId;

        #endregion IParent

    }
}