﻿<#@ template debug="false" hostspecific="true" language="C#" #>
<#@ assembly name="System.Core" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Text" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ include file="../../EFCore.CodeGenerator/Config/Log.SqlServer.ttinclude"#>
<#@ include file="../../EFCore.CodeGenerator/DbHelper/SqlServer.ttinclude"#>
<#@ output extension=".cs" #>
<#
	this.GenerationEnvironment.Clear();
	var dbHelper = new DbHelper(connectionString, database);

	var tables = dbHelper.GetTables(entityPrefix, entitySchemas, entityTables, entityViews, entityViewPrefixes);
#>
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Microsoft.EntityFrameworkCore;
using Shinsoft.Core.EntityFrameworkCore;
using <#=entityNamespace#>;

namespace <#=dalNamespace#>
{
    public partial class <#=dbContextName#> : BaseDbContext
    {
	    public <#=dbContextName#>(DbContextOptions options)
			: base(options)
        { 
		}
<#
	foreach(var table in tables)
	{
		var entityName = table.SysTypeName;
#>

        /// <summary>
        /// <#=table.Comment#>
        /// </summary>
		public DbSet<<#=entityName#>> <#=entityName#> { get; set; }
<#
	}
#>

	}
}