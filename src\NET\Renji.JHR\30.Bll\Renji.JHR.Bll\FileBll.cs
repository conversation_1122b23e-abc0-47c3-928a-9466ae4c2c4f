﻿using Renji.JHR.Common;
using Renji.JHR.Common.Configration;
using Renji.JHR.Entities;
using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Text.RegularExpressions;
using System.Transactions;

namespace Renji.JHR.Bll
{
    public class FileBll : BaseFileBll
    {
        #region Constructs

        public FileBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public FileBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public FileBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public FileBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        #region Save File

        #region public

        /// <summary>
        /// 保存文件
        /// </summary>
        /// <param name="entity">文件索引实体</param>
        /// <param name="fileStream">文件流</param>
        /// <param name="uniqueCheck">是否做唯一性检查</param>
        /// <returns></returns>
        public BizResult<FileIndex> SaveFile(FileIndex entity, Stream fileStream, bool uniqueCheck = true)
        {
            return this.SaveFile(entity, fileStream, string.Empty, string.Empty, uniqueCheck, true);
        }

        /// <summary>
        /// 保存文件
        /// </summary>
        /// <param name="entity">文件索引实体</param>
        /// <param name="fileStream">文件流</param>
        /// <param name="baseFolder">基本目录（不为空时，强制使用文件方式存储）</param>
        /// <param name="subFolder">子目录（以"\"分割的字符串，其中{}内为日期格式化字符串）</param>
        /// <param name="uniqueCheck">是否做唯一性检查</param>
        /// <returns></returns>
        public BizResult<FileIndex> SaveFile(FileIndex entity, Stream fileStream, string baseFolder, string subFolder, bool uniqueCheck = true)
        {
            return this.SaveFile(entity, fileStream, baseFolder, subFolder, uniqueCheck, true);
        }

        #endregion public

        #region internal

        private const string _subRegex = "{[A-Za-z0-9_ -]*}";

        /// <summary>
        /// 保存文件
        /// </summary>
        /// <param name="entity">文件索引实体</param>
        /// <param name="fileStream">文件流</param>
        /// <param name="baseFolder">基本目录（不为空时，强制使用文件方式存储）</param>
        /// <param name="subFolder">子目录（以"\"分割的字符串，其中{}内为日期格式化字符串）</param>
        /// <param name="uniqueCheck">是否做唯一性检查</param>
        /// <param name="save">是否保存FileIndex(文件方式保存时，文件必定会保存)</param>
        /// <returns></returns>
        internal BizResult<FileIndex> SaveFile(FileIndex entity, Stream fileStream, string baseFolder, string subFolder, bool uniqueCheck, bool save)
        {
            var result = new BizResult<FileIndex>();

            if (fileStream.Length <= 0)
            {
                result.Error("文件流不存在");
            }
            else if (entity.FileName.IsEmpty())
            {
                result.Error("文件名为空");
            }
            else if (entity.ContentType.IsEmpty())
            {
                result.Error("文件类型为空");
            }
            else
            {
                entity.FileSize = fileStream.Length;
                entity.MD5 = fileStream.ToMD5();
                entity.SHA1 = fileStream.ToSHA1();

                var fileInfo = new FileInfo(entity.FileName);
                entity.FileName = fileInfo.Name;
                entity.FileExt = fileInfo.Extension;
            }

            if (result.Succeed)
            {
                var newFile = true;
                var fileSize = entity.FileSize;

                if (uniqueCheck)
                {
                    var md5 = entity.MD5;
                    var sha1 = entity.SHA1;
                    var dbFile = this.GetEntity<FileIndex>(p => p.FileSize == fileSize && p.MD5 == md5 && p.SHA1 == sha1);

                    if (dbFile != null)
                    {
                        newFile = false;
                        entity = dbFile;
                    }
                }

                if (newFile)
                {
                    Guid id = CombGuid.NewGuid();

                    entity.ID = id;

                    byte[] fileContent = new byte[fileSize];

                    fileStream.Position = 0;
                    fileStream.Read(fileContent, 0, fileContent.Length);

                    if (!baseFolder.IsEmpty() || !subFolder.IsEmpty() || Config.FileStore.Type == FileStoreType.File)
                    {
                        #region 文件存储

                        var fileName = $"{id}{entity.FileExt}";

                        if (baseFolder.IsEmpty())
                        {
                            baseFolder = Config.FileStore.BaseFolder;
                        }

                        if (subFolder.IsEmpty())
                        {
                            subFolder = Config.FileStore.SubFolder;
                        }

                        var subFormats = subFolder.Trim('\\').Split('\\');

                        if (baseFolder.IsEmpty())
                        {
                            throw new InvalidOperationException("文件存储目录设置为空");
                        }
                        else
                        {
                            var baseDir = new DirectoryInfo(baseFolder);

                            if (!baseDir.Exists)
                            {
                                baseDir.Create();
                            }
                        }

                        var subPath = string.Empty;

                        var now = DateTime.Now;
                        foreach (var subFormat in subFormats)
                        {
                            var sub = new StringBuilder(subFormat);

                            var matches = Regex.Matches(subFormat, _subRegex);

                            foreach (Match match in matches)
                            {
                                var dateFormat = match.Value.Trim('{', '}');
                                sub.Replace(match.Value, now.ToString(dateFormat));
                            }

                            subPath = Path.Combine(subPath, sub.ToString());
                            var subDir = new DirectoryInfo(Path.Combine(baseFolder, subPath));

                            if (!subDir.Exists)
                            {
                                subDir.Create();
                            }
                        }

                        subPath = Path.Combine(subPath, fileName);

                        var filePath = Path.Combine(baseFolder, subPath);

                        FileStream? fs = null;

                        try
                        {
                            fs = new FileStream(filePath, FileMode.Create, FileAccess.Write);
                            fs.Write(fileContent, 0, fileContent.Length);
                        }
                        finally
                        {
                            if (fs != null)
                            {
                                fs.Close();
                                fs.Dispose();
                            }
                        }
                        entity.BaseFolder = baseFolder;
                        entity.SubPath = subPath;

                        #endregion 文件存储
                    }
                    else
                    {
                        #region 数据库存储

                        entity.FileContent = new FileContent
                        {
                            ID = id,
                            Content = fileContent
                        };

                        #endregion 数据库存储
                    }

                    this.Add(entity, save);
                }

                result.Data = entity;
            }

            return result;
        }

        #endregion internal

        #endregion Save File

        public BizResult<Attachment> SaveFile(Attachment entity)
        {
            var result = new BizResult<Attachment>();

            var stream = entity.Stream;
            var fileSize = stream.Length;

            FileIndex file = new FileIndex();

            if (stream == null || fileSize <= 0)
            {
                result.Error("文件流不存在");
            }
            else
            {
                file.FileSize = fileSize;
                file.MD5 = stream.ToMD5();
                file.SHA1 = stream.ToSHA1();

                file.ContentType = entity.ContentType;
                file.FileName = entity.FileName;
                file.FileExt = entity.FileExt;

                file.BaseFolder = string.Empty;
                file.SubPath = string.Empty;
            }

            if (result.Succeed)
            {
                stream = stream.Value();

                var md5 = file.MD5;
                var sha1 = file.SHA1;
                var dbFile = this.GetEntity<FileIndex>(p => p.FileSize == fileSize && p.MD5 == md5 && p.SHA1 == sha1);

                if (dbFile == null)
                {
                    Guid id = CombGuid.NewGuid();

                    file.ID = id;

                    byte[] fileContent = new byte[fileSize];

                    stream.Position = 0;
                    stream.Read(fileContent, 0, fileContent.Length);

                    if (Config.FileStore.Type == FileStoreType.Database)
                    {
                        file.FileContent = new FileContent
                        {
                            ID = id,
                            Content = fileContent
                        };
                    }
                    else
                    {
                        var fileName = $"{id}{entity.FileExt}";
                        var baseFolder = Config.FileStore.BaseFolder;
                        var subFolder = Config.FileStore.SubFolder;

                        var subFormats = subFolder.Trim('\\').Split('\\');

                        if (baseFolder.IsEmpty())
                        {
                            throw new Exception("文件存储目录设置为空");
                        }
                        else
                        {
                            var baseDir = new DirectoryInfo(baseFolder);

                            if (!baseDir.Exists)
                            {
                                baseDir.Create();
                            }
                        }

                        var subPath = string.Empty;

                        var now = SysDateTime.Now;
                        foreach (var subFormat in subFormats)
                        {
                            var sub = new StringBuilder(subFormat);

                            var matches = Regex.Matches(subFormat, _subRegex);

                            foreach (Match match in matches)
                            {
                                var dateFormat = match.Value.Trim('{', '}');
                                sub.Replace(match.Value, now.ToString(dateFormat));
                            }

                            subPath = Path.Combine(subPath, sub.ToString());
                            var subDir = new DirectoryInfo(Path.Combine(baseFolder, subPath));

                            if (!subDir.Exists)
                            {
                                subDir.Create();
                            }
                        }

                        subPath = Path.Combine(subPath, fileName);

                        var filePath = Path.Combine(baseFolder, subPath);

                        FileStream? fs = null;

                        try
                        {
                            fs = new FileStream(filePath, FileMode.Create, FileAccess.Write);
                            fs.Write(fileContent, 0, fileContent.Length);
                        }
                        finally
                        {
                            if (fs != null)
                            {
                                fs.Close();
                                fs.Dispose();
                            }
                        }
                        file.BaseFolder = baseFolder;
                        file.SubPath = subPath;
                    }

                    this.Add(file);
                }
                else
                {
                    file = dbFile;
                }

                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity.FileIndexId = file.ID;

                this.BizRepo.Add(entity);

                result.Data = entity;
            }

            return result;
        }

        internal byte[]? GetFileBytes(FileIndex fileIndex)
        {
            byte[]? fileBytes = null;

            if (!fileIndex.BaseFolder.IsEmpty() && !fileIndex.SubPath.IsEmpty())
            {
                var filePath = Path.Combine(fileIndex.BaseFolder, fileIndex.SubPath);

                if (File.Exists(filePath))
                {
                    fileBytes = File.ReadAllBytes(filePath);
                }
            }
            else if (fileIndex.FileContent != null)
            {
                fileBytes = fileIndex.FileContent.Content;
            }

            return fileBytes;
        }

        internal byte[]? GetFileBytes(Guid fileIndexId)
        {
            var fileIndex = this.Get<FileIndex>(fileIndexId);

            return fileIndex == null
                ? null
                : this.GetFileBytes(fileIndex);
        }

        internal Stream? GetFileStream(FileIndex fileIndex)
        {
            var fileBytes = this.GetFileBytes(fileIndex);

            return fileBytes == null
                ? null
                : new MemoryStream(fileBytes);
        }

        internal Stream? GetFileStream(Guid fileIndexId)
        {
            var fileIndex = this.Get<FileIndex>(fileIndexId);

            return fileIndex == null
                ? null
                : this.GetFileStream(fileIndex);
        }
    }
}