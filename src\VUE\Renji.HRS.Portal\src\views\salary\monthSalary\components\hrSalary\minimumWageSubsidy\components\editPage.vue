<template>
  <div>
    <el-dialog :title="title" :visible="showDialog" width="80%" :close-on-press-escape="false" :close-on-click-modal="false" @close="closeDialog">
      <el-form ref="dataForm" :rules="rules" :model="dataModel" label-width="100px">
        <el-card style="margin-top: 3px;">
          <div slot="header">
            <span>基础信息</span>
          </div>
          <el-row>
            <el-col :span="6">
              <el-form-item label="员工姓名">
                <div>
                  <span>
                    {{ dataModel.employeeModel.empName }}
                  </span>
                  <el-button v-if="!isEdit" style="margin-left: 15px;" type="primary" title="选择员工" @click="selectEmployeeDialog">选 择</el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="唯一码">
                {{ dataModel.employeeModel.empUid }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="工号">
                {{ dataModel.employeeModel.empCode }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="性别">
                {{ dataModel.employeeModel.genderDesc }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="备注">
                <el-input v-model="dataModel.remark" type="textarea" :rows="3" maxlength="300" placeholder="备注" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" :loading="btnSaveLoading" @click="saveDialog">保 存</el-button>
      </div>
    </el-dialog>

    <selectUserComponent ref="selectEmployee" @selectRow="setEmployee" />

  </div>
</template>
<script>
import salaryApi from '@/api/salary'
import selectUserComponent from '@/views/salary/employeeSalary/components/selectUser'

export default {
  components: {
    selectUserComponent
  },
  data() {
    return {
      showDialog: false,
      title: '',
      rules: {
      },
      btnSaveLoading: false,
      isEdit: false,
      dataModel: {
        employeeModel: {}
      }
    }
  },
  methods: {
    initDialog(row) {
      if (!row) {
        this.title = '新增最低工资补助'
        this.isEdit = false
      } else {
        this.title = '编辑最低工资补助'
        this.isEdit = true
        this.getData(row.id)
      }
      this.dataModel.salaryId = this.salaryId
      this.showDialog = true
    },
    getData(id) {
      salaryApi.getMinimumWageSubsidy({ id: id }).then(res => {
        if (res.succeed) {
          this.dataModel = res.data
          this.setEmployee(res.data.employee)
        }
      }).catch(res => {
      })
    },
    selectEmployeeDialog() {
      this.$refs.selectEmployee.loadTree()
      this.$refs.selectEmployee.showEmployee = true
    },
    saveDialog() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.btnSaveLoading = true
          if (!this.isEdit) {
            salaryApi.addMinimumWageSubsidy(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '添加成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          } else {
            salaryApi.updateMinimumWageSubsidy(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '修改成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          }
        }
      })
    },
    closeDialog() {
      this.dataModel = {
        employeeModel: {}
      }
      this.showDialog = false
      this.$refs.dataForm.resetFields()
    },
    setEmployee(emp) {
      this.dataModel.employeeId = emp.id
      this.dataModel.salaryId = this.salaryId
      this.$set(this.dataModel, 'employeeModel',
        {
          employeeId: emp.id,
          empUid: emp.uid,
          empCode: emp.empCode,
          empName: emp.displayName,
          genderDesc: emp.enumGenderDesc,
          empDept: emp.deptName,
          hospitalAreaNameText: emp.hospitalAreaNameText,
          identityNumber: emp.identityNumber,
          hireStyleName: emp.employeeHR.hireStyleName,
          leaveStyleName: emp.employeeHR.leaveStyleName,
          deadDate: emp.employeeHR.deadDate
        })
    }
  }
}
</script>

<style>
</style>
