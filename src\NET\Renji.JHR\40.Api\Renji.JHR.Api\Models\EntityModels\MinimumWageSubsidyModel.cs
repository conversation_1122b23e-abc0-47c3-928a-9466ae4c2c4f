using Renji.JHR.Entities;
using Shinsoft.Core.AutoMapper;
using System;
using System.ComponentModel.DataAnnotations;

namespace Renji.JHR.Api.Models
{
    /// <summary>
    /// 最低工资补助模型
    /// </summary>
    public partial class MinimumWageSubsidyModel
    {
        /// <summary>
        /// 员工信息
        /// </summary>
        [MapFromProperty(typeof(MinimumWageSubsidy), MinimumWageSubsidy.Foreigns.Employee)]
        public EmployeeModel? Employee { get; set; }

        /// <summary>
        /// 薪资信息
        /// </summary>
        [MapFromProperty(typeof(MinimumWageSubsidy), MinimumWageSubsidy.Foreigns.Salary)]
        public SalaryModel? Salary { get; set; }
    }
}
