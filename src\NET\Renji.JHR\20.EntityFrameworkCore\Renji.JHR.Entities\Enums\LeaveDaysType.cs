﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Renji.JHR.Entities
{
    /// <summary>
    /// 请假类型
    /// </summary>
    public enum LeaveDaysType
    {
        None = 0,

        /// <summary>
        /// 卫生津贴
        /// </summary>
        [Description("卫生津贴")]
        [EnumGroup(Visible = false)]
        HealthAllowance = 1,

        /// <summary>
        /// 病假
        /// </summary>
        [Description("病假")]
        SickLeave = 2,

        /// <summary>
        /// 事假
        /// </summary>
        [Description("事假")]
        PersonalLeave = 3,

        /// <summary>
        /// 产假
        /// </summary>
        [Description("产假")]
        MaternityLeave = 4,

        /// <summary>
        /// 哺乳假
        /// </summary>
        [Description("哺乳假")]
        BreastfeedingLeave = 5,

        /// <summary>
        /// 探亲假
        /// </summary>
        [Description("探亲假")]
        HomeLeave = 6,

        /// <summary>
        /// 婚丧假
        /// </summary>
        [Description("婚丧假")]
        MarriageBereavementLeave = 8,
    }
}
