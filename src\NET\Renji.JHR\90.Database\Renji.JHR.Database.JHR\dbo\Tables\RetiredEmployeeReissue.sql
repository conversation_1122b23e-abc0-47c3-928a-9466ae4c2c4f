﻿CREATE TABLE [dbo].[RetiredEmployeeReissue]
(
    [ID] UNIQUEIDENTIFIER NOT NULL ,
    [EmployeeId]   UNIQUEIDENTIFIER NOT NULL,
    [SalaryId] UNIQUEIDENTIFIER NOT NULL,
    [Remark] NVARCHAR(300) NULL ,
    [BackPay]       DECIMAL(18, 4) NULL,

    [Deleted]       BIT NOT NULL,
    [Creator]       NVARCHAR(50)        NULL,
    [CreateTime]    DATETIME            NULL,
    [LastEditor]    NVARCHAR(50)        NULL,
    [LastEditTime]  DATETIME            NULL,
    CONSTRAINT [PK_RetiredEmployeeReissue] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_RetiredEmployeeReissue_Employee] FOREIGN KEY ([EmployeeId]) REFERENCES [dbo].[Employee] ([ID]),
    CONSTRAINT [FK_RetiredEmployeeReissue_Salary] FOREIGN KEY ([SalaryId]) REFERENCES [dbo].[Salary] ([ID])
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'退休人员补发',
    @level0type = N'SCHEMA',
    @level0name = N'dbo', 
    @level1type = N'TABLE', 
    @level1name = N'RetiredEmployeeReissue';
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'备注',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RetiredEmployeeReissue',
    @level2type = N'COLUMN',
    @level2name = N'Remark';
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'补发金额',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RetiredEmployeeReissue', 
    @level2type = N'COLUMN',
    @level2name = N'BackPay';
GO