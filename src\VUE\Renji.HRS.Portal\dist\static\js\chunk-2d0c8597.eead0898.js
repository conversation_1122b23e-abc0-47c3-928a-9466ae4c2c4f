(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c8597"],{5501:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-row",{staticClass:"filter-container",attrs:{gutter:11,type:"flex"}},[a("el-col",{attrs:{span:4}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"类型"},model:{value:e.listQuery.enumCalendarType,callback:function(t){e.$set(e.listQuery,"enumCalendarType",t)},expression:"listQuery.enumCalendarType"}},e._l(e.calendarTypeList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),a("el-col",{attrs:{span:3.5}},[a("el-date-picker",{attrs:{type:"date",clearable:!1,"value-format":"yyyy-MM-dd",placeholder:"请选择开始日期"},model:{value:e.listQuery.beginDate,callback:function(t){e.$set(e.listQuery,"beginDate",t)},expression:"listQuery.beginDate"}})],1),a("el-col",{attrs:{span:3.5}},[a("el-date-picker",{attrs:{type:"date",placeholder:"请选择结束日期","value-format":"yyyy-MM-dd"},model:{value:e.listQuery.endDate,callback:function(t){e.$set(e.listQuery,"endDate",t)},expression:"listQuery.endDate"}})],1),a("el-col",{attrs:{span:8}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.showDialog()}}},[e._v("添加")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","default-sort":{prop:"Date",order:"descending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{label:"日期",sortable:"custom",prop:"date","header-align":"center",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.date?new Date(o.date).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{sortable:"custom",prop:"EnumCalendarType",label:"类型","header-align":"center",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.enumCalendarTypeDesc))])]}}])}),a("el-table-column",{attrs:{sortable:"custom",prop:"EnumChoiseType",label:"选择类型","header-align":"center",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.enumChoiseTypeDesc))])]}}])}),a("el-table-column",{attrs:{sortable:"custom",prop:"OverTimeTypesDesc",label:"加班类型","header-align":"center",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.overTimeTypesDesc))])]}}])}),a("el-table-column",{attrs:{label:"备注","min-width":"150px ","header-align":"center",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",{staticStyle:{"white-space":"pre-wrap"}},[e._v(e._s(o.remark))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","header-align":"center",width:"160","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.showDialog(o)}}},[e._v(" 编辑 ")]),a("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.deleteData(o)}}},[e._v(" 删除 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}}),a("el-row",[a("el-col",{staticStyle:{width:"520px"}},[a("div",{staticStyle:{"margin-top":"20px","background-color":"#fff7f7",border:"1px solid #f56c6c","border-radius":"4px",padding:"15px",color:"#f56c6c","font-size":"14px","box-shadow":"0 2px 4px rgba(0, 0, 0, 0.1)","line-height":"1.6"}},[a("i",{staticClass:"el-icon-warning",staticStyle:{"margin-right":"8px"}}),a("strong",[e._v("提示：")]),a("ol",{staticStyle:{"padding-left":"20px","margin-top":"8px"}},[a("li",[e._v("特定的日历需要设置")]),a("li",[e._v("工作日改为周末需要设置")]),a("li",[e._v("工作日改为节假日需要设置")]),a("li",[e._v("周末改为工作日需要设置")]),a("li",[e._v("周末改为节假日需要设置")]),a("span",[e._v("例：2025年5月1日假期"),a("br"),e._v(" (1)2025年4月27日设置为工作日"),a("br"),e._v(" (2)2025年4月30日的24小时班，医生24小时班设置为节假日"),a("br"),e._v(" (3)2025年5月1日设置为节假日"),a("br"),e._v(" (4)2025年5月2日设置为节假日"),a("br"),e._v(" (5)2025年5月3日设置为节假日"),a("br"),e._v(" (6)2025年5月4日为周末,无需设置"),a("br"),e._v(" (7)2025年5月5日设置为周末")])])])])],1)]},proxy:!0}])}),a("editDialog",{ref:"editDialog",on:{refreshData:e.getPageList}})],1)},i=[],l=a("2efc"),n=a("f9ac"),s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{title:e.title,visible:e.showDialog,width:"40%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:e.closeDialog}},[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.dataModel,"label-width":"100px"}},[a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"日期",prop:"date"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:e.isEdit,editable:!1,clearable:!1,type:"date",placeholder:"选择日期"},model:{value:e.dataModel.date,callback:function(t){e.$set(e.dataModel,"date",t)},expression:"dataModel.date"}})],1)],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"类型",prop:"enumCalendarType"}},[a("el-select",{staticStyle:{width:"60%"},attrs:{placeholder:"类型",disabled:e.isEdit,editable:!1,clearable:!1},model:{value:e.dataModel.enumCalendarType,callback:function(t){e.$set(e.dataModel,"enumCalendarType",t)},expression:"dataModel.enumCalendarType"}},e._l(e.calendarTypeList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1)],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"选择类型",prop:"enumChoiseType"}},[a("el-select",{staticStyle:{width:"60%"},attrs:{placeholder:"类型"},on:{change:e.choiseTypeChange},model:{value:e.dataModel.enumChoiseType,callback:function(t){e.$set(e.dataModel,"enumChoiseType",t)},expression:"dataModel.enumChoiseType"}},e._l(e.choiseTypeList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1)],1)],1),e.showChoiseType?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"4小时班"}},[a("el-checkbox",{on:{change:e.handleChange1},model:{value:e.hour_4,callback:function(t){e.hour_4=t},expression:"hour_4"}})],1),a("el-form-item",{attrs:{label:"12小时班"}},[a("el-checkbox",{on:{change:e.handleChange3},model:{value:e.hour_12,callback:function(t){e.hour_12=t},expression:"hour_12"}})],1),a("el-form-item",{attrs:{label:"24小时班"}},[a("el-checkbox",{on:{change:e.handleChange5},model:{value:e.hour_24,callback:function(t){e.hour_24=t},expression:"hour_24"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"8小时班"}},[a("el-checkbox",{on:{change:e.handleChange2},model:{value:e.hour_8,callback:function(t){e.hour_8=t},expression:"hour_8"}})],1),a("el-form-item",{attrs:{label:"16小时班"}},[a("el-checkbox",{on:{change:e.handleChange4},model:{value:e.hour_16,callback:function(t){e.hour_16=t},expression:"hour_16"}})],1),a("el-form-item",{attrs:{label:"医生24小时班"}},[a("el-checkbox",{on:{change:e.handleChange6},model:{value:e.hour_Doctor24,callback:function(t){e.hour_Doctor24=t},expression:"hour_Doctor24"}})],1)],1)],1):e._e(),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{type:"textarea",rows:3,maxlength:"500",placeholder:"备注"},model:{value:e.dataModel.remark,callback:function(t){e.$set(e.dataModel,"remark",t)},expression:"dataModel.remark"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.closeDialog}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnSaveLoading},on:{click:e.saveDialog}},[e._v("保 存")])],1)],1)],1)},r=[],c=(a("ac1f"),a("1276"),{data:function(){return{showDialog:!1,title:"",rules:{date:[{required:!0,message:"日期必选",trigger:"change"}],enumCalendarType:[{required:!0,message:"类型必选",trigger:"change"}],enumChoiseType:[{required:!0,message:"选择类型必选",trigger:"change"}]},btnSaveLoading:!1,isEdit:!1,dataModel:{},calendarTypeList:[],choiseTypeList:[],showChoiseType:!1,overTimeTypes:[],overTimeType:"",hour_4:!1,hour_8:!1,hour_12:!1,hour_16:!1,hour_24:!1,hour_Doctor24:!1}},methods:{initDialog:function(e){e?(this.title="编辑日历",this.isEdit=!0,this.getData(e.id)):(this.title="新增日历",this.isEdit=!1),this.showDialog=!0,this.enumCalendarTypeList(),this.enumChoiseTypeList()},getData:function(e){var t=this;l["a"].getCalendar({id:e}).then((function(e){e.succeed&&(t.dataModel=e.data,t.showChoiseType=!1,2===t.dataModel.enumChoiseType&&(t.showChoiseType=!0,t.overTimeTypes=t.dataModel.overTimeTypes.split(","),"1"===t.overTimeTypes[0]?t.hour_4=!0:t.hour_4=!1,"1"===t.overTimeTypes[1]?t.hour_8=!0:t.hour_8=!1,"1"===t.overTimeTypes[2]?t.hour_12=!0:t.hour_12=!1,"1"===t.overTimeTypes[3]?t.hour_16=!0:t.hour_16=!1,"1"===t.overTimeTypes[4]?t.hour_24=!0:t.hour_24=!1,"1"===t.overTimeTypes[5]?t.hour_Doctor24=!0:t.hour_Doctor24=!1))})).catch((function(e){}))},choiseTypeChange:function(){2===this.dataModel.enumChoiseType?this.showChoiseType=!0:this.showChoiseType=!1},handleChange1:function(e){this.hour_4=e,this.$forceUpdate()},handleChange2:function(e){this.hour_8=e,this.$forceUpdate()},handleChange3:function(e){this.hour_12=e,this.$forceUpdate()},handleChange4:function(e){this.hour_16=e,this.$forceUpdate()},handleChange5:function(e){this.hour_24=e,this.$forceUpdate()},handleChange6:function(e){this.hour_Doctor24=e,this.$forceUpdate()},getoverTimeType:function(){this.overTimeType="",this.overTimeType+=!0===this.hour_4?"1,":"0,",this.overTimeType+=!0===this.hour_8?"1,":"0,",this.overTimeType+=!0===this.hour_12?"1,":"0,",this.overTimeType+=!0===this.hour_16?"1,":"0,",this.overTimeType+=!0===this.hour_24?"1,":"0,",this.overTimeType+=!0===this.hour_Doctor24?"1,":"0,",this.dataModel.overTimeTypes=this.overTimeType},saveDialog:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.btnSaveLoading=!0,e.getoverTimeType(),e.isEdit?l["a"].updateCalendar(e.dataModel).then((function(t){t.succeed&&(e.$message({message:"修改成功",type:"success"}),e.btnSaveLoading=!1,e.$emit("refreshData"),e.closeDialog())})).catch((function(t){e.btnSaveLoading=!1})):l["a"].addCalendar(e.dataModel).then((function(t){t.succeed&&(e.$message({message:"添加成功",type:"success"}),e.btnSaveLoading=!1,e.$emit("refreshData"),e.closeDialog())})).catch((function(t){e.btnSaveLoading=!1})))}))},closeDialog:function(){this.dataModel={},this.hour_4=!1,this.hour_8=!1,this.hour_12=!1,this.hour_16=!1,this.hour_24=!1,this.hour_Doctor24=!1,this.showDialog=!1,this.showChoiseType=!1,this.$refs.dataForm.resetFields()},enumCalendarTypeList:function(){var e=this,t={enumType:"CalendarType"};n["a"].getEnumInfos(t).then((function(t){e.calendarTypeList=t.data.datas})).catch((function(e){console.log(e)}))},enumChoiseTypeList:function(){var e=this,t={enumType:"ChoiseType"};n["a"].getEnumInfos(t).then((function(t){e.choiseTypeList=t.data.datas})).catch((function(e){console.log(e)}))}}}),d=c,u=a("2877"),h=Object(u["a"])(d,s,r,!1,null,null,null),p=h.exports,g={components:{editDialog:p},data:function(){return{addForm:{},dataList:[],calendarTypeList:[],total:0,listQuery:{pageIndex:1,pageSize:10,order:"+Date",enumCalendarType:null,beginDate:null},listLoading:!1}},created:function(){this.loadCalendarType(),this.getPageList()},methods:{getPageList:function(){var e=this;if(null===this.listQuery.beginDate){var t=new Date,a=t.getFullYear();this.listQuery.beginDate=new Date(a,0,1)}this.listLoading=!0,l["a"].queryCalendar(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.dataList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},loadCalendarType:function(){var e=this;n["a"].getEnumInfos({enumType:"CalendarType"}).then((function(t){e.calendarTypeList=t.data.datas})).catch((function(e){console.log(e)}))},search:function(){this.listQuery.pageIndex=1,this.getPageList()},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="-"),"ascending"===e.order&&(o="+"),this.listQuery.order=o+e.prop,this.getPageList()},showDialog:function(e){this.$refs.editDialog.initDialog(e)},deleteData:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteCalendar(e).then((function(e){e.succeed?(t.getPageList(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))}}},m=g,f=Object(u["a"])(m,o,i,!1,null,null,null);t["default"]=f.exports}}]);