﻿using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Renji.JHR.Common
{
    public static class DictionaryExtender
    {
        public static Dictionary<TKey, TValue> Filter<TKey, TValue>(this Dictionary<TKey, TValue> dictionary, List<TKey> list)
            where TK<PERSON> : notnull
        {
            if (list == null)
                return new Dictionary<TKey, TValue>();

            var dict = dictionary.Where(d => list.Contains(d.Key));

            return dict.ToDictionary(d => d.Key, d => d.Value);
        }

        public static bool ContainsKeyIgnoreCase<TKey, TValue>(this Dictionary<TKey, TValue> dictionary, TKey checkKey)
            where TKey : notnull
        {
            if (typeof(TKey) == typeof(string))
            {
                return dictionary.Keys.Any(key => string.Equals(key.AsString(), checkKey.AsString(), StringComparison.CurrentCultureIgnoreCase));
            }

            return dictionary.ContainsKey(checkKey);
        }

        public static TValue? GetValueIgnoreCase<TKey, TValue>(this Dictionary<TKey, TValue> dictionary, TKey checkKey)
            where TK<PERSON> : notnull
        {
            if (typeof(TKey) == typeof(string))
            {
                foreach (var item in dictionary.Keys.Where(item => string.Equals(item.AsString(), checkKey.AsString(), StringComparison.CurrentCultureIgnoreCase)))
                {
                    return dictionary[item];
                }
                return default;
            }

            return dictionary.ContainsKey(checkKey) ? dictionary[checkKey] : default;
        }
    }
}
