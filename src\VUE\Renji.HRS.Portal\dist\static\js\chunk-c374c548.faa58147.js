(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c374c548"],{"3cb7":function(t,e,a){"use strict";var o=a("bbef"),l=a.n(o);l.a},7788:function(t,e,a){"use strict";var o=a("c8dd"),l=a.n(o);l.a},"78d5":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:t._u([{key:"main",fn:function(){return[a("el-row",{staticClass:"filter-container",attrs:{gutter:11,type:"flex"}},[a("el-col",{attrs:{span:4}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"状态"},model:{value:t.listQuery.enumSalaryStatus,callback:function(e){t.$set(t.listQuery,"enumSalaryStatus",e)},expression:"listQuery.enumSalaryStatus"}},t._l(t.salaryStatusList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.desc,value:t.value}})})),1)],1),a("el-col",{attrs:{span:5}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM",type:"monthrange","range-separator":"至","start-placeholder":"开始月份","end-placeholder":"结束月份"},model:{value:t.listQuery.months,callback:function(e){t.$set(t.listQuery,"months",e)},expression:"listQuery.months"}})],1),a("el-col",{attrs:{span:8}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.search}},[t._v("查询")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(e){return t.showDialog()}}},[t._v("添加")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:t.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","default-sort":{prop:"Month",order:"descending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":t.sortChange}},[a("el-table-column",{attrs:{label:"薪资月份",prop:"Month",sortable:"custom","header-align":"center",align:"center","min-width":"90px"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[a("span",[t._v(" "+t._s(o.month&&t.$moment(o.month).isSame(t.$moment(o.month).endOf("year"),"day")?t.$moment(o.month).year()+"-13":null==o.month||void 0==o.month?"":t.$moment(o.month).format("YYYY-MM"))+" ")])]}}])}),a("el-table-column",{attrs:{sortable:"custom",prop:"EmployeeCount",label:"员工数量","header-align":"center",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[a("span",[t._v(t._s(0==o.employeeCount||null==o.employeeCount||void 0==o.employeeCount?"-":o.employeeCount))])]}}])}),a("el-table-column",{attrs:{sortable:"custom",prop:"TotalSalary",label:"总薪资","header-align":"right",align:"right"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[a("span",[t._v(t._s(t._f("formatMoney2")(0==o.totalSalary||null==o.totalSalary||void 0==o.totalSalary?"-":o.totalSalary)))])]}}])}),a("el-table-column",{attrs:{sortable:"custom",prop:"EnumStatus",label:"状态","header-align":"center",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[a("span",[t._v(t._s(o.enumStatusDesc))])]}}])}),a("el-table-column",{attrs:{label:"备注","min-width":"150px ","header-align":"center",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[a("span",{staticStyle:{"white-space":"pre-wrap"}},[t._v(t._s(o.remark))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","header-align":"center",width:"200","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[1==o.enumStatus||2==o.enumStatus||3==o.enumStatus?a("el-button",{staticStyle:{"margin-left":"5px !important","padding-left":"5px !important",background:"#60db2c",color:"#FFFFFF"},attrs:{size:"mini"},on:{click:function(e){return t.handlHrSalary(o)}}},[t._v(" 人事数据 ")]):t._e(),2==o.enumStatus||3==o.enumStatus?a("el-button",{staticStyle:{"margin-left":"5px !important","padding-left":"5px !important"},attrs:{type:"warning",size:"mini"},on:{click:function(e){return t.financeSalary(o)}}},[t._v(" 财务数据 ")]):t._e(),3==o.enumStatus||2==o.enumStatus?a("el-button",{staticStyle:{"margin-left":"5px !important","padding-left":"5px !important"},attrs:{type:"info",size:"mini"},on:{click:function(e){return t.baseInfo(o)}}},[t._v(" 基本信息 ")]):t._e(),1==o.enumStatus||2==o.enumStatus?a("el-button",{staticStyle:{"margin-left":"5px !important","padding-left":"5px !important"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.showDialog(o)}}},[t._v(" 编辑 ")]):t._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[10,20,50],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.getPageList}})]},proxy:!0}])}),a("editDialog",{ref:"editDialog",on:{refreshData:t.getPageList}}),a("baseInfoDialog",{ref:"baseInfoDialog"})],1)},l=[],n=a("2efc"),r=a("f9ac"),i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-dialog",{attrs:{title:t.title,visible:t.showDialog,width:"40%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:t.closeDialog}},[a("el-form",{ref:"dataForm",attrs:{model:t.dataModel,"label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"薪资年份",prop:"RecordMonth"}},[t.isEdit?[a("span",[t._v(t._s(t.currentYear))])]:[a("el-date-picker",{attrs:{type:"year",placeholder:"选择年"},model:{value:t.year,callback:function(e){t.year=e},expression:"year"}})]],2)],1),a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"月份"}},[t.isEdit?[a("span",[t._v(t._s(t.selectedMonth))])]:[a("el-select",{model:{value:t.selectedMonth,callback:function(e){t.selectedMonth=e},expression:"selectedMonth"}},t._l(t.months,(function(t){return a("el-option",{key:t,attrs:{label:t.toString(),value:t}})})),1)]],2)],1)],1),a("el-row",[a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"年度社保基数调整"}},[a("input",{directives:[{name:"model",rawName:"v-model",value:t.dataModel.yearSocialSecurityBaseCorrection,expression:"dataModel.yearSocialSecurityBaseCorrection"}],staticStyle:{width:"16px",height:"16px"},attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.dataModel.yearSocialSecurityBaseCorrection)?t._i(t.dataModel.yearSocialSecurityBaseCorrection,null)>-1:t.dataModel.yearSocialSecurityBaseCorrection},on:{change:function(e){var a=t.dataModel.yearSocialSecurityBaseCorrection,o=e.target,l=!!o.checked;if(Array.isArray(a)){var n=null,r=t._i(a,n);o.checked?r<0&&t.$set(t.dataModel,"yearSocialSecurityBaseCorrection",a.concat([n])):r>-1&&t.$set(t.dataModel,"yearSocialSecurityBaseCorrection",a.slice(0,r).concat(a.slice(r+1)))}else t.$set(t.dataModel,"yearSocialSecurityBaseCorrection",l)}}})])],1),t.dataModel.yearSocialSecurityBaseCorrection?a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"调整月份数",prop:"correctionMonths"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,precision:0,step:1,placeholder:"请输入调整月份数"},model:{value:t.dataModel.correctionMonths,callback:function(e){t.$set(t.dataModel,"correctionMonths",e)},expression:"dataModel.correctionMonths"}})],1)],1):t._e()],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{type:"textarea",rows:3,maxlength:"500",clearable:"",placeholder:"备注"},model:{value:t.dataModel.remark,callback:function(e){t.$set(t.dataModel,"remark",e)},expression:"dataModel.remark"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:t.closeDialog}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:t.btnSaveLoading},on:{click:t.saveDialog}},[t._v("保 存")])],1)],1)],1)},s=[],c=(a("a630"),a("3ca3"),{data:function(){return{showDialog:!1,title:"",btnSaveLoading:!1,isEdit:!1,dataModel:{},year:new Date,currentYear:(new Date).getFullYear(),selectedMonth:null,months:Array.from({length:13},(function(t,e){return e+1})),rules:{correctionMonths:[{required:!0,message:"请输入调整月份数",trigger:"blur"},{type:"number",min:1,message:"调整月份数必须大于0",trigger:"blur"}]}}},watch:{"dataModel.yearSocialSecurityBaseCorrection":{handler:function(t){t&&!this.dataModel.correctionMonths&&(this.dataModel.correctionMonths=1)},immediate:!1}},methods:{initDialog:function(t){if(this.showDialog=!0,t)this.title="编辑月度薪资",this.isEdit=!0,this.getData(t.id);else{this.title="新增月度薪资",this.isEdit=!1,this.getNowTime();var e=this.dataModel.month?new Date(this.dataModel.month).getMonth()+1:0;8===e&&(this.dataModel.yearSocialSecurityBaseCorrection=!0,this.dataModel.correctionMonths=1)}},getNowTime:function(){this.selectedMonth=(new Date).getMonth()+1},getData:function(t){var e=this;n["a"].getSalary({id:t}).then((function(t){if(t.succeed){e.dataModel=t.data;var a=new Date(e.dataModel.month);e.currentYear=a.getFullYear(),13===e.dataModel.recordMonth?e.selectedMonth=a.getMonth()+2:e.selectedMonth=a.getMonth()+1}})).catch((function(t){}))},saveDialog:function(){var t=this;this.$refs["dataForm"].validate((function(e){t.currentYear=new Date(t.year).getFullYear(),e&&(t.dataModel.recordYear=new Date(t.year).getFullYear(),t.dataModel.recordMonth=t.selectedMonth,13===t.selectedMonth?t.dataModel.month=new Date(t.currentYear,t.dataModel.recordMonth-2,31).toISOString():t.dataModel.month=new Date(t.currentYear,t.dataModel.recordMonth-1).toISOString(),t.btnSaveLoading=!0,t.isEdit?n["a"].updateSalary(t.dataModel).then((function(e){e.succeed&&(t.$message({message:"修改成功",type:"success"}),t.btnSaveLoading=!1,t.$emit("refreshData"),t.closeDialog())})).catch((function(e){t.btnSaveLoading=!1})):n["a"].addSalary(t.dataModel).then((function(e){e.succeed&&(t.$message({message:"添加成功",type:"success"}),t.btnSaveLoading=!1,t.$emit("refreshData"),t.closeDialog())})).catch((function(e){t.btnSaveLoading=!1,t.closeDialog()})))}))},closeDialog:function(){this.dataModel={},this.showDialog=!1,this.$refs.dataForm.resetFields()}}}),d=c,u=(a("3cb7"),a("905f"),a("2877")),m=Object(u["a"])(d,i,s,!1,null,"e30ed54c",null),h=m.exports,f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-dialog",{attrs:{title:t.title,visible:t.showDialog,width:"40%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:t.closeDialog}},[a("el-form",{ref:"dataForm",attrs:{rules:t.rules,model:t.dataModel,"label-width":"100px"}},[a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"薪资月份"}},[t._v(" "+t._s(t.dataModel.month&&t.$moment(t.dataModel.month).isSame(t.$moment(t.dataModel.month).endOf("year"),"day")?t.$moment(t.dataModel.month).year()+"-13":null==t.dataModel.month||void 0==t.dataModel.month?"":t.$moment(t.dataModel.month).format("YYYY-MM"))+" ")])],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"员工数量"}},[t._v(" "+t._s(t.dataModel.employeeCount)+" ")])],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"总薪资"}},[t._v(" "+t._s(t._f("formatMoney2")(t.dataModel.totalSalary))+" ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"创建人"}},[t._v(" "+t._s(t.dataModel.creator)+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"创建时间"}},[t._v(" "+t._s(t.dataModel.createTime)+" ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"HR提交人"}},[t._v(" "+t._s(t.dataModel.hrSubmitter)+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"HR提交时间"}},[t._v(" "+t._s(t.dataModel.hrSubmitTime)+" ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"财务完成人"}},[t._v(" "+t._s(t.dataModel.financeCompleter)+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"财务完成时间"}},[t._v(" "+t._s(t.dataModel.financeCompleteTime)+" ")])],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"备注"}},[t._v(" "+t._s(t.dataModel.remark)+" ")])],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:t.closeDialog}},[t._v("关 闭")])],1)],1)],1)},g=[],p={data:function(){return{showDialog:!1,title:"基础数据",rules:{month:[{required:!0,message:"薪资月份必填",trigger:"change"}]},btnSaveLoading:!1,isEdit:!1,dataModel:{}}},methods:{initDialog:function(t){this.dataModel=t,this.showDialog=!0},getData:function(t){var e=this;n["a"].getSalary({id:t}).then((function(t){t.succeed&&(e.dataModel=t.data)})).catch((function(t){}))},closeDialog:function(){this.dataModel={},this.showDialog=!1}}},y=p,S=(a("7788"),a("b38a"),Object(u["a"])(y,f,g,!1,null,"9b6a6ef6",null)),v=S.exports,b={components:{editDialog:h,baseInfoDialog:v},data:function(){return{addForm:{},dataList:[],salaryStatusList:[],total:0,listQuery:{pageIndex:1,pageSize:10,order:"-Month",enumSalaryStatus:null},listLoading:!1,temp:{}}},created:function(){this.loadSalaryStatus(),this.getPageList()},methods:{handlHrSalary:function(t){var e=this,a=this.$router.resolve({path:"/hrSalaryBlank",query:{salaryId:t.id}}),o=window.open(a.href,"_blank","400,400"),l=setInterval((function(t){o.closed&&(clearInterval(l),e.getPageList())}),1e3)},financeSalary:function(t){var e=this,a=this.$router.resolve({path:"/financeSalaryBlank",query:{salaryId:t.id}}),o=window.open(a.href,"_blank","400,400"),l=setInterval((function(t){o.closed&&(clearInterval(l),e.getPageList())}),1e3)},getPageList:function(){var t=this;this.listLoading=!0,null==this.listQuery.months&&delete this.listQuery.months,null!==this.listQuery.enumSalaryStatus&&""!==this.listQuery.enumSalaryStatus||delete this.listQuery.enumSalaryStatus,n["a"].querySalary(this.listQuery).then((function(e){t.listLoading=!1,e.succeed?(t.dataList=e.data.datas,t.total=e.data.recordCount,t.listQuery.pageIndex=e.data.pageIndex):t.$notice.resultTip(e)})).catch((function(e){console.log(e),t.listLoading=!1}))},loadSalaryStatus:function(){var t=this;r["a"].getEnumInfos({enumType:"SalaryStatus"}).then((function(e){t.salaryStatusList=e.data.datas})).catch((function(t){console.log(t)}))},search:function(){this.listQuery.pageIndex=1,this.getPageList()},sortChange:function(t,e,a){this.listQuery.pageIndex=1;var o="";"descending"===t.order&&(o="-"),"ascending"===t.order&&(o="+"),this.listQuery.order=o+t.prop,this.getPageList()},deleteSalary:function(t){var e=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n["a"].deleteSalary(t).then((function(t){t.succeed?(e.getPageList(),e.$notice.message("删除成功","success")):t.succeed||e.$notice.message("删除失败，请联系管理员","info")})).catch((function(t){e.listLoading=!1,console.log(t)}))})).catch((function(t){e.listLoading=!1,console.log(t)}))},showDialog:function(t){this.$refs.editDialog.initDialog(t)},closeAddDialog:function(){this.$refs["ref_addForm"].resetFields(),this.$refs["ref_addForm"].clearValidate()},baseInfo:function(t){this.$refs.baseInfoDialog.initDialog(t)},exportData:function(t){var e=this;n["a"].exportSalaryDetail({salaryId:t.id}).then((function(t){var o=a("19de"),l="员工薪资"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?o(t.data,l):o(t,l),e.getPageList()}))}}},M=b,w=Object(u["a"])(M,o,l,!1,null,null,null);e["default"]=w.exports},"905f":function(t,e,a){"use strict";var o=a("93f4"),l=a.n(o);l.a},"93f4":function(t,e,a){},b38a:function(t,e,a){"use strict";var o=a("ddfa"),l=a.n(o);l.a},bbef:function(t,e,a){},c8dd:function(t,e,a){},ddfa:function(t,e,a){}}]);