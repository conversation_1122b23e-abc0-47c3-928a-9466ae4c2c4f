(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-94c1b0a8"],{"28b9":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container "},[i("layout1",{scopedSlots:e._u([{key:"header",fn:function(){return[i("el-button",{attrs:{type:"primary",icon:"el-icon-setting"},on:{click:e.querySetting}},[e._v("查询设置")])]},proxy:!0},{key:"aside",fn:function(){return[i("el-checkbox",{on:{change:e.selectedChange},model:{value:e.listQuery.IsContainSubDept,callback:function(t){e.$set(e.listQuery,"IsContainSubDept",t)},expression:"listQuery.IsContainSubDept"}},[e._v("包含下级部门")]),i("c-tree",{directives:[{name:"loading",rawName:"v-loading",value:e.treeLoading,expression:"treeLoading"}],attrs:{options:e.treeData,props:e.treeProps,"expanded-keys":e.treeExpandedKeys},on:{nodeClick:e.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[i("el-card",[i("el-table",{staticStyle:{width:"100%"},attrs:{data:e.pageEmpInfoList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChangeForEmpInfo}},[i("el-table-column",{attrs:{fixed:"",label:"唯一码",sortable:"custom",prop:"Uid","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",{staticClass:"link-type",on:{click:function(t){return e.handleWatchEmpInfo(a)}}},[e._v(e._s(a.uid))])]}}])}),i("el-table-column",{attrs:{label:"工号","min-width":"80px",sortable:"custom",prop:"EmpCode"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.empCode))])]}}])}),i("el-table-column",{attrs:{label:"姓名","min-width":"80px",sortable:"custom",prop:"DisplayName"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.displayName))])]}}])}),i("el-table-column",{attrs:{label:"部门","min-width":"80px",sortable:"custom",prop:"DepartmentName"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.departmentName))])]}}])}),i("el-table-column",{attrs:{label:"岗位","min-width":"80px",sortable:"custom",prop:"PositionName"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.positionName))])]}}])}),i("el-table-column",{attrs:{label:"在职方式","min-width":"120px",sortable:"custom",prop:"HireStyleName"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.hireStyleName))])]}}])})],1),i("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.queryEmployeeByConditions}})],1)]},proxy:!0}])}),e.dialogAppInfoVisible?i("el-dialog",{staticClass:"empManager",attrs:{title:e.empDialogTitle,visible:e.dialogAppInfoVisible,width:"90%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogAppInfoVisible=t}}},[i("hRInfo",{ref:"hRInfo",attrs:{"emp-model":e.empModel}})],1):e._e(),e.dialogQuerySettingVisible?i("el-dialog",{staticClass:"empManager",attrs:{title:"查询设置",visible:e.dialogQuerySettingVisible,width:"90%","before-close":e.hideQuerySettingDialog},on:{"update:visible":function(t){e.dialogQuerySettingVisible=t}}},[i("querySetting",{ref:"querySetting",on:{search:e.quickSearch,CloseDialog:e.hideQuerySettingDialog}})],1):e._e()],1)},n=[],o=(i("d3b7"),i("d368")),l=i("e44c"),s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.empModel,"label-position":"right","label-width":"100px"}},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("span",[e._v("人事信息")])]),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"姓名",prop:"DisplayName"}},[i("span",[e._v(e._s(e.empModel.displayName))])])],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"工号 ",prop:"EmpCode"}},[i("span",[e._v(e._s(e.empModel.empCode))])])],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"唯一码 ",prop:"Uid"}},[i("span",[e._v(e._s(e.empModel.uid))])])],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"进院日期",prop:"hireDate"}},[i("span",[e._v(e._s(e.empModel.hireDateFormat))])])],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"身份证号码",prop:"IdentityNumber"}},[i("span",[e._v(e._s(e.empModel.identityNumber))])])],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"在职方式",prop:"hireStyleId"}},[i("span",[e._v(e._s(e.empModel.hireStyleName))])])],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:16}},[i("el-form-item",{attrs:{label:"家庭地址",prop:"ResidentAddress"}},[i("span",[e._v(e._s(e.empModel.address))])])],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"家庭地址邮编 ",prop:"ResidentZip"}},[i("span",[e._v(e._s(e.empModel.zipCode))])])],1)],1)],1)],1),i("el-form",{ref:"financialForm",attrs:{rules:e.financialRules,model:e.financialInformation,"label-position":"right","label-width":"100px"}},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("span",[e._v("财务信息")])]),i("el-row",{attrs:{type:"flex",justify:"end"}},[i("el-col",{attrs:{span:2}},[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")])],1),i("el-col",{attrs:{span:2}},[i("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.downloadexceltemplate1}},[e._v("模板下载")])],1),i("el-col",{attrs:{span:2}},[i("el-upload",{attrs:{action:"","http-request":e.importExcel1,accept:".xlsx","show-file-list":!1}},[i("el-button",{attrs:{size:"mini",type:"primary"}},[e._v("导入")])],1)],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"银行名称一",prop:"bankName1"}},[i("el-input",{model:{value:e.financialInformation.bankName1,callback:function(t){e.$set(e.financialInformation,"bankName1",t)},expression:"financialInformation.bankName1"}})],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"银行名称二",prop:"bankName2"}},[i("el-input",{model:{value:e.financialInformation.bankName2,callback:function(t){e.$set(e.financialInformation,"bankName2",t)},expression:"financialInformation.bankName2"}})],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"银行名称三",prop:"bankName3"}},[i("el-input",{model:{value:e.financialInformation.bankName3,callback:function(t){e.$set(e.financialInformation,"bankName3",t)},expression:"financialInformation.bankName3"}})],1)],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"银行账号一",prop:"bankAccount1"}},[i("el-input",{model:{value:e.financialInformation.bankAccount1,callback:function(t){e.$set(e.financialInformation,"bankAccount1",t)},expression:"financialInformation.bankAccount1"}})],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"银行账号二",prop:"bankAccount2"}},[i("el-input",{model:{value:e.financialInformation.bankAccount2,callback:function(t){e.$set(e.financialInformation,"bankAccount2",t)},expression:"financialInformation.bankAccount2"}})],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"银行账号三",prop:"bankAccount3"}},[i("el-input",{model:{value:e.financialInformation.bankAccount3,callback:function(t){e.$set(e.financialInformation,"bankAccount3",t)},expression:"financialInformation.bankAccount3"}})],1)],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"社保账号",prop:"annuityAccount"}},[i("el-input",{model:{value:e.financialInformation.annuityAccount,callback:function(t){e.$set(e.financialInformation,"annuityAccount",t)},expression:"financialInformation.annuityAccount"}})],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"公积金账号",prop:"accumFundAccount"}},[i("el-input",{model:{value:e.financialInformation.accumFundAccount,callback:function(t){e.$set(e.financialInformation,"accumFundAccount",t)},expression:"financialInformation.accumFundAccount"}})],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"工资单编号",prop:"paySlipNumber"}},[i("el-input",{model:{value:e.financialInformation.paySlipNumber,callback:function(t){e.$set(e.financialInformation,"paySlipNumber",t)},expression:"financialInformation.paySlipNumber"}})],1)],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"免个人所得税",prop:"dispenseTax"}},[i("el-checkbox",{model:{value:e.financialInformation.dispenseTax,callback:function(t){e.$set(e.financialInformation,"dispenseTax",t)},expression:"financialInformation.dispenseTax"}})],1)],1)],1)],1)],1)],1)},r=[],c={name:"",components:{},props:{empModel:{type:Object,default:function(){console.log("propE default invoked.")}}},data:function(){return{rules:{},financialRules:{},financialInformation:{}}},created:function(){this.getFinancialInformation()},methods:{getFinancialInformation:function(){var e=this;l["a"].getSocialSecurityInfo({id:this.empModel.id}).then((function(t){t.data&&(e.financialInformation=t.data)}))},save:function(){var e=this;this.financialInformation.ID=this.empModel.id,l["a"].updateEmployeeSocialSecurity(this.financialInformation).then((function(t){t.succeed?e.$notice.message("保存成功","success"):-3!==t.type&&e.$notice.resultTip(t)}))},downloadexceltemplate1:function(){l["a"].downlodaImportExcelTemplate({type:"importempsoc"}).then((function(e){var t=i("19de"),a="EmployeeSocialInsuranceTemplate.xlsx";e.data?t(e.data,a):t(e,a)})).catch((function(e){}))},importExcel1:function(e){var t=this,i=e.file;l["a"].importExcel(i,{type:"importempsoc"}).then((function(e){if(e.succeed){var i=e.data;t.$message.success(i)}})).catch((function(e){}))}}},p=c,d=(i("84de"),i("2877")),u=Object(d["a"])(p,s,r,!1,null,"8a11500c",null),m=u.exports,f=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("el-tabs",{attrs:{type:"card"},on:{"tab-click":e.tabClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[i("el-tab-pane",{attrs:{label:"查询设置",name:"querySetting"}},[i("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"100px"}},[i("el-row",{staticStyle:{"padding-top":"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"逻辑关系",prop:"enumLogicRelationships"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{"value-key":"value",clearable:"",placeholder:"请选择逻辑关系"},on:{change:e.relationChange},model:{value:e.tempData.tempFormModel.enumLogicRelationships,callback:function(t){e.$set(e.tempData.tempFormModel,"enumLogicRelationships",t)},expression:"tempData.tempFormModel.enumLogicRelationships"}},e._l(e.logicRelationshipOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.desc,value:e}})})),1)],1)],1),i("el-col",{attrs:{span:2}}),i("el-col",{attrs:{span:10}},[i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.quickSearch}},[e._v("快捷查询")])],1)],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"字段",prop:"selectedColumn"}},[i("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{"value-key":"id",clearable:"",placeholder:"请选择字段"},on:{change:e.columnChange},model:{value:e.tempData.tempFormModel.selectedColumn,callback:function(t){e.$set(e.tempData.tempFormModel,"selectedColumn",t)},expression:"tempData.tempFormModel.selectedColumn"}},e._l(e.empColumnOptions,(function(e){return i("el-option",{key:e.id,attrs:{label:e.displayName,value:e}})})),1)],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"运算符",prop:"selectedCondition"}},[i("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{"value-key":"value",clearable:"",placeholder:"请选择运算符"},on:{change:e.conditionChange},model:{value:e.tempData.tempFormModel.selectedCondition,callback:function(t){e.$set(e.tempData.tempFormModel,"selectedCondition",t)},expression:"tempData.tempFormModel.selectedCondition"}},e._l(e.selectConditionOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.desc,value:e}})})),1)],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"关键字",prop:"keywords"}},[i("el-input",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请输入关键字"},model:{value:e.tempData.tempFormModel.keywords,callback:function(t){e.$set(e.tempData.tempFormModel,"keywords",t)},expression:"tempData.tempFormModel.keywords"}})],1)],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:24}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"dataListTable",staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}}},[i("el-table-column",{attrs:{type:"selection",width:"55"}}),i("el-table-column",{attrs:{label:"关系","min-width":"80px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",[e._v(e._s(a.displayRelationship))])]}}])}),i("el-table-column",{attrs:{label:"运算符","min-width":"300px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.children?e._e():i("span",[e._v(e._s(a.displayName)+e._s(a.displayOperation)+e._s(a.keywords))]),a.children?i("span",[e._v(e._s(a.description))]):e._e()]}}])}),i("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteData(a)}}},[e._v(" 删除 ")])]}}])})],1)],1)],1),i("el-row",{staticStyle:{padding:"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:24,align:"center"}},[i("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.addData(e.tempData.tempFormModel)}}},[e._v("添加")]),i("el-button",{attrs:{type:"primary",icon:"el-icon-connection"},on:{click:e.showCombineData}},[e._v("组合")]),i("el-button",{attrs:{type:"primary",icon:"el-icon-check"},on:{click:e.search}},[e._v("确定")]),i("el-button",{attrs:{type:"warning",icon:"el-icon-refresh-left"},on:{click:e.cancle}},[e._v("取消")]),i("el-button",{attrs:{type:"primary",icon:"el-icon-document"},on:{click:e.showSaveDialog}},[e._v("保存")])],1)],1)],1),e.dialogCombineVisible?i("el-dialog",{attrs:{title:"组合",visible:e.dialogCombineVisible,"append-to-body":""},on:{"update:visible":function(t){e.dialogCombineVisible=t}}},[i("el-row",{staticStyle:{padding:"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:24,align:"center"}},[i("el-radio",{attrs:{label:10},model:{value:e.relationshipForCombine,callback:function(t){e.relationshipForCombine=t},expression:"relationshipForCombine"}},[e._v("AND组合")]),i("el-radio",{attrs:{label:20},model:{value:e.relationshipForCombine,callback:function(t){e.relationshipForCombine=t},expression:"relationshipForCombine"}},[e._v("OR组合")])],1)],1),i("el-row",{staticStyle:{padding:"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:24,align:"center"}},[i("el-button",{attrs:{type:"primary",icon:"el-icon-check"},on:{click:e.combineData}},[e._v("保存")]),i("el-button",{attrs:{type:"warning",icon:"el-icon-circle-close"},on:{click:function(t){return e.colseCombine()}}},[e._v("关闭")])],1)],1)],1):e._e(),e.dialogSaveVisible?i("el-dialog",{attrs:{title:"保存查询",visible:e.dialogSaveVisible,"append-to-body":""},on:{"update:visible":function(t){e.dialogSaveVisible=t}}},[i("el-form",{ref:"dataFormForSave",attrs:{rules:e.rulesForSave,model:e.tempData.tempFormModelForSave,"label-position":"right","label-width":"100px"}},[i("el-row",{staticStyle:{"padding-top":"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"名称",prop:"name"}},[i("el-input",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请输入名称"},model:{value:e.tempData.tempFormModelForSave.name,callback:function(t){e.$set(e.tempData.tempFormModelForSave,"name",t)},expression:"tempData.tempFormModelForSave.name"}})],1)],1)],1),i("el-row",{staticStyle:{padding:"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:24,align:"center"}},[i("el-button",{attrs:{type:"primary",icon:"el-icon-check"},on:{click:e.save}},[e._v("保存")]),i("el-button",{attrs:{type:"warning",icon:"el-icon-circle-close"},on:{click:e.cancleSave}},[e._v("取消")])],1)],1)],1)],1):e._e()],1),i("el-tab-pane",{attrs:{label:"已存查询",name:"savedQuery"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageSettingList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}}},[i("el-table-column",{attrs:{label:"名称","min-width":"200px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",{staticClass:"link-type",on:{click:function(t){return e.handleQuickSearch(a)}}},[e._v(e._s(a.name))])]}}])}),i("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteSetting(a)}}},[e._v(" 删除 ")])]}}])})],1),i("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQueryForSetting.total>0,expression:"listQueryForSetting.total > 0"}],attrs:{total:e.listQueryForSetting.total,"page-sizes":[10,20,50],page:e.listQueryForSetting.pageIndex,limit:e.listQueryForSetting.pageSize},on:{"update:page":function(t){return e.$set(e.listQueryForSetting,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQueryForSetting,"pageSize",t)},pagination:e.getSettingList}})],1)],1)],1)},h=[],g=(i("a4d3"),i("e01a"),i("4de4"),i("c975"),i("a434"),i("b0c0"),i("f9ac")),y={name:"",components:{},data:function(){return{activeName:"querySetting",dataList:[],listQuery:{conditionList:[]},listLoading:!1,rules:{keywords:[{max:100,message:"关键字不允许超过100个字符",trigger:"blur"}]},tempData:{tempFormModel:{keywords:""},tempFormModelForSave:{}},logicRelationshipOptions:[],empColumnOptions:[],selectConditionOptions:[],dialogCombineVisible:!1,relationshipForCombine:10,rulesForSave:{name:[{required:!0,message:"请输入名称",trigger:"blur"},{max:100,message:"名称不允许超过100个字符",trigger:"blur"}]},dialogSaveVisible:!1,pageSettingList:[],listQueryForSetting:{total:1,pageIndex:1,pageSize:10}}},created:function(){this.loadRelationships(),this.loadColumns(),this.loadConditions()},methods:{tabClick:function(e){"savedQuery"===e.name&&(this.listQueryForSetting.pageIndex=1,this.reloadSettingList())},loadRelationships:function(){var e=this;g["a"].getEnumInfos({enumType:"LogicRelationships"}).then((function(t){e.logicRelationshipOptions=t.data.datas,e.logicRelationshipOptions.length>0&&(e.tempData.tempFormModel.enumLogicRelationships=e.logicRelationshipOptions[0])})).catch((function(e){console.log(e)}))},loadColumns:function(){var e=this;l["a"].querySettingColumns().then((function(t){e.empColumnOptions=t.data.datas,e.empColumnOptions.length>0&&(e.tempData.tempFormModel.selectedColumn=e.empColumnOptions[0])})).catch((function(e){console.log(e)}))},loadConditions:function(){var e=this;g["a"].getEnumInfos({enumType:"Operations"}).then((function(t){e.selectConditionOptions=t.data.datas,e.selectConditionOptions.length>0&&(e.tempData.tempFormModel.selectedCondition=e.selectConditionOptions[e.selectConditionOptions.length-1])})).catch((function(e){console.log(e)}))},columnChange:function(e){this.$forceUpdate()},conditionChange:function(e){this.$forceUpdate()},relationChange:function(e){this.$forceUpdate()},quickSearch:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var i=e.tempData.tempFormModel,a={enumLogicRelationships:i.enumLogicRelationships.value,displayRelationship:i.enumLogicRelationships.desc,entityColumnName:i.selectedColumn.entityColumnName,displayName:i.selectedColumn.displayName,entityColumnType:i.selectedColumn.entityColumnType,enumOperations:i.selectedCondition.value,displayOperation:i.selectedCondition.desc,keywords:e.tempData.tempFormModel.keywords};e.listQuery.conditionList=e.dataList,e.listQuery.conditionList.push(a),e.listQuery.employeeQuerySettingId&&delete e.listQuery.employeeQuerySettingId,e.$emit("search",e.listQuery)}}))},search:function(){this.listQuery.conditionList=this.dataList,this.listQuery.conditionList.length>0&&(this.listQuery.employeeQuerySettingId&&delete this.listQuery.employeeQuerySettingId,this.$emit("search",this.listQuery))},handleQuickSearch:function(e){this.listQuery.conditionList&&delete this.listQuery.conditionList,this.listQuery.employeeQuerySettingId=e.id,this.$emit("search",this.listQuery)},clear:function(){this.$refs["dataForm"].resetFields(),this.tempData.tempFormModel={},this.dataList=[]},addData:function(e){var t=this;this.$refs["dataForm"].validate((function(i){if(i){var a={enumLogicRelationships:e.enumLogicRelationships.value,displayRelationship:e.enumLogicRelationships.desc,entityColumnName:e.selectedColumn.entityColumnName,displayName:e.selectedColumn.displayName,entityColumnType:e.selectedColumn.entityColumnType,enumOperations:e.selectedCondition.value,displayOperation:e.selectedCondition.desc,keywords:e.keywords};a.description=a.displayName+" "+a.displayOperation+" "+a.keywords,t.dataList.push(a)}}))},deleteData:function(e){this.dataList.splice(this.dataList.indexOf(e),1)},showCombineData:function(){this.dialogCombineVisible=!0},colseCombine:function(){this.dialogCombineVisible=!1,this.relationshipForCombine=10},combineData:function(){var e=this,t=this.$refs.dataListTable.selection;if(!t||t.length<2)this.$notice.message("所选记录数量不足，无需组合。","info");else{for(var i={enumLogicRelationships:t[0].enumLogicRelationships,displayRelationship:t[0].displayRelationship,description:""},a=[],n=0;n<t.length;n++){var o=JSON.parse(JSON.stringify(t[n]));o.enumLogicRelationships=this.relationshipForCombine,o.displayRelationship=this.logicRelationshipOptions.filter((function(t){return t.value===e.relationshipForCombine}))[0].desc,a.push(o),i.description=0===n?o.description:i.description+" "+o.displayRelationship+" "+o.description,this.dataList.splice(this.dataList.indexOf(t[n]),1)}i.children=a,this.dataList.push(i),this.colseCombine()}},showSaveDialog:function(){this.dialogSaveVisible=!0},save:function(){var e=this;if(this.addData(this.tempData.tempFormModel),!this.dataList||this.dataList&&0===this.dataList)this.$notice.message("列表中无数据，无法保存。","info");else{var t={name:this.tempData.tempFormModelForSave.name,employeeQuerySettingItem:this.dataList};this.$refs["dataFormForSave"].validate((function(i){i&&l["a"].addEmployeeQuerySetting(t).then((function(t){t.succeed?(e.$notice.message("保存成功","success"),e.activeName="savedQuery",e.reloadSettingList(),e.clearSaveForm(),e.colseCombine(),e.dialogSaveVisible=!1):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("保存失败。","error")}))}))}},cancle:function(){this.$emit("CloseDialog")},cancleSave:function(){this.dialogSaveVisible=!1,this.clearSaveForm()},clearSaveForm:function(){this.$refs["dataFormForSave"].resetFields(),this.tempData.tempFormModelForSave={}},getSettingList:function(){var e=this;l["a"].queryEmployeeQuerySetting(this.listQueryForSetting).then((function(t){t.succeed?(e.pageSettingList=t.data.datas,e.listQueryForSetting.total=t.data.recordCount,e.listQueryForSetting.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))},reloadSettingList:function(){this.listQueryForSetting.pageIndex=1,this.getSettingList()},deleteSetting:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeQuerySetting(e).then((function(e){e.succeed?(t.getSettingList(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))}}},b=y,v=(i("8381"),Object(d["a"])(b,f,h,!1,null,"667c56dd",null)),S=v.exports,k={components:{hRInfo:m,querySetting:S},data:function(){return{treeData:[],treeExpandedKeys:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},empDialogTitle:"人事信息",selected:"",currentNode:{},includeDownDept:!1,listQuery:{total:1,pageIndex:1,pageSize:10},listLoading:!1,treeLoading:!1,dialogAppInfoVisible:!1,activeName:"tab1",empId:"",empModel:{},dialogQuerySettingVisible:!1,pageEmpInfoList:[]}},created:function(){this.loadTree()},methods:{loadTree:function(){var e=this;this.treeLoading=!0,o["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data,e.treeData&&e.treeData.length>0&&e.treeExpandedKeys.push(e.treeData[0].id)})).catch((function(e){console.log(e)})).finally((function(){e.treeLoading=!1})),this.resetCurrentNode()},resetCurrentNode:function(){this.currentNode=null},treeNodeClick:function(e){this.currentNode=e,this.listQuery.deptId=e.id,this.listQuery.pageIndex=1,this.queryEmployeeByConditions()},handleWatchEmpInfo:function(e){this.empDialogTitle="人事信息——"+e.displayName,this.empId=e.id,this.empModel=e,this.dialogAppInfoVisible=!0},handleClose:function(){this.dialogAppInfoVisible=!1},updateEmpId:function(e){this.empId=e},selectedChange:function(){this.listQuery.pageIndex=1,this.queryEmployeeByConditions()},querySetting:function(){this.dialogQuerySettingVisible=!0},quickSearch:function(e){this.hideQuerySettingDialog(),e.conditionList&&(this.listQuery.conditionList=e.conditionList,this.listQuery.employeeQuerySettingId=null,this.listQuery.deptId=null),e.employeeQuerySettingId&&(this.listQuery.employeeQuerySettingId=e.employeeQuerySettingId,this.listQuery.deptId=null,this.listQuery.conditionList=null),this.listQuery.pageIndex=1,this.queryEmployeeByConditions()},queryEmployeeByConditions:function(){var e=this;this.listQuery.deptId&&(this.listQuery.employeeQuerySettingId=null,this.listQuery.conditionList=null),l["a"].queryEmployeeByConditions(this.listQuery).then((function(t){t.succeed?(e.pageEmpInfoList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))},sortChangeForEmpInfo:function(e,t,i){console.log(e),console.log(t),console.log(i),this.listQuery.pageIndex=1;var a="";"descending"===e.order&&(a="desc"),"ascending"===e.order&&(a="asc"),this.listQuery.order=e.prop+" "+a,this.queryEmployeeByConditions()},hideQuerySettingDialog:function(){this.dialogQuerySettingVisible=!1,this.$refs["querySetting"].clear()}}},C=k,x=(i("d427"),i("3981"),Object(d["a"])(C,a,n,!1,null,"60745f3b",null));t["default"]=x.exports},"2cf46":function(e,t,i){},3981:function(e,t,i){"use strict";var a=i("a9ac"),n=i.n(a);n.a},8381:function(e,t,i){"use strict";var a=i("cf90"),n=i.n(a);n.a},"84de":function(e,t,i){"use strict";var a=i("c091"),n=i.n(a);n.a},a9ac:function(e,t,i){},c091:function(e,t,i){},cf90:function(e,t,i){},d427:function(e,t,i){"use strict";var a=i("2cf46"),n=i.n(a);n.a}}]);