﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Renji.JHR.Common.Consts
{
    public static class EmployeeLists
    {
        public static string SQL_Select = " Select ";
        public static string SQL_From = " From ";
        public static string SQL_Where = " Where ";
        public static string SQL_WhereTrue = " 1 = 1 ";
        public static string SQL_And = " And ";
        public static string SQL_INNERJOIN = " Inner Join ";
        public static string SQL_LEFTJOIN = " Left Join ";

        public static string SQL_View = "View";

        public static string DateFormat = " CONVERT(varchar(100), {0}.{1}, 111) AS {1} ";
        public static string DictFormat = " dbo.GetDictText({0}.{1}) As {1} ";

        public static string DeptFormat = " [dbo].[GetDeptName]( {0}.{1} ) AS {1} ";
        public static string OrgClassFormat = " [dbo].[GetOrgClassName]( {0}.{1} ) AS {1} ";
        public static string CompGroupFormat = " [dbo].[GetCompGroupName]( {0}.{1} ) AS {1} ";
        public static string OrgSalaryFormat = " [dbo].[GetOrgSalaryName]( {0}.{1} ) AS {1} ";
        public static string OrgSalaryLevelFormat = " [dbo].[GetOrgSalaryLevelName]( {0}.{1} ) AS {1} ";
        public static string OrgPositionSalaryFormat = " [dbo].[GetOrgPositionSalaryName]( {0}.{1} ) AS {1} ";

        public static string DictFormat_Gender = " dbo.[GetDictTextByCode]('00019', {0}.{1}) as {1} ";
        public static string RelationFormat = " {0}.{1} = {2}.{3} ";
        public static string ValidFormat = " {0}.Deleted = 0 ";

        public static string Column_Gender = " EnumGender ";

        public static string Tag_Split = "|";
        public static string Tag_Comma = ",";
        public static string Tag_Spot = ".";

        public static string Yes_Value = "1";
        public static string Yes_Text = "是";
        public static string No_Value = "0";
        public static string No_Text = "否";

        public static string Employee_Table_Name = "Employee";

        public static string Employee_PK_Name = "Employee.Id";


        //达梦拼接sql
        public static string DM_Tag_QuotationMark = "\"";

        public static string DM_DateFormat = " CONVERT(varchar(100), \"JHR\".\"{0}\".\"{1}\") AS \"{1}\" ";
        public static string DM_DictFormat = " \"JHR\".GETDICTTEXT(\"JHR\".\"{0}\".\"{1}\") As \"{1}\" ";

        public static string DM_DeptFormat = " \"JHR\".GETDEPTNAME( \"JHR\".\"{0}\".\"{1}\" ) AS \"{1}\" ";
        public static string DM_OrgClassFormat = " \"JHR\".GETORGCLASSNAME( \"JHR\".\"{0}\".\"{1}\" ) AS \"{1}\" ";
        public static string DM_CompGroupFormat = " \"JHR\".GETCOMPGROUPNAME( \"JHR\".\"{0}\".\"{1}\" ) AS \"{1}\" ";
        public static string DM_OrgSalaryFormat = " \"JHR\".GETORGSALARYNAME( \"JHR\".\"{0}\".\"{1}\" ) AS \"{1}\" ";
        public static string DM_OrgSalaryLevelFormat = " \"JHR\".GETORGSALARYLEVELNAME( \"JHR\".\"{0}\".\"{1}\" ) AS \"{1}\" ";
        public static string DM_OrgPositionSalaryFormat = " \"JHR\".GETORGPOSITIONSALARYNAME( \"JHR\".\"{0}\".\"{1}\" ) AS \"{1}\" ";

        public static string DM_DictFormat_Gender = " \"JHR\".GETDICTTEXTBYCODE('00019', \"JHR\".\"{0}\".\"{1}\") as \"{1}\" ";
        public static string DM_RelationFormat = " \"JHR\".\"{0}\".\"{1}\" = \"JHR\".\"{2}\".\"{3}\" ";
        public static string DM_ValidFormat = " \"JHR\".\"{0}\".\"Deleted\" = 0 ";

        public static string DM_Column_Name = "\"Name\"";

        public static string DM_SQL_Schema = "\"JHR\".";

        public static string DM_SQL_Table_Column = "\"JHR\".\"{0}\".\"{1}\"";

        public static string DM_Employee_Table_Name = " \"JHR\".\"Employee\"";

        public static string DM_Employee_PK_Name = " \"JHR\".\"Employee\".ID";

    }
}
