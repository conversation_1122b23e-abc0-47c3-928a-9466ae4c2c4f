<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Renji.JHR.Common</name>
    </assembly>
    <members>
        <member name="F:Renji.JHR.Common.Configration.Config.Title">
            <summary>
            标题
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Configration.Config.DefaultPwd">
            <summary>
            创建用户时生成的默认密码
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Configration.Config.AppTokenLength">
            <summary>
            应用令牌默认长度
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Configration.Config.AppLoginExpireMins">
            <summary>
            应用登录默认过期分钟
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Configration.Config.ContractsExpire">
            <summary>
            合同到期提醒时间
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Configration.Config.LeaderFileName">
            <summary>
            合同续签页（领导）
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Configration.Config.EmpFileName">
            <summary>
            合同续签页（员工）
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Configration.Config.RenjiEmail">
            <summary>
            邮箱后缀
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.WebApi.Key">
            <summary>
            Key
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.WebApi.Domain">
            <summary>
            Domain
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.WebApi.ChkMin">
            <summary>
            接口检验时间
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.SM4.Key">
            <summary>
            邮箱后缀
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.SM4.Iv">
            <summary>
            邮箱后缀
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.SM4.HexString">
            <summary>
            HexString
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.SM4.CryptoMode">
            <summary>
            邮箱后缀
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Configration.Config.NotUploadEmployeeUids">
            <summary>
            不推送平台人员
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.Salary.ThirteenSalary">
            <summary>
            十三薪
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.Salary.Retirement">
            <summary>
            退休库
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Configration.Config.Salary.HireDateMonthDifference">
            <summary>
            报道日期距当前月
            </summary>
        </member>
        <member name="T:Renji.JHR.Common.Configration.Config.DictCode">
            <summary>
            字典项
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.DictCode.HireStyleCode">
            <summary>
            离职方式
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.DictCode.LeaveStyleCode">
            <summary>
            离职方式
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.DictCode.LeaveStyleCode_Deceased">
            <summary>
            已故
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.DictCode.LeaveStyleCode_Retire">
            <summary>
            退休
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.DictCode.LeaveStyleCode_Resign">
            <summary>
            辞职
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.DictCode.LeaveStyleCode_Outbound">
            <summary>
            培训离站
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.DictCode.AgeRange">
            <summary>
            年龄段
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.DictCode.TreatmentCategory">
            <summary>
            待遇类别
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Configration.Config.InterfaceTimeout">
            <summary>
            Domain
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.AppCodes.HR">
            <summary>
            HR系统
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.AppCodes.IH">
            <summary>
            智慧医院
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Depts.HumanResourcesDivisionCode">
            <summary>
            人力资源处
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode">
            <summary>
            员工状态
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Hired">
            <summary>
            员工状态  在职
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode_Leave">
            <summary>
            员工状态  离职
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.OfficialRankCode">
            <summary>
            职别
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.OfficialRankCode_Nursing">
            <summary>
            职别  护理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.OfficialRankCode_Worker">
            <summary>
            职别  工人
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.OfficialRankCode_Other">
            <summary>
            职别  其他业务技术
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.ModulePart_Logistics">
            <summary>
            职别  后勤人员
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.ModulePart_Nursing">
            <summary>
            职别  护理人员
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.RankCode">
            <summary>
            聘任职别
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode">
            <summary>
            在职方式
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOnStaff">
            <summary>
            在职方式 正式_在编
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOffStaff">
            <summary>
            在职方式 正式_非在编
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialSecondMedicalStaff">
            <summary>
            在职方式 正式_二医编制
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOffStaffSelfEmployed">
            <summary>
            在职方式 正式_非在编自主择业
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialRetirementPending">
            <summary>
            在职方式 正式_待退休
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOnStaffNotHandled">
            <summary>
            在职方式 正式_在编未办
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOnStaffSouthCampus">
            <summary>
            在职方式 正式_在编南院
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialDisabledNotOnDuty">
            <summary>
            在职方式 正式_残疾不在岗及待岗
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOffStaffSouthCampus">
            <summary>
            在职方式 正式_非在编南院
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialSouthCampusOnStaffNotHandled">
            <summary>
            在职方式 正式_南院在编未办
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_UnofficialDispatchedLabor">
            <summary>
            在职方式 非正式_派遣用工
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_UnofficialInformalEmployment">
            <summary>
            在职方式 非正式_非正规就业
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_UnofficialExternalRetiredAndOther">
            <summary>
            在职方式 非正式_外单位退休及其他
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_UnofficialRetiredRehired">
            <summary>
            在职方式 非正式_退休返聘
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_UnofficialResidentDoctorTraining">
            <summary>
            在职方式 非正式_住院医师培训
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_UnofficialSouthCampusResidentDoctorTraining">
            <summary>
            在职方式 非正式_南院住院医师培训
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_UnofficialPostdoc">
            <summary>
            在职方式 非正式_进站博士后
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOffStaffDispatched">
            <summary>
            在职方式 正式_非在编派遣
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOffStaffSouthCampusDispatched">
            <summary>
            在职方式 正式_非在编南院派遣
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_UnofficialPartTime">
            <summary>
            在职方式 非正式_兼职
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_DepartmentSelfHired">
            <summary>
            在职方式 科室自聘
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HireStyleCode_UnofficialOutsourcedStaff">
            <summary>
            在职方式 非正式_外包人员
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.LeaveStyleCode">
            <summary>
            离职方式
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.LeaveStyleCode_Deceased">
            <summary>
            离职方式 已故
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.LeaveStyleCode_Retire">
            <summary>
            离职方式 退休
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.LeaveStyleCode_Resign">
            <summary>
            离职方式 辞职
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.LeaveStyleCode_RetiredRehiredEnd">
            <summary>
            离职方式 返聘终止
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.LeaveStyleCode_Outbound">
            <summary>
            离职方式 培训离站
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.MarryCode">
            <summary>
            婚姻情况
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.NationalityCode">
            <summary>
            民族
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.RegisterTypeCode">
            <summary>
            户口类型
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.DegreesCode">
            <summary>
            学位
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.EducationCode">
            <summary>
            学历
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.PartyCode">
            <summary>
            政治面貌
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.RecruitmentCategoryCode">
            <summary>
            招募来源
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.RecruitmentCompanyCode">
            <summary>
            招募公司
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.LevelCode">
            <summary>
            职称级别
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.GraduationCode">
            <summary>
            毕业情况
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.LearnWayCode">
            <summary>
            学习方式
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.AbroadTypeCode">
            <summary>
            出国类别
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.ContractTypeCode">
            <summary>
            合同类型
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.TrainLevelCode">
            <summary>
            培养计划级别
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.EvaluateResultCode">
            <summary>
            考核结果
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.StartYear">
            <summary>
            考核年度 开始年份
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.EndYear">
            <summary>
            考核年度 结束年份
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.IncentTypeCode">
            <summary>
            奖惩类型
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.IncentLevelCode">
            <summary>
            奖惩级别
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.AccidentTypeCode">
            <summary>
            医疗事故性质
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.IncomeTypeCode">
            <summary>
            论文收录情况
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.ClassLevelCode">
            <summary>
            课题级别
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.TeacherTypeCode">
            <summary>
            博硕导
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.AwardLevelCode">
            <summary>
            获奖级别
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.WorkState">
            <summary>
            出勤状态
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.AdvancedQueryTypeCode">
            <summary>
            高级查询类型
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.Sex">
            <summary>
            性别
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.PartyPositionsCode">
            <summary>
            党内职务
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.OrganizationCode">
            <summary>
            组织关系
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.OtherEmpTypeCode">
            <summary>
            人员类型 
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.EmpStatusStaffCode">
            <summary>
            员工状态 在职状态
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.SocialSecurityTypeCode">
            <summary>
            社保
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.DocumentType">
            <summary>
            证件类型
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.AdministrativePosition">
            <summary>
            行政职务
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.HighTalentType">
            <summary>
            高层次人才类别
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.AgeRange">
            <summary>
            年龄段
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.PartyPosition_None">
            <summary>
            党内职务
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.Dicts.AssistanceForeignType">
            <summary>
            援外类型
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Consts.ImportEmployee.ImportEmp">
            <summary>
            导入用户信息 配置
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.DutyAllowance">
            <summary>
            一值班二值班费
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.CommutingAllowance">
            <summary>
            上下班交通费
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.NightShiftAllowance24Hours">
            <summary>
            中夜班费24小时班
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.NightShiftAllowanceMidShift">
            <summary>
            中夜班费中班
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.NightShiftAllowanceNightShift">
            <summary>
            中夜班费夜班
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.NightShiftAllowanceEmergency24Hours">
            <summary>
            中夜班费急诊24小时班
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.NightShiftAllowanceEmergencyMidShift">
            <summary>
            中夜班费急诊中班
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.NightShiftAllowanceEmergencyNightShift">
            <summary>
            中夜班费急诊夜班
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.NightShiftAllowanceNursingGradeA24Hours">
            <summary>
            中夜班费护理A档24小时班
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.NightShiftAllowanceNursingGradeAMidShift">
            <summary>
            中夜班费护理A档中班
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.NightShiftAllowanceNursingGradeANightShift">
            <summary>
            中夜班费护理A档夜班
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.MembershipFeePercentage">
            <summary>
            会费比例
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.ParkingSubsidy">
            <summary>
            停车补贴
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.HousingFundPercentage">
            <summary>
            公积金比例
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.PensionInsurancePercentage">
            <summary>
            养老保险比例
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.MedicalInsurancePercentage">
            <summary>
            医疗保险比例
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.SouthCampusAdminDutyAllowanceDayNightShift">
            <summary>
            南院行政值班费日班夜班
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.SouthCampusAdminDutyAllowanceHolidays">
            <summary>
            南院行政值班费节假日
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.PostdoctoralMembershipFee">
            <summary>
            博士后会费
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.PostdoctoralHousingAllowance">
            <summary>
            博士后房贴
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.UnemploymentInsurancePercentage">
            <summary>
            失业保险比例
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.DailyWageCalculationDays">
            <summary>
            日工资计算天数
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.MinimumWage">
            <summary>
            最低工资
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.MinimumSocialSecurityPaymentBase">
            <summary>
            最低社保缴费基数
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.MaximumSocialSecurityPaymentBase">
            <summary>
            最高社保缴费基数
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.OneChildAllowance">
            <summary>
            独生子女费
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.FoodAllowance">
            <summary>
            粮油补贴
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.OccupationalAnnuity">
            <summary>
            职业年金比例
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Consts.SalaryData.SupplementaryHousingFundPercentage">
            <summary>
            补充公积金比例
            </summary>
        </member>
        <member name="T:Renji.JHR.Common.Permissions.SysManage">
            <summary>
            系统管理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.SysManage.sysManage">
            <summary>
            系统管理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.SysManage.dicInfo">
            <summary>
            字典信息定义
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.SysManage.DefineDictionType">
            <summary>
            字典类型定义
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.SysManage.RolePermission">
            <summary>
            用户组权限管理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.SysManage.UserManage">
            <summary>
            用户管理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.SysManage.RoleManage">
            <summary>
            用户组管理
            </summary>
        </member>
        <member name="T:Renji.JHR.Common.Permissions.AttendanceManage">
            <summary>
            考勤管理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.DutyFee">
            <summary>
            一值班二值班费
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.AttendanceData">
            <summary>
            考勤数据
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.AttendanceStaging">
            <summary>
            考勤数据暂存
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.AttendanceApply">
            <summary>
            考勤数据申报
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.AttendanceSearch1">
            <summary>
            考勤数据查询1
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.AttendanceSearch2">
            <summary>
            考勤数据查询2
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.AttendanceSearch3">
            <summary>
            考勤数据查询3
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.AttendanceHR">
            <summary>
            考勤数据人事
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.AttendanceHRApproval">
            <summary>
            考勤数据人事审批
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.AttendanceUpdate">
            <summary>
            考勤数据修改
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.AttDayOffRecordProphylactic">
            <summary>
            防保科考勤申报
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.ProphylacticQuery">
            <summary>
            防保科考勤查询
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.ProphylacticChange">
            <summary>
            防保科考勤变动
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.MiddleNightShiftFee">
            <summary>
            中夜班费
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.MonthShiftRecordHR">
            <summary>
            中夜班费人事
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.MonthShiftRecordStaging">
            <summary>
            中夜班费暂存
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.MonthShiftRecordApply">
            <summary>
            中夜班费申请
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.MonthShiftRecordUpdate">
            <summary>
            中夜班费修改
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.MonthShiftRecordSearch">
            <summary>
            中夜班费查询
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.WatchFee">
            <summary>
            行政值班费
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.HolidayOvertimeFee">
            <summary>
            节日加班费
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.holidayOvertimeFeeStaging">
            <summary>
            节日加班费暂存
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.holidayOvertimeFeeApply">
            <summary>
            节日加班费申请
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.holidayOvertimeFeeHr">
            <summary>
            节日加班费人事
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.holidayOvertimeFeeSearch">
            <summary>
            节日加班费查询
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.AttendanceManage.holidayOvertimeFeeUpdate">
            <summary>
            节日加班费修改
            </summary>
        </member>
        <member name="T:Renji.JHR.Common.Permissions.MsgComList">
            <summary>
            消息通知
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.MsgComList.MsgComlist">
            <summary>
            消息通知
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.MsgComList.Msglist">
            <summary>
            公告发布
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.MsgComList.MsgPerlist">
            <summary>
            消息发布
            </summary>
        </member>
        <member name="T:Renji.JHR.Common.Permissions.OrganizationManage">
            <summary>
            组织机构管理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.OrganizationManage.Organization">
            <summary>
            组织管理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.OrganizationManage.PositionLevelManage">
            <summary>
            岗位级别管理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.OrganizationManage.PositionManage">
            <summary>
            岗位名称管理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.OrganizationManage.SalaryScaleManage">
            <summary>
            薪级工资管理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.OrganizationManage.JobSubsidy">
            <summary>
            岗位津贴
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.OrganizationManage.Seniority">
            <summary>
            工龄津贴
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.OrganizationManage.TelephoneFee">
            <summary>
            电话费
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.OrganizationManage.CarSubsidy">
            <summary>
            公车补贴
            </summary>
        </member>
        <member name="T:Renji.JHR.Common.Permissions.HRManage">
            <summary>
            人事资源管理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.HRManage.StaffInfo">
            <summary>
            人员信息
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.HRManage.EmpOrgModifier">
            <summary>
            人员部门调整
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.HRManage.LogisticsStaffManage">
            <summary>
            后勤人员管理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.HRManage.EmpOrgModifierNurseManage">
            <summary>
            护理人员管理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.HRManage.FinanceSearch">
            <summary>
            财务查看
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.HRManage.EmpRoster">
            <summary>
            高级查询
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.HRManage.OtherEmpManage">
            <summary>
            其他人员库管理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.HRManage.ContractManage">
            <summary>
            合同到期管理
            </summary>
        </member>
        <member name="T:Renji.JHR.Common.Permissions.SalaryManage">
            <summary>
            薪资管理
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.SalaryManage.employeeSalary">
            <summary>
            员工薪资
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Permissions.SalaryManage.ThirteenthSalary">
            <summary>
            十三薪
            </summary>
        </member>
        <member name="T:Renji.JHR.Common.SysControlRightPanel">
            <summary>
            人事分页 和 人事信息按钮
            </summary>
        </member>
        <member name="T:Renji.JHR.Common.SysControlRightPanel.Button">
            <summary>
            2
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.SysControlRightPanel.Button.Code_Query">
            <summary>
            查询设置按钮
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.SysControlRightPanel.Button.Code_Add">
            <summary>
            添加按钮
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.SysControlRightPanel.Button.Code_Delete">
            <summary>
            删除按钮
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.SysControlRightPanel.Button.Code_SocAge">
            <summary>
            工龄更新
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.SysControlRightPanel.Button.Code_GeneralHoliday">
            <summary>
            工休更新
            </summary>
        </member>
        <member name="T:Renji.JHR.Common.SysControlRightPanel.PaginationNavigation">
            <summary>
            0
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.SysPositions.Staff">
            <summary>
            员工
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.SysPositions.DeptHead">
            <summary>
            部门负责人
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.SysPositions.Superviser">
            <summary>
            分管院领导
            </summary>
        </member>
        <member name="M:Renji.JHR.Common.DataTableHelper.CheckColumns(System.Data.DataTable,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            检查列头是否符合格式要求
            </summary>
            <param name="uploadData">从Excel中读取的表</param>
            <param name="columnsMapping">Excel导入时字段映射，取Key值</param>
        </member>
        <member name="M:Renji.JHR.Common.DataTableHelper.TransferToDataBaseDataTable(System.Data.DataTable,System.Collections.Generic.Dictionary{System.String,System.String},System.Collections.Generic.Dictionary{System.String,System.Type})">
            <summary>
            将Excel中读取到的表转换为与数据库表结构一致
            </summary>
            <param name="dataTable"></param>
            <param name="mappingColumns">Excel与数据库表列映射</param>
            <param name="dataBaseOrderedColumn">数据库列顺序</param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.DataTableHelper.RemoveEmptyRow(System.Data.DataTable,System.Collections.Generic.List{System.String})">
            <summary>
            删除空行
            </summary>
            <param name="dt"></param>
            <param name="excludeColumnNames"></param>
        </member>
        <member name="M:Renji.JHR.Common.DynamicQuery.GetDynamicQuery``1(System.Collections.Generic.List{Renji.JHR.Common.QueryCondition})">
            <summary>
            动态获取查询表达式
            </summary>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.DynamicQuery.GetMemberExpression(System.Linq.Expressions.Expression,System.Type,System.String[],System.Int32)">
            <summary>
            获取Member表达式
            </summary>
            <param name="member"></param>
            <param name="type"></param>
            <param name="property"></param>
            <param name="index"></param>
            <returns></returns>
        </member>
        <member name="P:Renji.JHR.Common.QueryCondition.EnumLogicRelationship">
            <summary>
            逻辑关系：ANR , OR
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.QueryCondition.EntityColumnName">
            <summary>
            组合查询的列名，如果是扩展属性的列名，如下格式：EmployeeHR.HireDate。暂时只考虑一对一或者多对一的导航属性。一对多的暂时不支持。
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.QueryCondition.EntityColumnType">
            <summary>
            列的字段类型
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.QueryCondition.EnumOperation">
            <summary>
            查询条件：> , == , != , >= 等等
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.QueryCondition.Keywords">
            <summary>
            查询关键字
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.QueryCondition.Children">
            <summary>
            内部查询
            </summary>
        </member>
        <member name="M:Renji.JHR.Common.Utility.ExcelHelper.DataTableToExcel(System.IO.Stream,System.Data.DataTable,System.String,System.Boolean,Renji.JHR.Common.Utility.ExcelHelper.ExcelType)">
            <summary>
            将DataTable数据导入到excel中
            </summary>
            <param name="stream"></param>
            <param name="data">要导入的数据</param>
            <param name="isColumnWritten">DataTable的列名是否要导入</param>
            <param name="sheetName">要导入的excel的sheet的名称</param>
            <param name="excelType"></param>
            <returns>导入数据行数(包含列名那一行)</returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.ExcelHelper.ExcelToDataTable(System.IO.Stream,System.String,System.Boolean,System.Data.DataTable,Renji.JHR.Common.Utility.ExcelHelper.ExcelType)">
            <summary>
            将excel中的数据导入到DataTable中
            </summary>
            <param name="stream"></param>
            <param name="sheetName">excel工作薄sheet的名称</param>
            <param name="isFirstRowColumn">第一行是否是DataTable的列名</param>
            <param name="data"></param>
            <param name="excelType"></param>
            <returns>返回的DataTable</returns>
        </member>
        <member name="P:Renji.JHR.Common.Utility.SM4Config.Data">
            <summary>
            数据
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Utility.SM4Config.Key">
            <summary>
            秘钥(//密钥长度必须为16字节)
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Utility.SM4Config.Iv">
            <summary>
            向量
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Utility.SM4Config.HexString">
            <summary>
            明文是否是十六进制
            </summary>
        </member>
        <member name="P:Renji.JHR.Common.Utility.SM4Config.CryptoMode">
            <summary>
            加密模式(默认ECB)
            统一改为ECB模式
            </summary>
        </member>
        <member name="T:Renji.JHR.Common.Utility.SM4Utils">
            <summary>
            Sm4算法
            对标国际DES算法
            </summary>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4Utils.EncryptECB(Renji.JHR.Common.Utility.SM4Config)">
            <summary>
            ECB加密
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4Utils.EncryptCBC(Renji.JHR.Common.Utility.SM4Config)">
            <summary>
            CBC加密
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4Utils.DecryptECB(Renji.JHR.Common.Utility.SM4Config)">
            <summary>
             ECB解密
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4Utils.DecryptCBC(Renji.JHR.Common.Utility.SM4Config)">
            <summary>
            CBC解密
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="T:Renji.JHR.Common.Utility.SM4Utils.Sm4CryptoEnum">
            <summary>
            加密类型
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Utility.SM4Utils.Sm4CryptoEnum.ECB">
            <summary>
            ECB(电码本模式)
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Utility.SM4Utils.Sm4CryptoEnum.CBC">
            <summary>
            CBC(密码分组链接模式)
            </summary>
        </member>
        <member name="T:Renji.JHR.Common.Utility.Sm4Context">
             <summary>
            
             </summary>
        </member>
        <member name="F:Renji.JHR.Common.Utility.Sm4Context.Mode">
            <summary>
            1表示加密，0表示解密
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Utility.Sm4Context.Key">
            <summary>
            密钥
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Utility.Sm4Context.IsPadding">
            <summary>
            是否补足16进制字符串
            </summary>
        </member>
        <member name="T:Renji.JHR.Common.Utility.SM4">
             <summary>
            
             </summary>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4.GetULongByBe(System.Byte[],System.Int32)">
            <summary>
            加密 非线性τ函数B=τ(A)
            </summary>
            <param name="b"></param>
            <param name="i"></param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4.PutULongToBe(System.Int64,System.Byte[],System.Int32)">
            <summary>
            解密 非线性τ函数B=τ(A)
            </summary>
            <param name="n"></param>
            <param name="b"></param>
            <param name="i"></param>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4.Rotl(System.Int64,System.Int32)">
            <summary>
            循环移位,为32位的x循环左移n位
            </summary>
            <param name="x"></param>
            <param name="n"></param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4.Swap(System.Int64[],System.Int32)">
            <summary>
            将密钥逆序
            </summary>
            <param name="sk"></param>
            <param name="i"></param>
        </member>
        <member name="F:Renji.JHR.Common.Utility.SM4.SboxTable">
            <summary>
            S盒
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Utility.SM4.FK">
            <summary>
            系统参数FK
            </summary>
        </member>
        <member name="F:Renji.JHR.Common.Utility.SM4.CK">
            <summary>
            固定参数CK
            </summary>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4.Sm4Sbox(System.Byte)">
            <summary>
            Sm4的S盒取值
            </summary>
            <param name="inch"></param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4.Sm4Lt(System.Int64)">
            <summary>
            线性变换 L
            </summary>
            <param name="ka"></param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4.Sm4F(System.Int64,System.Int64,System.Int64,System.Int64,System.Int64)">
            <summary>
            轮函数 F
            </summary>
            <param name="x0"></param>
            <param name="x1"></param>
            <param name="x2"></param>
            <param name="x3"></param>
            <param name="rk"></param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4.Sm4CalciRk(System.Int64)">
            <summary>
            轮密钥rk
            </summary>
            <param name="ka"></param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4.SetKey(System.Int64[],System.Byte[])">
            <summary>
            加密密钥
            </summary>
            <param name="SK"></param>
            <param name="key"></param>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4.Sm4OneRound(System.Int64[],System.Byte[],System.Byte[])">
            <summary>
            解密函数
            </summary>
            <param name="sk">轮密钥</param>
            <param name="input">输入分组的密文</param>
            <param name="output">输出的对应的分组明文</param>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4.Padding(System.Byte[],System.Int32)">
            <summary>
            补足 16 进制字符串的 0 字符，返回不带 0x 的16进制字符串
            </summary>
            <param name="input"></param>
            <param name="mode">1表示加密，0表示解密</param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4.SetKeyEnc(Renji.JHR.Common.Utility.Sm4Context,System.Byte[])">
            <summary>
            设置加密的key
            </summary>
            <param name="ctx"></param>
            <param name="key"></param>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4.Sm4SetKeyDec(Renji.JHR.Common.Utility.Sm4Context,System.Byte[])">
            <summary>
            设置解密的key
            </summary>
            <param name="ctx"></param>
            <param name="key"></param>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4.Sm4CryptEcb(Renji.JHR.Common.Utility.Sm4Context,System.Byte[])">
            <summary>
            ECB
            </summary>
            <param name="ctx"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.SM4.Sm4CryptCbc(Renji.JHR.Common.Utility.Sm4Context,System.Byte[],System.Byte[])">
            <summary>
            CBC
            </summary>
            <param name="ctx"></param>
            <param name="iv"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.Utility.FormatDate(System.Nullable{System.DateTime})">
            <summary>
            获取格式化日期,yyyy-MM-dd
            </summary>
            <param name="obj">日期</param>
            <returns>格式化后的日期</returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.Utility.ToDataTable``1(System.Collections.Generic.List{``0})">
            <summary>
            将泛型集合类转换成DataTable
            </summary>
            <typeparam name="T">集合项类型</typeparam>
            <param name="list">集合</param>
            <returns>数据集(表)</returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.Utility.ListToDataTable``1(System.Collections.Generic.List{``0})">
            <summary>
            将泛类型集合List类转换成DataTable
            </summary>
            <param name="entitys">泛类型集合</param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.Utility.ConvertToList(System.Data.DataTable)">
            <summary>
            DataTable转动态List
            </summary>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.Utility.Utility.ConvertDataReaderToList(System.Data.Common.DbDataReader)">
            <summary>
            DataReader转动态List
            </summary>
            <param name="dr"></param>
            <returns></returns>
        </member>
        <member name="M:Renji.JHR.Common.WebServiceCaller.QuerySoapWebService(System.String,System.String,System.Collections.Hashtable,System.Boolean)">
            <summary>
            通用WebService调用(Soap),参数Pars为String类型的参数名、参数值
            </summary>
        </member>
        <member name="M:Renji.JHR.Common.WebServiceCaller.SetWebRequest(System.Net.HttpWebRequest)">
            <summary>
            设置凭证与超时时间
            </summary>
            <param name="request"></param>
        </member>
    </members>
</doc>
