﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Renji.JHR.Entities
{
    /// <summary>
    /// 加班类型
    /// </summary>
    public enum OverTimeType
    {
        /// <summary>
        /// 无
        /// </summary>
        [Description("无")]
        None = -1,
        /// <summary>
        /// 4小时班
        /// </summary>
        [Description("4小时班")]
        Hour_4 = 1,
        /// <summary>
        /// 8小时班
        /// </summary>
        [Description("8小时班")]
        Hour_8 = 2,
        /// <summary>
        /// 12小时班
        /// </summary>
        [Description("12小时班")]
        Hour_12 = 3,
        /// <summary>
        /// 16小时班
        /// </summary>
        [Description("16小时班")]
        Hour_16 = 4,
        /// <summary>
        /// 24小时班
        /// </summary>
        [Description("24小时班")]
        Hour_24 = 5,
        /// <summary>
        /// 医生24小时班
        /// </summary>
        [Description("医生24小时班")]
        Hour_Doctor24 = 6,


    }
}
