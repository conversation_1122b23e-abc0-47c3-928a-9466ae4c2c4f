﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Renji.JHR.Api.Models;
using Renji.JHR.Bll;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.Mvc;
using Shinsoft.Core.NLog;
using System;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Net;

namespace Renji.JHR.Api.Controllers
{
    /// <summary>
    /// 附件
    /// </summary>
    public class FileController : BaseApiController<FileBll>
    {
        //代码检测时，注释Upload方法

        /// <summary>
        /// 附件上传
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.File, Operate = "附件上传")]
        public BizResult<AttachmentModel> Upload([FromForm] IFormCollection form)
        {
            var result = new BizResult<AttachmentModel>();

            if (form.Files.Count == 0)
            {
                result.Error("请选择需要上传的文件");
            }
            else
            {
                var ms = new MemoryStream();

                var file = form.Files[0];

                file.CopyTo(ms);

                var fileInfo = new FileInfo(file.FileName);

                var entity = new Attachment
                {
                    ContentType = file.ContentType,
                    FileName = fileInfo.Name,
                    FileExt = fileInfo.Extension,
                    Stream = ms,

                    ObjectType = form["ObjectType"].AsString(),
                    ObjectId = form["ObjectId"].AsString(),
                    ObjectItemId = form["ObjectItemId"].AsString(),
                };

                var bizResult = this.Repo.SaveFile(entity);

                result.Data = bizResult.Data?.Map<AttachmentModel>();
            }

            return result;
        }

        /// <summary>
        /// 附件下载
        /// </summary>
        /// <param name="id">附件ID</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("{id}")]
        [LogApi(ApiType.Query, Operate = "附件下载")]
        public IActionResult Download([FromRoute, Required] Guid id)
        {
            var bll = this.Repo as IRepo;
            var attachment = bll.BizRepo.Get<Attachment>(id);

            HttpStatusCode statusCode = HttpStatusCode.OK;
            byte[]? bytes = null;

            if (attachment == null)
            {
                statusCode = HttpStatusCode.NotFound;
            }
            else
            {
                var fileIndex = bll.Get<FileIndex>(FileIndex.Inverses.FileContent, attachment.FileIndexId);

                if (fileIndex == null)
                {
                    statusCode = HttpStatusCode.NotFound;
                }
                else
                {
                    if (!fileIndex.BaseFolder.IsEmpty() && !fileIndex.SubPath.IsEmpty())
                    {
                        var filePath = Path.Combine(fileIndex.BaseFolder, fileIndex.SubPath);

                        if (System.IO.File.Exists(filePath))
                        {
                            bytes = System.IO.File.ReadAllBytes(filePath);
                        }
                        else
                        {
                            statusCode = HttpStatusCode.NotFound;
                        }
                    }
                    else if (fileIndex.FileContent != null)
                    {
                        bytes = fileIndex.FileContent.Content;
                    }
                    else
                    {
                        statusCode = HttpStatusCode.NotFound;
                    }
                }
            }

            if (statusCode == HttpStatusCode.OK)
            {
                return this.File(bytes!, attachment!.ContentType, attachment.FileName);
            }
            else
            {
                return this.StatusCode((int)statusCode);
            }
        }
    }
}