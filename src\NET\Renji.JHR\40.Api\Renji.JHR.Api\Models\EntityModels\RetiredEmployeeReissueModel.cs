﻿﻿namespace Renji.JHR.Api.Models
{
    public partial class RetiredEmployeeReissueModel
    {
        [MapFromProperty(typeof(RetiredEmployeeReissue), RetiredEmployeeReissue.Foreigns.Employee)]
        public EmployeeModel? Employee { get; set; }

        /// <summary>
        /// 薪资类型
        /// </summary>
        [MapFromProperty(typeof(RetiredEmployeeReissue), RetiredEmployeeReissue.Foreigns.Employee, Entities.Employee.Inverses.EmployeeSalary, EmployeeSalary.Columns.EnumSalaryType)]
        public SalaryType? EnumSalaryType { get; set; }
    }
}
