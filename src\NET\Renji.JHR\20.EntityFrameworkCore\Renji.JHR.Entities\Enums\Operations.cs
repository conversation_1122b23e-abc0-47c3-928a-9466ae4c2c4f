﻿using Shinsoft.Core;

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Renji.JHR.Entities
{
    public enum Operations
    {
        /// <summary>
        /// 无
        /// </summary>
        [Description("无")]
        None = 0,
        /// <summary>
        /// 等于
        /// </summary>
        [Description("等于")]
        Equals = 10,
        /// <summary>
        /// 不等于
        /// </summary>
        [Description("不等于")]
        NotEqual = 15,
        /// <summary>
        /// 大于
        /// </summary>
        [Description("大于")]
        GreaterThan = 20,
        /// <summary>
        /// 大于等于
        /// </summary>
        [Description("大于等于")]
        GreaterThanOrEqual = 25,
        /// <summary>
        /// 小于
        /// </summary>
        [Description("小于")]
        LessThan = 30,
        /// <summary>
        /// 小于等于
        /// </summary>
        [Description("小于等于")]
        LessThanOrEqual = 35,
        ///// <summary>
        ///// 不为Null
        ///// </summary>
        //[Description("不为Null")]
        //NotNull = 40,
        ///// <summary>
        ///// 为Null
        ///// </summary>
        //[Description("为Null")]
        //IsNull = 45,
        ///// <summary>
        /////  不为Null且不为Empty
        ///// </summary>
        //[Description("不为Null且不为Empty")]
        //NotNullAndNotEmpty = 50,
        ///// <summary>
        ///// 在列表中(逗号分隔)
        ///// </summary>
        //[Description("在列表中(逗号分隔)")]
        //InList = 60,
        ///// <summary>
        ///// 不在列表中(逗号分隔)
        ///// </summary>
        //[Description("不在列表中(逗号分隔)")]
        //NotInList = 65,
        ///// <summary>
        ///// 右边包含
        ///// </summary>
        //[Description("右边包含")]
        //EndsWith = 70,
        ///// <summary>
        ///// 左边包含
        ///// </summary>
        //[Description("左边包含")]
        //StartsWith = 75,
        /// <summary>
        /// 包含
        /// </summary>
        [Description("包含")]
        Contains = 80,
    }
}
