﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Renji.JHR.Api
{
    public class RedirectionHandlerMiddleware
    {
        private RequestDelegate _next;
        public RedirectionHandlerMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            await context.Response.WriteAsync("");
        }
    }
}
