﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Renji.JHR.Api.Models;
using Renji.JHR.Bll;
using Renji.JHR.Common;
using Renji.JHR.Common.Consts;
using Renji.JHR.Common.Utility;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.DynamicQuery;
using Shinsoft.Core.Mvc;
using Shinsoft.Core.NLog;

namespace Renji.JHR.Api.Controllers
{
    public class SysManageController : BaseApiController<SysManageBll>
    {
        [AllowAnonymous]
        [HttpGet]
        public BizResult<Guid> NewGuid()
        {
            return this.BizResult(CombGuid.NewGuid());
        }

        [HttpGet]
        public QueryResult<EnumInfo> GetEnumInfos([FromQuery] EnumFilter filter)
        {
            return this.QueryResult(filter.GetEnumInfos());
        }

        #region Dict

        /// <summary>
        /// 查询字典项
        /// parentName为空，查询字典类型
        /// parentName不为空，查询字典类型为parentName的字典信息
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询字典项")]
        public QueryResult<DictQuery> QueryDict([FromQuery] DictFilter filter)
        {
            if (filter.Order.IsEmpty())
            {
                filter.Order = $"{Dict.Foreigns.Parent}.{Dict.Columns.Code} ASC,{Dict.Columns.Code} ASC";
            }

            var result = this.Repo.GetDynamicQuery<Dict, DictQuery>(filter);

            return result;
        }

        /// <summary>
        /// 查询字典类型
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "查询字典类型")]
        public QueryResult<DictQuery> QueryDictType([FromBody] DictTypeFilter filter)
        {
            var exps = this.NewExps<Entities.Dict>();

            exps.And(p => p.ParentId == null);

            if (filter.ConditionList != null && filter.ConditionList.Any())
            {
                var expression = new Common.DynamicQuery().GetDynamicQuery<Entities.Dict>(filter.ConditionList);
                exps.And(expression);
            }

            if (filter.Order.IsEmpty())
            {
                filter.Order = "Code ,Name asc";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<DictQuery>();

            var aa = this.QueryResult(models, recoredCount, filter);
            return aa;
        }

        /// <summary>
        /// 获取字典项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取字典项")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.dicInfo, Permissions.SysManage.DefineDictionType)]
        public BizResult<DictModel> GetDict([FromQuery, Required] Guid id)
        {
            var result = this.BizResult<DictModel>();

            var entity = this.Repo.Get<Dict>(id);

            if (entity == null)
            {
                result.Error("字典项不存在");
            }
            else
            {
                var model = entity.Map<DictModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增字典项
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增字典项")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.dicInfo, Permissions.SysManage.DefineDictionType)]
        public BizResult<DictModel> AddDict([FromBody] DictModel model)
        {
            var entity = model.Map<Dict>();

            var result = this.Repo.AddDict(entity);

            return result.Map<DictModel>();
        }

        /// <summary>
        /// 更新字典项
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新字典项")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.dicInfo, Permissions.SysManage.DefineDictionType)]
        public BizResult<DictModel> UpdateDict([FromBody] DictModel model)
        {
            var entity = model.Map<Dict>();

            var result = this.Repo.UpdateDict(entity);

            return result.Map<DictModel>();
        }

        /// <summary>
        /// 删除字典项
        /// </summary>
        [HttpPost]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.dicInfo, Permissions.SysManage.DefineDictionType)]
        [LogApi(ApiType.Save, Operate = "删除字典项")]
        public BizResult DeleteDict([FromBody] DictModel model)
        {
            var entity = new Dict
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteDict(entity);

            return result;
        }

        #endregion Dict

        #region SysSetting

        /// <summary>
        /// 查询系统配置项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询系统配置项")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.dicInfo, Permissions.SysManage.DefineDictionType)]
        public QueryResult<SysSettingQuery> QuerySysSetting([FromQuery] SysSettingFilter filter)
        {
            var exps = this.NewExps<Entities.SysSetting>();

            if (!filter.Name.IsEmpty())
            {
                exps.And(p => p.Name.Contains(filter.Name!));
            }

            if (filter.Order.IsEmpty())
            {
                filter.Order = "Key asc";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<SysSettingQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 获取系统配置项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取系统配置项")]
        [Permission(Permissions.SysManage.sysManage)]
        public BizResult<SysSettingModel> GetSysSetting([FromQuery, Required] Guid id)
        {
            var result = this.BizResult<SysSettingModel>();

            var entity = this.Repo.Get<Entities.SysSetting>(id);

            if (entity == null)
            {
                result.Error("系统配置项不存在");
            }
            else
            {
                var model = entity.Map<SysSettingModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增系统配置项
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增系统配置项")]
        [Permission(Permissions.SysManage.sysManage)]
        public BizResult<SysSettingModel> AddSysSetting([FromBody] SysSettingModel model)
        {
            var entity = model.Map<Entities.SysSetting>();

            var result = this.Repo.AddSysSetting(entity);

            return result.Map<SysSettingModel>();
        }

        /// <summary>
        /// 更新系统配置项
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新系统配置项")]
        [Permission(Permissions.SysManage.sysManage)]
        public BizResult<SysSettingModel> UpdateSysSetting([FromBody] SysSettingModel model)
        {
            var entity = model.Map<Entities.SysSetting>();

            var result = this.Repo.UpdateSysSetting(entity);

            return result.Map<SysSettingModel>();
        }

        /// <summary>
        /// 删除系统配置项
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除系统配置项")]
        [Permission(Permissions.SysManage.sysManage)]
        public BizResult DeleteSysSetting([FromBody] SysSettingModel model)
        {
            var entity = new Entities.SysSetting
            {
                ID = model.ID
            };

            var result = this.Repo.DeleteSysSetting(entity);

            return result;
        }

        /// <summary>
        /// 查询语言配置项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询语言配置项")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.dicInfo, Permissions.SysManage.DefineDictionType)]
        public QueryResult<SysSettingQuery> QueryLanguage()
        {
            SysSettingFilter filter = new SysSettingFilter() { Name = Renji.JHR.Common.Consts.SysSetting.NAME_LANGUAGE, Order = "Key desc" };

            return this.QuerySysSetting(filter);
        }

        #endregion SysSetting

        #region 用户组管理

        [HttpPost]
        [LogApi(ApiType.Query, Operate = "查询用户组")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.RoleManage)]
        public QueryResult<RoleModel> QueryUserGroups([FromBody] RoleFilter filter)
        {
            var exps = this.NewExps<Role>();

            if (filter.ConditionList != null && filter.ConditionList.Any())
            {
                var expression = new Common.DynamicQuery().GetDynamicQuery<Role>(filter.ConditionList);
                exps.And(expression);
            }
            if (filter.Order.IsEmpty())
            {
                filter.Order = $"{nameof(Role.Code)} {GeneralConsts.OrderByAsc}";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<RoleModel>();

            return this.QueryResult(models, recoredCount, filter);
        }

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "维护用户组")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.RoleManage)]
        public BizResult SaveUserGroup([FromBody] RoleModel model)
        {
            var result = new BizResult();
            //已经存在该编号
            if (model.Code.IsEmpty())
            {
                result.Error("[用户组编号] 是必填项！");
                return result;
            }
            if (model.Name.IsEmpty())
            {
                result.Error("[用户组名称] 是必填项！");
                return result;
            }
            var codeRepeat = this.Repo.GetUserGroupByCode(model.Code);
            if (codeRepeat != null && codeRepeat.ID != model.ID)
            {
                result.Error("已经存在该编号");
                return result;
            }

            var entity = model.Map<Role>();
            this.Repo.SaveUserGroup(entity);

            return result;
        }

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除用户组")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.RoleManage)]
        public BizResult DeleteUserGroup([FromBody] RoleModel model)
        {
            var result = new BizResult();
            var e = this.Repo.Find<Role>(model.ID);
            if (e != null)
                this.Repo.DeleteUserGroup(e);
            else
                result.Error("未找到用户组");

            return result;
        }

        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取用户组(下拉列表)")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.UserManage, Permissions.SysManage.RolePermission)]
        public BizResult<List<SelectModel>> DropdownUserGroups()
        {
            var result = new BizResult<List<SelectModel>>();
            var list = this.Repo.GetUserGroups();
            result.Data = list.Select(s => new SelectModel { Label = s.Name, Value = s.ID.ToString() }).ToList();

            return result;
        }

        #endregion 用户组管理

        #region 用户管理

        [HttpPost]
        [LogApi(ApiType.Query, Operate = "查询用户")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.UserManage)]
        public QueryResult<ViewUserInfoModel> QueryUsers([FromBody] UserFilter filter)
        {
            var exps = this.NewExps<ViewUserInfo>();

            if (filter.ConditionList != null && filter.ConditionList.Any())
            {
                var expression = new Common.DynamicQuery().GetDynamicQuery<ViewUserInfo>(filter.ConditionList);
                exps.And(expression);
            }
            if (filter.Order.IsEmpty())
            {
                filter.Order = $"{nameof(ViewUserInfo.CreateTime)} {GeneralConsts.OrderByAsc}";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<ViewUserInfoModel>();

            return this.QueryResult(models, recoredCount, filter);
        }

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除用户")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.UserManage)]
        public BizResult DeleteUser([FromBody] ViewUserInfoModel model)
        {
            var result = new BizResult();
            var e = this.Repo.Find<User>(model.ID);
            if (e != null)
                this.Repo.DeleteUser(e);
            else
                result.Error("未找到用户");

            return result;
        }

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "初始化密码")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.UserManage)]
        public BizResult<string> InitPwd([FromBody] UserModel model)
        {
            var result = new BizResult<string>();
            var e = this.Repo.Find<User>(model.ID);
            if (e != null)
            {
                var initPwd = StringUtil.GenRandomString(6);

                result.Data = initPwd;
                switch (e.EnumPwdType)
                {
                    case PwdType.None:
                        e.PwdText = initPwd;
                        break;

                    case PwdType.MD5:
                        e.PwdText = result.Data.ToMD5();
                        break;

                    case PwdType.SHA1:
                        e.PwdText = result.Data.ToSHA1();
                        break;

                    default:
                        break;
                }

                this.Repo.SaveUser(e);
            }
            else
            {
                result.Error("未找到用户");
            }

            return result;
        }

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "维护用户")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.UserManage)]
        public BizResult SaveUser([FromBody] UserModel model)
        {
            var result = new BizResult();
            //
            if (model.Username.IsEmpty())
            {
                result.Error("[登录名] 是必填项!");
                return result;
            }
            if (model.EmpCode.IsEmpty() || model.Uid == 0)
            {
                result.Error("[用户名] 是必填项!");
                return result;
            }
            if (model.ID == default)
            {
                if (model.NewPwdText.IsEmpty())
                {
                    result.Error("[密码] 是必填项!");
                    return result;
                }
                model.PwdText = SM4Utils.DecryptCBC(new SM4Config(model.NewPwdText ?? ""));
                Regex regex = new Regex(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%\*-\+=:,\\?\[\]\{}]).{8}");
                if (!regex.IsMatch(model.PwdText))
                {
                    result.Error("[密码] 必须是大写字母，小写字母，数字，字符组成，且长度不少于八位！");
                    return result;
                }
            }
            model.Username = SM4Utils.DecryptCBC(new SM4Config(model.Username));
            var usernameRepeat = this.Repo.GetUserByUsername(model.Username);
            if (usernameRepeat != null && usernameRepeat.ID != model.ID)
            {
                result.Error("已经存在该登录名");
                return result;
            }

            if (model.ID != default)
            {
                var e = this.Repo.Find<User>(model.ID);

                if (e != null)
                {
                    e.EmpCode = model.EmpCode;
                    e.DisplayName = model.DisplayName;
                    e.Uid = model.Uid;

                    this.Repo.SaveUser(e);

                    var rolemembers = model.RoleMembers?.Select(s => new RoleMember { RoleId = s.RoleId }).ToList();
                    this.Repo.UpdateUserRole(e, rolemembers);
                }
            }
            else
            {
                //新增
                var e = model.Map<User>();
                e.EnumType = UserType.Employee;
                e.EnumPwdType = PwdType.MD5;
                e.EnumStatus = UserStatus.Valid;
                e.PwdText = e.PwdText.ToMD5();

                this.Repo.SaveUser(e);

                var rolemembers = model.RoleMembers?.Select(s => new RoleMember { RoleId = s.RoleId }).ToList();
                this.Repo.UpdateUserRole(e, rolemembers);
            }




            return result;
        }

        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询用户byid")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.UserManage)]
        public BizResult<UserModel> GetUserById(Guid id)
        {
            var result = new BizResult<UserModel>();
            var e = this.Repo.Find<User>(id);
            var model = e?.Map<UserModel>();

            result.Data = model;
            return result;
        }

        [HttpPost]
        [LogApi(ApiType.Query, Operate = "查询Employee")]
        public QueryResult<EmployeeModel> QueryEmployees([FromBody] UserFilter filter)
        {
            var exps = this.NewExps<Entities.Employee>();

            //if (filter.ConditionList != null && filter.ConditionList.Any())
            //{
            //    var expression = new Common.DynamicQuery().GetDynamicQuery<ViewUserInfo>(filter.ConditionList);
            //    exps.And(expression);
            //}
            if (!filter.EmpCode.IsEmpty())
            {
                exps.And(s => s.EmpCode.Contains(filter.EmpCode!));
            }
            if (!filter.DisplayName.IsEmpty())
            {
                exps.And(s => s.DisplayName.Contains(filter.DisplayName!));
            }
            if (filter.Sex.HasValue)
            {
                exps.And(s => s.EnumGender == filter.Sex.Value);
            }
            if (filter.DeptId.HasValue)
            {
                var deptIds = this.Repo.GetDeptByCurrentUser(filter.DeptId.Value);
                exps.And(p => p.DeptId.HasValue && deptIds.Contains(p.DeptId.Value));
            }

            //if (filter.Order.IsEmpty())
            //{
            //    filter.Order = $"{nameof(ViewUserInfo.CreateTime)} asc";
            //}

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<EmployeeModel>();

            return this.QueryResult(models, recoredCount, filter);
        }

        #endregion 用户管理

        #region 用户组权限管理

        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询模块")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.RolePermission)]
        public BizResult<List<PermissionModel>> QueryModuleInfos()
        {
            var entities = this.Repo.GetEntities<Permission>();

            var all = entities.Maps<PermissionModel>();

            SemiNumericComparer comp = new SemiNumericComparer();

            var list = all.Where(s => !s.ParentId.HasValue).OrderBy(s => s.Sequence).ToList();

            foreach (var model in list)
            {
                model.Children = all.Where(p => p.ParentId == model.ID).OrderBy(p => p.Sequence).ToList();
                if (model.Children != null && model.Children.Count > 0)
                {
                    foreach (var subItem in model.Children)
                    {
                        subItem.Children = all.Where(p => p.ParentId == subItem.ID).OrderBy(p => p.Sequence).ToList();
                    }
                }
            }

            return this.BizResult(list);
        }

        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询用户组模块")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.RolePermission)]
        public BizResult<List<RolePermissionModel>> GetRightSettingByUserGroup(Guid usergroupid)
        {
            var entities = this.Repo.GetEntities<RolePermission>(predicate: s => s.RoleId == usergroupid && s.Permission.Children.Count == 0);

            var all = entities.Maps<RolePermissionModel>();

            return this.BizResult(all);
        }

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "保存用户组模块")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.RolePermission)]
        public BizResult SaveRightSetting([FromBody] RightSettingSaveModel model)
        {
            var result = new BizResult();
            var entities = model.ModuleInfoIds.Select(s => new RolePermission
            {
                RoleId = model.UserGroupId,
                PermissionId = s
            });

            this.Repo.SaveRightSetting(entities, model.UserGroupId);
            return result;
        }

        //组织结果
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询用户组 部门")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.RolePermission)]
        public BizResult<List<RightOfDeptModel>> GetRightOfDeptByUserGroup(Guid usergroupid)
        {
            var entities = this.Repo.GetEntities<RightOfDept>(predicate: s => s.RoleId == usergroupid);

            var all = entities.Maps<RightOfDeptModel>();

            return this.BizResult(all);
        }

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "保存用户组部门")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.RolePermission)]
        public BizResult SaveRightOfDept([FromBody] RightOfDeptSaveModel model)
        {
            var result = new BizResult();
            var entities = model.DeptIds.Select(s => new RightOfDept
            {
                RoleId = model.UserGroupId,
                DepartmentId = s,
                // RightLevel
            });

            this.Repo.SaveRightOfDept(entities, model.UserGroupId);
            return result;
        }

        //人事分页
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "查询人事分页")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.RolePermission)]
        public QueryResult<ControlRightModel> QueryControlRight([FromBody] ControlRightFilter filter)
        {
            #region insert

            var ycz = this.Repo.GetControlRightPanelIdsByUserGroupId(filter.UserGroupId);
            var controlRightPanels = this.Repo.GetAllControlRightPanel();
            var all = controlRightPanels.Select(s => s.ID).ToList();
            var insertids = all.Except(ycz);
            if (insertids.Count() > 0)
            {
                var insets = controlRightPanels.Where(s => insertids.Contains(s.ID)).ToList();
                var insertList = insets.Select(s => new ControlRight
                {
                    RoleId = filter.UserGroupId,
                    ControlRightPanelId = s.ID,
                    EnumUserRight = UserRight.NoPermission
                });
                this.Repo.AddControlRight(insertList);
            }

            #endregion insert

            var exps = this.NewExps<ControlRight>();

            exps.And(s => s.RoleId == filter.UserGroupId && s.ControlRightPanel.Deleted == false);

            //if (filter.ConditionList != null && filter.ConditionList.Any())
            //{
            //    var expression = new Common.DynamicQuery().GetDynamicQuery<ViewUserInfo>(filter.ConditionList);
            //    exps.And(expression);
            //}
            if (filter.Order.IsEmpty())
            {
                filter.Order = $"ControlRightPanel.Order {GeneralConsts.OrderByAsc}";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<ControlRightModel>();

            return this.QueryResult(models, recoredCount, filter);
        }

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "保存人事分页")]
        [Permission(Permissions.SysManage.sysManage, Permissions.SysManage.RolePermission)]
        public BizResult SaveControlRights([FromBody] List<ControlRightModel> models)
        {
            var result = new BizResult();
            var list = models.Maps<ControlRight>();

            this.Repo.UpdateControlRights(list);
            return result;
        }

        /// <summary>
        ///
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取当前登录人人事分页")]
        public BizResult<ControlRightStaffModel> GetControlRightByCurrentUser()
        {
            var result = new BizResult<ControlRightStaffModel>();
            var list = this.Repo.GetControlRightsByCurrentUser();

            var buttons = list.Where(s => s.ControlRightPanel.EnumType == ControlRightPanelType.Button && s.EnumUserRight != UserRight.NoPermission);

            var staff = new ControlRightStaffModel
            {
                IsShowQuery = buttons.Any(s => s.ControlRightPanel.Code == SysControlRightPanel.Button.Code_Query),
                IsShowAdd = buttons.Any(s => s.ControlRightPanel.Code == SysControlRightPanel.Button.Code_Add),
                IsShowDelete = buttons.Any(s => s.ControlRightPanel.Code == SysControlRightPanel.Button.Code_Delete),
                IsShowSocAge = buttons.Any(s => s.ControlRightPanel.Code == SysControlRightPanel.Button.Code_SocAge),
                IsShowGeneralHoliday = buttons.Any(s => s.ControlRightPanel.Code == SysControlRightPanel.Button.Code_GeneralHoliday),
            };

            UserRight GetControlRight(string code)
            {
                return list.Where(s => s.ControlRightPanel.EnumType == ControlRightPanelType.PaginationNavigation &&
                       s.ControlRightPanel.Code == code
                         )
              .OrderByDescending(s => s.EnumUserRight)
              .Select(s => s.EnumUserRight)
              .FirstOrDefault();
            }

            var pns = list.Where(s => s.ControlRightPanel.EnumType == ControlRightPanelType.PaginationNavigation && s.EnumUserRight != UserRight.NoPermission);

            #region HrBaseInfo

            var hrBaseInfoUserRight = GetControlRight(nameof(ControlRightStaffModel.HrBaseInfo));

            staff.HrBaseInfo = new SaveClear
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.HrBaseInfo)),
                IsShowBtnSave = hrBaseInfoUserRight == UserRight.Writable || hrBaseInfoUserRight == UserRight.FullControl,
                IsShowBtnClear = hrBaseInfoUserRight == UserRight.Writable || hrBaseInfoUserRight == UserRight.FullControl,
                UserRight = hrBaseInfoUserRight
            };

            #endregion HrBaseInfo

            #region HrInfo

            var hrHrInfoUserRight = GetControlRight(nameof(ControlRightStaffModel.HrInfo));

            staff.HrInfo = new SaveClear
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.HrInfo)),
                IsShowBtnSave = hrHrInfoUserRight == UserRight.Writable || hrHrInfoUserRight == UserRight.FullControl,
                IsShowBtnClear = hrHrInfoUserRight == UserRight.Writable || hrHrInfoUserRight == UserRight.FullControl,
                UserRight = hrHrInfoUserRight
            };

            #endregion HrInfo

            #region Party

            var hrPartyUserRight = GetControlRight(nameof(ControlRightStaffModel.Party));

            staff.Party = new SaveClear
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Party)),
                IsShowBtnSave = hrPartyUserRight == UserRight.Writable || hrPartyUserRight == UserRight.FullControl,
                IsShowBtnClear = hrPartyUserRight == UserRight.Writable || hrPartyUserRight == UserRight.FullControl,
                UserRight = hrPartyUserRight
            };

            #endregion Party

            #region Station

            var stationUserRight = GetControlRight(nameof(ControlRightStaffModel.Station));

            staff.Station = new NewSaveDel
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Station)),
                IsShowBtnNew = stationUserRight == UserRight.Writable || stationUserRight == UserRight.FullControl,
                IsShowBtnSave = stationUserRight == UserRight.Writable || stationUserRight == UserRight.FullControl,
                IsShowBtnDelete = stationUserRight == UserRight.FullControl,
                UserRight = stationUserRight
            };

            #endregion Station

            #region Qualification

            var qualificationUserRight = GetControlRight(nameof(ControlRightStaffModel.Qualification));

            staff.Qualification = new NewSaveDel
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Qualification)),
                IsShowBtnNew = qualificationUserRight == UserRight.Writable || qualificationUserRight == UserRight.FullControl,
                IsShowBtnSave = qualificationUserRight == UserRight.Writable || qualificationUserRight == UserRight.FullControl,
                IsShowBtnDelete = qualificationUserRight == UserRight.FullControl,
                UserRight = qualificationUserRight
            };

            #endregion Qualification

            #region Certify

            var certifyUserRight = GetControlRight(nameof(ControlRightStaffModel.Certify));

            staff.Certify = new NewSaveDel
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Certify)),
                IsShowBtnNew = certifyUserRight == UserRight.Writable || certifyUserRight == UserRight.FullControl,
                IsShowBtnSave = certifyUserRight == UserRight.Writable || certifyUserRight == UserRight.FullControl,
                IsShowBtnDelete = certifyUserRight == UserRight.FullControl,
                UserRight = certifyUserRight
            };

            #endregion Certify

            #region Wages

            var WagesUserRight = GetControlRight(nameof(ControlRightStaffModel.Wages));

            staff.Wages = new SaveClear
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Wages)),
                IsShowBtnSave = WagesUserRight == UserRight.Writable || WagesUserRight == UserRight.FullControl,
                IsShowBtnClear = WagesUserRight == UserRight.Writable || WagesUserRight == UserRight.FullControl,
                UserRight = WagesUserRight
            };

            #endregion Wages

            #region Socialsecurity

            var socialsecurityUserRight = GetControlRight(nameof(ControlRightStaffModel.Socialsecurity));

            staff.Socialsecurity = new SaveClear
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Socialsecurity)),
                IsShowBtnSave = socialsecurityUserRight == UserRight.Writable || socialsecurityUserRight == UserRight.FullControl,
                IsShowBtnClear = socialsecurityUserRight == UserRight.Writable || socialsecurityUserRight == UserRight.FullControl,
                UserRight = socialsecurityUserRight
            };

            #endregion Socialsecurity

            #region Certieducation

            var certieducationUserRight = GetControlRight(nameof(ControlRightStaffModel.Certieducation));

            staff.Certieducation = new NewSaveDel
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Certieducation)),
                IsShowBtnNew = certieducationUserRight == UserRight.Writable || certieducationUserRight == UserRight.FullControl,
                IsShowBtnSave = certieducationUserRight == UserRight.Writable || certieducationUserRight == UserRight.FullControl,
                IsShowBtnDelete = certieducationUserRight == UserRight.FullControl,
                UserRight = certieducationUserRight
            };

            #endregion Certieducation

            #region Work

            var workUserRight = GetControlRight(nameof(ControlRightStaffModel.Work));

            staff.Work = new NewSaveDel
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Work)),
                IsShowBtnNew = workUserRight == UserRight.Writable || workUserRight == UserRight.FullControl,
                IsShowBtnSave = workUserRight == UserRight.Writable || workUserRight == UserRight.FullControl,
                IsShowBtnDelete = workUserRight == UserRight.FullControl,
                UserRight = workUserRight
            };

            #endregion Work

            #region AbroadInfo

            var abroadInfoUserRight = GetControlRight(nameof(ControlRightStaffModel.AbroadInfo));

            staff.AbroadInfo = new NewSaveDel
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.AbroadInfo)),
                IsShowBtnNew = abroadInfoUserRight == UserRight.Writable || abroadInfoUserRight == UserRight.FullControl,
                IsShowBtnSave = abroadInfoUserRight == UserRight.Writable || abroadInfoUserRight == UserRight.FullControl,
                IsShowBtnDelete = abroadInfoUserRight == UserRight.FullControl,
                UserRight = abroadInfoUserRight
            };

            #endregion AbroadInfo

            #region Contract

            var contractUserRight = GetControlRight(nameof(ControlRightStaffModel.Contract));

            staff.Contract = new NewSaveDel
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Contract)),
                IsShowBtnNew = contractUserRight == UserRight.Writable || contractUserRight == UserRight.FullControl,
                IsShowBtnSave = contractUserRight == UserRight.Writable || contractUserRight == UserRight.FullControl,
                IsShowBtnDelete = contractUserRight == UserRight.FullControl,
                UserRight = contractUserRight
            };

            #endregion Contract

            #region Train

            var trainUserRight = GetControlRight(nameof(ControlRightStaffModel.Train));

            staff.Train = new NewSaveDel
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Train)),
                IsShowBtnNew = trainUserRight == UserRight.Writable || trainUserRight == UserRight.FullControl,
                IsShowBtnSave = trainUserRight == UserRight.Writable || trainUserRight == UserRight.FullControl,
                IsShowBtnDelete = trainUserRight == UserRight.FullControl,
                UserRight = trainUserRight
            };

            #endregion Train

            #region Assessment

            var assessmentUserRight = GetControlRight(nameof(ControlRightStaffModel.Assessment));

            staff.Assessment = new NewSaveDel
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Assessment)),
                IsShowBtnNew = assessmentUserRight == UserRight.Writable || assessmentUserRight == UserRight.FullControl,
                IsShowBtnSave = assessmentUserRight == UserRight.Writable || assessmentUserRight == UserRight.FullControl,
                IsShowBtnDelete = assessmentUserRight == UserRight.FullControl,
                UserRight = assessmentUserRight
            };

            #endregion Assessment

            #region Incentive

            var incentiveUserRight = GetControlRight(nameof(ControlRightStaffModel.Incentive));

            staff.Incentive = new NewSaveDel
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Incentive)),
                IsShowBtnNew = incentiveUserRight == UserRight.Writable || incentiveUserRight == UserRight.FullControl,
                IsShowBtnSave = incentiveUserRight == UserRight.Writable || incentiveUserRight == UserRight.FullControl,
                IsShowBtnDelete = incentiveUserRight == UserRight.FullControl,
                UserRight = incentiveUserRight
            };

            #endregion Incentive

            #region Health

            var healthUserRight = GetControlRight(nameof(ControlRightStaffModel.Health));

            staff.Health = new SaveClear
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Health)),
                IsShowBtnSave = healthUserRight == UserRight.Writable || healthUserRight == UserRight.FullControl,
                IsShowBtnClear = healthUserRight == UserRight.Writable || healthUserRight == UserRight.FullControl,
                UserRight = healthUserRight
            };

            #endregion Health

            #region Accident

            var accidentUserRight = GetControlRight(nameof(ControlRightStaffModel.Accident));

            staff.Accident = new NewSaveDel
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Accident)),
                IsShowBtnNew = accidentUserRight == UserRight.Writable || accidentUserRight == UserRight.FullControl,
                IsShowBtnSave = accidentUserRight == UserRight.Writable || accidentUserRight == UserRight.FullControl,
                IsShowBtnDelete = accidentUserRight == UserRight.FullControl,
                UserRight = accidentUserRight
            };

            #endregion Accident

            #region Teach

            var teachUserRight = GetControlRight(nameof(ControlRightStaffModel.Teach));

            staff.Teach = new NewSaveDel
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Teach)),
                IsShowBtnNew = teachUserRight == UserRight.Writable || teachUserRight == UserRight.FullControl,
                IsShowBtnSave = teachUserRight == UserRight.Writable || teachUserRight == UserRight.FullControl,
                IsShowBtnDelete = teachUserRight == UserRight.FullControl,
                UserRight = teachUserRight
            };

            #endregion Teach

            #region Tech

            var techUserRight = GetControlRight(nameof(ControlRightStaffModel.Tech));

            staff.Tech = new NewSaveDel
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Tech)),
                IsShowBtnNew = techUserRight == UserRight.Writable || techUserRight == UserRight.FullControl,
                IsShowBtnSave = techUserRight == UserRight.Writable || techUserRight == UserRight.FullControl,
                IsShowBtnDelete = techUserRight == UserRight.FullControl,
                UserRight = techUserRight
            };

            #endregion Tech

            #region Relation

            var relationUserRight = GetControlRight(nameof(ControlRightStaffModel.Relation));

            staff.Relation = new NewSaveDel
            {
                IsShow = pns.Any(s => s.ControlRightPanel.Code == nameof(ControlRightStaffModel.Relation)),
                IsShowBtnNew = relationUserRight == UserRight.Writable || relationUserRight == UserRight.FullControl,
                IsShowBtnSave = relationUserRight == UserRight.Writable || relationUserRight == UserRight.FullControl,
                IsShowBtnDelete = relationUserRight == UserRight.FullControl,
                UserRight = relationUserRight
            };

            #endregion Relation

            result.Data = staff;
            return result;
        }

        #endregion 用户组权限管理

        #region 薪资模块

        /// <summary>
        /// 查询岗位树
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询岗位树")]
        public BizResult<List<StationQuery>> QueryStationTree()
        {
            var result = new BizResult<List<StationQuery>>();
            var staAllEntities = this.Repo.GetEntities<Station>().OrderBy(c => c.Number).Maps<StationQuery>();
            var staEntities = staAllEntities.Where(x => !x.ParentId.HasValue && x.IsCategory.HasValue && x.IsCategory.Value).ToList();

            InitStationTree(staEntities, staAllEntities);
            result.Data = staEntities;

            return result;
        }

        /// <summary>
        /// 查询多级岗位选择器
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询多级岗位选择器")]
        public BizResult<List<StationQuery>> QueryStationSelector([FromQuery] Guid id)
        {
            var result = new BizResult<List<StationQuery>>();
            var staAllEntities = this.Repo.GetEntities<Station>().Maps<StationQuery>();
            var staEntities = staAllEntities.Where(x => x.ParentId == id).ToList();

            InitStationTree(staEntities, staAllEntities);
            result.Data = staEntities;

            return result;
        }

        protected void InitStationTree(List<StationQuery> parentStations, List<StationQuery> allStations)
        {
            foreach (var item in parentStations)
            {
                var childStations = allStations.Where(x => x.ParentId == item.ID).ToList();
                if (childStations.Any())
                {
                    item.Children = childStations;
                    InitStationTree(item.Children.ToList(), allStations);
                }
            }
        }

        /// <summary>
        /// 查询岗位类型选择器
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询岗位类型选择器")]
        public BizResult<List<StationQuery>> QueryStationTypeSelector()
        {
            var result = new BizResult<List<StationQuery>>();
            var stations = this.Repo.GetEntities<Station>(x => x.IsCategory.HasValue && !x.ParentId.HasValue).Maps<StationQuery>();

            result.Data = stations;

            return result;
        }


        /// <summary>
        /// 查询薪级选择器
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询薪级选择器")]
        public BizResult<List<SalaryScaleSelector>> QuerySalaryScaleSelector([FromQuery] Guid id)
        {
            var result = new BizResult<List<SalaryScaleSelector>>();
            var scaleEntities = this.Repo.GetEntities<SalaryScale>(x => x.StationId == id).Select(x => new SalaryScaleSelector() { ID = x.ID, Scale = x.Scale, Wage = x.Wage }).OrderBy(x => x.Scale).ToList();

            result.Data = scaleEntities;

            return result;
        }

        /// <summary>
        /// 查询电话费选择器
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询电话费选择器")]
        public BizResult<List<TelephoneFeeSelector>> QueryTelephoneFeeSelector([FromQuery] Guid id)
        {
            var result = new BizResult<List<TelephoneFeeSelector>>();
            var telEntities = this.Repo.GetEntities<TelephoneFee>().Select(x => new TelephoneFeeSelector() { ID = x.ID, Name = x.Name, Allowance = x.Allowance }).OrderBy(x => x.Name).ToList();

            result.Data = telEntities;

            return result;
        }

        /// <summary>
        /// 查询公车补贴选择器
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询公车补贴选择器")]
        public BizResult<List<CarSubsidySelector>> QueryCarSubsidySelector([FromQuery] Guid id)
        {
            var result = new BizResult<List<CarSubsidySelector>>();
            var scaleEntities = this.Repo.GetEntities<CarSubsidy>().Select(x => new CarSubsidySelector() { ID = x.ID, Name = x.Name, Allowance = x.Allowance }).OrderBy(x => x.Name).ToList();

            result.Data = scaleEntities;

            return result;
        }

        /// <summary>
        /// 岗位津贴
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询岗位津贴")]
        public BizResult<List<StationAllowanceModel>> QueryStationAllowance()
        {
            var result = new BizResult<List<StationAllowanceModel>>();

            var entitys = this.SysCache.StationAllowances;

            if (entitys == null)
            {
                result.Error("岗位津贴数据不存在");
            }
            else
            {
                var model = entitys.Maps<StationAllowanceModel>();

                result.Data = model;
            }

            return result;
        }

        #endregion
    }
}