﻿<#@ import namespace="System.Collections.Generic" #>
<#+
	const string connectionString = "Server=xl7.corp.shinsoft.net,9210;Database=Renji_JHR_Dev; uid=*****;pwd=*****;";

	const string database = "Renji_JHR_Dev";

	const string entityNamespace = "Renji.JHR.Entities";
	const string entityPrefix = "";

	const string dalNamespace = "Renji.JHR.Dal";
	const string dbContextName = "BizDbContext";

	const string companyIdTypeName = "Guid";
	const string companyTypeName = "Company";

	string[] entitySchemas = new string[]
	{
		"dbo"
	};

	string[] entityTables = new string[]
	{
	};

	string[] entityViews = new string[]
	{
	};

	string[] entityViewPrefixes = new string[]
	{
	};

	string[] enumNamespaces = new string[]
	{
		"Shinsoft.Core.Mail"
	};

#>