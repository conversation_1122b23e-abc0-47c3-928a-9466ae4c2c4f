﻿using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Xml.Serialization;

namespace Renji.JHR.Entities
{
    public partial class Position : IOrder
    {
        #region IOrder

        IComparable IOrder.Order => -this.Ordinal;

        #endregion IOrder

        /// <summary>
        /// 删除时是否同时删除岗位职位对应关系和员工岗位对应关系
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public bool ConfirmToDelete { get; set; }
    }
}