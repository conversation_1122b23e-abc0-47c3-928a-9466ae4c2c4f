﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Renji.JHR.Common
{
    public static class Permissions
    {
        public static class Management
        {
            public const string Query_Log = "Query_Log";
        }

        /// <summary>
        /// 系统管理
        /// </summary>
        public static class SysManage
        {
            /// <summary>
            /// 系统管理
            /// </summary>
            public const string sysManage = "sysManage";

            /// <summary>
            /// 字典信息定义
            /// </summary>
            public const string dicInfo = "dicInfo";

            /// <summary>
            /// 字典类型定义
            /// </summary>
            public const string DefineDictionType = "DefineDictionType";

            /// <summary>
            /// 用户组权限管理
            /// </summary>
            public const string RolePermission = "RolePermission";

            /// <summary>
            /// 用户管理
            /// </summary>
            public const string UserManage = "UserManage";

            /// <summary>
            /// 用户组管理
            /// </summary>
            public const string RoleManage = "RoleManage";
        }

        /// <summary>
        /// 考勤管理
        /// </summary>
        public static class AttendanceManage
        {
            /// <summary>
            /// 一值班二值班费
            /// </summary>
            public const string DutyFee = "DutyFee";

            /// <summary>
            /// 考勤数据
            /// </summary>
            public const string AttendanceData = "AttendanceData";

            /// <summary>
            /// 考勤数据暂存
            /// </summary>
            public const string AttendanceStaging = "AttendanceStaging";

            /// <summary>
            /// 考勤数据申报
            /// </summary>
            public const string AttendanceApply = "AttendanceApply";

            /// <summary>
            /// 考勤数据查询1
            /// </summary>
            public const string AttendanceSearch1 = "AttendanceSearch1";

            /// <summary>
            /// 考勤数据查询2
            /// </summary>
            public const string AttendanceSearch2 = "AttendanceSearch2";

            /// <summary>
            /// 考勤数据查询3
            /// </summary>
            public const string AttendanceSearch3 = "AttendanceSearch3";

            /// <summary>
            /// 考勤数据人事
            /// </summary>
            public const string AttendanceHR = "AttendanceHR";

            /// <summary>
            /// 考勤数据人事审批
            /// </summary>
            public const string AttendanceHRApproval = "AttendanceHRApproval";

            /// <summary>
            /// 考勤数据修改
            /// </summary>
            public const string AttendanceUpdate = "AttendanceUpdate";

            /// <summary>
            /// 防保科考勤申报
            /// </summary>
            public const string AttDayOffRecordProphylactic = "AttDayOffRecordProphylactic";

            /// <summary>
            /// 防保科考勤查询
            /// </summary>
            public const string ProphylacticQuery = "ProphylacticQuery";

            /// <summary>
            /// 防保科考勤变动
            /// </summary>
            public const string ProphylacticChange = "ProphylacticChange";

            /// <summary>
            /// 中夜班费
            /// </summary>
            public const string MiddleNightShiftFee = "MiddleNightShiftFee";

            /// <summary>
            /// 中夜班费人事
            /// </summary>
            public const string MonthShiftRecordHR = "MonthShiftRecordHR";

            /// <summary>
            /// 中夜班费暂存
            /// </summary>
            public const string MonthShiftRecordStaging = "MonthShiftRecordStaging";

            /// <summary>
            /// 中夜班费申请
            /// </summary>
            public const string MonthShiftRecordApply = "MonthShiftRecordApply";

            /// <summary>
            /// 中夜班费修改
            /// </summary>
            public const string MonthShiftRecordUpdate = "MonthShiftRecordUpdate";

            /// <summary>
            /// 中夜班费查询
            /// </summary>
            public const string MonthShiftRecordSearch = "MonthShiftRecordSearch";


            /// <summary>
            /// 行政值班费
            /// </summary>
            public const string WatchFee = "WatchFee";

            /// <summary>
            /// 节日加班费
            /// </summary>
            public const string HolidayOvertimeFee = "HolidayOvertimeFee";

            /// <summary>
            /// 节日加班费暂存
            /// </summary>
            public const string holidayOvertimeFeeStaging = "holidayOvertimeFeeStaging";

            /// <summary>
            /// 节日加班费申请
            /// </summary>
            public const string holidayOvertimeFeeApply = "holidayOvertimeFeeApply";

            /// <summary>
            /// 节日加班费人事
            /// </summary>
            public const string holidayOvertimeFeeHr = "holidayOvertimeFeeHr";

            /// <summary>
            /// 节日加班费查询
            /// </summary>
            public const string holidayOvertimeFeeSearch = "holidayOvertimeFeeSearch";

            /// <summary>
            /// 节日加班费修改
            /// </summary>
            public const string holidayOvertimeFeeUpdate = "holidayOvertimeFeeUpdate";
        }

        /// <summary>
        /// 消息通知
        /// </summary>
        public static class MsgComList
        {
            /// <summary>
            /// 消息通知
            /// </summary>
            public const string MsgComlist = "MsgComlist";

            /// <summary>
            /// 公告发布
            /// </summary>
            public const string Msglist = "Msglist";

            /// <summary>
            /// 消息发布
            /// </summary>
            public const string MsgPerlist = "MsgPerlist";

        }

        /// <summary>
        /// 组织机构管理
        /// </summary>
        public static class OrganizationManage
        {
            /// <summary>
            /// 组织管理
            /// </summary>
            public const string Organization = "Organization";

            /// <summary>
            /// 岗位级别管理
            /// </summary>
            public const string PositionLevelManage = "PositionLevelManage";

            /// <summary>
            /// 岗位名称管理
            /// </summary>
            public const string PositionManage = "PositionManage";

            /// <summary>
            /// 薪级工资管理
            /// </summary>
            public const string SalaryScaleManage = "SalaryScaleManage";

            /// <summary>
            /// 岗位津贴
            /// </summary>
            public const string JobSubsidy = "JobSubsidy";

            /// <summary>
            /// 工龄津贴
            /// </summary>
            public const string Seniority = "Seniority";

            /// <summary>
            /// 电话费
            /// </summary>
            public const string TelephoneFee = "TelephoneFee";

            /// <summary>
            /// 公车补贴
            /// </summary>
            public const string CarSubsidy = "CarSubsidy";

        }

        /// <summary>
        /// 人事资源管理
        /// </summary>
        public static class HRManage
        {
            /// <summary>
            /// 人员信息
            /// </summary>
            public const string StaffInfo = "StaffInfo";

            /// <summary>
            /// 人员部门调整
            /// </summary>
            public const string EmpOrgModifier = "EmpOrgModifier";

            /// <summary>
            /// 后勤人员管理
            /// </summary>
            public const string LogisticsStaffManage = "LogisticsStaffManage";

            /// <summary>
            /// 护理人员管理
            /// </summary>
            public const string EmpOrgModifierNurseManage = "EmpOrgModifierNurseManage";

            /// <summary>
            /// 财务查看
            /// </summary>
            public const string FinanceSearch = "FinanceSearch";

            /// <summary>
            /// 高级查询
            /// </summary>
            public const string EmpRoster = "EmpRoster";

            /// <summary>
            /// 其他人员库管理
            /// </summary>
            public const string OtherEmpManage = "OtherEmpManage";

            /// <summary>
            /// 合同到期管理
            /// </summary>
            public const string ContractManage = "ContractManage";

        }
		
        /// <summary>
        /// 薪资管理
        /// </summary>
        public static class SalaryManage
		{
            /// <summary>
            /// 员工薪资
            /// </summary>
            public const string employeeSalary = "employeeSalary";

            /// <summary>
            /// 十三薪
            /// </summary>
            public const string ThirteenthSalary = "ThirteenthSalary";
		}
    }
}
