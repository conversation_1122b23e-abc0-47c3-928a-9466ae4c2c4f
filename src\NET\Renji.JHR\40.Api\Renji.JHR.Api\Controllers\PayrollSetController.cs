﻿using Microsoft.AspNetCore.Mvc;
using Renji.JHR.Api.Models;
using Renji.JHR.Bll;
using Renji.JHR.Common.Utility;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.Mvc;
using Shinsoft.Core.NLog;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using System.IO;
using System.Data;
using System.Linq.Expressions;
using Renji.JHR.Common;
using Renji.JHR.Api.Utils;

namespace Renji.JHR.Api.Controllers
{
    /// <summary>
    /// 工资管理
    /// </summary>
    public class PayrollSetController : BaseApiController<PayrollSetBll>
    {
        #region 行政值班费

        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询行政值班费")]
        [Permission(Permissions.AttendanceManage.WatchFee)]
        public QueryResult<EmployeePayRollSalaryAddModel> SearchEmployeePayRollSalaryAddModel([FromQuery] EmployeePayRollSalaryAddFilter filter)
        {
            var result = new QueryResult<EmployeePayRollSalaryAddModel>();

            int fromRow = (filter.PageIndex - 1) * filter.PageSize + 1;
            int toRow = fromRow + filter.PageSize - 1;

            var list = this.Repo.QueryEmployeePayRollSalaryAdd(
                filter.RecordMonth, filter.GtZeroValue, filter.DeptId, filter.EmpCode, filter.EmpName,
                fromRow, toRow, out int recoredCount
                );

            var models = list.Maps<EmployeePayRollSalaryAddModel>();

            result.SetResult(models, recoredCount, filter);

            return result;
        }

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "维护行政值班费")]
        [Permission(Permissions.AttendanceManage.WatchFee)]
        public BizResult UpdateEmployeePayRollSalaryAdd([FromBody] EmployeePayRollSalaryAddModel model)
        {
            var result = new BizResult();
            var entity = model.Map<EmployeePayRollSalaryAdd>();
            result.Data = this.Repo.UpdateEmployeePayRollSalaryAdd(entity);
            return result;
        }

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "导出行政值班费")]
        [Permission(Permissions.AttendanceManage.WatchFee)]
        public IActionResult GetEmployeePayRollSalaryAddExcel([FromQuery] EmployeePayRollSalaryAddFilter filter)
        {
            var list = this.Repo.QueryEmployeePayRollSalaryAdd(
                filter.RecordMonth, filter.GtZeroValue, filter.DeptId, filter.EmpCode, filter.EmpName,
                1, int.MaxValue, out int recoredCount
                );

            var models = list.Maps<AttPayRollSalaryAdd>();

            var year = filter.RecordMonth.GetValueOrDefault().Split('-')[0];
            var month = filter.RecordMonth.GetValueOrDefault().Split('-')[1];

            DataTable? dt = Utility.ToDataTable(models);//list转datatable
            string exportFileName = $"{AppDomain.CurrentDomain.BaseDirectory}{string.Format(Common.Consts.Exports.FileExcel, $"{year}年{month}月" + Common.Consts.Exports.FileWatchFee)}";//生成后存放的地址 和文件名

            var _NpoiExcelUtility = new NpoiExcelUtility();// (exportFileName, templateFileName);
            if (dt != null)
            {
                _NpoiExcelUtility.CreatExcelSheet(Common.Consts.Exports.FileWatchFee, dt);
            }
            var bytes = _NpoiExcelUtility.SaveExcelBytes();
            return File(bytes, Common.Consts.Exports.ContentType, Path.GetFileName(exportFileName));
        }

        #endregion 行政值班费
    }
}