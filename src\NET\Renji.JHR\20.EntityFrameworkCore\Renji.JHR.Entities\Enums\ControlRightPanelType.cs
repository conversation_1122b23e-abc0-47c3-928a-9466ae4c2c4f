﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using Shinsoft.Core;

namespace Renji.JHR.Entities
{
    public enum ControlRightPanelType
    {
        /// <summary>
        /// 无权限
        /// </summary>
        [Description("分页导航")]
        //[EnumGroup("", Ordinal = 10)]
        PaginationNavigation = 0,

        /// <summary>
        /// 页面
        /// </summary>
        [Description("页面")]
        Page = 1,

        /// <summary>
        /// 按钮
        /// </summary>
        [Description("按钮")]
        Button = 2,

        /// <summary>
        /// 表单
        /// </summary>
        [Description("表单")]
        Form = 3

    }
}
