﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;
using System.Text;
using System.Text.Json.Serialization;
using System.Xml.Serialization;
using Shinsoft.Core;

namespace Renji.JHR.Entities
{
    public partial class EmployeeHR
    {
        /// <summary>
        /// 人事附件ID
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public Guid? AttachmentId { get; set; }
        /// <summary>
        /// 人事附件名称
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public string AttachmentName { get; set; }
    }
}
