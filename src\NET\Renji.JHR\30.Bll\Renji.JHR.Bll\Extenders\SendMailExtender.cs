﻿using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.Configuration;
using Shinsoft.Core.Json;
using Shinsoft.Core.Mail;
using Shinsoft.Core.NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Renji.JHR.Bll
{
    public static class SendMailExtender
    {
        public static bool SendMail<T>(this IMailServer server, T mail, List<MailAttachment> attachments, int skipFrames = 1)
            where T : class, IMail
        {
            bool success = false;

            if (BaseConfig.Mail.Send)
            {
                try
                {
                    switch (server.EnumType)
                    {
                        case MailServerType.SmtpServer:
                            using (var client = server.GetSmtpClient())
                            {
                                success = client.SendMail(mail, server, attachments, skipFrames: skipFrames + 1);

                                client.Disconnect(true);
                            }
                            break;

                        case MailServerType.ExchangeServer:
                        //success = mail.SendByExchange(server);
                        //break;

                        default:
                            mail.SendMessage = "邮件发送服务器类型不确定";
                            break;
                    }
                }
                catch (Exception ex)
                {
                    mail.SendMessage = ex.Message;

                    Dictionary<string, object> dict = new()
                    {
                        { "MailServerCode", server.Code },
                        { "MailId", mail.ID },
                        { "MailSubject", mail.Subject }
                    };

                    NLogHelper.Error(ex, "发送邮件", ex.Message, remark: dict.ToJson(), skipFrames: skipFrames + 1);
                }
            }

            return success;
        }

        public static void SendMails<TMail>(this IMailServer server, IEnumerable<TMail> mails, List<MailAttachment> attachments
            , Action<TMail>? beforeSend = null, Action<TMail, bool>? afterSend = null, int skipFrames = 1)
            where TMail : class, IMail
        {
            if (BaseConfig.Mail.Send)
            {
                switch (server.EnumType)
                {
                    case MailServerType.SmtpServer:
                        using (var client = server.GetSmtpClient())
                        {
                            foreach (var mail in mails)
                            {
                                beforeSend?.Invoke(mail);

                                var success = client.SendMail(mail, server, attachments, skipFrames: skipFrames + 1);

                                afterSend?.Invoke(mail, success);

                                if (server.SendInterval > 0)
                                {
                                    Thread.Sleep(server.SendInterval);
                                }
                            }

                            client.Disconnect(true);
                        }

                        break;

                    case MailServerType.ExchangeServer:
                        break;

                    default:
                        return;
                }
            }
        }

        public static bool SendMailByHsbtSmtp(this MailServer server, Mail mail, List<MailAttachment> attachments, int skipFrames = 1)
        {
            bool success = false;

            string message = string.Empty;

            mail.SendMessage = string.Empty;
            var smtpServer = server.SmtpServer;

            int maxRetry = server.MaxRetry <= 0
                ? 1
                : server.MaxRetry;

            if (smtpServer == null)
            {
                mail.SendMessage = "SMTP服务器设置不存在";
            }
            else
            {
                var smtpClient = new System.Net.Mail.SmtpClient
                {
                    DeliveryMethod = System.Net.Mail.SmtpDeliveryMethod.Network,
                    Host = smtpServer.Host,
                    Port = smtpServer.Port
                };

                //if (smtpClient.EnableSsl)
                //{
                smtpClient.EnableSsl = true;
                //}
                //else
                //{
                //    smtpClient.EnableSsl = false;
                //}

                if (!smtpServer.Username.IsEmpty() && !smtpServer.Password.IsEmpty())
                {
                    smtpClient.UseDefaultCredentials = false;

                    var password = smtpServer.Password;

                    smtpClient.Credentials = new System.Net.NetworkCredential(smtpServer.Username, password);
                }

                var mailBody = new System.Net.Mail.MailMessage
                {
                    //IsBodyHtml = mail.IsHtmlBody,
                    SubjectEncoding = Encoding.UTF8,
                    BodyEncoding = Encoding.UTF8
                };

                if (server.From.IsEmpty())
                {
                    mailBody.From = new System.Net.Mail.MailAddress(mail.From);
                }
                else
                {
                    mailBody.From = new System.Net.Mail.MailAddress(server.From);
                    mail.RealFrom = server.From;
                }

                if (BaseConfig.Mail.Debug && !string.IsNullOrWhiteSpace(server.DebugTo))
                {
                    string[] array = server.DebugTo.Split(new char[] { ',', ';' }, StringSplitOptions.RemoveEmptyEntries);
                    foreach (string text in array)
                    {
                        if (!string.IsNullOrWhiteSpace(text))
                        {
                            mailBody.To.Add(text);
                        }
                    }

                    mail.DebugTo = server.DebugTo;
                }
                else
                {
                    if (!string.IsNullOrWhiteSpace(mail.To))
                    {
                        string[] array = mail.To.Split(new char[] { ',', ';' }, StringSplitOptions.RemoveEmptyEntries);
                        foreach (string text in array)
                        {
                            if (!string.IsNullOrWhiteSpace(text))
                            {
                                mailBody.To.Add(text);
                            }
                        }
                    }

                    if (!string.IsNullOrWhiteSpace(mail.Cc))
                    {
                        string[] array = mail.Cc.Split(new char[] { ',', ';' }, StringSplitOptions.RemoveEmptyEntries);
                        foreach (string text in array)
                        {
                            if (!string.IsNullOrWhiteSpace(text))
                            {
                                mailBody.CC.Add(text);
                            }
                        }
                    }

                    if (!string.IsNullOrWhiteSpace(mail.Bcc))
                    {
                        string[] array = mail.Bcc.Split(';', ',');
                        foreach (string text in array)
                        {
                            if (!string.IsNullOrWhiteSpace(text))
                            {
                                mailBody.Bcc.Add(text);
                            }
                        }
                    }
                }

                mailBody.Subject = mail.Subject;

                mailBody.Body = mail.Content;

                if (attachments.Count > 0)
                {
                    foreach (var mailAttachment in attachments)
                    {
                        var fileStream = mailAttachment.FileStream;

                        if (fileStream == null)
                        {
                            continue;
                        }

                        mailBody.Attachments.Add(new System.Net.Mail.Attachment(fileStream, mailAttachment.FileName));
                    }
                }

                ServicePointManager.ServerCertificateValidationCallback = delegate (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors)
                {
                    return true;
                };

                for (var retry = 0; retry <= maxRetry; retry++)
                {
                    try
                    {
                        smtpClient.Send(mailBody);
                        success = true;
                        break;
                    }
                    catch (Exception ex)
                    {
                        success = false;
                        message = ex.Message;

                        if (retry >= maxRetry)
                        {
                            Dictionary<string, object> dict = new()
                            {
                                { "MailServerCode", smtpServer.MailServer.Code },
                                { "MailId", mail.ID },
                                { "MailSubject", mail.Subject }
                            };

                            NLogHelper.Error(ex, "发送邮件", message, remark: dict.ToJson(), skipFrames: skipFrames + 1);
                        }

                        if (server.RetryInterval > 0)
                        {
                            Thread.Sleep(server.RetryInterval);
                        }
                    }
                }

                mail.SendCount++;
                mail.SendTime = SysDateTime.Now;
                mail.SendMessage = message;

                if (success)
                {
                    mail.EnumStatus = MailStatus.Success;
                }
                else
                {
                    if (mail.SendCount > server.MaxResend || mail.EnumStatus == MailStatus.Immediately)
                    {
                        mail.EnumStatus = MailStatus.Failed;
                    }
                    else
                    {
                        mail.EnumStatus = MailStatus.Resend;
                    }
                }
            }
            return success;
        }
    }
}