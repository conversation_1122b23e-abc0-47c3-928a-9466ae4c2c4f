﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Renji.JHR.Api.CommonUtils
{
    public static class HttpContextExtension
    {
        public static string GetClientIP(this HttpContext context)
        {
            var ip = context.Request.Headers["Cdn-Src-Ip"].FirstOrDefault();
            if (!string.IsNullOrEmpty(ip))
                return IpReplace(ip);

            ip = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(ip))
                return IpReplace(ip);

            ip = context.Connection.RemoteIpAddress.AsString();

            return IpReplace(ip);
        }

        private static string IpReplace(string inip)
        {
            //::ffff:
            //::ffff:************* 这种IP处理
            if (inip.Contains("::ffff:"))
            {
                inip = inip.Replace("::ffff:", "");
            }
            return inip;
        }
    }
}
