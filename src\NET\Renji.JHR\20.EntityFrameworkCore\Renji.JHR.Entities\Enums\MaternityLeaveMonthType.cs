﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Renji.JHR.Entities
{
    public enum MaternityLeaveMonthType
    {
        None = 0,

        /// <summary>
        /// 第一个月
        /// </summary>
        [Description("A")]
        A = 1,

        /// <summary>
        /// 第二个月
        /// </summary>
        [Description("B")]
        B = 2,

        /// <summary>
        /// 第三个月
        /// </summary>
        [Description("C")]
        C = 3,

        /// <summary>
        /// 第四个月
        /// </summary>
        [Description("D")]
        D = 4,

        /// <summary>
        /// 第五个月
        /// </summary>
        [Description("E")]
        E = 5,

        /// <summary>
        /// 第六个月
        /// </summary>
        [Description("F")]
        F = 6
    }
}
