﻿using System.ComponentModel;

namespace Renji.JHR.Entities
{
    public enum IdentityType
    {
        None = 0,

        /// <summary>
        /// 身份证
        /// </summary>
        [Description("身份证")]
        IdentityCard = 1,

        /// <summary>
        /// 军人证
        /// </summary>
        [Description("军人证")]
        ServicemanCard = 2,

        /// <summary>
        /// 护照
        /// </summary>
        [Description("护照")]
        Passport = 3,

        /// <summary>
        /// 学生证
        /// </summary>
        [Description("学生证")]
        StudentIDCard = 4,

        /// <summary>
        /// 回乡证
        /// </summary>
        [Description("回乡证")]
        ReturnPermit = 5,

        /// <summary>
        /// 驾驶证
        /// </summary>
        [Description("驾驶证")]
        DriverLicense = 6,

        /// <summary>
        /// 台胞证
        /// </summary>
        [Description("台胞证")]
        TaiwaneseSyndrome = 7,

        /// <summary>
        /// 其他
        /// </summary>
        [Description("其他")]
        Other = 9
    }
}
