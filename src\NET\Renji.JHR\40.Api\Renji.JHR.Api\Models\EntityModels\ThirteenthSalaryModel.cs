using Renji.JHR.Entities;
using Shinsoft.Core.AutoMapper;
using System;
using System.ComponentModel.DataAnnotations;

namespace Renji.JHR.Api.Models
{
    /// <summary>
    /// 十三薪模型
    /// </summary>
    public partial class ThirteenthSalaryModel
    {
        /// <summary>
        /// 员工信息
        /// </summary>
        [MapFromProperty(typeof(ThirteenthSalary), ThirteenthSalary.Foreigns.Employee)]
        public EmployeeModel? Employee { get; set; }

        /// <summary>
        /// 薪资信息
        /// </summary>
        [MapFromProperty(typeof(ThirteenthSalary), ThirteenthSalary.Foreigns.Salary)]
        public SalaryModel? Salary { get; set; }

        public decimal? BaseMoney { get; set; }
    }
}
