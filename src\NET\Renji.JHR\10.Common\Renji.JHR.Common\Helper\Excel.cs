﻿using NPOI.HSSF.UserModel;
using NPOI.HSSF.Util;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.IO;
using System.Linq;

namespace Renji.JHR.Common
{
    public static class Excel
    {
        public static byte[] WriteToExcelBytes(DataTable table, string sheetName, int titleHasBackColorFrom = -1, int titleHasBackColorTo = -1)
        {
            IWorkbook workbook = new XSSFWorkbook();
            ISheet sheet = workbook.CreateSheet(sheetName);

            IDataFormat format = workbook.CreateDataFormat();

            //表头样式
            ICellStyle hstyle = workbook.CreateCellStyle();
            hstyle.Alignment = HorizontalAlignment.Center;//居中对齐
            hstyle.VerticalAlignment = VerticalAlignment.Center;
            //表头字体设置
            IFont hfont = workbook.CreateFont();
            hfont.FontHeightInPoints = 12;//字号
            hfont.Boldweight = 600;//加粗
            hstyle.SetFont(hfont);

            ICellStyle hstyle2 = workbook.CreateCellStyle();
            hstyle2.Alignment = HorizontalAlignment.Center;//居中对齐
            hstyle2.VerticalAlignment = VerticalAlignment.Center;
            hstyle2.SetFont(hfont);
            hstyle2.BorderTop = BorderStyle.Thin;
            hstyle2.BorderBottom = BorderStyle.Thin;
            hstyle2.BorderLeft = BorderStyle.Thin;
            hstyle2.BorderRight = BorderStyle.Thin;
            hstyle2.FillBackgroundColor = HSSFColor.PaleBlue.Index;
            hstyle2.FillForegroundColor = HSSFColor.PaleBlue.Index;
            hstyle2.FillPattern = FillPattern.SolidForeground;


            IRow headerRow = sheet.CreateRow(0);
            // excel标题行
            for (int j = 0; j < table.Columns.Count; j++)
            {
                ICell hCell = headerRow.CreateCell(table.Columns[j].Ordinal);
                hCell.SetCellValue(table.Columns[j].Caption);

                if (titleHasBackColorFrom >=0 && titleHasBackColorTo >= 0 && titleHasBackColorFrom <= j && titleHasBackColorTo >= j)
                    hCell.CellStyle = hstyle2;
                else
                    hCell.CellStyle = hstyle;
                // 自适应列宽
                sheet.AutoSizeColumn(j);

            }

            int rowIndex = 1;
            foreach (DataRow row in table.Rows)
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                foreach (DataColumn column in table.Columns)
                {
                    ICell dCell = dataRow.CreateCell(column.Ordinal);

                    SetSheetColumnDefaultStyle(workbook, format, row, dCell, column);
                }
                rowIndex++;
            }
            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                byte[] buff = ms.ToArray();
                if (workbook != null)
                {
                    workbook.Close();
                }

                return buff;
            }
        }

        //public static byte[] WriteToExcelBytes(DataTable table, string sheetName)
        //{
        //    IWorkbook workbook = new XSSFWorkbook();
        //    ISheet sheet = workbook.CreateSheet(sheetName);

        //    IDataFormat format = workbook.CreateDataFormat();

        //    //表头样式
        //    ICellStyle hstyle = workbook.CreateCellStyle();
        //    hstyle.Alignment = HorizontalAlignment.Center;//居中对齐
        //    hstyle.VerticalAlignment = VerticalAlignment.Center;
        //    //表头字体设置
        //    IFont hfont = workbook.CreateFont();
        //    hfont.FontHeightInPoints = 12;//字号
        //    hfont.Boldweight = 600;//加粗
        //    hstyle.SetFont(hfont);
        //    hstyle.BorderTop = BorderStyle.Thin;
        //    hstyle.BorderBottom = BorderStyle.Thin;
        //    hstyle.BorderLeft = BorderStyle.Thin;
        //    hstyle.BorderRight = BorderStyle.Thin;
        //    hstyle.FillBackgroundColor = HSSFColor.PaleBlue.Index;
        //    hstyle.FillForegroundColor = HSSFColor.PaleBlue.Index;
        //    hstyle.FillPattern = FillPattern.SolidForeground;


        //    IRow headerRow = sheet.CreateRow(0);
        //    // excel标题行
        //    for (int j = 0; j < table.Columns.Count; j++)
        //    {
        //        ICell hCell = headerRow.CreateCell(table.Columns[j].Ordinal);
        //        hCell.SetCellValue(table.Columns[j].Caption);
        //        hCell.CellStyle = hstyle;
        //        // 自适应列宽
        //        sheet.AutoSizeColumn(j);
                
        //    }

        //    int rowIndex = 1;
        //    foreach (DataRow row in table.Rows)
        //    {
        //        IRow dataRow = sheet.CreateRow(rowIndex);
        //        foreach (DataColumn column in table.Columns)
        //        {
        //            ICell dCell = dataRow.CreateCell(column.Ordinal);
                
        //            SetSheetColumnDefaultStyle(workbook, format, row, dCell, column);
        //        }
        //        rowIndex++;
        //    }
        //    using (MemoryStream ms = new MemoryStream())
        //    {
        //        workbook.Write(ms);
        //        byte[] buff = ms.ToArray();
        //        if (workbook != null)
        //        {
        //            workbook.Close();
        //        }

        //        return buff;
        //    }
        //}

        public static byte[] WriteToExcelBytes(List<DataTable> tables, int columnWidth = 15)
        {
            IWorkbook workbook = new XSSFWorkbook();
            IDataFormat format = workbook.CreateDataFormat();

            //表头样式
            ICellStyle hstyle = workbook.CreateCellStyle();
            hstyle.Alignment = HorizontalAlignment.Center;//居中对齐
            hstyle.VerticalAlignment = VerticalAlignment.Center;
            //背景
            hstyle.BorderTop = BorderStyle.Thin;
            hstyle.BorderBottom = BorderStyle.Thin;
            hstyle.BorderLeft = BorderStyle.Thin;
            hstyle.BorderRight = BorderStyle.Thin;
            hstyle.FillBackgroundColor = HSSFColor.PaleBlue.Index;
            hstyle.FillForegroundColor = HSSFColor.PaleBlue.Index;
            hstyle.FillPattern = FillPattern.SolidForeground;
            //边框
            hstyle.BottomBorderColor = HSSFColor.Black.Index;
            hstyle.TopBorderColor = HSSFColor.Black.Index;
            hstyle.LeftBorderColor = HSSFColor.Black.Index;
            hstyle.RightBorderColor = HSSFColor.Black.Index;
            hstyle.WrapText = true;

            //表头字体设置
            IFont hfont = workbook.CreateFont();
            hfont.FontHeightInPoints = 12;//字号
            hfont.Boldweight = 600;//加粗
            hstyle.SetFont(hfont);

            foreach (var table in tables)
            {
                ISheet sheet = workbook.CreateSheet(table.TableName);

                IRow headerRow = sheet.CreateRow(0);
                // excel标题行
                for (int j = 0; j < table.Columns.Count; j++)
                {
                    //sheet.SetColumnWidth(j, 200);
                    sheet.AutoSizeColumn(j);
                    sheet.SetColumnWidth(j, Math.Max(sheet.GetColumnWidth(j) + 150, 256 * columnWidth));
                    ICell hCell = headerRow.CreateCell(table.Columns[j].Ordinal);
                    hCell.SetCellValue(table.Columns[j].Caption);
                    hCell.CellStyle = hstyle;
                }

                int rowIndex = 1;
                foreach (DataRow row in table.Rows)
                {
                    IRow dataRow = sheet.CreateRow(rowIndex);
                    int columnIndex = 0;
                    foreach (DataColumn column in table.Columns)
                    {
                        ICell dCell = dataRow.CreateCell(column.Ordinal);
                        sheet.AutoSizeColumn(columnIndex);
                        sheet.SetColumnWidth(columnIndex, Math.Max(sheet.GetColumnWidth(columnIndex) + 150, 256 * 10));
                        SetSheetColumnDefaultStyle(workbook, format, row, dCell, column);
                        columnIndex++;
                    }

                    rowIndex++;
                }
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                byte[] buff = ms.ToArray();
                if (workbook != null)
                {
                    workbook.Close();
                }

                return buff;
            }
        }

        public static byte[] SalaryWriteToExcelBytes(List<DataTable> tables, int columnWidth = 15)
        {
            IWorkbook workbook = new XSSFWorkbook();
            IDataFormat format = workbook.CreateDataFormat();

            //表头样式
            ICellStyle hstyle = workbook.CreateCellStyle();
            hstyle.Alignment = HorizontalAlignment.Center;//居中对齐
            hstyle.VerticalAlignment = VerticalAlignment.Center;
            //背景
            hstyle.BorderTop = BorderStyle.Thin;
            hstyle.BorderBottom = BorderStyle.Thin;
            hstyle.BorderLeft = BorderStyle.Thin;
            hstyle.BorderRight = BorderStyle.Thin;
            hstyle.FillBackgroundColor = HSSFColor.PaleBlue.Index;
            hstyle.FillForegroundColor = HSSFColor.PaleBlue.Index;
            hstyle.FillPattern = FillPattern.SolidForeground;
            //边框
            hstyle.BottomBorderColor = HSSFColor.Black.Index;
            hstyle.TopBorderColor = HSSFColor.Black.Index;
            hstyle.LeftBorderColor = HSSFColor.Black.Index;
            hstyle.RightBorderColor = HSSFColor.Black.Index;
            hstyle.WrapText = true;

            //表头字体设置
            IFont hfont = workbook.CreateFont();
            hfont.FontHeightInPoints = 12;//字号
            hfont.Boldweight = 600;//加粗
            hstyle.SetFont(hfont);

            int i = 0;
            foreach (var table in tables)
            {
                // 处理工作表名称
                string sheetName = table.TableName;
                if (string.IsNullOrEmpty(sheetName))
                {
                    // 如果名称为空，生成一个默认名称
                    sheetName = "Sheet_" + tables.IndexOf(table);
                }
                else if (sheetName.Length > 31)
                {
                    // 如果名称超过 31 个字符，截取前 31 个字符
                    sheetName = sheetName.Substring(0, 31);
                }

                ISheet sheet = workbook.CreateSheet(sheetName);

                IRow headerRow = sheet.CreateRow(0);
                // excel标题行
                for (int j = 0; j < table.Columns.Count; j++)
                {
                    sheet.AutoSizeColumn(j);
                    sheet.SetColumnWidth(j, Math.Max(sheet.GetColumnWidth(j) + 150, 256 * columnWidth));
                    if (i != 0)
                    {
                        ICell hCell = headerRow.CreateCell(table.Columns[j].Ordinal);
                        hCell.SetCellValue(table.Columns[j].Caption);
                        hCell.CellStyle = hstyle;
                    }
                }

                int rowIndex = 1;
                foreach (DataRow row in table.Rows)
                {
                    IRow dataRow = sheet.CreateRow(rowIndex);
                    int columnIndex = 0;
                    foreach (DataColumn column in table.Columns)
                    {
                        ICell dCell = dataRow.CreateCell(column.Ordinal);
                        SetSheetColumnDefaultStyle(workbook, format, row, dCell, column);
                        columnIndex++;
                    }

                    rowIndex++;
                }
                i++;
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                byte[] buff = ms.ToArray();
                if (workbook != null)
                {
                    workbook.Close();
                }

                return buff;
            }
        }

        private static readonly Dictionary<IWorkbook, Dictionary<string, ICellStyle>> workbookStyleCache = new();

        private static void SetSheetColumnDefaultStyle(IWorkbook workbook, IDataFormat format, DataRow row, ICell dCell, DataColumn column)
        {   
            // 获取与当前 Workbook 关联的样式缓存
            if (!workbookStyleCache.TryGetValue(workbook, out var styleCache))
            {
                styleCache = new Dictionary<string, ICellStyle>();
                workbookStyleCache[workbook] = styleCache;
            }

            string key = column.DataType.FullName ?? string.Empty; // 以数据类型为缓存的键

            if (!styleCache.TryGetValue(key, out ICellStyle? cellStyle))
            {
                // 如果缓存中不存在，则创建样式并添加到缓存
                cellStyle = workbook.CreateCellStyle();

                Type type = column.DataType;

                if (type == typeof(short) || type == typeof(int) || type == typeof(long)
                    || type == typeof(short?) || type == typeof(int?) || type == typeof(long?))
                {
                    cellStyle.DataFormat = format.GetFormat("#,##0");
                }
                else if (type == typeof(decimal) || type == typeof(double) || type == typeof(float)
                         || type == typeof(decimal?) || type == typeof(double?) || type == typeof(float?))
                {
                    cellStyle.DataFormat = format.GetFormat("#,##0.00");
                }
                else if (type == typeof(DateTime) || type == typeof(DateTime?) ||
                         type == typeof(DateTimeOffset) || type == typeof(DateTimeOffset?))
                {
                    cellStyle.DataFormat = format.GetFormat("yyyy-MM-dd HH:mm:ss");
                }

                // 添加到缓存中
                styleCache[key] = cellStyle;
            }

            // 根据类型设置单元格的值
            Type columnType = column.DataType;
            if (columnType == typeof(short) || columnType == typeof(int) || columnType == typeof(long)
                || columnType == typeof(short?) || columnType == typeof(int?) || columnType == typeof(long?))
            {
                if (!row[column].AsString().IsEmpty())
                {
                    dCell.SetCellValue(row[column].AsString().As<int>());
                }
            }
            else if (columnType == typeof(decimal) || columnType == typeof(double) || columnType == typeof(float)
                     || columnType == typeof(decimal?) || columnType == typeof(double?) || columnType == typeof(float?))
            {
                if (!row[column].AsString().IsEmpty())
                {
                    dCell.SetCellValue(row[column].AsString().As<double>());
                }
            }
            else if (columnType == typeof(DateTime) || columnType == typeof(DateTime?) ||
                     columnType == typeof(DateTimeOffset) || columnType == typeof(DateTimeOffset?))
            {
                DateTime dtDate;
                if (DateTime.TryParse(row[column].AsString(), out dtDate))
                {
                    dCell.SetCellValue(dtDate);
                }
            }
            else
            {
                dCell.SetCellValue(row[column].AsString());
            }

            dCell.CellStyle = cellStyle; // 应用缓存的样式
        }

        public static DataTable ReadTemplateFromExcelToDataTable(Stream stream, string extension = ".xlsx", bool numAsString = false)
        {
            stream.Position = 0;
            try
            {
                IWorkbook workbook;
                if (extension.Equals(ConstDefinition.Common.ExtensionOfExcel, StringComparison.CurrentCultureIgnoreCase))
                {
                    workbook = new HSSFWorkbook(stream);
                }
                else
                {
                    workbook = new XSSFWorkbook(stream); //07版本及以上
                }

                var dt = new DataTable();
                //读取当前表数据
                ISheet sheet = workbook.GetSheetAt(0);
                //表头
                IRow header = sheet.GetRow(sheet.FirstRowNum);
                List<int> columns = new();

                for (int i = 0; i < header.LastCellNum; i++)
                {
                    object obj = GetValueType(header.Cells[i]);
                    if (obj == null || obj.ToString()?.Trim() == string.Empty)
                    {
                        continue;
                    }
                    else
                        dt.Columns.Add(new DataColumn(obj.ToString()));
                    columns.Add(i);
                }
                //数据
                for (int i = sheet.FirstRowNum + 1; i <= sheet.LastRowNum; i++)
                {
                    int nullCount = 0;
                    var sheetRow = sheet.GetRow(i);
                    if (sheetRow == null)
                        continue;

                    DataRow dr = dt.NewRow();

                    foreach (int j in columns)
                    {
                        var cell = sheetRow.GetCell(j);
                        if (cell == null)
                        {
                            ++nullCount;
                            continue;
                        }

                        if (cell.CellType == CellType.Formula)
                        {
                            cell.SetCellType(CellType.String);
                            var obj = cell.StringCellValue.AsString().AsTrim();
                            dr[j] = obj;
                            if (obj == null || obj.ToString() == string.Empty || string.IsNullOrWhiteSpace(obj.ToString()))
                                ++nullCount;
                        }
                        else
                        {
                            var obj = GetValueType(cell);
                            if (obj == null || obj.ToString() == string.Empty || string.IsNullOrWhiteSpace(obj.ToString()))
                                ++nullCount;

                            dr[j] = obj?.ToString()?.Trim();
                        }
                    }
                    //跳过空行
                    if (nullCount != columns.Count)
                        dt.Rows.Add(dr);
                }
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception("Excel文件读取失败,请检查文件是否与系统要求一致", ex);
            }
            finally
            {
                stream.Dispose();
            }
        }

        public static DataSet ReadTemplateFromExcelToDataSet(Stream stream, string extension = ".xlsx", bool numAsString = false)
        {
            stream.Position = 0;
            try
            {
                IWorkbook workbook;
                if (extension.Equals(ConstDefinition.Common.ExtensionOfExcel, StringComparison.CurrentCultureIgnoreCase))
                {
                    workbook = new HSSFWorkbook(stream);
                }
                else
                {
                    workbook = new XSSFWorkbook(stream); //07版本及以上
                }

                var ds = new DataSet();
                for (int b = 0; b < workbook.NumberOfSheets; b++)
                {
                    var dt = new DataTable();

                    //读取当前表数据
                    ISheet sheet = workbook.GetSheetAt(b);
                    dt.TableName = sheet.SheetName;
                    //表头
                    IRow header = sheet.GetRow(sheet.FirstRowNum);
                    List<int> columns = new();

                    for (int i = 0; i < header.LastCellNum; i++)
                    {
                        object obj = GetValueType(header.Cells[i]);
                        if (obj == null || obj.ToString()?.Trim() == string.Empty)
                        {
                            continue;
                        }
                        else
                        {
                            dt.Columns.Add(new DataColumn(obj.ToString()));
                        }
                        columns.Add(i);
                    }

                    //数据
                    for (int i = sheet.FirstRowNum + 1; i <= sheet.LastRowNum; i++)
                    {
                        int nullCount = 0;
                        var sheetRow = sheet.GetRow(i);
                        if (sheetRow == null)
                            continue;

                        DataRow dr = dt.NewRow();

                        foreach (int j in columns)
                        {
                            var cell = sheetRow.GetCell(j);
                            if (cell == null)
                            {
                                ++nullCount;
                                continue;
                            }

                            if (cell.CellType == CellType.Formula)
                            {
                                cell.SetCellType(CellType.String);
                                var obj = cell.StringCellValue.AsString().AsTrim();
                                dr[j] = obj;
                                if (obj == null || obj.ToString() == string.Empty || string.IsNullOrWhiteSpace(obj.ToString()))
                                    ++nullCount;
                            }
                            else
                            {
                                var obj = GetValueType(cell);
                                if (obj == null || obj.ToString() == string.Empty || string.IsNullOrWhiteSpace(obj.ToString()))
                                    ++nullCount;

                                dr[j] = obj?.ToString()?.Trim();
                            }
                        }
                        //跳过空行
                        if (nullCount != columns.Count)
                        {
                            dt.Rows.Add(dr);
                        }
                    }

                    ds.Tables.Add(dt);
                }

                return ds;
            }
            catch (Exception ex)
            {
                throw new Exception("Excel文件读取失败,请检查文件是否与系统要求一致", ex);
            }
            finally
            {
                stream.Dispose();
            }
        }

        private static object GetValueType(ICell cell, bool numAsString = false)
        {
            if (cell == null)
                return "";
            switch (cell.CellType)
            {
                case CellType.Blank: //BLANK:
                    return "";

                case CellType.Boolean: //BOOLEAN:
                    return cell.BooleanCellValue;

                case CellType.Numeric: //NUMERIC:

                    if (numAsString)
                    {
                        return cell.StringCellValue;
                    }
                    else
                    {
                        try
                        {
                            var cellValue = cell.ToString();
                            if (!string.IsNullOrEmpty(cellValue) && IsNumeric(cellValue))
                            {
                                return cell.NumericCellValue;
                            }

                            DateTime date = cell.DateCellValue;
                            return date.ToString().Replace("0:00:00", "");
                        }
                        catch
                        {
                            return cell.NumericCellValue;
                        }
                    }

                case CellType.String: //STRING:
                    return cell.StringCellValue;

                case CellType.Error: //ERROR:
                    return cell.ErrorCellValue;

                default:
                    return "=" + cell.CellFormula;
            }
        }

        private static bool IsNumeric(string value)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(value, @"^[+-]?\d*[.]?\d*$");
        }
    }
}
