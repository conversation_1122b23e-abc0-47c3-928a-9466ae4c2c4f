﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using Shinsoft.Core;

namespace Renji.JHR.Entities
{
    public enum  UserRight
    {
        //无权限
        [Description("无权限")]
        //[EnumGroup("", Ordinal = 10)]
        NoPermission = 0,

        /// <summary>
        /// 只读
        /// </summary>
        [Description("只读")]
        Readonly = 1,

        /// <summary>
        /// 可写
        /// </summary>
        [Description("可写")]
        Writable = 2,

        /// <summary>
        /// 完全
        /// </summary>
        [Description("完全")]
        FullControl = 3
    }
}
