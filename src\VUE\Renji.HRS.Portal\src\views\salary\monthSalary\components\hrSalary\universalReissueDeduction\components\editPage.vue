<template>
  <div>
    <el-dialog :title="title" :visible="showDialog" width="80%" :close-on-press-escape="false" :close-on-click-modal="false" @close="closeDialog">
      <el-form ref="dataForm" :rules="rules" :model="dataModel" label-width="100px">
        <el-card style="margin-top: 3px;">
          <div slot="header">
            <span>基础信息</span>
          </div>
          <el-row>
            <el-col :span="6">
              <el-form-item label="员工姓名">
                <div>
                  <span>
                    {{ dataModel.employeeModel.empName }}
                  </span>
                  <el-button v-if="!isEdit" style="margin-left: 15px;" type="primary" title="选择员工" @click="selectEmployeeDialog">选 择</el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="唯一码">
                {{ dataModel.employeeModel.empUid }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="工号">
                {{ dataModel.employeeModel.empCode }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="性别">
                {{ dataModel.employeeModel.genderDesc }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        <el-card style="margin-top: 3px;">
          <div slot="header">
            <span>通用补发/补扣信息</span>
          </div>
          <el-row>
            <el-col :span="6">
              <el-form-item label="补发/补扣名称" prop="salaryExtendedColumnId" label-width="120px">
                <el-select v-model="dataModel.salaryExtendedColumnId" clearable placeholder="补发/补扣名称" @change="handleChange">
                  <el-option v-for="item in groupList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="补发/补扣类型" prop="paymentType" label-width="120px">
                <el-select v-model="dataModel.enumPaymentType" placeholder="补发/补扣类型" title="width:120px">
                  <el-option v-for="item in payTypeOptions" :key="item.value" :label="item.desc" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="补发/补扣" prop="reissueDeduction" label-width="120px">
                <el-input-number v-model="dataModel.reissueDeduction" :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
              </el-form-item>
            </el-col> <!--
            <el-col :span="6">
              <el-form-item label="计税" prop="taxCalculation" label-width="170px">
                <el-switch v-model="dataModel.taxCalculation" active-color="#13ce66" />
              </el-form-item>
            </el-col>
            -->
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="备注" prop="remark" label-width="120px">
                <el-input v-model="dataModel.remark" type="textarea" :rows="3" maxlength="300" placeholder="备注" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" :loading="btnSaveLoading" @click="saveDialog">保 存</el-button>
      </div>
    </el-dialog>
    <selectUserComponent ref="selectEmployee" @selectRow="setEmployee" />
  </div>
</template>
<script>
import salaryApi from '@/api/salary'
import hRManageApi from '@/api/hRManage'
import sysManageApi from "@/api/sysManage";
import selectUserComponent from '@/views/salary/employeeSalary/components/selectUser'
export default {
  components: {
    selectUserComponent
  },
  data() {
    var validateMoney = (rule, value, callback) => {
      if (!(/^-?(\d|[1-9]\d+)(\.\d{1,2})?$/).test(value)) {
        callback(new Error('请输入正确格式数字,小数不超过2位'))
      } else {
        callback()
      }
    }
    return {
      showDialog: false,
      title: '',
      rules: {
        remark: [{ required: true, message: '请输入备注', trigger: 'blur' }],
        reissueDeduction: [
          { required: true, message: '请输入补发/补扣', trigger: 'blur' },
          { validator: validateMoney, trigger: 'blur' }
        ],
        salaryExtendedColumnId: [
          { required: true, message: '请选择补发/补扣名称', trigger: 'blur' }
        ]
      },
      btnSaveLoading: false,
      isEdit: false,
      dataModel: {
        employeeModel: {}
      },
      typeList: [],
      groupList: [],
      payTypeOptions: [],
      groupType: 1
    }
  },
  created() {
    this.getGroup()
    this.loadtypeList()
    this.loadPayType()
  },
  methods: {
    initDialog(row) {
      this.getGroup()
      this.loadtypeList()
      this.loadPayType()
      if (!row) {
        this.title = '新增通用补发/补扣'
        this.isEdit = false
      } else {
        this.title = '编辑通用补发/补扣'
        this.isEdit = true
        this.getData(row.id)
      }
      this.dataModel.salaryId = this.salaryId
      this.showDialog = true
    },
    loadPayType() {
       sysManageApi
        .getEnumInfos({ enumType: 'PaymentType' })
        .then(result => {
          this.payTypeOptions = result.data.datas
        })
        .catch(error => {
          console.log(error)
        })
    },
    getData(id) {
      salaryApi.getSalaryExtendedDetails({ id: id }).then(res => {
        if (res.succeed) {
          this.dataModel = res.data
          this.setEmployee(res.data.employee)
        }
      }).catch(res => {
      })
    },
    getGroup() {
      salaryApi.getSalaryExtendedColumn({ type: this.groupType }).then(res => {
        if (res.succeed) {
          this.groupList = res.data
        }
      }).catch(res => {
      })
    },
    handleChange(value) {
      this.groupList.forEach(v => {
        if (v.id === value) {
          this.dataModel.enumPaymentType = v.enumPaymentType
        }
      })
    },
    selectEmployeeDialog() {
      this.$refs.selectEmployee.loadTree()
      this.$refs.selectEmployee.showEmployee = true
    },
    saveDialog() {
      console.log(this.dataModel.enumPaymentType)
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.btnSaveLoading = true
          if (!this.isEdit) {
            salaryApi.addSalaryExtendedDetails(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '添加成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          } else {
            salaryApi.updateSalaryExtendedDetails(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '修改成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          }
        }
      })
    },
    loadtypeList() {
      hRManageApi.queryDictByParentCode({ code: '0731001' }).then(result => {
        this.typeList = result.data.datas
      }).catch(error => {
        console.log(error)
      })
    },
    closeDialog() {
      this.dataModel = {
        employeeModel: {}
      }
      this.typeList = []
      this.groupList = []
      this.showDialog = false
      this.$refs.dataForm.resetFields()
    },
    setEmployee(emp) {
      this.dataModel.employeeId = emp.id
      this.dataModel.salaryId = this.salaryId
      this.$set(this.dataModel, 'employeeModel',
        {
          employeeId: emp.id,
          empUid: emp.uid,
          empCode: emp.empCode,
          empName: emp.displayName,
          genderDesc: emp.enumGenderDesc,
          empDept: emp.deptName,
          hospitalAreaNameText: emp.hospitalAreaNameText,
          identityNumber: emp.identityNumber,
          hireStyleName: emp.employeeHR.hireStyleName,
          leaveStyleName: emp.employeeHR.leaveStyleName,
          deadDate: emp.employeeHR.deadDate
        })
    }
  }
}
</script>

<style>
</style>
