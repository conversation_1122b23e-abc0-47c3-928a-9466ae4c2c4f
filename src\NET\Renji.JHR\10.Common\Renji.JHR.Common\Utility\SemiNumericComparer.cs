﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Renji.JHR.Common.Utility
{
    public class SemiNumericComparer : IComparer<string?>
    {
        public int Compare(string? s1, string? s2)
        {
            if (IsNumeric(s1) && IsNumeric(s2))
            {
                if (Convert.ToInt32(s1) > Convert.ToInt32(s2)) return 1;
                if (Convert.ToInt32(s1) < Convert.ToInt32(s2)) return -1;
                if (Convert.ToInt32(s1) == Convert.ToInt32(s2)) return 0;
            }

            if (IsNumeric(s1) && !IsNumeric(s2))
                return -1;

            if (!IsNumeric(s1) && IsNumeric(s2))
                return 1;

            return string.Compare(s1, s2, true);
        }

        public static bool IsNumeric(object? value)
        {
            try
            {
                if (value != null)
                {
                    int i = Convert.ToInt32(value.ToString());
                    return true;
                }
                return false;
            }
            catch (FormatException)
            {
                return false;
            }
        }
    }
}