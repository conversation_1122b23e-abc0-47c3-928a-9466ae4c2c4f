﻿using Renji.JHR.Common.Configration;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;

namespace Renji.JHR.Common
{
#pragma warning disable CS8600 // 将 null 字面量或可能为 null 的值转换为非 null 类型。
#pragma warning disable CS8601 // 引用类型赋值可能为 null。
#pragma warning disable CS8602 // 解引用可能出现空引用。
#pragma warning disable CS8603 // 可能返回 null 引用。
#pragma warning disable CS8604 // 引用类型参数可能为 null。
#pragma warning disable SYSLIB0014 // 类型或成员已过时

    public class WebServiceCaller
    {
        public static XmlDocument LoadXml(string xml)
        {
            var doc = new XmlDocument();

            //为了代码检测改的， 但是似乎没效果
            var stream = new MemoryStream(Encoding.Default.GetBytes(xml));
            var settings = new XmlReaderSettings
            {
                DtdProcessing = DtdProcessing.Ignore,
                XmlResolver = null
            };

            using var reader = XmlTextReader.Create(stream, settings);

            doc.Load(reader);

            reader.Close();
            reader.Dispose();

            //代码检测时，注释下面这行代码
            doc.LoadXml(xml);

            return doc;
        }

        public static string QueryWebService(String URL, Hashtable Pars)
        {
            var text = "";
            string boundary = "----------------------------" + SysDateTime.Now.Ticks.ToString("x");
            HttpWebRequest request = (HttpWebRequest)HttpWebRequest.Create(URL);
            request.ContentType = "multipart/form-data; boundary=" + boundary;
            request.Method = "POST";
            request.KeepAlive = true;
            //request.Credentials = CredentialCache.DefaultCredentials;
            request.Expect = "";
            //request.Headers.Add("SOAPAction", "\"" + XmlNs + (XmlNs.EndsWith("/") ? "" : "/") + "\"");
            SetWebRequest(request);
            byte[] data = EncodePars(Pars);

            WriteRequestData(request, data);

            using (WebResponse response = request.GetResponse())
            using (Stream resSteam = response.GetResponseStream())
            using (StreamReader sr = new StreamReader(resSteam))
            {
                text = sr.ReadToEnd();
            }

            //XmlDocument doc = new XmlDocument(), doc2 = new XmlDocument();
            //doc = ReadXmlResponse(request.GetResponse());

            //XmlNamespaceManager mgr = new XmlNamespaceManager(doc.NameTable);
            //mgr.AddNamespace("soap", "http://schemas.xmlsoap.org/soap/envelope/");
            //String RetXml = doc.SelectSingleNode("//soap:Body/*/*", mgr).InnerXml;
            //doc2.LoadXml("<root>" + RetXml + "</root>");
            //AddDelaration(doc2);
            return text;
        }

        private static byte[] EncodePars(Hashtable Pars)
        {
            XmlDocument doc = LoadXml("<A_Form_Record xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" FGID=\"\" SGID=\"\" ></A_Form_Record>");
            AddDelaration(doc);

            foreach (string k in Pars.Keys)
            {
                XmlElement field = doc.CreateElement("Field");
                XmlElement key = doc.CreateElement("Key");
                XmlElement val = doc.CreateElement("Value");
                key.InnerXml = k;
                val.InnerXml = Pars[k].ToString();
                field.AppendChild(key);
                field.AppendChild(val);
                doc.DocumentElement.AppendChild(field);
            }

            return Encoding.UTF8.GetBytes(doc.OuterXml);
        }

        /// <summary>
        /// 通用WebService调用(Soap),参数Pars为String类型的参数名、参数值
        /// </summary>
        public static XmlDocument QuerySoapWebService(String URL, String MethodName, Hashtable Pars, bool withInput = false)
        {
            return QuerySoapWebService(URL, MethodName, Pars, "https://cn.bing.com/", withInput);        //GetNamespace(URL)
        }

        private static XmlDocument QuerySoapWebService(String URL, String MethodName, Hashtable Pars, string XmlNs, bool withInput = false)
        {
            HttpWebRequest request = (HttpWebRequest)HttpWebRequest.Create(URL);
            request.Method = "POST";
            request.ContentType = "text/xml; charset=utf-8";
            request.Headers.Add("SOAPAction", "\"" + XmlNs + (XmlNs.EndsWith("/") ? "" : "/") + MethodName + "\"");
            SetWebRequest(request);
            byte[] data;
            if (withInput)
            {
                data = EncodeParsToSoapWithInput(Pars, XmlNs, MethodName);
            }
            else
            {
                data = EncodeParsToSoap(Pars, XmlNs, MethodName);
            }

            WriteRequestData(request, data);
            XmlDocument doc = new XmlDocument();
            doc = ReadXmlResponse(request.GetResponse());

            XmlNamespaceManager mgr = new XmlNamespaceManager(doc.NameTable);
            mgr.AddNamespace("soap", "http://schemas.xmlsoap.org/soap/envelope/");
            String RetXml = doc.SelectSingleNode("//soap:Body/*/*", mgr).InnerXml;
            var doc2 = LoadXml("<root>" + RetXml + "</root>");
            AddDelaration(doc2);
            return doc2;
        }

        private static byte[] EncodeParsToSoap(Hashtable Pars, String XmlNs, String MethodName)
        {
            XmlDocument doc = LoadXml("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"></soap:Envelope>");
            AddDelaration(doc);

            XmlElement soapBody = doc.CreateElement("soap", "Body", "http://schemas.xmlsoap.org/soap/envelope/");

            XmlElement soapMethod = doc.CreateElement(MethodName);
            soapMethod.SetAttribute("xmlns", XmlNs);
            foreach (string k in Pars.Keys)
            {
                XmlElement soapPar = doc.CreateElement(k);
                soapPar.InnerXml = Pars[k] == null ? "" : ObjectToSoapXml(Pars[k]);
                soapMethod.AppendChild(soapPar);
            }
            soapBody.AppendChild(soapMethod);
            doc.DocumentElement.AppendChild(soapBody);
            return Encoding.UTF8.GetBytes(doc.OuterXml);
        }

        private static byte[] EncodeParsToSoapWithInput(Hashtable Pars, String XmlNs, String MethodName)
        {
            XmlDocument doc = LoadXml("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"></soap:Envelope>");
            AddDelaration(doc);

            XmlElement soapBody = doc.CreateElement("soap", "Body", "http://schemas.xmlsoap.org/soap/envelope/");

            XmlElement soapMethod = doc.CreateElement(MethodName);
            soapMethod.SetAttribute("xmlns", XmlNs);
            //XmlElement inputMethod = soapMethod.AppendChild("Input");
            XmlElement inputMethod = doc.CreateElement("Input");
            soapMethod.AppendChild(inputMethod);
            foreach (string k in Pars.Keys)
            {
                XmlElement soapPar = doc.CreateElement(k);
                soapPar.InnerXml = Pars[k] == null ? "" : ObjectToSoapXml(Pars[k]);
                inputMethod.AppendChild(soapPar);
            }
            soapBody.AppendChild(soapMethod);
            doc.DocumentElement.AppendChild(soapBody);
            return Encoding.UTF8.GetBytes(doc.OuterXml);
        }

        private static string ObjectToSoapXml(object o)
        {
            XmlSerializer mySerializer = new XmlSerializer(o.GetType());
            MemoryStream ms = new MemoryStream();
            mySerializer.Serialize(ms, o);
            XmlDocument doc = LoadXml(Encoding.UTF8.GetString(ms.ToArray()));
            if (doc.DocumentElement != null)
            {
                return doc.DocumentElement.InnerXml;
            }
            else
            {
                return o.ToString();
            }
        }

        /// <summary>
        /// 设置凭证与超时时间
        /// </summary>
        /// <param name="request"></param>
        private static void SetWebRequest(HttpWebRequest request)
        {
            request.Credentials = CredentialCache.DefaultCredentials;
            request.Timeout = Config.InterfaceTimeout;
        }

        private static void WriteRequestData(HttpWebRequest request, byte[] data)
        {
            request.ContentLength = data.Length;
            Stream writer = request.GetRequestStream();
            writer.Write(data, 0, data.Length);
            writer.Close();
        }

        private static XmlDocument ReadXmlResponse(WebResponse response)
        {
            using StreamReader sr = new StreamReader(response.GetResponseStream(), Encoding.UTF8);
            String retXml = sr.ReadToEnd();
            sr.Close();
            sr.Dispose();

            XmlDocument doc = LoadXml(retXml);

            return doc;
        }

        private static void AddDelaration(XmlDocument doc)
        {
            XmlDeclaration decl = doc.CreateXmlDeclaration("1.0", "utf-8", null);
            doc.InsertBefore(decl, doc.DocumentElement);
        }
    }

#pragma warning restore CS8600 // 将 null 字面量或可能为 null 的值转换为非 null 类型。
#pragma warning restore CS8601 // 引用类型赋值可能为 null。
#pragma warning restore CS8602 // 解引用可能出现空引用。
#pragma warning restore CS8603 // 可能返回 null 引用。
#pragma warning restore CS8604 // 引用类型参数可能为 null。
#pragma warning restore SYSLIB0014 // 类型或成员已过时
}