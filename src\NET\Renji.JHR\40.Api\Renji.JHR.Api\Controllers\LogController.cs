﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualBasic;
using Renji.JHR.Api.Models;
using Renji.JHR.Bll;
using Renji.JHR.Common;
using Renji.JHR.Common.Configration;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.Mvc;
using Shinsoft.Core.NLog;
using System;
using System.Collections.Generic;

namespace Renji.JHR.Api.Controllers.Admin
{
    /// <summary>
    /// 日志
    /// </summary>
    public class LogController : BaseApiController<LogBll>
    {
        /// <summary>
        /// 查询日志
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query)]
        public QueryResult<VwLogQuery> QueryLogs([FromQuery] LogFilter filter)
        {
            var exps = this.NewExps<VwLog>(p => p.Platform == Config.Log.Platform);

            if (!filter.Program.IsEmpty())
            {
                exps.And(p => p.Program!.Contains(filter.Program!));
            }

            if (!filter.Categroy.IsEmpty())
            {
                exps.And(p => p.Category!.Contains(filter.Categroy!));
            }

            if (!filter.Level.IsEmpty())
            {
                exps.And(p => p.Level!.Contains(filter.Level!));
            }

            if (!filter.Logger.IsEmpty())
            {
                exps.And(p => p.Logger!.Contains(filter.Logger!));
            }

            if (!filter.Operate.IsEmpty())
            {
                exps.And(p => p.Operate!.Contains(filter.Operate!));
            }

            if (!filter.Message.IsEmpty())
            {
                exps.And(p => p.Message!.Contains(filter.Message!));
            }
            if (!filter.StartDate.IsEmpty())
            {
                exps.And(p => p.LogTime >= filter.StartDate!.As<DateTime>("yyyy-MM-dd"));
            }
            if (!filter.EndDate.IsEmpty())
            {
                exps.And(p => p.LogTime < filter.EndDate!.As<DateTime>("yyyy-MM-dd").AddDays(1));
            }
            var order = filter.Order.IsEmpty()
                ? $"{VwLog.Columns.LogTime} DESC"
                : filter.Order;
            var pageIndex = filter.PageIndex;
            var pageSize = filter.PageSize;
            int recordCount = -1;

            List<VwLog> entities;
            if (filter.RequireRecordCount)
            {
                entities = this.Repo.GetEntities(exps, order, out recordCount, pageIndex, pageSize);
            }
            else
            {
                entities = this.Repo.GetEntities(exps, order, pageIndex, pageSize);
            }

            var models = entities.Maps<VwLogQuery>();

            return recordCount >= 0
                ? this.QueryResult(models, recordCount, pageIndex, pageSize)
                : this.QueryResult(models, pageIndex, pageSize);
        }

        /// <summary>
        /// 查询类别
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query)]
        [Permission(Permissions.Management.Query_Log)]
        public QueryResult<LogCategory> QueryCategory()
        {
            var exps = this.NewExps<LogCategory>();

            var entities = this.Repo.GetEntities(exps);

            var models = entities.Maps<LogCategory>();

            return this.QueryResult(models);
        }

        /// <summary>
        /// 查询级别
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query)]
        [Permission(Permissions.Management.Query_Log)]
        public QueryResult<string> QueryLevel()
        {
            var level = new List<string>();
            level.Add("Info");
            level.Add("Error");

            return this.QueryResult(level);
        }
    }
}