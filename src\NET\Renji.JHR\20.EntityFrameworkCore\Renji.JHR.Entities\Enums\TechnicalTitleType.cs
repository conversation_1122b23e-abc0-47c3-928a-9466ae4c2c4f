﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Renji.JHR.Entities
{
    /// <summary>
    /// 申康字段：专技职称
    /// </summary>
    public enum TechnicalTitleType
    {

        #region 卫生专业技术职称（主系列）

        #region 卫生技术人员（医生）
        /// <summary>
        /// 主任医师
        /// </summary>
        [Description("231")]
        ChiefPhysician = 231,

        /// <summary>
        /// 副主任医师
        /// </summary>
        [Description("232")]
        AssociateChiefPhysician = 232,

        /// <summary>
        /// 主治医师
        /// </summary>
        [Description("233")]
        AttendingPhysician = 233,

        /// <summary>
        /// 医师
        /// </summary>
        [Description("234")]
        Physician = 234,

        /// <summary>
        /// 医士
        /// </summary>
        [Description("235")]
        MedicalAssistant = 235,
        #endregion

        #region 卫生技术人员（药剂）
        /// <summary>
        /// 主任药师
        /// </summary>
        [Description("241")]
        ChiefPharmacist = 241,

        /// <summary>
        /// 副主任药师
        /// </summary>
        [Description("242")]
        AssociateChiefPharmacist = 242,

        /// <summary>
        /// 主管药师
        /// </summary>
        [Description("243")]
        PharmacySupervisor = 243,

        /// <summary>
        /// 药师
        /// </summary>
        [Description("244")]
        Pharmacist = 244,

        /// <summary>
        /// 药士
        /// </summary>
        [Description("245")]
        PharmacyAssistant = 245,
        #endregion

        #region 卫生技术人员（护理）
        /// <summary>
        /// 主任护师
        /// </summary>
        [Description("251")]
        ChiefNurse = 251,

        /// <summary>
        /// 副主任护师
        /// </summary>
        [Description("252")]
        AssociateChiefNurse = 252,

        /// <summary>
        /// 主管护师
        /// </summary>
        [Description("253")]
        NursingSupervisor = 253,

        /// <summary>
        /// 护师
        /// </summary>
        [Description("254")]
        Nurse = 254,

        /// <summary>
        /// 护士
        /// </summary>
        [Description("255")]
        NursingAssistant = 255,
        #endregion

        #region 卫生技术人员（医技）
        /// <summary>
        /// 主任技师
        /// </summary>
        [Description("261")]
        ChiefTechnologist = 261,

        /// <summary>
        /// 副主任技师
        /// </summary>
        [Description("262")]
        AssociateChiefTechnologist = 262,

        /// <summary>
        /// 主管技师
        /// </summary>
        [Description("263")]
        TechnologistSupervisor = 263,

        /// <summary>
        /// 技师
        /// </summary>
        [Description("264")]
        Technologist = 264,

        /// <summary>
        /// 技士
        /// </summary>
        [Description("265")]
        Technician = 265,
        #endregion

        #region 卫生技术人员（研究）
        /// <summary>
        /// 研究员（医学科研）
        /// </summary>
        [Description("101")]
        ResearcherMedicalScience = 101,

        /// <summary>
        /// 副研究员（医学科研）
        /// </summary>
        [Description("102")]
        AssociateResearcherMedicalScience = 102,

        /// <summary>
        /// 助理研究员（医学科研）
        /// </summary>
        [Description("103")]
        AssistantResearcherMedicalScience = 103,

        /// <summary>
        /// 研究实习员（医学科研）
        /// </summary>
        [Description("104")]
        ResearchInternMedicalScience = 104,

        /// <summary>
        /// 研究员（卫生事业研究）
        /// </summary>
        [Description("105")]
        ResearcherHealthCare = 105,

        /// <summary>
        /// 副研究员（卫生事业研究）
        /// </summary>
        [Description("106")]
        AssociateResearcherHealthCare = 106,

        /// <summary>
        /// 助理研究员（医院管理）
        /// </summary>
        [Description("107")]
        AssistantResearcherHospitalManagement = 107,

        /// <summary>
        /// 研究实习员（医院管理）
        /// </summary>
        [Description("108")]
        ResearchInternHospitalManagement = 108,

        /// <summary>
        /// 研究员（临床研究辅助）
        /// </summary>
        [Description("109")]
        ResearcherClinicalResearchSupport = 109,

        /// <summary>
        /// 副研究员（临床研究辅助）
        /// </summary>
        [Description("110")]
        AssociateResearcherClinicalResearchSupport = 110,

        /// <summary>
        /// 助理研究员（临床研究辅助）
        /// </summary>
        [Description("111")]
        AssistantResearcherClinicalResearchSupport = 111,

        /// <summary>
        /// 研究实习员（临床研究辅助）
        /// </summary>
        [Description("112")]
        ResearchInternClinicalResearchSupport = 112,
        #endregion

        #endregion

        #region 其他专业技术职称（辅系列）

        #region 教师系列
        /// <summary>
        /// 教授
        /// </summary>
        [Description("011")]
        Professor = 11,

        /// <summary>
        /// 副教授
        /// </summary>
        [Description("012")]
        AssociateProfessor = 12,

        /// <summary>
        /// 讲师
        /// </summary>
        [Description("013")]
        Lecturer = 13,

        /// <summary>
        /// 助教
        /// </summary>
        [Description("014")]
        TeachingAssistant = 14,
        #endregion

        #region 实验系列
        /// <summary>
        /// 正高级实验师
        /// </summary>
        [Description("071")]
        SeniorChiefExperimentalist = 71,

        /// <summary>
        /// 高级实验师
        /// </summary>
        [Description("072")]
        SeniorExperimentalist = 72,

        /// <summary>
        /// 实验师
        /// </summary>
        [Description("073")]
        Experimentalist = 73,

        /// <summary>
        /// 助理实验师
        /// </summary>
        [Description("074")]
        AssistantExperimentalist = 74,

        /// <summary>
        /// 实验员
        /// </summary>
        [Description("075")]
        ExperimentalTechnician = 75,
        #endregion

        #region 工程系列
        /// <summary>
        /// 高级工程师(教授级)
        /// </summary>
        [Description("081")]
        ChiefEngineerProfessorLevel = 81,

        /// <summary>
        /// 高级工程师
        /// </summary>
        [Description("082")]
        SeniorEngineer = 82,

        /// <summary>
        /// 工程师
        /// </summary>
        [Description("083")]
        Engineer = 83,

        /// <summary>
        /// 助理工程师
        /// </summary>
        [Description("084")]
        AssistantEngineer = 84,

        /// <summary>
        /// 技术员
        /// </summary>
        [Description("085")]
        TechnicalStaff = 85,
        #endregion

        #region 党务系列
        /// <summary>
        /// 研究员（党务管理）
        /// </summary>
        [Description("113")]
        ResearcherPartyAffairs = 113,

        /// <summary>
        /// 副研究员（党务管理）
        /// </summary>
        [Description("114")]
        AssociateResearcherPartyAffairs = 114,

        /// <summary>
        /// 助理研究员（党务管理）
        /// </summary>
        [Description("115")]
        AssistantResearcherPartyAffairs = 115,

        /// <summary>
        /// 研究实习员（党务管理）
        /// </summary>
        [Description("116")]
        ResearchInternPartyAffairs = 116,
        #endregion

        #region 经济系列
        /// <summary>
        /// 正高级经济师
        /// </summary>
        [Description("121")]
        SeniorChiefEconomist = 121,

        /// <summary>
        /// 高级经济师
        /// </summary>
        [Description("122")]
        SeniorEconomist = 122,

        /// <summary>
        /// 经济师
        /// </summary>
        [Description("123")]
        Economist = 123,

        /// <summary>
        /// 助理经济师
        /// </summary>
        [Description("124")]
        AssistantEconomist = 124,

        /// <summary>
        /// 经济员
        /// </summary>
        [Description("125")]
        EconomicTechnician = 125,
        #endregion

        #region 会计系列
        /// <summary>
        /// 正高级会计师
        /// </summary>
        [Description("131")]
        SeniorChiefAccountant = 131,

        /// <summary>
        /// 高级会计师
        /// </summary>
        [Description("132")]
        SeniorAccountant = 132,

        /// <summary>
        /// 会计师
        /// </summary>
        [Description("133")]
        Accountant = 133,

        /// <summary>
        /// 助理会计师
        /// </summary>
        [Description("134")]
        AssistantAccountant = 134,

        /// <summary>
        /// 会计员
        /// </summary>
        [Description("135")]
        AccountingClerk = 135,
        #endregion

        #region 统计系列
        /// <summary>
        /// 正高级统计师
        /// </summary>
        [Description("141")]
        SeniorChiefStatistician = 141,

        /// <summary>
        /// 高级统计师
        /// </summary>
        [Description("142")]
        SeniorStatistician = 142,

        /// <summary>
        /// 统计师
        /// </summary>
        [Description("143")]
        Statistician = 143,

        /// <summary>
        /// 助理统计师
        /// </summary>
        [Description("144")]
        AssistantStatistician = 144,

        /// <summary>
        /// 统计员
        /// </summary>
        [Description("145")]
        StatisticsClerk = 145,
        #endregion

        #region 编辑系列
        /// <summary>
        /// 编审
        /// </summary>
        [Description("151")]
        EditorialReviewer = 151,

        /// <summary>
        /// 副编审
        /// </summary>
        [Description("152")]
        AssociateEditorialReviewer = 152,

        /// <summary>
        /// 编辑
        /// </summary>
        [Description("153")]
        Editor = 153,
        #endregion

        #region 记者系列
        /// <summary>
        /// 高级记者
        /// </summary>
        [Description("191")]
        SeniorReporter = 191,

        /// <summary>
        /// 主任记者
        /// </summary>
        [Description("192")]
        ChiefReporter = 192,

        /// <summary>
        /// 记者
        /// </summary>
        [Description("193")]
        Reporter = 193,

        /// <summary>
        /// 助理记者
        /// </summary>
        [Description("194")]
        AssistantReporter = 194,
        #endregion

        #region 法律系列
        /// <summary>
        /// 一级律师
        /// </summary>
        [Description("391")]
        FirstLevelLawyer = 391,

        /// <summary>
        /// 二级律师
        /// </summary>
        [Description("392")]
        SecondLevelLawyer = 392,

        /// <summary>
        /// 三级律师
        /// </summary>
        [Description("393")]
        ThirdLevelLawyer = 393,

        /// <summary>
        /// 四级律师
        /// </summary>
        [Description("394")]
        FourthLevelLawyer = 394,

        /// <summary>
        /// 律师助理
        /// </summary>
        [Description("395")]
        LawyerAssistant = 395,
        #endregion

        #region 科学研究系列
        /// <summary>
        /// 研究员（自然科学)
        /// </summary>
        [Description("611")]
        ResearcherNaturalScience = 611,

        /// <summary>
        /// 副研究员（自然科学)
        /// </summary>
        [Description("612")]
        AssociateResearcherNaturalScience = 612,

        /// <summary>
        /// 助理研究员（自然科学)
        /// </summary>
        [Description("613")]
        AssistantResearcherNaturalScience = 613,

        /// <summary>
        /// 研究实习员（自然科学)
        /// </summary>
        [Description("614")]
        ResearchInternNaturalScience = 614,
        #endregion

        #region 档案系列
        /// <summary>
        /// 研究馆员
        /// </summary>
        [Description("661")]
        Archivist = 661,

        /// <summary>
        /// 副研究馆员
        /// </summary>
        [Description("662")]
        AssociateArchivist = 662,

        /// <summary>
        /// 馆员
        /// </summary>
        [Description("663")]
        ArchivistAssistant = 663,

        /// <summary>
        /// 助理馆员
        /// </summary>
        [Description("664")]
        AssistantArchivist = 664,

        /// <summary>
        /// 管理员
        /// </summary>
        [Description("665")]
        ArchivistManager = 665,
        #endregion

        #region 审计系列
        /// <summary>
        /// 正高级审计师
        /// </summary>
        [Description("681")]
        SeniorChiefAuditor = 681,

        /// <summary>
        /// 高级审计师
        /// </summary>
        [Description("682")]
        SeniorAuditor = 682,

        /// <summary>
        /// 审计师
        /// </summary>
        [Description("683")]
        Auditor = 683,

        /// <summary>
        /// 助理审计师
        /// </summary>
        [Description("684")]
        AssistantAuditor = 684,

        /// <summary>
        /// 审计员
        /// </summary>
        [Description("685")]
        AuditClerk = 685,
        #endregion

        #region 政工系列
        /// <summary>
        /// 高级政工师
        /// </summary>
        [Description("982")]
        SeniorPoliticalWorker = 982,

        /// <summary>
        /// 政工师
        /// </summary>
        [Description("983")]
        PoliticalWorker = 983,

        /// <summary>
        /// 助理政工师
        /// </summary>
        [Description("984")]
        AssistantPoliticalWorker = 984,

        /// <summary>
        /// 政工员
        /// </summary>
        [Description("985")]
        PoliticalWorkerClerk = 985,
        #endregion

        #region 社工系列
        /// <summary>
        /// 正高级社工师
        /// </summary>
        [Description("991")]
        SeniorChiefSocialWorker = 991,

        /// <summary>
        /// 高级社工师
        /// </summary>
        [Description("992")]
        SeniorSocialWorker = 992,

        /// <summary>
        /// 社工师
        /// </summary>
        [Description("993")]
        SocialWorker = 993,

        /// <summary>
        /// 助理社工
        /// </summary>
        [Description("994")]
        AssistantSocialWorker = 994,
        #endregion
        #endregion

        #region 其他类职称
        /// <summary>
        /// 其他类正高级职称
        /// </summary>
        [Description("771")]
        OtherSeniorTitle = 771,

        /// <summary>
        /// 其他类副高级职称
        /// </summary>
        [Description("772")]
        OtherAssociateSeniorTitle = 772,

        /// <summary>
        /// 其他类中级职称
        /// </summary>
        [Description("773")]
        OtherIntermediateTitle = 773,

        /// <summary>
        /// 其他类初级职称
        /// </summary>
        [Description("774")]
        OtherJuniorTitle = 774,

        /// <summary>
        /// 未聘
        /// </summary>
        [Description("888")]
        NotEmployed = 888,

        /// <summary>
        /// 无职称
        /// </summary>
        [Description("999")]
        NoTitle = 999,
        #endregion

    }
}
