﻿using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Xml.Serialization;

namespace Renji.JHR.Entities
{
    public partial class Station : IParent<Station>, IOrder
    {
        #region IParent

        IComparable IParent.ID => this.ID;

        IComparable? IParent.ParentId => this.ParentId;

        #endregion IParent

        #region IOrder

        IComparable IOrder.Order => -this.Ordinal;

        #endregion IOrder

        /// <summary>
        /// 上级岗位名称
        /// </summary>
        [Description("上级岗位名称")]
        [NotMapped]
        public string? ParentName => this.Parent?.Name;

        /// <summary>
        /// 关联的职位
        /// </summary>
        [Description("关联的职位")]
        [NotMapped, JsonIgnore, XmlIgnore]
        public List<Guid> PositionIds { get; set; }

        /// <summary>
        /// 删除时是否同时删除用户岗位对应关系
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public bool ConfirmToDelete { get; set; }
    }
}