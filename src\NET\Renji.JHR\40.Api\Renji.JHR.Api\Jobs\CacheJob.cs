﻿using Quartz;
using Renji.JHR.Bll.Caching;
using Shinsoft.Core;
using Shinsoft.Core.Hosting;
using Shinsoft.Core.NLog;
using Shinsoft.Core.Tasks;
using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace Renji.JHR.Api.Jobs
{
    //public class CacheJob : IJob
    //{
    //	public Task Execute(IJobExecutionContext context)
    //	{
    //		return TaskHelper.Run(() =>
    //		{
    //			var stopwatch = Stopwatch.StartNew();
    //			try
    //			{
    //				using var scope = HostContext.CreateScope();
    //				var cache = scope.GetService<MasterDataCache>();

    //				cache.RefreshAll();

    //				this.Info("刷新缓存", "刷新缓存", duration: stopwatch.ElapsedMilliseconds, remark: "CacheJob");
    //			}
    //			catch (Exception ex)
    //			{
    //				ex = ex.GetBaseException();
    //				this.Error(ex, "刷新缓存", ex.Message, stopwatch.ElapsedMilliseconds, "CacheJob");
    //			}
    //		});
    //	}
    //}
}