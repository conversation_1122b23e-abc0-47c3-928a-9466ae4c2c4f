﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Renji.JHR.Entities
{
    public enum TreatmentType
    {
        [Description("")]
        None = 0,

        /// <summary>
        /// 分子
        /// </summary>
        /// <summary>
        [Description("分子")]
        Member = 1,

        /// <summary>
        /// 普通
        /// </summary>
        [Description("普通")]
        General = 2,

        /// <summary>
        /// 普通（外籍）
        /// </summary>
        [Description("普通（外籍）")]
        GeneralForeigner = 3,

        /// <summary>
        /// 普通-导师自支
        /// </summary>
        [Description("普通-导师自支")]
        GeneralTutorSelfSupport = 4
    }
}
