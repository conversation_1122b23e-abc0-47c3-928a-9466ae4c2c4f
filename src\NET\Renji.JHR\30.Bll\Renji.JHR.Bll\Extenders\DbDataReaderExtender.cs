﻿using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Text;

namespace Renji.JHR.Bll
{
    public static class DbDataReaderExtender
    {
        public static T? GetValueExt<T>(this DbDataReader reader, string name)
        {
            var ordinal = reader.GetOrdinal(name);
            if (reader.IsDBNull(ordinal))
                return default;
            return reader.GetFieldValue<T>(ordinal);
        }

        public static bool IsDBNullExt(this DbDataReader reader, string name)
        {
            return reader.IsDBNull(reader.GetOrdinal(name));
        }
    }
}