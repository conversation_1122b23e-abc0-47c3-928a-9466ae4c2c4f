﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Renji.JHR.Entities
{
    /// <summary>
    /// 申康编码:人员类别
    /// </summary>
    public enum UserCategoryType
    {
        /// <summary>
        /// 临床医师
        /// </summary>
        [Description("01")]
        Clinician = 01,

        /// <summary>
        /// 规培
        /// </summary>
        [Description("02")]
        Training = 02,

        /// <summary>
        /// 护理人员
        /// </summary>
        [Description("03")]
        Nursing = 03,

        /// <summary>
        /// 医技人员
        /// </summary>
        [Description("04")]
        MedicalTechnicians = 04,

        /// <summary>
        /// 药剂人员
        /// </summary>
        [Description("05")]
        Pharmacists = 05,

        /// <summary>
        /// 科研人员
        /// </summary>
        [Description("06")]
        Researchers = 06,

        /// <summary>
        /// 其他专业技术人员
        /// </summary>
        [Description("07")]
        OtherProfessional = 07,

        /// <summary>
        /// 管理人员
        /// </summary>
        [Description("08")]
        Managers = 08,

        /// <summary>
        /// 工勤技能人员
        /// </summary>
        [Description("09")]
        Worker = 09,

        /// <summary>
        /// 文员辅助人员
        /// </summary>
        [Description("10")]
        Clerk = 10
    }
}
