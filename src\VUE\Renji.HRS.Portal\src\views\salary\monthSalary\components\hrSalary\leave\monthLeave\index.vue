<template>
  <div class="app-container">
    <layout4>
      <template #main>
        <el-form ref="ref_searchFrom" :inline="true" :model="listQuery">
          <el-form-item>
            <el-input v-model="listQuery.uid" clearable placeholder="唯一码" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.empCode" clearable placeholder="工号" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.displayName" clearable placeholder="姓名" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.deptName" clearable placeholder="部门" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.officialRankName" clearable placeholder="职别" />
          </el-form-item>
          <el-form-item>
            <el-select v-model="listQuery.enumSalaryLeaveType" filterable clearable placeholder="请假天数类型" style="width: 100%" class="filter-item">
              <el-option v-for="item in leaveTypeList" :key="item.value" :label="item.desc" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
            <el-button v-if="salaryData.enumStatus == 1" type="primary" icon="el-icon-upload" @click="autoImport">自动导入</el-button>
          </el-form-item>
        </el-form>
        <el-table ref="tableList" v-loading="listLoading" :data="pageList" border stripe fit highlight-current-row style="width: 100%;" :header-cell-style="{ background: '#F5F7FA', color: '#606266' }" @sort-change="sortChange">
          <el-table-column label="部门" sortable="custom" prop="Employee.Department.Name">
            <template slot-scope="{ row }">
              <span>{{ row.deptName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="职别" sortable="custom" prop="Employee.EmployeeHR.OfficialRank.Name">
            <template slot-scope="{ row }">
              <span>{{ row.officialRankName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="请假类型" prop="EnumSalaryLeaveType" :min-width="100" sortable="custom">
            <template slot-scope="{ row }">
              <span>{{ row.enumSalaryLeaveTypeDesc }}</span>
            </template>
          </el-table-column>
          <el-table-column label="扣除比例" sortable="custom" :min-width="100" prop="DiscountRatio">
            <template slot-scope="{ row } ">
              <span>{{ formatDiscountRatio(row.discountRatio) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="病假" prop="H2" :min-width="100">
            <template slot-scope="{ row }">
              <span>{{ row.h2 | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="事假" prop="H3" :min-width="100">
            <template slot-scope="{ row }">
              <span>{{ row.h3 | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="产假" prop="H4" :min-width="100">
            <template slot-scope="{ row }">
              <span>{{ row.h4 | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="哺乳假" prop="H5" :min-width="100">
            <template slot-scope="{ row }">
              <span>{{ row.h5 | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="探亲假" prop="H6" :min-width="100">
            <template slot-scope="{ row }">
              <span>{{ row.h6 | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="计生假" prop="H7" :min-width="100">
            <template slot-scope="{ row }">
              <span>{{ row.h7 | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="婚丧假" prop="H8" :min-width="100">
            <template slot-scope="{ row }">
              <span>{{ row.h8 | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="150px" sortable="custom" prop="Remark">
            <template slot-scope="{ row } ">
              <span>{{ row.remark }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" header-align="center" width="150" class-name="small-padding fixed-width">
            <template slot-scope="{ row }">
              <el-button v-if="salaryData.enumStatus == 1" type="primary" style="margin-left:5px !important; padding-left: 5px !important;" size="mini" @click="showDialog(row)">
                编辑
              </el-button>
              <el-button v-if="salaryData.enumStatus == 1" style="padding-left: 5px !important;" size="mini" type="danger" @click="deleteSalaryLeave(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
          <employeeTableColumns />
        </el-table>
        <c-pagination v-show="listQuery.total > 0" :total="listQuery.total" :page-sizes="[10, 20, 50]" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getPageList" />
        <el-row>
          <el-col style="width: 750px;">
            <div
              style="
                margin-top: 20px;
                background-color: #fff7f7;
                border: 1px solid #f56c6c;
                border-radius: 4px;
                padding: 15px;
                color: #f56c6c;
                font-size: 14px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                line-height: 1.6;
              "
            >
              <i class="el-icon-warning" style="margin-right: 8px;"></i>
              <strong>提示：</strong>
              <ol style="padding-left: 20px; margin-top: 8px;">
                <span>病假</span>
                <li>病假一：(病假<=62天)—按天数扣当月上下班交通费。</li>
                <li>病假二：(病假63~180天工龄10年以下)—按天数扣当月上下班交通费。扣(岗资+薪资+岗位津贴)*10%。</li>
                <li>病假二：(病假63~180天工龄10年及以上)—按天数扣当月上下班交通费。</li>
                <li>病假三：(病假>=181天工龄10年以下)—按天数扣当月上下班交通费。扣(岗资+薪资+岗位津贴)*30%。</li>
                <li>病假三：(病假>=181天工龄10年及以上)—按天数扣当月上下班交通费。扣(岗资+薪资+岗位津贴)*20%。</li>
              </ol>
              <ol style="padding-left: 20px; margin-top: 8px;">
                <span>事假</span>
                <li>事假一：累计<=20天或连续<=10,按天数扣上下班交通费。</li>
                <li>事假二：连续>10天或累计>20天,超过天数(岗资+薪资+岗位津贴)*0.7,按天数扣上下班交通费。</li>
                <li>事假三：累计>30天,超过天数(岗资+薪资+岗位津贴)*0.5,按天数扣上下班交通费。</li>
                <li>事假四：累计>60天,超过天数(岗资+薪资+岗位津贴)*0。</li>
              </ol>
            </div>
          </el-col>
        </el-row>
      </template>
    </layout4>

    <editDialog ref="editDialog" @refreshData="getPageList" />
  </div>
</template>
<script>
import hRManageApi from '@/api/hRManage'
import salaryApi from '@/api/salary'
import sysManageApi from '@/api/sysManage'
import editDialog from './components/editPage'
import employeeTableColumns from '@/views/salary/monthSalary/components/hrSalary/employeeTableColumns'

export default {
  components: {
    editDialog,
    employeeTableColumns
  },
  props: {
    employeeId: {
      type: String,
      default: '',
      required: false
    }
  },
  data() {
    return {
      listLoading: false,
      salaryId: '',
      leaveTypeList: [],
      pageList: [],
      listQuery: {
        total: 1,
        pageIndex: 1,
        pageSize: 10,
        employeeId: ''
      },
      salaryData: {}
    }
  },
  methods: {
    init() {
      this.salaryId = this.$route.query.salaryId
      if (this.salaryId) {
        this.getSalary()
        if (this.employeeId) {
          this.listQuery.employeeId = this.employeeId
        }
      } else {
        this.$notice.message('系统错误,请刷新页面重试或联系管理员', 'error')
      }
      this.loadLeaveDaysType()
      this.getPageList()
    },
    getSalary() {
      salaryApi.getSalary({ id: this.salaryId }).then(result => {
        if (result.succeed) {
          this.salaryData = result.data
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    sortChange(c) { // column, prop, order
      if (c.order === 'ascending') {
        this.listQuery.order = c.prop + ' ' + 'asc'
      } else if (c.order === 'descending') {
        this.listQuery.order = c.prop + ' ' + 'desc'
      } else {
        this.$delete(this.listQuery, 'order')
      }
      this.search()
    },
    search() {
      this.listQuery.pageIndex = 1
      this.getPageList()
    },
    getPageList() {
      this.listLoading = true
      this.listQuery.salaryId = this.salaryId
      salaryApi.querySalaryLeave(this.listQuery).then(result => {
        if (result.succeed) {
          this.pageList = result.data.datas
          this.listQuery.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      }).finally(() => {
        this.listLoading = false
      })
    },
    autoImport() {
      this.autoImportSalaryLeave()
    },
    autoImportSalaryLeave() {
      this.$confirm('确定导入病产假员工信息吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        salaryApi.autoImportSalaryLeave({ salaryId: this.salaryId }).then(result => {
          if (result.succeed) {
            this.init()
            this.$message({ message: '导入成功', type: 'success' })
          } else {
            this.$notice.resultTip(result)
          }
        }).catch(error => {
          console.log(error)
        })
      })
    },
    deleteSalaryLeave(data) {
      this.$confirm('确定删除这条记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        salaryApi.deleteSalaryLeave(data).then(result => {
          if (result.succeed) {
            this.getPageList()
            this.$notice.message('删除成功', 'success')
          } else {
            if (!result.succeed) {
              this.$notice.message('删除失败,请联系管理员', 'info')
            }
          }
        })
          .catch(error => {
            this.listLoading = false
            console.log(error)
          })
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    showDialog(row) {
      this.$refs.editDialog.salaryId = this.salaryId
      this.$refs.editDialog.initDialog(row)
    },
    loadLeaveDaysType() {
      sysManageApi.getEnumInfos({ enumType: 'SalaryLeaveType' }).then(result => {
        this.leaveTypeList = result.data.datas
      }).catch(error => {
        console.log(error)
      })
    },
    downloadexceltemplate() {
      hRManageApi.downlodaImportExcelTemplate({ type: 'importSalaryLeave' }).then(res => {
        const fileDownload = require('js-file-download')
        var filename = 'SalaryLeaveTemplate.xlsx'
        if (res.data) {
          fileDownload(res.data, filename)
        } else {
          fileDownload(res, filename)
        }
      }).catch((e) => {})
    },
    // 导入
    importExcel(params) {
      const file = params.file
      const formData = new FormData()
      salaryApi.importSalaryLeave(file, formData, this.salaryId).then(res => {
        if (res.succeed) {
          this.$message({ message: '导入成功', type: 'success' })
          this.search()
        }
      }).catch(res => {
        this.search()
      })
    },
    // 格式化折扣比例为百分比显示
    formatDiscountRatio(ratio) {
      if (ratio === null || ratio === undefined) {
        return '-'
      }
      if (ratio === 0) {
        return '0%'
      }
      return (ratio * 100).toFixed(0) + '%'
    }
  }
}
</script>
