﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;
using System.Text;
using System.Text.Json.Serialization;
using System.Xml.Serialization;
using Shinsoft.Core;

namespace Renji.JHR.Entities
{
    public partial class Employee : IPerson
    {
        [NotMapped, JsonIgnore, XmlIgnore]
        public string PwdText { get; set; }

        /// <summary>
        /// 删除时是否同时删除用户岗位对应关系
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public bool ConfirmToDelete { get; set; }

        /// <summary>
        /// 用户照片ID
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public Guid? AttachmentId { get; set; }
    }
}
