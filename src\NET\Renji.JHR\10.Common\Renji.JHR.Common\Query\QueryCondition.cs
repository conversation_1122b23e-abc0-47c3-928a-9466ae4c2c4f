﻿using Shinsoft.Core.AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Renji.JHR.Common
{
    public class QueryCondition
    {
        /// <summary>
        /// 逻辑关系：ANR , OR
        /// </summary>
        public LogicRelationships EnumLogicRelationship { get; set; }

        /// <summary>
        /// 组合查询的列名，如果是扩展属性的列名，如下格式：EmployeeHR.HireDate。暂时只考虑一对一或者多对一的导航属性。一对多的暂时不支持。
        /// </summary>
        public string? EntityColumnName { get; set; }

        /// <summary>
        /// 列的字段类型
        /// </summary>
        public string? EntityColumnType { get; set; }

        /// <summary>
        /// 查询条件：> , == , != , >= 等等
        /// </summary>
        public Entities.Operations EnumOperation { get; set; }

        /// <summary>
        /// 查询关键字
        /// </summary>
        public string? Keywords { get; set; }

        /// <summary>
        /// 内部查询
        /// </summary>
        public List<QueryCondition>? Children { get; set; }
    }
}