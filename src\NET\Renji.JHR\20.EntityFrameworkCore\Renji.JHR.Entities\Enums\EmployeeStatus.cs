﻿using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Renji.JHR.Entities
{
    public enum EmployeeStatus
    {
        None = 0,

        /// <summary>
        /// 在职
        /// </summary>
        [Description("在职")]
        InService = 1,

        /// <summary>
        /// 试用期
        /// </summary>
        [Description("试用期")]
        Probation = 2,

        /// <summary>
        /// 离职
        /// </summary>
        [Description("离职")]
        [EnumGroup("", Ordinal = 3)]
        Leave = -1,

        /// <summary>
        /// 退休
        /// </summary>
        [Description("退休")]
        [EnumGroup("", Ordinal = 4)]
        Retire = -2,

        /// <summary>
        /// 禁用
        /// </summary>
        [Description("禁用")]
        [EnumGroup("", Ordinal = 10)]
        Forbidden = -10,
    }
}
