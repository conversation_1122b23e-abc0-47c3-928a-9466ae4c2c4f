﻿using System;
using System.Collections.Generic;
using System.Text;
using Shinsoft.Core.Mail;

namespace Renji.JHR.Entities
{
    public static class MailExtender
    {
        public static Mail ToMail(this MailTemplate template, IDictionary<string, string>? datas = null)
        {
            var mail = new Mail
            {
                IsHtmlBody = template.IsBodyHtml,
                Subject = template.Subject,
                Content = template.Content
            };

            if (datas != null)
            {
                mail.Fill(datas);
            }

            return mail;
        }
    }
}