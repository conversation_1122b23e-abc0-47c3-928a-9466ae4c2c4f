(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-243402b5"],{"165c":function(t,e,n){},3509:function(t,e,n){"use strict";var i=n("7dc4"),a=n.n(i);a.a},"3e67":function(t,e,n){"use strict";var i=n("165c"),a=n.n(i);a.a},"7dc4":function(t,e,n){},"81c1":function(t,e,n){"use strict";var i=n("c124"),a=n.n(i);a.a},c124:function(t,e,n){},cbd2:function(t,e,n){"use strict";var i=n("cfe3"),a="AttendanceManage",l=new i["a"](a);e["a"]={getAttMonthShiftRecord:function(t){return l.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return l.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return l.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return l.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return l.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return l.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return l.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return l.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return l.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return l.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return l.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return l.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return l.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return l.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return l.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return l.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return l.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return l.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return l.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return l.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return l.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return l.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return l.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return l.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return l.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return l.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return l.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return l.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return l.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return l.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return l.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return l.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return l.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return l.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return l.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return l.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return l.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return l.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return l.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return l.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return l.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return l.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return l.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return l.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return l.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return l.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return l.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return l.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return l.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return l.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return l.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return l.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return l.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return l.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return l.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return l.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return l.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return l.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return l.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return l.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return l.get("GetSameDeptEmployeeWithHealthAllowance",t)}}},e0af:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("layout4",{scopedSlots:t._u([{key:"main",fn:function(){return[n("el-row",{staticClass:"filter-container",attrs:{gutter:10,type:"flex"}},[n("el-col",{attrs:{span:4}},[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"month",placeholder:"请选择月份",editable:!1,clearable:!1,"value-format":"yyyy-MM"},on:{change:function(e){return t.dateChange()}},model:{value:t.listQuery.recordMonth,callback:function(e){t.$set(t.listQuery,"recordMonth",e)},expression:"listQuery.recordMonth"}})],1),n("el-col",{attrs:{span:4}},[n("c-select-tree",{attrs:{options:t.treeData,selectplaceholder:"请选择部门",placeholder:"请输入关键字","tree-props":t.treeProps},model:{value:t.listQuery.deptId,callback:function(e){t.$set(t.listQuery,"deptId",e)},expression:"listQuery.deptId"}})],1),n("el-col",{attrs:{span:4}},[n("el-input",{attrs:{clearable:"",placeholder:"姓名"},model:{value:t.listQuery.empName,callback:function(e){t.$set(t.listQuery,"empName",e)},expression:"listQuery.empName"}})],1),n("el-col",{attrs:{span:4}},[n("el-button",{attrs:{type:"primary"},on:{click:t.handleFilter}},[t._v("查询")]),n("el-button",{attrs:{type:"primary"},on:{click:t.approve}},[t._v("审批确认")])],1)],1),n("el-table",{staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:t.tableData,stripe:"",border:"",fit:"","highlight-current-row":"","default-sort":{prop:"EmpCode",order:"descending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"},"row-class-name":t.handleRowClass},on:{"sort-change":t.sortChange}},[n("el-table-column",{attrs:{fixed:"",label:"序号",index:t.indexMethod,type:"index",align:"center"}}),n("el-table-column",{attrs:{prop:"EmpUid",label:"唯一码",align:"center",width:"85",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[n("span",[t._v(t._s(i.empUid))])]}}])}),n("el-table-column",{attrs:{prop:"EmpCode",label:"工号","header-align":"center",align:"center",width:"70px",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[n("span",[t._v(t._s(i.empCode))])]}}])}),n("el-table-column",{attrs:{prop:"EmpName",label:"姓名","header-align":"center",align:"left","min-width":"80px",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[n("span",[t._v(t._s(i.empName))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"选择确认",align:"center","header-align":"center",width:"90",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[n("el-radio-group",{on:{change:function(e){return t.handleSelectTypeChange(i)}},model:{value:i.selectType,callback:function(e){t.$set(i,"selectType",e)},expression:"row.selectType"}},[n("div",[n("el-radio",{attrs:{label:1}},[t._v("考勤员")])],1),n("div",[n("el-radio",{attrs:{label:2}},[t._v("防保科")])],1)])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"在职方式",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[n("span",[t._v(t._s(i.hireStyleName))])]}}])}),n("el-table-column",{attrs:{label:"部门","header-align":"center",align:"left","min-width":"130px"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[n("span",[t._v(t._s(i.empDept))])]}}])}),n("el-table-column",{attrs:{label:"月份",prop:"recordMonth","header-align":"center",align:"center","min-width":"90px"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[n("span",[t._v(t._s(i.recordMonth?new Date(i.recordMonth).Format("yyyy-MM"):""))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"应发公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[n("span",[t._v(t._s(0===i.generalHoliday?"":i.generalHoliday))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"剩余公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return i.historyH12=Math.abs(i.historyH12)}},model:{value:i.historyH12,callback:function(e){t.$set(i,"historyH12",t._n(e))},expression:"row.historyH12"}})]}}])}),n("el-table-column",{attrs:{prop:"name",label:"公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return i.h12=Math.abs(i.h12)}},model:{value:i.h12,callback:function(e){t.$set(i,"h12",t._n(e))},expression:"row.h12"}})]}}])}),t._v(" --\x3e "),n("el-table-column",{attrs:{prop:"name",label:"上月卫贴标准",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[n("span",[t._v(t._s(i.preMonthH1))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"本月卫贴标准",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[n("el-input",{staticClass:"numrule",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return i.h1=Math.abs(i.h1)}},model:{value:i.h1,callback:function(e){t.$set(i,"h1",t._n(e))},expression:"row.h1"}})]}}])}),n("el-table-column",{attrs:{prop:"name",label:"病假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[i.checkFilling?n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(i.checkFilling.h2?i.checkFilling.h2:"")),n("br")]):t._e(),i.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(i.prophylacticFilling.h2?i.prophylacticFilling.h2:""))]):t._e(),n("el-input",{staticClass:"numrule",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return i.h2=Math.abs(i.h2)}},model:{value:i.h2,callback:function(e){t.$set(i,"h2",t._n(e))},expression:"row.h2"}})]}}])}),n("el-table-column",{attrs:{prop:"name",label:"事假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[i.checkFilling?n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(i.checkFilling.h3?i.checkFilling.h3:"")),n("br")]):t._e(),i.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(i.prophylacticFilling.h3?i.prophylacticFilling.h3:""))]):t._e(),n("el-input",{staticClass:"numrule",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return i.h3=Math.abs(i.h3)}},model:{value:i.h3,callback:function(e){t.$set(i,"h3",t._n(e))},expression:"row.h3"}})]}}])}),n("el-table-column",{attrs:{prop:"name",label:"产假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[i.checkFilling?n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(i.checkFilling.h4?i.checkFilling.h4:"")),n("br")]):t._e(),i.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(i.prophylacticFilling.h4?i.prophylacticFilling.h4:""))]):t._e(),n("el-input",{staticClass:"numrule",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return i.h4=Math.abs(i.h4)}},model:{value:i.h4,callback:function(e){t.$set(i,"h4",t._n(e))},expression:"row.h4"}})]}}])}),n("el-table-column",{attrs:{prop:"name",label:"哺乳假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[i.checkFilling?n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(i.checkFilling.h5?i.checkFilling.h5:"")),n("br")]):t._e(),i.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(i.prophylacticFilling.h5?i.prophylacticFilling.h5:""))]):t._e(),n("el-input",{staticClass:"numrule",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return i.h5=Math.abs(i.h5)}},model:{value:i.h5,callback:function(e){t.$set(i,"h5",t._n(e))},expression:"row.h5"}})]}}])}),n("el-table-column",{attrs:{prop:"name",label:"探亲假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[i.checkFilling?n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(i.checkFilling.h6?i.checkFilling.h6:"")),n("br")]):t._e(),i.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(i.prophylacticFilling.h6?i.prophylacticFilling.h6:""))]):t._e(),n("el-input",{staticClass:"numrule",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return i.h6=Math.abs(i.h6)}},model:{value:i.h6,callback:function(e){t.$set(i,"h6",t._n(e))},expression:"row.h6"}})]}}])}),n("el-table-column",{attrs:{prop:"name",label:"计生假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[i.checkFilling?n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(i.checkFilling.h7?i.checkFilling.h7:"")),n("br")]):t._e(),i.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(i.prophylacticFilling.h7?i.prophylacticFilling.h7:""))]):t._e(),n("el-input",{staticClass:"numrule",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return i.h7=Math.abs(i.h7)}},model:{value:i.h7,callback:function(e){t.$set(i,"h7",t._n(e))},expression:"row.h7"}})]}}])}),n("el-table-column",{attrs:{prop:"name",label:"婚丧假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[i.checkFilling?n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(i.checkFilling.h8?i.checkFilling.h8:"")),n("br")]):t._e(),i.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(i.prophylacticFilling.h8?i.prophylacticFilling.h8:""))]):t._e(),n("el-input",{staticClass:"numrule",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return i.h8=Math.abs(i.h8)}},model:{value:i.h8,callback:function(e){t.$set(i,"h8",t._n(e))},expression:"row.h8"}})]}}])}),n("el-table-column",{attrs:{prop:"name",label:"脱产读研",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[i.checkFilling?n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(i.checkFilling.h9?i.checkFilling.h9:"")),n("br")]):t._e(),i.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(i.prophylacticFilling.h9?i.prophylacticFilling.h9:""))]):t._e(),n("el-input",{staticClass:"numrule",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return i.h9=Math.abs(i.h9)}},model:{value:i.h9,callback:function(e){t.$set(i,"h9",t._n(e))},expression:"row.h9"}})]}}])}),n("el-table-column",{attrs:{prop:"name",label:"因公出国",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[i.checkFilling?n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(i.checkFilling.h10?i.checkFilling.h10:"")),n("br")]):t._e(),i.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(i.prophylacticFilling.h10?i.prophylacticFilling.h10:""))]):t._e(),n("el-input",{staticClass:"numrule",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return i.h10=Math.abs(i.h10)}},model:{value:i.h10,callback:function(e){t.$set(i,"h10",t._n(e))},expression:"row.h10"}})]}}])}),n("el-table-column",{attrs:{prop:"name",label:"因私出国",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[i.checkFilling?n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(i.checkFilling.h11?i.checkFilling.h11:"")),n("br")]):t._e(),i.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(i.prophylacticFilling.h11?i.prophylacticFilling.h11:""))]):t._e(),n("el-input",{staticClass:"numrule",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return i.h11=Math.abs(i.h11)}},model:{value:i.h11,callback:function(e){t.$set(i,"h11",t._n(e))},expression:"row.h11"}})]}}])}),n("el-table-column",{attrs:{prop:"name",label:"修改人",align:"right","header-align":"center","min-width":"140px"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[i.checkFilling?n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(i.checkFilling.updator)),n("br")]):t._e(),i.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(i.prophylacticFilling.updator))]):t._e()]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[20,50,100],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.paginationChanged}})]},proxy:!0}])})],1)},a=[],l=(n("99af"),n("fb6a"),n("d3b7"),n("25f0"),n("4d90"),n("d368")),r=n("cbd2"),c=n("f9ac"),o={components:{},data:function(){return{span:4,total:0,listQuery:{pageIndex:1,pageSize:20,order:"-EmpCode",recordMonth:this.getNowTime()},headModel:{},leaveTypeList:[],holidayTypeList:[],statusList:[],listLoading:!1,treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},tableData:[],allData:[],treeExpandedKeys:[]}},created:function(){this.loadTree(),this.initLeaveTypeList(),this.initHolidayTypeList(),this.initAttDayOffRecordProphylacticDetailStatusList(),this.getList()},methods:{indexMethod:function(t){return(this.listQuery.pageIndex-1)*this.listQuery.pageSize+t+1},getNowTime:function(){var t=new Date,e=t.getFullYear(),n=t.getMonth();n=n.toString().padStart(2,"0"),"00"===n&&(e-=1,n="12");var i="".concat(e,"-").concat(n);return i},loadTree:function(){var t=this;l["a"].queryDeptByUser({}).then((function(e){t.treeData=e.data})).catch((function(t){console.log(t)}))},handleFilter:function(){this.listQuery.pageIndex=1,this.getList()},dateChange:function(){this.handleFilter()},sortChange:function(t,e,n){this.listQuery.pageIndex=1;var i="";"descending"===t.order?i="desc":"ascending"===t.order&&(i="asc"),this.listQuery.order=i?t.prop+" "+i:""},approve:function(){for(var t=this,e="",n=0;n<this.allData.length;n++)this.allData[n].selectType||(e+=n+1+",");if(e)return this.$notice.message("第"+e.substring(0,e.length-1)+"行,没有确认!","warning"),!1;this.$confirm("数据已经确认完毕，确认审批吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.headModel.details=t.allData,r["a"].approveAttDayOffRecord(t.headModel).then((function(e){e.succeed?(t.$notice.message("确认审批完成","success"),t.handleFilter()):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))}))},handleSelectTypeChange:function(t){1==t.selectType?(this.$set(t,"h2",t.checkFilling.h2),this.$set(t,"h3",t.checkFilling.h3),this.$set(t,"h4",t.checkFilling.h4),this.$set(t,"h5",t.checkFilling.h5),this.$set(t,"h6",t.checkFilling.h6),this.$set(t,"h7",t.checkFilling.h7),this.$set(t,"h8",t.checkFilling.h8),this.$set(t,"h9",t.checkFilling.h9),this.$set(t,"h10",t.checkFilling.h10),this.$set(t,"h11",t.checkFilling.h11),this.$set(t,"h12",t.checkFilling.h12),this.$set(t,"historyH12",t.checkFilling.historyH12)):(this.$set(t,"h2",t.prophylacticFilling.h2),this.$set(t,"h3",t.prophylacticFilling.h3),this.$set(t,"h4",t.prophylacticFilling.h4),this.$set(t,"h5",t.prophylacticFilling.h5),this.$set(t,"h6",t.prophylacticFilling.h6),this.$set(t,"h7",t.prophylacticFilling.h7),this.$set(t,"h8",t.prophylacticFilling.h8),this.$set(t,"h9",t.prophylacticFilling.h9),this.$set(t,"h10",t.prophylacticFilling.h10),this.$set(t,"h11",t.prophylacticFilling.h11))},getList:function(){var t=this;this.listLoading=!0,r["a"].queryPersonnelPendingApproval(this.listQuery).then((function(e){t.listLoading=!1,e.succeed?(t.allData=e.data.datas,t.total=e.data.recordCount,t.getTableData()):t.$notice.resultTip(e)})).catch((function(e){console.log(e),t.listLoading=!1}))},initLeaveTypeList:function(){var t=this,e={enumType:"LeaveType"};c["a"].getEnumInfos(e).then((function(e){t.leaveTypeList=e.data.datas})).catch((function(t){console.log(t)}))},initHolidayTypeList:function(){var t=this,e={enumType:"HolidayType"};c["a"].getEnumInfos(e).then((function(e){t.holidayTypeList=e.data.datas})).catch((function(t){console.log(t)}))},initAttDayOffRecordProphylacticDetailStatusList:function(){var t=this,e={enumType:"AttDayOffRecordProphylacticDetailStatus"};c["a"].getEnumInfos(e).then((function(e){t.statusList=e.data.datas})).catch((function(t){console.log(t)}))},paginationChanged:function(){this.getTableData()},getTableData:function(){var t=this.listQuery.pageSize,e=this.listQuery.pageIndex;this.tableData=this.allData.slice((e-1)*t,e*t)},handleRowClass:function(t,e){return t.rowIndex%2===0?"cellStyle":"stripedStyle"}}},s=o,h=(n("3509"),n("3e67"),n("81c1"),n("2877")),u=Object(h["a"])(s,i,a,!1,null,"31976a30",null);e["default"]=u.exports}}]);