﻿using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Transactions;

namespace Renji.JHR.Bll
{
    public class OrganizationBll : BaseBll
    {
        #region Constructs

        public OrganizationBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public OrganizationBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public OrganizationBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public OrganizationBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        /// <summary>
        /// 设置子级UidPath
        /// </summary>
        /// <typeparam name="T">父项类型</typeparam>
        /// <param name="parent">父项</param>
        /// <param name="descendants">依赖项</param>
        /// <param name="action">其他修改</param>
        protected void CalcChildrenPath<T>(T parent, List<T> descendants, Action<T>? action = null)
          where T : IParentUid<T>
        {
            var children = descendants.Where(p => p.ParentId == parent.ID).ToList();

            if (children.Any())
            {
                foreach (var child in children)
                {
                    child.UidPath = $"{parent.UidPath}{child.Uid}|";
                    if (action != null)
                    {
                        action(child);
                    }

                    this.CalcChildrenPath(child, descendants, action);
                }
            }
        }

        #region Department

        public BizResult<Department> AddDepartment(Department entity)
        {
            var result = new BizResult<Department>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            if (entity.Code.IsEmpty())
            {
                result.Error("编码不可以为空");
            }

            if (entity.Name.IsEmpty())
            {
                result.Error("名称不可以为空");
            }

            if (entity.HospitalAreaId.IsEmpty())
            {
                result.Error("院区必须选择");
            }

            var exists = this.GetEntities<Department>(p => p.Code == entity.Code);

            if (exists.Any(p => p.Code.Equals(entity.Code, StringComparison.OrdinalIgnoreCase)))
            {
                result.Error("编码已存在");
            }

            if (entity.ParentId.HasValue)
            {
                var parent = this.Get<Department>(entity.ParentId.Value);

                if (parent == null)
                {
                    result.Error("上级部门不存在");
                }
                else
                {
                    entity.Level = parent.Level + 1;
                    if (string.IsNullOrEmpty(entity.OrgSerial))
                    {
                        var orgSerial = MakeOrgSerial(parent);
                        if (!string.IsNullOrEmpty(orgSerial))
                        {
                            entity.OrgSerial = orgSerial;
                        }
                    }
                }
            }
            else
            {
                entity.Level = 1;
            }

            if (result.Succeed)
            {
                using (var trans = this.GetDbContext().Database.BeginTransaction())
                {
                    this.Add(entity, false);

                    this.SaveChanges();

                    if (entity.ParentId.HasValue)
                    {
                        var parent = entity.Parent;

                        if (parent != null)
                        {
                            entity.UidPath = $"{parent.UidPath}{entity.Uid}|";
                        }
                    }
                    else
                    {
                        entity.UidPath = $"|{entity.Uid}|";
                    }

                    if (!entity.Sequence.HasValue)
                    {
                        entity.Sequence = entity.Uid;
                    }

                    this.SaveChanges();

                    trans.Commit();
                }

                result.Data = entity;
            }

            return result;
        }

        public string MakeOrgSerial(Department parent)
        {
            var eneityOrgSerial = "";
            var orgParentSerial = parent.OrgSerial;
            if (!string.IsNullOrEmpty(orgParentSerial))
            {
                var orgSerials = this.GetEntities<Department>(p => p.ParentId == parent.ID).Select(c => c.OrgSerial);
                var orgSerialMax = "";
                foreach (var orgSerial in orgSerials)
                {
                    if (orgSerial == "中心实验室")
                    {
                        continue;
                    }
                    if (!string.IsNullOrEmpty(orgSerial) && string.Compare(orgSerial, orgSerialMax) == 1)
                    {
                        orgSerialMax = orgSerial;
                    }
                }

                if (string.IsNullOrEmpty(orgSerialMax))
                {
                    eneityOrgSerial = orgParentSerial + "01";
                }
                else
                {
                    var serial = orgSerialMax.Substring(orgSerialMax.Length - 2);
                    var iserial = 0;
                    if (int.TryParse(serial, out iserial))
                    {
                        eneityOrgSerial = orgParentSerial + (++iserial).ToString().PadLeft(2, '0');
                    }
                    else
                    {
                        eneityOrgSerial = orgParentSerial + "01";
                    }
                }
            }

            return eneityOrgSerial;
        }

        public BizResult<Department> UpdateDepartment(Department entity)
        {
            var result = new BizResult<Department>();

            var id = entity.ID;
            var dbEntity = this.Get<Department>(id);

            if (dbEntity == null)
            {
                result.Error("科室不存在");
            }
            else
            {
                var dbCode = dbEntity.Code;

                if (this.Update(ref entity, false))
                {
                    if (entity.Code.IsEmpty())
                    {
                        result.Error("编码不可以为空");
                    }

                    if (entity.Name.IsEmpty())
                    {
                        result.Error("名称不可以为空");
                    }

                    if (entity.HospitalAreaId.IsEmpty())
                    {
                        result.Error("院区必须选择");
                    }

                    var exists = this.GetEntities<Department>(p => p.ID != id && (p.Code == entity.Code));

                    if (exists.Any(p => p.Code.Equals(entity.Code, StringComparison.OrdinalIgnoreCase)))
                    {
                        result.Error("编码已存在");
                    }

                    //if (exists.Any(p => p.Name.Equals(entity.Name, StringComparison.OrdinalIgnoreCase)))
                    //{
                    //    result.Error("名称已存在");
                    //}

                    entity.Code = dbCode;

                    if (entity.ParentId.HasValue)
                    {
                        var parent = entity.Parent;

                        if (parent != null)
                        {
                            if (parent.UidPath.Contains($"|{entity.Uid}|"))
                            {
                                result.Error("不可以将原下级部门调整为上级部门");
                            }

                            entity.Level = parent.Level + 1;
                        }
                    }
                    else
                    {
                        entity.Level = 1;
                    }

                    if (result.Succeed)
                    {
                        result.Data = entity;

                        if (!entity.Sequence.HasValue)
                        {
                            entity.Sequence = entity.Uid;
                        }

                        this.SaveChanges();
                    }
                    else
                    {
                        this.Detach(entity);
                    }
                }
                else
                {
                    result.Data = dbEntity;
                }
            }

            return result;
        }

        public BizResult DeleteDepartment(Department entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<Department>(id);

            if (dbEntity == null)
            {
                result.Error("科室不存在");
            }
            else
            {
                //var deptIds = this.GetDeptByCurrentUser(id);
                var descendants = this.GetEntities<Department>(p => p.ID != id && p.ParentId == id);
                var emps = this.GetEntities<Employee>(e => e.DeptId == entity.ID);
                if (result.Succeed && !entity.ConfirmToDelete && (descendants.Any() || emps.Any()))
                {
                    result.Error("部门下已有子部门或员工，不可删除");
                }

                if (result.Succeed)
                {
                    result.Data = dbEntity.Uid;
                    this.Delete(entity, false);

                    foreach (var dept in descendants)
                    {
                        this.Delete(dept, false);
                    }

                    foreach (var emp in emps)
                    {
                        this.Delete(emp, false);
                    }
                    this.SaveChanges();
                }
            }

            return result;
        }

        public BizResult MoveDepartment(Department entity)
        {
            var result = new BizResult();

            if (entity.OriginDept.IsEmpty())
            {
                result.Error("移动对象不可以为空");
            }
            else
            {
                entity.ID = Guid.Parse(entity.OriginDept);
            }
            if (entity.ToParentDept.IsEmpty())
            {
                result.Error("目标上级不可以为空");
            }

            var dbEntity = this.Get<Department>(entity.ID);

            if (dbEntity == null)
            {
                result.Error("移动对象不存在");
            }
            else
            {
                entity.ParentId = Guid.Parse(entity.ToParentDept);

                if (this.Update(ref entity, false))
                {
                    var parent = entity.Parent;
                    if (parent != null)
                    {
                        if (parent.UidPath.Contains($"|{entity.Uid}|"))
                        {
                            result.Error("不可以将原下级部门调整为上级部门");
                        }
                        else
                        {
                            entity.UidPath = $"{parent.UidPath}{entity.Uid}|";
                            entity.Level = parent.Level + 1;
                        }
                    }
                    else
                    {
                        entity.UidPath = $"|{entity.Uid}|";
                        entity.Level = 1;
                    }

                    if (result.Succeed)
                    {
                        using (var trans = this.GetDbContext().Database.BeginTransaction())
                        {
                            var deptIds = this.GetDeptByCurrentUser(entity.ID);
                            var descendants = this.GetEntities<Department>(p => p.ID != entity.ID && deptIds.Contains(p.ID));
                            //var descendants = this.GetEntities<Department>(p => p.ID != entity.ID && p.UidPath.Contains($"|{entity.Uid}|"));
                            if (descendants.Any())
                            {
                                Action<Department> action = p => p.Level = p.Parent?.Level ?? 0 + 1;
                                this.CalcChildrenPath(entity, descendants, action);
                            }
                            this.SaveChanges();
                            trans.Commit();
                        }
                    }
                    else
                    {
                        this.Detach(entity);
                    }
                }
            }

            return result;
        }

        public BizResult MergeDepartment(Department entity)
        {
            var result = new BizResult();

            if (entity.OriginDept.IsEmpty())
            {
                result.Error("原部门不可以为空");
            }
            else
            {
                entity.ID = Guid.Parse(entity.OriginDept);
            }
            if (entity.MergeToDept.IsEmpty())
            {
                result.Error("合并到部门不可以为空");
            }
            var dbOriginEntity = this.Get<Department>(entity.ID);
            var dbMergeToEntity = this.Get<Department>(Guid.Parse(entity.MergeToDept));

            if (dbMergeToEntity == null)
            {
                result.Error("合并到部门不存在");
            }
            else
            {
                if (dbMergeToEntity.UidPath.Contains($"|{dbOriginEntity?.Uid}|"))
                {
                    result.Error("不可以将上级部门合并到子部门");
                }

                if (result.Succeed)
                {
                    var emps = this.GetEntities<Employee>(e => e.DeptId == entity.ID);
                    if (emps.Any())
                    {
                        foreach (var emp in emps)
                        {
                            emp.DeptId = dbMergeToEntity.ID;
                        }
                    }
                    this.Delete(entity, false);
                    this.SaveChanges();
                }
            }

            return result;
        }

        #endregion Department

        #region Position

        public BizResult<Position> AddPosition(Position entity)
        {
            var result = new BizResult<Position>();

            if (entity.Name.IsEmpty())
            {
                result.Error("名称不可以为空");
            }

            var exists = this.GetEntities<Position>(p => p.Code == entity.Code || p.Name == entity.Name);

            if (entity.Code != "" && exists.Any(p => p.Code.Equals(entity.Code, StringComparison.OrdinalIgnoreCase)))
            {
                result.Error("编码已存在");
            }

            if (exists.Any(p => p.Name.Equals(entity.Name, StringComparison.OrdinalIgnoreCase)))
            {
                result.Error("名称已存在");
            }

            if (result.Succeed)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                this.Add(entity);

                result.Data = entity;
            }

            return result;
        }

        public BizResult<Position> UpdatePosition(Position entity)
        {
            var result = new BizResult<Position>();

            var id = entity.ID;
            var dbEntity = this.Get<Position>(id);

            if (dbEntity == null)
            {
                result.Error("职位不存在");
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    if (entity.Name.IsEmpty())
                    {
                        result.Error("名称不可以为空");
                    }

                    var exists = this.GetEntities<Position>(p => p.ID != id && (p.Code == entity.Code || p.Name == entity.Name));

                    if (entity.Code != "" && exists.Any(p => p.Code.Equals(entity.Code, StringComparison.OrdinalIgnoreCase)))
                    {
                        result.Error("编码已存在");
                    }

                    if (exists.Any(p => p.Name.Equals(entity.Name, StringComparison.OrdinalIgnoreCase)))
                    {
                        result.Error("名称已存在");
                    }

                    if (result.Succeed)
                    {
                        result.Data = entity;

                        this.SaveChanges();
                    }
                    else
                    {
                        this.Detach(entity);
                    }
                }
                else
                {
                    result.Data = dbEntity;
                }
            }

            return result;
        }

        public BizResult DeletePosition(Position entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<Position>(id);

            if (dbEntity == null)
            {
                result.Error("职位不存在");
            }
            else
            {
                var positionStations = this.GetEntities<PositionStation>(ps => ps.PositionId == id);
                if (positionStations.Any(ps => ps.EmployeeStation.Count > 0))
                {
                    result.Error("有员工职位对应关系，无法删除");
                }

                if (result.Succeed && !entity.ConfirmToDelete && positionStations.Any())
                {
                    result.Confirm("职位已经被分配到现有岗位之下，是否要将分配关系同时删除");
                }
                else if (result.Succeed)
                {
                    //删除职位岗位对应关系
                    foreach (var positionStation in positionStations)
                    {
                        this.Remove(positionStation, false);
                    }

                    //逻辑删除职位
                    this.Delete(entity, false);
                    this.SaveChanges();
                }
            }

            return result;
        }

        #endregion Position

        #region Station

        public BizResult<Station> AddStation(Station entity)
        {
            var result = new BizResult<Station>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            if (entity.ParentId.IsEmpty())
            {
                entity.ParentId = null;
            }

            if (entity.Name.IsEmpty())
            {
                result.Error("名称不可以为空");
            }

            var exists = this.GetEntities<Station>(p => p.Name == entity.Name);

            if (exists.Any(p => p.Name.Equals(entity.Name, StringComparison.OrdinalIgnoreCase)))
            {
                result.Error("名称已存在");
            }

            if (result.Succeed)
            {
                using (var trans = this.GetDbContext().Database.BeginTransaction())
                {
                    this.Add(entity, false);

                    this.SaveChanges();

                    if (entity.ParentId.HasValue)
                    {
                        var parent = entity.Parent;

                        if (parent != null)
                        {
                            entity.UidPath = $"{parent.UidPath}{entity.Uid}|";
                        }
                    }
                    else
                    {
                        entity.UidPath = $"|{entity.Uid}|";
                    }

                    this.SaveChanges();

                    trans.Commit();
                }

                this.SysCache.RemoveCache<Station>();
                result.Data = entity;
            }

            return result;
        }

        public BizResult<Station> UpdateStation(Station entity)
        {
            var result = new BizResult<Station>();

            var id = entity.ID;
            var dbEntity = this.Get<Station>(id);

            if (dbEntity == null)
            {
                result.Error("职位不存在");
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    if (entity.Name.IsEmpty())
                    {
                        result.Error("名称不可以为空");
                    }

                    var exists = this.GetEntities<Station>(p => p.ID != id && (p.Name == entity.Name));

                    if (exists.Any(p => p.Name.Equals(entity.Name, StringComparison.OrdinalIgnoreCase)))
                    {
                        result.Error("名称已存在");
                    }

                    if (entity.ParentId.HasValue)
                    {
                        var parent = entity.Parent;

                        if (parent != null)
                        {
                            if (parent.UidPath.Contains($"|{entity.Uid}|"))
                            {
                                result.Error("不可以将原下级职位调整为上级职位");
                            }
                        }
                    }

                    if (result.Succeed)
                    {
                        result.Data = entity;

                        this.SaveChanges();
                        this.SysCache.RemoveCache<Station>();
                    }
                    else
                    {
                        this.Detach(entity);
                    }
                }
                else
                {
                    result.Data = dbEntity;
                }
            }

            return result;
        }

        public BizResult DeleteStation(Station entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<Station>(id);

            if (dbEntity == null)
            {
                result.Error("岗位不存在");
            }
            else
            {
                var descendants = this.GetEntities<Station>(p => p.ID != id && p.ParentId == id);
                var emps = this.GetEntities<EmployeeStation>(e => e.StationId == entity.ID);
                if (result.Succeed && !entity.ConfirmToDelete && (descendants.Any() || emps.Any()))
                {
                    result.Error("岗位下已有子岗位或员工，不可删除");
                }

                if (result.Succeed)
                {
                    result.Data = dbEntity.Uid;
                    this.Delete(entity, false);

                    foreach (var dept in descendants)
                    {
                        this.Delete(dept, false);
                    }

                    foreach (var emp in emps)
                    {
                        this.Delete(emp, false);
                    }
                    this.SaveChanges();
                    this.SysCache.RemoveCache<Station>();
                }
            }

            return result;
        }

        #endregion Station

        #region PositionStation

        public BizResult AllocatePosition(List<PositionStation> entities)
        {
            var result = new BizResult();
            var stationId = entities.First().StationId;
            var station = this.Get<Station>(stationId);
            if (station != null)
            {
                var positionIds = entities.Select(ps => ps.PositionId);
                if (positionIds.Count() > 0)
                {
                    var postions = this.GetEntities<Position>(p => positionIds.Contains(p.ID));

                    var dbEntities = this.GetEntities<PositionStation>(ps => ps.StationId == stationId);
                    if (dbEntities.Any())
                    {
                        /**
                        三种情况：
                        1.原来包括现在也包括
                        2.原来没有包括但现在包括
                        3.原来包括但现在不包括
                         */
                        var dbEntitiesPositionIds = dbEntities.Select(d => d.PositionId);
                        var notExist = entities.Where(e => !dbEntitiesPositionIds.Contains(e.PositionId));
                        var oldDbEntities = dbEntities.Where(db => !positionIds.Contains(db.PositionId));

                        if (result.Succeed)
                        {
                            //删除没有empStation关联的职位岗位对应关系
                            foreach (var entity in oldDbEntities.Where(ps => ps.EmployeeStation.Count == 0))
                            {
                                this.Remove(entity, false);
                            }
                            //添加新的对应关系
                            foreach (var entity in notExist)
                            {
                                entity.ID = CombGuid.NewGuid();
                                entity.PositionName = postions.First(p => p.ID == entity.PositionId).Name;
                                entity.StationName = station.Name;
                                this.Add<PositionStation>(entity, false);
                            }
                            this.SaveChanges();

                            //原来包括但现在不包括的，要检查是否有empStation关联，有就不能删
                            if (oldDbEntities.Any(ps => ps.EmployeeStation.Count > 0))
                            {
                                var names = oldDbEntities.Where(ps => ps.EmployeeStation.Count > 0)
                                                           .Select(ps => ps.PositionName);
                                var infoPositionName = string.Join(",", names);

                                result.Info($"{infoPositionName}有员工职位对应关系，无法取消分配,其他分配成功！");
                            }
                        }
                    }
                    else
                    {
                        foreach (var entity in entities)
                        {
                            entity.ID = CombGuid.NewGuid();
                            entity.PositionName = postions.First(p => p.ID == entity.PositionId).Name;
                            entity.StationName = station.Name;
                            this.Add<PositionStation>(entity, false);
                        }
                        this.SaveChanges();
                    }
                }
                else
                {
                    ClearPositionStation(stationId, ref result);
                }
            }
            else
            {
                result.Error("岗位不存在！");
            }

            return result;
        }

        public BizResult DeletePositionStation(PositionStation entity)
        {
            var result = new BizResult();
            var dbEntity = this.GetEntity<PositionStation>(ps => ps.StationId == entity.StationId && ps.PositionId == entity.PositionId);

            if (dbEntity != null)
            {
                if (dbEntity.EmployeeStation.Any())
                {
                    result.Error("有员工职位对应关系，无法删除");
                }
                if (result.Succeed)
                {
                    this.Remove(dbEntity, false);
                    this.SaveChanges();
                }
            }

            return result;
        }

        /// <summary>
        /// 移除所有关联职位
        /// </summary>
        /// <param name="stationId"></param>
        /// <param name="result"></param>
        public void ClearPositionStation(Guid stationId, ref BizResult result)
        {
            var dbEntities = this.GetEntities<PositionStation>(ps => ps.StationId == stationId);

            foreach (var entity in dbEntities.Where(ps => ps.EmployeeStation.Count == 0))
            {
                this.Delete(entity, false);
            }
            if (result.Succeed)
            {
                if (dbEntities.Any(ps => ps.EmployeeStation.Count > 0))
                {
                    var names = dbEntities.Where(ps => ps.EmployeeStation.Count > 0)
                                               .Select(ps => ps.PositionName);
                    var infoPositionName = string.Join(",", names);

                    result.Info($"{infoPositionName}有员工职位对应关系，无法取消分配,其他分配成功！");
                }
                this.SaveChanges();
            }
        }

        public BizResult<Information> AddInformation(Information entity)
        {
            var result = new BizResult<Information>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        #endregion PositionStation

        #region 工龄津贴

        public BizResult<Seniority> AddSeniority(Seniority entity)
        {
            var result = new BizResult<Seniority>();

            if (entity.WorkAge <= 0)
            {
                result.Error("工龄必须大于零");
            }

            if (entity.WorkAllowance < 0)
            {
                result.Error("工作量津贴必须大于等于零");
            }

            if (entity.NursingAgeWage < 0)
            {
                result.Error("护龄工资必须大于等于零");
            }
            if (result.Succeed)
            {
                var exist = this.GetEntity<Seniority>(p => p.WorkAge == entity.WorkAge);

                if (exist != null)
                {
                    result.Error("已存在相同的工龄");
                }
            }

            if (result.Succeed)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity = this.Add(entity);
                this.SysCache.RemoveCache<Seniority>();
                result.Data = entity;
            }

            return result;
        }

        public BizResult<Seniority> UpdateSeniority(Seniority entity)
        {
            var result = new BizResult<Seniority>();

            var dbEntity = this.Get<Seniority>(entity.ID);

            if (dbEntity == null)
            {
                result.Error("工龄津贴不存在");
            }
            else
            {
                if (entity.WorkAge <= 0)
                {
                    result.Error("工龄必须大于零");
                }

                if (entity.WorkAllowance < 0)
                {
                    result.Error("工作量津贴必须大于等于零");
                }

                if (entity.NursingAgeWage < 0)
                {
                    result.Error("护龄工资必须大于等于零");
                }

                if (result.Succeed)
                {
                    this.Update(ref entity);
                    this.SysCache.RemoveCache<Seniority>();
                    result.Data = entity;
                }
            }

            return result;
        }

        /// <summary>
        /// 导入工龄津贴
        /// </summary>
        /// <param name="departmentDatas"></param>
        /// <returns></returns>
        public BizResult ImportSeniority(List<Seniority> senioritys)
        {
            var result = new BizResult();
            var dbSenioritys = this.GetEntities<Seniority>();

            senioritys.ForEach(seniority =>
            {
                var dbSeniority = dbSenioritys.Find(p => p.WorkAge == seniority.WorkAge);
                if (dbSeniority != null)
                {
                    dbSeniority.WorkAge = seniority.WorkAge;
                    dbSeniority.WorkAllowance = seniority.WorkAllowance;
                    dbSeniority.NursingAgeWage = seniority.NursingAgeWage;
                    dbSeniority.Memo = seniority.Memo;

                    this.Update(dbSeniority, false);
                }
                else
                {
                    this.Add(seniority, false);
                }
            });

            this.SaveChanges();
            this.SysCache.RemoveCache<Seniority>();
            return result;
        }

        #endregion 工龄津贴

        #region 岗位津贴

        public BizResult<StationAllowance> AddStationAllowance(StationAllowance entity)
        {
            var result = new BizResult<StationAllowance>();

            if (entity.ParentStationId.IsEmpty())
            {
                result.Error("岗位大类不能为空");
            }

            if (entity.StationId.IsEmpty())
            {
                result.Error("岗位级别不能为空");
            }

            if (entity.WorkAge <= 0)
            {
                result.Error("工龄不能小于等于0");
            }

            if (entity.Allowance < 0)
            {
                result.Error("岗位津贴必须大于等于零");
            }
            if (result.Succeed)
            {
                var exist = this.GetEntity<StationAllowance>(p => p.ParentStationId == entity.ParentStationId && p.StationId == entity.StationId && p.WorkAge == entity.WorkAge);

                if (exist != null)
                {
                    result.Error("已存在相同的岗位津贴");
                }
            }

            if (result.Succeed)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity = this.Add(entity);
                this.SysCache.RemoveCache<StationAllowance>();
                result.Data = entity;
            }

            return result;
        }

        public BizResult<StationAllowance> UpdateStationAllowance(StationAllowance entity)
        {
            var result = new BizResult<StationAllowance>();

            var dbEntity = this.Get<StationAllowance>(entity.ID);

            if (dbEntity == null)
            {
                result.Error("岗位津贴不存在");
            }
            else
            {
                if (entity.ParentStationId.IsEmpty())
                {
                    result.Error("岗位大类不能为空");
                }

                if (entity.StationId.IsEmpty())
                {
                    result.Error("岗位级别不能为空");
                }

                if (entity.WorkAge <= 0)
                {
                    result.Error("工龄不能小于等于0");
                }

                if (entity.Allowance < 0)
                {
                    result.Error("岗位津贴必须大于等于零");
                }

                if (result.Succeed)
                {
                    this.Update(ref entity);
                    this.SysCache.RemoveCache<StationAllowance>();
                    result.Data = entity;
                }
            }

            return result;
        }

        /// <summary>
        /// 导入工龄津贴
        /// </summary>
        /// <param name="departmentDatas"></param>
        /// <returns></returns>
        public BizResult ImportStationAllowance(List<StationAllowance> stationAllowances)
        {
            var result = new BizResult();
            var dbStationAllowances = this.GetEntities<StationAllowance>();

            stationAllowances.ForEach(StationAllowance =>
            {
                var dbStationAllowance = dbStationAllowances.Find(p => p.ParentStationId == StationAllowance.ParentStationId && p.StationId == StationAllowance.StationId && p.WorkAge == StationAllowance.WorkAge);
                if (dbStationAllowance != null)
                {
                    dbStationAllowance.ParentStationId = StationAllowance.ParentStationId;
                    dbStationAllowance.StationId = StationAllowance.StationId;
                    dbStationAllowance.WorkAge = StationAllowance.WorkAge;
                    dbStationAllowance.Allowance = StationAllowance.Allowance;

                    this.Update(dbStationAllowance, false);
                }
                else
                {
                    this.Add(StationAllowance, false);
                }
            });

            this.SaveChanges();
            this.SysCache.RemoveCache<StationAllowance>();

            return result;
        }

        #endregion 岗位津贴

        #region 薪级工资

        public BizResult<SalaryScale> AddSalaryScale(SalaryScale entity)
        {
            var result = new BizResult<SalaryScale>();

            if (entity.StationId.IsEmpty())
            {
                result.Error("岗位大类不能为空");
            }

            if (entity.Scale <= 0)
            {
                result.Error("薪级必须大于零");
            }

            if (entity.Wage <= 0)
            {
                result.Error("薪级工资必须大于零");
            }

            if (result.Succeed)
            {
                var exist = this.GetEntity<SalaryScale>(p => p.StationId == entity.StationId && p.Scale == entity.Scale);

                if (exist != null)
                {
                    result.Error("已存在相同的岗位大类、薪级");
                }
            }

            if (result.Succeed)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity = this.Add(entity);
                this.SysCache.RemoveCache<SalaryScale>();
                result.Data = entity;
            }

            return result;
        }

        public BizResult<SalaryScale> UpdateSalaryScale(SalaryScale entity)
        {
            var result = new BizResult<SalaryScale>();

            var dbEntity = this.Get<SalaryScale>(entity.ID);

            if (dbEntity == null)
            {
                result.Error("薪级工资不存在");
            }
            else
            {
                if (entity.StationId.IsEmpty())
                {
                    result.Error("岗位大类不能为空");
                }

                if (entity.Scale <= 0)
                {
                    result.Error("薪级必须大于零");
                }

                if (entity.Wage <= 0)
                {
                    result.Error("薪级工资必须大于零");
                }

                if (result.Succeed)
                {
                    // 不更新业务类型与ID
                    entity.RemoveChangedColumn(SalaryScale.Columns.StationId);
                    entity.RemoveChangedColumn(SalaryScale.Columns.Scale);

                    this.Update(ref entity);
                    this.SysCache.RemoveCache<SalaryScale>();
                    result.Data = entity;
                }
            }

            return result;
        }

        public BizResult DeleteSalaryScale(Guid id)
        {
            var result = new BizResult();
            var dbEntity = this.Get<SalaryScale>(id);

            if (dbEntity == null)
            {
                result.Error("薪级工资不存在");
            }
            else
            {
                //TODO 判断不允许删除条件
                var employeeWage = this.GetEntity<EmployeeWage>(p => p.SalaryScaleId == id);
                if (employeeWage != null)
                {
                    result.Error("薪级工资正在被使用，不允许删除");
                }

                if (result.Succeed)
                {
                    this.Delete(dbEntity);
                    this.SysCache.RemoveCache<SalaryScale>();
                }
            }

            return result;
        }

        /// <summary>
        /// 导入薪级工资
        /// </summary>
        /// <param name="departmentDatas"></param>
        /// <returns></returns>
        public BizResult ImportSalaryScale(List<SalaryScale> salaryScales)
        {
            var result = new BizResult();
            var dbSalaryScales = this.GetEntities<SalaryScale>();

            salaryScales.ForEach(salaryScale =>
            {
                var dbSalaryScale = dbSalaryScales.Find(p => p.StationId == salaryScale.StationId && p.Scale == salaryScale.Scale);
                if (dbSalaryScale != null)
                {
                    dbSalaryScale.Scale = salaryScale.Scale;
                    dbSalaryScale.Wage = salaryScale.Wage;
                    dbSalaryScale.Memo = salaryScale.Memo;

                    this.Update(dbSalaryScale, false);
                }
                else
                {
                    this.Add(salaryScale, false);
                }
            });

            this.SaveChanges();
            this.SysCache.RemoveCache<SalaryScale>();

            return result;
        }

        #endregion 薪级工资

        #region 电话费

        public BizResult<TelephoneFee> AddTelephoneFee(TelephoneFee entity)
        {
            var result = new BizResult<TelephoneFee>();

            if (entity.Name.IsEmpty())
            {
                result.Error("名称必须输入");
            }

            if (entity.Allowance < 0)
            {
                result.Error("津贴必须大于等于零");
            }

            if (result.Succeed)
            {
                var exist = this.GetEntity<TelephoneFee>(p => p.Name == entity.Name);

                if (exist != null)
                {
                    result.Error("已存在相同的名称");
                }
            }

            if (result.Succeed)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity = this.Add(entity);
                this.SysCache.RemoveCache<TelephoneFee>();
                result.Data = entity;
            }

            return result;
        }

        public BizResult<TelephoneFee> UpdateTelephoneFee(TelephoneFee entity)
        {
            var result = new BizResult<TelephoneFee>();

            var dbEntity = this.Get<TelephoneFee>(entity.ID);

            if (dbEntity == null)
            {
                result.Error("电话费不存在");
            }
            else
            {
                if (entity.Name.IsEmpty())
                {
                    result.Error("名称必须输入");
                }

                if (entity.Allowance < 0)
                {
                    result.Error("津贴必须大于等于零");
                }

                if (result.Succeed)
                {
                    var exist = this.GetEntity<TelephoneFee>(p => p.Name == entity.Name && p.ID != entity.ID);

                    if (exist != null)
                    {
                        result.Error("已存在相同的名称");
                    }
                }

                if (result.Succeed)
                {
                    this.Update(ref entity);
                    this.SysCache.RemoveCache<TelephoneFee>();
                    result.Data = entity;
                }
            }

            return result;
        }

        public BizResult DeleteTelephoneFee(TelephoneFee entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<TelephoneFee>(id);

            if (dbEntity == null)
            {
                result.Error("电话费不存在");
            }
            else
            {
                //var positionStations = this.GetEntities<PositionStation>(ps => ps.PositionId == id);
                //if (positionStations.Any(ps => ps.EmployeeStation.Count > 0))
                //{
                //    result.Error("电话费对应关系，无法删除");
                //}

                if (result.Succeed)
                {
                    //逻辑删除电话费
                    this.Delete(entity, false);
                    this.SaveChanges();
                    this.SysCache.RemoveCache<TelephoneFee>();
                }
            }

            return result;
        }

        #endregion 电话费

        #region 公车补贴

        public BizResult<CarSubsidy> AddCarSubsidy(CarSubsidy entity)
        {
            var result = new BizResult<CarSubsidy>();

            if (entity.Name.IsEmpty())
            {
                result.Error("名称必须输入");
            }

            if (entity.Allowance < 0)
            {
                result.Error("津贴必须大于等于零");
            }

            if (result.Succeed)
            {
                var exist = this.GetEntity<CarSubsidy>(p => p.Name == entity.Name);

                if (exist != null)
                {
                    result.Error("已存在相同的名称");
                }
            }

            if (result.Succeed)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity = this.Add(entity);
                this.SysCache.RemoveCache<CarSubsidy>();
                result.Data = entity;
            }

            return result;
        }

        public BizResult<CarSubsidy> UpdateCarSubsidy(CarSubsidy entity)
        {
            var result = new BizResult<CarSubsidy>();

            var dbEntity = this.Get<CarSubsidy>(entity.ID);

            if (dbEntity == null)
            {
                result.Error("公车补贴不存在");
            }
            else
            {
                if (entity.Name.IsEmpty())
                {
                    result.Error("名称必须输入");
                }

                if (entity.Allowance < 0)
                {
                    result.Error("津贴必须大于等于零");
                }

                if (result.Succeed)
                {
                    var exist = this.GetEntity<CarSubsidy>(p => p.Name == entity.Name && p.ID != entity.ID);

                    if (exist != null)
                    {
                        result.Error("已存在相同的名称");
                    }
                }

                if (result.Succeed)
                {
                    this.Update(ref entity);
                    this.SysCache.RemoveCache<CarSubsidy>();
                    result.Data = entity;
                }
            }

            return result;
        }

        public BizResult DeleteCarSubsidy(CarSubsidy entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<CarSubsidy>(id);

            if (dbEntity == null)
            {
                result.Error("公车补贴不存在");
            }
            else
            {
                //var positionStations = this.GetEntities<PositionStation>(ps => ps.PositionId == id);
                //if (positionStations.Any(ps => ps.EmployeeStation.Count > 0))
                //{
                //    result.Error("公车补贴对应关系，无法删除");
                //}

                if (result.Succeed)
                {
                    //逻辑删除公车补贴
                    this.Delete(entity, false);
                    this.SaveChanges();
                    this.SysCache.RemoveCache<CarSubsidy>();
                }
            }

            return result;
        }

        #endregion 公车补贴
    }
}