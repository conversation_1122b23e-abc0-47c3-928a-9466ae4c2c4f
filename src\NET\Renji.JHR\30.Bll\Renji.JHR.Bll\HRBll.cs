﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using NPOI.SS.Formula.Functions;
using Renji.JHR.Common.Configration;
using Renji.JHR.Common.Utility;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Transactions;


namespace Renji.JHR.Bll
{
    public class HRBll : BaseBll
    {
        #region Constructs

        public HRBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public HRBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public HRBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public HRBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        #region Employee

        public BizResult<Employee> AddEmployee(Employee entity, Guid? deptPrincipalID, ref Guid historyId)
        {
            var result = new BizResult<Employee>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            if (entity.AttachmentId.HasValue)
            {
                var attachment = this.Get<Attachment>(entity.AttachmentId.Value);

                UpdateBirthDay(entity);

                result.Data = this.Add(entity, false);

                if (attachment != null)
                {
                    attachment.ObjectId = entity.ID.ToString();
                    attachment.ObjectType = JHR.Common.Consts.Employee.EmployeeTableName;

                    this.Update(attachment, false);
                }
            }
            else
            {
                if (result.Succeed)
                {
                    entity.EmpCode = entity.EmpCode.ToLower();
                    entity.UUID = entity.ID.ToString().ToLower();
                    entity.EmpCodeUpdate = true;
                    result.Data = this.Add(entity);
                }
            }

                //部分负责人
                historyId = SaveDeptEmp(entity.ID, deptPrincipalID, false);


            this.SaveChanges();
            return result;
        }

        private void UpdateBirthDay(Employee entity)
        {
            if (entity.DocumentTypeId.HasValue && !entity.IdentityNumber.IsEmpty())
            {
                var dbDict = this.Get<Dict>(entity.DocumentTypeId);
                if (dbDict != null && dbDict.Code == Renji.JHR.Common.Consts.Dicts.DocumentTypeIDNum && entity.IdentityNumber != null && entity.IdentityNumber.Length > 12)
                {
                    var year = entity.IdentityNumber.Substring(6, 4);
                    var month = entity.IdentityNumber.Substring(10, 2);
                    var day = entity.IdentityNumber.Substring(12, 2);

                    var birthday = string.Format("{0}-{1}-{2}", year, month, day);

                    if (entity.Birthday != birthday)
                        entity.Birthday = birthday;
                }
            }
        }

        public BizResult<Employee> UpdateEmployee(Employee entity, Guid? deptPrincipalID, ref Guid historyId)
        {
            var result = new BizResult<Employee>();

            var id = entity.ID;
            var dbEntity = this.Get<Employee>(id);

            if (dbEntity == null)
            {
                result.Error("员工不存在");
            }
            else
            {
                var oldAttachment = this.GetEntity<Attachment>(p => p.ObjectId.ToLower() == entity.ID.ToString().ToLower() && p.ObjectType == JHR.Common.Consts.Employee.EmployeeTableName);

                Attachment? newAttachment = null;
                if (entity.AttachmentId.HasValue)
                {
                    newAttachment = this.Get<Attachment>(entity.AttachmentId.Value);
                }
                if (string.IsNullOrEmpty(dbEntity.UUID))
                {
                    entity.UUID = entity.ID.ToString().ToLower();
                }
                if (dbEntity.EmpCodeUpdate.HasValue && dbEntity.EmpCodeUpdate.Value)
                {
                    entity.EmpCode = entity.EmpCode.ToLower();
                }
                UpdateBirthDay(entity);
                result.Data = this.Update(entity, false);

                // 旧附件不为空，删除旧附件关联
                if (oldAttachment != null)
                {
                    //附件为空 || 附件不为空，并且附件有修改
                    if (!entity.AttachmentId.HasValue
                        || entity.AttachmentId.HasValue && newAttachment != null && newAttachment.ObjectId.ToLower() != entity.ID.ToString().ToLower())
                    {
                        oldAttachment.ObjectId = string.Empty;
                        this.Update(oldAttachment, false);
                    }
                }

                //附件不为空，并且附件有修改
                if (newAttachment != null && newAttachment.ObjectId.ToLower() != entity.ID.ToString().ToLower())
                {
                    newAttachment.ObjectId = entity.ID.ToString();
                    newAttachment.ObjectType = JHR.Common.Consts.Employee.EmployeeTableName;

                    this.Update(newAttachment, false);
                }

                    //部分负责人
                    historyId = SaveDeptEmp(entity.ID, deptPrincipalID, false);


                this.SaveChanges();
            }

            return result;
        }

        /// <summary>
        /// 删除员工
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<Employee> DeleteEmployee(Employee entity)
        {
            var result = new BizResult<Employee>();

            var id = entity.ID;
            var dbEntity = this.Get<Employee>(id);

            if (dbEntity == null)
            {
                result.Error("员工不存在");
            }
            else
            {
                //逻辑删除
                result.Data = this.Delete(entity, false);

                //删除人事信息
                var dbHREntity = this.Get<EmployeeHR>(id);
                if (dbHREntity != null)
                {
                    this.Delete(dbHREntity, false);
                }

                //删除聘任职务、聘任职称
                var employeeStationEntities = this.GetQuery<EmployeeStation>(p => p.EmployeeId == id);
                foreach (var item in employeeStationEntities)
                {
                    this.Delete(item, false);
                }
                //删除职称资格
                var employeeCertifyEntities = this.GetQuery<EmployeeCertify>(p => p.EmployeeId == id);
                foreach (var item in employeeCertifyEntities)
                {
                    this.Delete(item, false);
                }
                //删除学习经历
                var employeeEducationEntities = this.GetQuery<EmployeeEducation>(p => p.EmployeeId == id);
                foreach (var item in employeeEducationEntities)
                {
                    this.Delete(item, false);
                }
                //删除工作经历
                var employeeWorkEntities = this.GetQuery<EmployeeWork>(p => p.EmployeeId == id);
                foreach (var item in employeeWorkEntities)
                {
                    this.Delete(item, false);
                }
                //删除出国
                var employeeAbroadInfoEntities = this.GetQuery<EmployeeAbroadInfo>(p => p.EmployeeId == id);
                foreach (var item in employeeAbroadInfoEntities)
                {
                    this.Delete(item, false);
                }
                //删除合同
                var employeeContractEntities = this.GetQuery<EmployeeContract>(p => p.EmployeeId == id);
                foreach (var item in employeeContractEntities)
                {
                    this.Delete(item, false);
                    //TODO: Zhoujia 删除合同文件
                }
                //删除培养计划
                var employeeTrainEntities = this.GetQuery<EmployeeTrain>(p => p.EmployeeId == id);
                foreach (var item in employeeTrainEntities)
                {
                    this.Delete(item, false);
                }
                //删除考核
                var employeeAssessmentEntities = this.GetQuery<EmployeeAssessment>(p => p.EmployeeId == id);
                foreach (var item in employeeAssessmentEntities)
                {
                    this.Delete(item, false);
                }
                var deductWorkingAgeEntities = this.GetQuery<EmployeeDeductWorkingAge>(p => p.EmployeeId == id);
                foreach (var item in deductWorkingAgeEntities)
                {
                    this.Delete(item, false);
                }
                //删除奖惩
                var employeeIncentiveEntities = this.GetQuery<EmployeeIncentive>(p => p.EmployeeId == id);
                foreach (var item in employeeIncentiveEntities)
                {
                    this.Delete(item, false);
                }
                //删除健康信息
                var employeeHealthEntity = this.Get<EmployeeHealth>(id);
                if (employeeHealthEntity != null)
                {
                    this.Delete(employeeHealthEntity, false);
                }
                //删除医疗
                var employeeAccidentEntities = this.GetQuery<EmployeeAccident>(p => p.EmployeeId == id);
                foreach (var item in employeeAccidentEntities)
                {
                    this.Delete(item, false);
                }
                //删除教学信息
                var employeeTeachEntities = this.GetQuery<EmployeeTeach>(p => p.EmployeeId == id);
                foreach (var item in employeeTeachEntities)
                {
                    this.Delete(item, false);
                }
                //删除科研信息
                //论文
                var employeeArticleEntities = this.GetQuery<EmployeeArticle>(p => p.EmployeeId == id);
                foreach (var item in employeeArticleEntities)
                {
                    this.Delete(item, false);
                }
                //课题信息
                var employeeClassEntities = this.GetQuery<EmployeeClass>(p => p.EmployeeId == id);
                foreach (var item in employeeClassEntities)
                {
                    this.Delete(item, false);
                }
                //专利信息
                var employeePatentEntities = this.GetQuery<EmployeePatent>(p => p.EmployeeId == id);
                foreach (var item in employeePatentEntities)
                {
                    this.Delete(item, false);
                }
                //导师信息
                var employeeTeacherEntities = this.GetQuery<EmployeeTeacher>(p => p.EmployeeId == id);
                foreach (var item in employeeTeacherEntities)
                {
                    this.Delete(item, false);
                }
                //获奖信息
                var employeeAwardEntities = this.GetQuery<EmployeeAward>(p => p.EmployeeId == id);
                foreach (var item in employeeAwardEntities)
                {
                    this.Delete(item, false);
                }
                //删除社会关系
                var employeeRelationEntities = this.GetQuery<EmployeeRelation>(p => p.EmployeeId == id);
                foreach (var item in employeeRelationEntities)
                {
                    this.Delete(item, false);
                }
                this.SaveChanges();
            }
            return result;
        }

        /// <summary>
        /// 验证员工码是否存在
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public bool IsEmpCodeExists(Employee model)
        {
            var emp = new Employee();
            if (model.ID == Guid.Empty)
            {
                emp = this.GetEntity<Employee>(x => !x.Deleted && x.EmpCode == model.EmpCode);
            }
            else
            {
                emp = this.GetEntity<Employee>(x => !x.Deleted && x.EmpCode == model.EmpCode && x.ID != model.ID);
            }

            /*
            var exps = this.NewExps<Employee>();

            //在职状态
            exps.And(p => p.EmpCode == model.EmpCode );

            if (model.ID != Guid.Empty)
            {
                exps.And(p => p.ID != model.ID);
            }

            var entity = this.GetEntities(exps).FirstOrDefault();

            return entity != null;
            */
            return emp != null;
        }

        public Employee? GetEmpByID(Guid id)
        {
            return this.DbContext.Employee.Find(id);
        }

        public bool IdentityNumberRepeat(Employee model)
        {
            var exps = this.NewExps<Employee>();
            if (model.ID != Guid.Empty)
            {
                exps.Add(x => x.ID != model.ID);
            }
            exps.Add(i => i.IdentityNumber == model.IdentityNumber);
            var entity = this.BizRepo.GetEntity<Employee>(exps);
            return entity != null;
        }

        #endregion Employee

        /// <summary>
        /// 根据父字典名称查询字典项
        /// </summary>
        /// <param name="parentCode"></param>
        /// <returns></returns>
        public List<Dict> QueryDictByParentCode(string parentCode)
        {
            var exps = this.NewExps<Dict>();

            exps.And(p => p.Parent != null && p.Parent.Code == parentCode);

            var entities = this.GetEntities<Dict>(exps, "Code asc,Name asc");

            return entities;
        }

        /// <summary>
        /// 根据父字典名称查询字典项
        /// </summary>
        /// <param name="parentName"></param>
        /// <returns></returns>
        public List<Dict> QueryDictByCode(List<string> code, string parentName)
        {
            var exps = this.NewExps<Dict>();

            exps.And(p => code.Contains(p.Code) && p.Parent != null && p.Parent.Name == parentName);

            var entities = this.GetEntities<Dict>(exps, "Code asc,Name asc");

            return entities;
        }

        /// <summary>
        /// 获取部门
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Department? GetDepartment(Guid id)
        {
            var entity = this.BizRepo.Get<Department>(id);
            return entity;
        }

        #region 组合查询

        /// <summary>
        /// 根据模块名查询列
        /// </summary>
        /// <param name="moduleName"></param>
        /// <returns></returns>
        public List<QuerySettingColumn> QuerySettingColumnsByModuleName(string moduleName)
        {
            var exps = this.NewExps<QuerySettingColumn>();

            exps.And(p => p.QuerySetting.ModuleCode == moduleName);

            var entities = this.GetEntities<QuerySettingColumn>(exps, "Sort +");

            return entities;
        }

        /// <summary>
        /// 根据字段类型查询运算符
        /// </summary>
        /// <param name="columnType"></param>
        /// <returns></returns>
        public List<DataTypeOperation> QueryOperationByColumnType(string columnType)
        {
            var exps = this.NewExps<DataTypeOperation>();

            exps.And(p => p.DataType.EntityColumnType == columnType);

            var entities = this.GetEntities<DataTypeOperation>(exps, "Sort +");

            return entities;
        }

        public BizResult<EmployeeQuerySetting> AddEmployeeQuerySetting(EmployeeQuerySetting entity)
        {
            var result = new BizResult<EmployeeQuerySetting>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            var existes = this.GetEntities<EmployeeQuerySetting>(p => p.UserId == entity.UserId && p.ModuleCode == entity.ModuleCode && p.Name == entity.Name);

            if (existes.Any())
            {
                result.Error("名称不能重复！");
                result.Data = entity;
                return result;
            }

            var items = entity.EmployeeQuerySettingItem.ToList();
            entity.EmployeeQuerySettingItem = null;
            result.Data = this.Add(entity);
            this.AddEmployeeQuerySettingItems(items, entity.ID, null);

            this.SaveChanges();
            return result;
        }

        private void AddEmployeeQuerySettingItems(List<EmployeeQuerySettingItem> entities, Guid employeeQuerySettingId, EmployeeQuerySettingItem? parent = null)
        {
            foreach (var item in entities)
            {
                item.EmployeeQuerySettingId = employeeQuerySettingId;
                if (item.ID.IsEmpty())
                {
                    item.ID = CombGuid.NewGuid();
                }
                if (parent != null)
                {
                    item.ParentId = parent.ID;
                }
                List<EmployeeQuerySettingItem>? children = null;
                if (item.Children != null && item.Children.Any())
                {
                    children = item.Children.ToList();
                    item.Children = null;
                }
                this.Add(item);
                if (children != null && children.Any())
                {
                    this.AddEmployeeQuerySettingItems(children, employeeQuerySettingId, item);
                }
            }
        }

        public BizResult DeleteEmployeeQuerySetting(EmployeeQuerySetting entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeQuerySetting>(id);

            if (dbEntity == null)
            {
                result.Error("员工查询设置不存在");
            }
            else
            {
                this.Delete(entity, false);

                var entities = this.GetQuery<EmployeeQuerySettingItem>(p => p.EmployeeQuerySettingId == id).ToList();
                foreach (var item in entities)
                {
                    this.Delete(item, false);
                }
                this.SaveChanges();
            }
            return result;
        }

        #endregion 组合查询

        #region EmployeeHR

        public EmployeeHR? GetEmpHRByID(Guid id)
        {
            return this.GetEntity<EmployeeHR>(path: nameof(EmployeeHR.EmpStatus), predicate: s => s.ID == id);
        }

        public BizResult<EmployeeHR> SaveEmployeeHR(EmployeeHR entity, ref int isAdd)
        {
            var result = new BizResult<EmployeeHR>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeHR>(id);

            if (dbEntity == null)
            {
                dbEntity = AddEmployeeHR(entity);
                isAdd = 1;
            }
            else
            {
                // 排除政治面貌相关字段更新
                entity.RemoveChangedColumn(EmployeeHR.Columns.PartyId);
                entity.RemoveChangedColumn(EmployeeHR.Columns.PartyJoiningTime);
                entity.RemoveChangedColumn(EmployeeHR.Columns.ProbationTime);
                entity.RemoveChangedColumn(EmployeeHR.Columns.PartyPositionsId);
                entity.RemoveChangedColumn(EmployeeHR.Columns.OfficeTime);
                entity.RemoveChangedColumn(EmployeeHR.Columns.Organization);
                entity.RemoveChangedColumn(EmployeeHR.Columns.PartyFeeStandard);

                dbEntity = UpdateEmployeeHR(entity, dbEntity);
                isAdd = 2;
            }
            result.Data = dbEntity;

            return result;
        }

        private EmployeeHR AddEmployeeHR(EmployeeHR entity)
        {
            EmployeeHR dbEntity;
            if (entity.AttachmentId.HasValue)
            {
                var attachment = this.Get<Attachment>(entity.AttachmentId.Value);
                dbEntity = this.Add(entity, false);

                if (attachment != null)
                {
                    attachment.ObjectId = entity.ID.ToString();
                    attachment.ObjectType = JHR.Common.Consts.EmployeeHR.EmployeeHRTableName;

                    this.Update(attachment, false);
                }

                this.SaveChanges();
            }
            else
            {
                if (entity.ProposedRetireDate.IsEmpty())
                {
                    entity.ProposedRetireDate = CalProposedRetireDate(entity);
                }
                dbEntity = this.Add(entity);
            }

            return dbEntity;
        }

        private EmployeeHR UpdateEmployeeHR(EmployeeHR entity, EmployeeHR dbEntity)
        {
            var oldAttachment = this.GetEntity<Attachment>(p => p.ObjectId.ToLower() == entity.ID.ToString().ToLower() && p.ObjectType == JHR.Common.Consts.EmployeeHR.EmployeeHRTableName);

            Attachment? newAttachment = null;
            if (entity.AttachmentId.HasValue)
            {
                newAttachment = this.Get<Attachment>(entity.AttachmentId.Value);
            }
            using (var trans = new TransactionScope())
            {
                if (entity.ProposedRetireDate.IsEmpty() && dbEntity.ProposedRetireDate.IsEmpty())
                {
                    entity.ProposedRetireDate = CalProposedRetireDate(entity);
                }
                dbEntity = this.Update(entity, false);

                // 旧附件不为空，删除旧附件关联
            }
            if (oldAttachment != null)
            {
                //附件为空 || 附件不为空，并且附件有修改
                if (!entity.AttachmentId.HasValue
                    || entity.AttachmentId.HasValue && newAttachment != null && newAttachment.ObjectId.ToLower() != entity.ID.ToString().ToLower())
                {
                    oldAttachment.ObjectId = string.Empty;
                    this.Update(oldAttachment, false);
                }
            }

            //附件不为空，并且附件有修改
            if (newAttachment != null && newAttachment.ObjectId.ToLower() != entity.ID.ToString().ToLower())
            {
                newAttachment.ObjectId = entity.ID.ToString();
                newAttachment.ObjectType = JHR.Common.Consts.EmployeeHR.EmployeeHRTableName;

                this.Update(newAttachment, false);
            }
            this.SaveChanges();
            return dbEntity;
        }

        /// <summary>
        /// 计算拟退休日期
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public DateTime? CalProposedRetireDate(EmployeeHR entity)
        {
            DateTime? proposedRetireDate = null;

            var empstatus = this.QueryDictByParentCode(Renji.JHR.Common.Consts.Dicts.EmployeeStatusCode);
            var staff = empstatus.FirstOrDefault(s => s.Code == Renji.JHR.Common.Consts.Dicts.EmpStatusStaffCode);
            if (entity.EmpStatusId == staff.ID)
            {
                if (entity.HireStyleId.HasValue && entity.OfficialRankId.HasValue)
                {
                    var retireType = this.GetEntity<RetireType>(c => c.HireStyleId == entity.HireStyleId && c.OfficialRankId == entity.OfficialRankId);
                    if (retireType != null)
                    {
                        var emp = this.Get<Employee>(entity.ID);
                        if (emp != null)
                        {
                            DateTime birthday;
                            if (DateTime.TryParse(emp.Birthday, out birthday))
                            {
                                RetireEmpType emptype = RetireEmpType.None;
                                if (emp.EnumGender == Gender.Male)
                                {
                                    emptype = RetireEmpType.Male;
                                }
                                else if (emp.EnumGender == Gender.Female)
                                {
                                    if (retireType.RetireAge == 55)
                                    {
                                        emptype = RetireEmpType.Female_55;
                                    }
                                    else if (retireType.RetireAge == 50)
                                    {
                                        emptype = RetireEmpType.Female_50;
                                    }
                                }

                                if (emptype != RetireEmpType.None)
                                {
                                    var retireYear = this.GetEntity<RetireYear>(c => c.EnumRetireEmpType == emptype && c.BeginDate <= birthday && c.EndDate >= birthday);
                                    if (retireYear != null)
                                    {
                                        proposedRetireDate = birthday.AddYears(retireYear.WorkYear).AddMonths(retireYear.WorkMonth);
                                    }
                                }
                            }
                        }
                    } 
                }
            }

            return proposedRetireDate;
        }

        public void UpdateCompanyAge()
        {
            this.ExecuteNonQuery(System.Data.CommandType.StoredProcedure, JHR.Common.Consts.EmployeeHR.ProcedureNameUpdateCompanyAge);
        }


        /// <summary>
        /// 计算公休
        /// </summary>
        /// <returns></returns>
        public bool CalculateGeneralHolidays()
        {
            var currentYear = DateTime.Now.Year;

            // 获取需要更新的员工记录
            var empHrs = this.GetEntities<EmployeeHR>(c => 
                            c.HireStyleId.HasValue && 
                            (!c.GeneralHolidayCalculateTime.HasValue || c.GeneralHolidayCalculateTime.Value.Year != currentYear)
                            ).ToList();

            if (!empHrs.Any())
            {
                return false;
            }

            foreach (var employeeHR in empHrs)
            {
                employeeHR.GeneralHoliday = CalculateGeneralHoliday(employeeHR, currentYear);
                employeeHR.GeneralHolidayCalculateTime = DateTime.Now;
                this.Update(employeeHR,false);
            }

            this.SaveChanges();

            return true;
        }

        /// <summary>
        /// 计算公休
        /// </summary>
        /// <returns></returns>
        public decimal CalculateGeneralHoliday(EmployeeHR employeeHR, int currentYear)
        {
            // 公休天数
            decimal generalHolidayDays = 0;

            if (!employeeHR.SocietyAge.HasValue)
            {
                return generalHolidayDays;
            }

            // 正式员工类型
            var officialEmployeeTypes = new List<string>()
            {
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOnStaff,                 // 正式_在编
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOffStaff,                // 正式_非在编
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialSecondMedicalStaff,      // 正式_二医编制
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOffStaffSelfEmployed,    // 正式_非在编自主择业
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialRetirementPending,       // 正式_待退休
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOnStaffNotHandled,       // 正式_在编未办
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOnStaffSouthCampus,      // 正式_在编南院
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialDisabledNotOnDuty,       // 正式_残疾不在岗及待岗
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOffStaffSouthCampus,     // 正式_非在编南院
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialSouthCampusOnStaffNotHandled, // 正式_南院在编未办
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_UnofficialResidentDoctorTraining, // 非正式_住院医师培训
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_UnofficialSouthCampusResidentDoctorTraining, // 非正式_南院住院医师培训
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_UnofficialPostdoc,               // 非正式_进站博士后
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOffStaffDispatched,      // 正式_非在编派遣
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_OfficialOffStaffSouthCampusDispatched // 正式_非在编南院派遣
            };

            // 非正式员工类型
            var unofficialEmployeeTypes = new List<string>()
            {
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_UnofficialDispatchedLabor,    // 非正式_派遣用工
                Renji.JHR.Common.Consts.Dicts.HireStyleCode_UnofficialOutsourcedStaff, // 非正式_外包人员
            };

            // 正式员工
            if (employeeHR.HireStyle != null && officialEmployeeTypes.Contains(employeeHR.HireStyle.Code))
            {
                // 实际工龄 ≤ 5，公休5天
                if (employeeHR.SocietyAge <= 5)
                {
                    generalHolidayDays = 5;
                }
                // 6 ≤ 实际工龄 < 16，公休10天
                else if (employeeHR.SocietyAge >= 6 && employeeHR.SocietyAge < 16)
                {
                    generalHolidayDays = 10;
                }
                // 16 ≤ 实际工龄 < 26，公休15天
                else if (employeeHR.SocietyAge >= 16 && employeeHR.SocietyAge < 26)
                {
                    generalHolidayDays = 15;
                }
                // 实际工龄 ≥ 26，公休20天
                else if (employeeHR.SocietyAge >= 26)
                {
                    generalHolidayDays = 20;
                }
            }
            // 非正式员工
            else if (employeeHR.HireStyle != null && unofficialEmployeeTypes.Contains(employeeHR.HireStyle.Code))
            {
                // 实际工龄 ≤ 1，公休 0 天
                if (employeeHR.SocietyAge <= 1)
                {
                    generalHolidayDays = 0;
                }
                // 实际工龄 = 2，根据参加工作时间计算
                else if (employeeHR.SocietyAge == 2)
                {
                    var firstDayOfYear = new DateTime(currentYear, 1, 1);

                    // 如果参加工作时间至当年度1月已满1年，公休 5 天
                    if (employeeHR.SocietyDate <= firstDayOfYear.AddYears(-1))
                    {
                        generalHolidayDays = 5;
                    }
                    // 如果未满1年，公休按5*（参加工作时间至当年度1月工作月数）/12天计算
                    else
                    {
                        if (employeeHR.SocietyDate.HasValue)
                        {
                            var monthsWorked = (firstDayOfYear.Year - employeeHR.SocietyDate.Value.Year) * 12 + firstDayOfYear.Month - employeeHR.SocietyDate.Value.Month;
                            generalHolidayDays = Math.Round(5m * monthsWorked / 12.0m, MidpointRounding.AwayFromZero);
                        }
                    }
                }
                // 3 ≤ 实际工龄 < 10，公休 5 天
                else if (employeeHR.SocietyAge >= 3 && employeeHR.SocietyAge < 10)
                {
                    generalHolidayDays = 5;
                }
                // 10 ≤ 实际工龄 < 20，公休 10 天
                else if (employeeHR.SocietyAge >= 10 && employeeHR.SocietyAge < 20)
                {
                    generalHolidayDays = 10;
                }
                // 实际工龄 ≥ 20，公休 15 天
                else if (employeeHR.SocietyAge >= 20)
                {
                    generalHolidayDays = 15;
                }
            }

            // 6月30日的日期
            var june30 = new DateTime(currentYear, 6, 30);

            // 判断入职时间是否在当年度6月30日前
            if (generalHolidayDays > 0 && employeeHR.HireDate.HasValue && employeeHR.HireDate.Value.Year == currentYear && employeeHR.HireDate.Value <= june30.AddDays(1))
            {
                // (计算所得公休 / 12 *（12 - 当前月）)
                // 当年度入职，公休天数按公式计算：工龄公休天数 * (当年度工作月数 / 12)
                var monthsWorked = (12 - employeeHR.HireDate.Value.Month);  // 计算当年度工作月数

                // 按当年度工作月数比例计算实际公休天数
                generalHolidayDays = Math.Round(generalHolidayDays * monthsWorked / 12.0m, MidpointRounding.AwayFromZero);
            }
            // 当年度6月30日后入职，不发放公休
            else if (generalHolidayDays > 0 && employeeHR.HireDate.HasValue && employeeHR.HireDate.Value.Year == currentYear && employeeHR.HireDate.Value > june30.AddDays(1))
            {
                generalHolidayDays = 0;
            }
            return generalHolidayDays;
        }
        public BizResult<EmployeeHR> EditEmployeeParty(EmployeeHR entity)
        {
            var result = this.CheckedEditEmployeeParty(entity);

            if (result.Succeed)
            {
                var employeeHR = this.Get<EmployeeHR>(entity.ID);

                if (employeeHR == null)
                {
                    var data = this.Add(entity);

                    entity = data;
                }
                else
                {
                    employeeHR.PartyId = entity.PartyId;
                    employeeHR.PartyJoiningTime = entity.PartyJoiningTime;
                    employeeHR.ProbationTime = entity.ProbationTime;
                    employeeHR.PartyPositionsIds = entity.PartyPositionsIds;
                    employeeHR.OfficeTime = entity.OfficeTime;
                    employeeHR.Organization = entity.Organization;
                    employeeHR.PartyFeeStandard = entity.PartyFeeStandard;

                    this.Update(ref employeeHR);

                    entity = employeeHR;
                }

                result.Data = entity;
            }

            return result;
        }

        private BizResult<EmployeeHR> CheckedEditEmployeeParty(EmployeeHR entity)
        {
            var result = new BizResult<EmployeeHR>();

            if (entity.ID.IsEmpty())
            {
                result.Error("请先保存基本信息");
            }
            else
            {
                var employee = this.Get<Employee>(entity.ID);
                if (employee == null)
                {
                    result.Error("员工不存在");
                }
            }

            if (!entity.PartyId.IsEmpty())
            {
                var dict = this.BizRepo.Get<Dict>(entity.PartyId!);

                if (dict == null)
                {
                    result.Error("政治面貌不存在");
                }
            }
            else
            {
                result.Error("政治面貌必须选择");
            }

            //if (entity.PartyJoiningTime.IsEmpty())
            //{
            //    result.Error("人党时间必须输入");
            //}


            if (entity.PartyFeeStandard.HasValue && entity.PartyFeeStandard < 0)
            {
                result.Error("党费标准必须大于等于零");
            }

            return result;
        }

        #region 所在支部

        public BizResult<EmployeeHRAffiliatedBranch> AddEmployeeHRAffiliatedBranch(EmployeeHRAffiliatedBranch entity)
        {
            var result = new BizResult<EmployeeHRAffiliatedBranch>();

            if (entity.EmployeeHRId.IsEmpty())
            {
                result.Error("请先保存政治面貌信息");
            }
            else
            {
                var employeeHR = this.Get<EmployeeHR>(entity.EmployeeHRId);

                if (employeeHR == null)
                {
                    result.Error("数据错误，请刷新页面或联系管理员");
                }
            }

            if (entity.TransferTime >= entity.TransferOutTime)
            {
                result.Error("转出时间必须大于转入时间");
            }

            if (entity.AffiliatedBranch.IsEmpty() && !entity.TransferTime.HasValue && !entity.TransferOutTime.HasValue)
            {
                result.Error("请填写数据后在保存");
            }

            if (result.Succeed)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity = this.Add(entity);

                result.Data = entity;
            }

            return result;
        }

        public BizResult<EmployeeHRAffiliatedBranch> UpdateEmployeeHRAffiliatedBranch(EmployeeHRAffiliatedBranch entity)
        {
            var result = new BizResult<EmployeeHRAffiliatedBranch>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeHRAffiliatedBranch>(id);

            if (dbEntity == null)
            {
                result.Error("所在支部不存在");
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    if (entity.EmployeeHRId.IsEmpty())
                    {
                        result.Error("请先保存政治面貌信息");
                    }
                    else
                    {
                        var employeeHR = this.Get<EmployeeHR>(entity.EmployeeHRId);

                        if (employeeHR == null)
                        {
                            result.Error("数据错误，请刷新页面或联系管理员");
                        }
                    }

                    if (entity.AffiliatedBranch.IsEmpty() && !entity.TransferTime.HasValue && !entity.TransferOutTime.HasValue)
                    {
                        result.Error("请填写数据后在保存");
                    }

                    if (entity.TransferTime >= entity.TransferOutTime)
                    {
                        result.Error("转出时间必须大于转入时间");
                    }
                }

                if (result.Succeed)
                {
                    this.SaveChanges();

                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                }
            }

            return result;
        }

        public BizResult DeleteEmployeeHRAffiliatedBranch(Guid id)
        {
            var result = new BizResult<EmployeeHRAffiliatedBranch>();

            var dbEntity = this.Get<EmployeeHRAffiliatedBranch>(id);

            if (dbEntity == null)
            {
                result.Error("所在支部不存在");
            }
            else
            {
                this.Delete(dbEntity);
            }

            return result;
        }
        #endregion

        #region 党内职务
        public BizResult<EmployeeHRDict> AddEmployeeHRDict(EmployeeHRDict entity)
        {
            var result = new BizResult<EmployeeHRDict>();

            if (entity.EmployeeHRId.IsEmpty())
            {
                result.Error("请先保存政治面貌信息");
            }
            else
            {
                var employeeHR = this.Get<EmployeeHR>(entity.EmployeeHRId);

                if (employeeHR == null)
                {
                    result.Error("数据错误，请刷新页面或联系管理员");
                }
            }

            if (entity.OrganizationName.IsEmpty())
            {
                result.Error("请填写数据后在保存");
            }

            if (result.Succeed)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity = this.Add(entity);

                result.Data = entity;
            }

            return result;
        }

        public BizResult<EmployeeHRDict> UpdateEmployeeHRDict(EmployeeHRDict entity)
        {
            var result = new BizResult<EmployeeHRDict>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeHRDict>(id);

            if (dbEntity == null)
            {
                result.Error("当前组织不存在");
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    var employeeHR = this.Get<EmployeeHR>(entity.EmployeeHRId);

                    if (employeeHR == null)
                    {
                        result.Error("数据错误，请刷新页面或联系管理员");
                    }

                    if (entity.OrganizationName.IsEmpty())
                    {
                        result.Error("请填写数据后在保存");
                    }
                }

                if (result.Succeed)
                {
                    this.SaveChanges();

                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                }
            }

            return result;
        }

        public BizResult DeleteEmployeeHRDict(Guid id)
        {
            var result = new BizResult<EmployeeHRDict>();

            var dbEntity = this.Get<EmployeeHRDict>(id);

            if (dbEntity == null)
            {
                result.Error("党内职务不存在");
            }
            else
            {
                this.Delete(dbEntity);
            }

            return result;
        }

        #endregion 所在支部

        #region 党员奖惩

        public BizResult<EmployeeHRPartyMemberHonor> AddEmployeeHRPartyMemberHonor(EmployeeHRPartyMemberHonor entity)
        {
            var result = new BizResult<EmployeeHRPartyMemberHonor>();

            if (entity.EmployeeHRId.IsEmpty())
            {
                result.Error("请先保存政治面貌信息");
            }
            else
            {
                var employeeHR = this.Get<EmployeeHR>(entity.EmployeeHRId);

                if (employeeHR == null)
                {
                    result.Error("数据错误，请刷新页面或联系管理员");
                }
            }

            if (entity.HonorOrPunish.IsEmpty() && entity.HonorOrPunishCompany.IsEmpty() && !entity.Time.HasValue)
            {
                result.Error("请填写数据后在保存");
            }

            if (result.Succeed)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity = this.Add(entity);

                result.Data = entity;
            }

            return result;
        }

        public BizResult<EmployeeHRPartyMemberHonor> UpdateEmployeeHRPartyMemberHonor(EmployeeHRPartyMemberHonor entity)
        {
            var result = new BizResult<EmployeeHRPartyMemberHonor>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeHRPartyMemberHonor>(id);

            if (dbEntity == null)
            {
                result.Error("党员奖惩不存在");
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    if (entity.EmployeeHRId.IsEmpty())
                    {
                        result.Error("请先保存政治面貌信息");
                    }
                    else
                    {
                        var employeeHR = this.Get<EmployeeHR>(entity.EmployeeHRId);

                        if (employeeHR == null)
                        {
                            result.Error("数据错误，请刷新页面或联系管理员");
                        }
                    }

                    if (entity.HonorOrPunish.IsEmpty() && entity.HonorOrPunishCompany.IsEmpty() && !entity.Time.HasValue)
                    {
                        result.Error("请填写数据后在保存");
                    }
                }

                if (result.Succeed)
                {
                    this.SaveChanges();

                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                }
            }

            return result;
        }

        public BizResult DeleteEmployeeHRPartyMemberHonor(Guid id)
        {
            var result = new BizResult<EmployeeHRPartyMemberHonor>();

            var dbEntity = this.Get<EmployeeHRPartyMemberHonor>(id);

            if (dbEntity == null)
            {
                result.Error("党员奖惩不存在");
            }
            else
            {
                this.Delete(dbEntity);
            }

            return result;
        }

        #endregion 党员奖惩

        #endregion EmployeeHR

        #region EmployeeSocialInsurance

        public BizResult<EmployeeSocialInsurance> UpdateEmployeeSocialSecurity(EmployeeSocialInsurance entity, string? paySlipNumber)
        {
            var result = new BizResult<EmployeeSocialInsurance>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeSocialInsurance>(id);
            var BenefitEntity = this.Get<EmployeeBenefit>(id);

            if (dbEntity == null)
            {
                dbEntity = this.Add(entity);
            }
            else
            {
                dbEntity = this.Update(entity);
            }

            if (!string.IsNullOrEmpty(paySlipNumber))
            {
                var EmployeeBenefit = new EmployeeBenefit();
                EmployeeBenefit.PaySlipNumber = paySlipNumber;
                if (dbEntity == null)
                {
                    this.Add(EmployeeBenefit);
                }
                else
                {
                    EmployeeBenefit.ID = id;
                    this.Update(EmployeeBenefit);
                }
            }

            result.Data = dbEntity;

            return result;
        }

        #endregion EmployeeSocialInsurance

        #region EmployeeBenefit

        public BizResult<EmployeeBenefit> UpdateEmployeeBenefit(EmployeeBenefit entity, DateTime? ChangeDate, string? Remark, List<EmployeePayrollHistory> HistoryList)
        {
            var result = new BizResult<EmployeeBenefit>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeBenefit>(id);

            if (dbEntity == null)
            {
                var Eph = new EmployeePayrollHistory();
                Eph.ID = Guid.NewGuid();
                Eph.EmployeeId = entity.ID;
                Eph.CompGroupId = entity.CompGroupId;
                Eph.ChangeItem = "第一次新加记录";
                Eph.Before = "";
                Eph.After = "";
                Eph.ChangeDate = ChangeDate;
                Eph.Remark = Remark;

                this.Add(Eph);

                dbEntity = this.Add(entity);
            }
            else
            {
                var ListEph = new List<EmployeePayrollHistory>();

                //判断工资标准类型
                if (dbEntity.OrgClassId != entity.OrgClassId)
                {
                    var Eph = new EmployeePayrollHistory();
                    Eph.ID = Guid.NewGuid();
                    Eph.EmployeeId = entity.ID;
                    Eph.CompGroupId = entity.CompGroupId;
                    Eph.ChangeItem = "工资标准";

                    var BeforeValue = this.Get<PayRollOrgClass>(dbEntity.OrgClassId);
                    Eph.Before = BeforeValue != null ? BeforeValue.Name : "";

                    var AfterValue = this.Get<PayRollOrgClass>(entity.OrgClassId);
                    Eph.After = AfterValue != null ? AfterValue.Name : "";

                    Eph.ChangeDate = ChangeDate;
                    Eph.Remark = Remark;
                    ListEph.Add(Eph);
                }
                //判断岗位工资级别
                if (dbEntity.OrgSalaryId != entity.OrgSalaryId)
                {
                    var Eph = new EmployeePayrollHistory();
                    Eph.ID = Guid.NewGuid();
                    Eph.EmployeeId = entity.ID;
                    Eph.CompGroupId = entity.CompGroupId;
                    Eph.ChangeItem = "岗位工资";

                    var BeforeValue = this.Get<PayRollOrgSalary>(dbEntity.OrgSalaryId);
                    Eph.Before = BeforeValue != null ? BeforeValue.Name + "-" + BeforeValue.Value : "";

                    var AfterValue = this.Get<PayRollOrgSalary>(entity.OrgSalaryId);
                    Eph.After = AfterValue != null ? AfterValue.Name + "-" + AfterValue.Value : "";

                    Eph.ChangeDate = ChangeDate;
                    Eph.Remark = Remark;
                    ListEph.Add(Eph);
                }
                //判断薪级工资级别
                if (dbEntity.OrgSalaryLevelId != entity.OrgSalaryLevelId)
                {
                    var Eph = new EmployeePayrollHistory();
                    Eph.ID = Guid.NewGuid();
                    Eph.EmployeeId = entity.ID;
                    Eph.CompGroupId = entity.CompGroupId;
                    Eph.ChangeItem = "薪级工资";

                    var BeforeValue = this.Get<PayRollOrgSalaryLevel>(dbEntity.OrgSalaryLevelId);
                    Eph.Before = BeforeValue != null ? BeforeValue.Name + "-" + BeforeValue.Value : "";

                    var AfterValue = this.Get<PayRollOrgSalaryLevel>(entity.OrgSalaryLevelId);
                    Eph.After = AfterValue != null ? AfterValue.Name + "-" + AfterValue.Value : "";

                    Eph.ChangeDate = ChangeDate;
                    Eph.Remark = Remark;
                    ListEph.Add(Eph);
                }
                //职务工资级别
                if (dbEntity.OrgPositionSalaryId != entity.OrgPositionSalaryId)
                {
                    var Eph = new EmployeePayrollHistory();
                    Eph.ID = Guid.NewGuid();
                    Eph.EmployeeId = entity.ID;
                    Eph.CompGroupId = entity.CompGroupId;
                    Eph.ChangeItem = "职务工资";

                    var BeforeValue = this.Get<PayRollOrgPositionSalarys>(dbEntity.OrgPositionSalaryId);
                    Eph.Before = BeforeValue != null ? BeforeValue.Name + "-" + BeforeValue.Value : "";

                    var AfterValue = this.Get<PayRollOrgPositionSalarys>(entity.OrgPositionSalaryId);
                    Eph.After = AfterValue != null ? AfterValue.Name + "-" + AfterValue.Value : "";

                    Eph.ChangeDate = ChangeDate;
                    Eph.Remark = Remark;
                    ListEph.Add(Eph);
                }

                if (ListEph.Count > 0)
                {
                    this.AddRange(ListEph, false);
                }

                foreach (var item in HistoryList)
                {
                    var Eph = new EmployeePayrollHistory();
                    Eph.Tag = item.Tag == true ? true : null;
                    Eph.ID = item.ID;
                    this.Update(Eph);
                }

                dbEntity = this.Update(entity);
            }
            result.Data = dbEntity;

            return result;
        }

        #endregion EmployeeBenefit

        #region EmployeePayrollPrint

        public BizResult<EmployeePayrollPrint> UpdateEmployeePayrollPrint(EmployeePayrollPrint entity)
        {
            var result = new BizResult<EmployeePayrollPrint>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
                result.Data = this.Add(entity);
            }
            else
            {
                result.Data = this.Update(entity);
            }
            return result;
        }

        public BizResult DeleteEmployeePayrollPrint(EmployeePayrollPrint entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeePayrollPrint>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Remove(entity);
            }
            return result;
        }

        #endregion EmployeePayrollPrint

        #region EmployeeStation

        public BizResult<EmployeeStation> AddEmployeeStation(EmployeeStation entity, Guid? deptPrincipalID)
        {
            var result = new BizResult<EmployeeStation>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            if (entity.PositionStationId != null)
            {
                var pos = this.Get<PositionStation>(entity.PositionStationId);
                if (pos != null)
                {
                    entity.PositionName = pos.PositionName;
                }  
            }
            

            entity = this.Add(entity);

            ////部分负责人
            //if (deptPrincipalID.HasValue)
            //{
            //    SaveDeptEmp(entity.EmployeeId, deptPrincipalID.Value, false);
            //}

            //更新聘任职务、聘任职称
            var employeeStationEntities = this.GetQuery<EmployeeStation>(p => p.EmployeeId == entity.EmployeeId && p.Type == entity.Type && p.ID != entity.ID);
            foreach (var item in employeeStationEntities)
            {
                bool update = false;
                if (entity.IsTopStation.HasValue && entity.IsTopStation.Value && item.IsTopStation.HasValue && item.IsTopStation.Value)
                {
                    item.IsTopStation = false;
                    update = true;
                }
                if (entity.IsTopStationRank.HasValue && entity.IsTopStationRank.Value && item.IsTopStationRank.HasValue && item.IsTopStationRank.Value)
                {
                    item.IsTopStation = false;
                    update = true;
                }
                if (update)
                {
                    this.Update(item, false);
                }
            }

            //result.Data = employeeStationEntities.Where(p => p.ID == entity.ID).FirstOrDefault();

            this.SaveChanges();
            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeStation> UpdateEmployeeStation(EmployeeStation entity, Guid? deptPrincipalID)
        {
            var result = new BizResult<EmployeeStation>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeStation>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (entity.PositionStationId != null)
                {
                    var pos = this.Get<PositionStation>(entity.PositionStationId);
                    if (pos != null)
                    {
                        entity.PositionName = pos.PositionName;
                    }
                }
                
                this.Update(ref entity, true);
                if (result.Succeed)
                {
                    ////部分负责人
                    //if (deptPrincipalID.HasValue)
                    //{
                    //    SaveDeptEmp(entity.EmployeeId, deptPrincipalID.Value, false);
                    //}

                    //更新聘任职务、聘任职称
                    var employeeStationEntities = this.GetQuery<EmployeeStation>(p => p.EmployeeId == entity.EmployeeId && p.Type == entity.Type && p.ID != entity.ID);
                    foreach (var item in employeeStationEntities)
                    {
                        bool update = false;
                        if (entity.IsTopStation.HasValue && entity.IsTopStation.Value && item.IsTopStation.HasValue && item.IsTopStation.Value)
                        {
                            item.IsTopStation = false;
                            update = true;
                        }
                        if (entity.IsTopStationRank.HasValue && entity.IsTopStationRank.Value && item.IsTopStationRank.HasValue && item.IsTopStationRank.Value)
                        {
                            item.IsTopStationRank = false;
                            update = true;
                        }
                        if (update)
                        {
                            this.Update(item, false);
                        }
                    }

                    //var major = employeeStationEntities.Where(p => p.IsParttime == false).OrderByDescending(p => p.StartDate).FirstOrDefault();
                    //if (major != null)
                    //{
                    //    major.IsMajor = true;
                    //    this.Update(major, false);
                    //}

                    result.Data = entity;

                    this.SaveChanges();
                }
                else
                {
                    this.Detach(entity);
                }
                result.Data = entity;
            }

            return result;
        }

        /// <summary>
        /// 删除员工聘任岗位
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeStation(EmployeeStation entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeStation>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        /// <summary>
        /// 保存部分负责人
        /// </summary>
        /// <param name="operatorid"></param>
        /// <param name="deptPrincipalID"></param>
        /// <param name="issave"></param>
        public Guid SaveDeptEmp(Guid operatorid, Guid? deptPrincipalID, bool issave = true)
        {
            var emp = this.Get<Employee>(operatorid);
            DepartmentEmployeeHistory history = new DepartmentEmployeeHistory();

            var deptid = emp?.DeptId;

            if (deptid.HasValue)
            {
                var de = this.GetEntity<DepartmentEmployee>(predicate: s => s.DepartmentID == deptid.Value);

                bool addHistory = false;
                history.DepartmentId = deptid.Value;
                if (de != null)
                {
                    if (deptPrincipalID != null)
                    {
                        if (!deptPrincipalID.Value.Equals(de.EmployeeID))
                        {
                            history.PreEmployeeId = de.EmployeeID;
                            history.NewEmployeeId = deptPrincipalID.Value;
                            addHistory = true;

                            de.EmployeeID = deptPrincipalID.Value;
                            this.Update(de, issave);
                        }
                    }
                    else
                    {
                        history.PreEmployeeId = de.EmployeeID;
                        addHistory = true;

                        this.Delete(de, issave);
                    }

                }
                else
                {
                    if (deptPrincipalID != null)
                    {
                        var entity = new DepartmentEmployee
                        {
                            DepartmentID = deptid.Value,
                            EmployeeID = deptPrincipalID.Value
                        };
                        this.Add(entity, issave);
                        history.NewEmployeeId = deptPrincipalID;
                        addHistory = true;
                    }
                }

                if (addHistory)
                {
                    history.ID = CombGuid.NewGuid();
                    this.Add(history, issave);
                }
            }

            return history.ID;
        }

        public Employee GetDeptEmp(Guid deptid)
        {
            var de = this.GetEntity<DepartmentEmployee>(s => s.DepartmentID == deptid);
            return de?.Employee;
        }

        #endregion EmployeeStation

        #region EmployeeCertify

        public BizResult<EmployeeCertify> AddEmployeeCertify(EmployeeCertify entity)
        {
            var result = new BizResult<EmployeeCertify>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }
            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeCertify> UpdateEmployeeCertify(EmployeeCertify entity)
        {
            var result = new BizResult<EmployeeCertify>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeCertify>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }

            return result;
        }

        /// <summary>
        /// 删除员工职称资格
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeCertify(EmployeeCertify entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeCertify>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeCertify

        #region EmployeeEducation

        public BizResult<EmployeeEducation> AddEmployeeEducation(EmployeeEducation entity)
        {
            var result = new BizResult<EmployeeEducation>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            UpdateTopEducation(entity);
            if (entity.Employee == null)
            {
                entity.Employee = this.Get<Employee>(entity.EmployeeId);
            }

            result.Data = entity;

            this.SaveChanges();

            return result;
        }

        public BizResult<EmployeeEducation> UpdateEmployeeEducation(EmployeeEducation entity)
        {
            var result = new BizResult<EmployeeEducation>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeEducation>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    UpdateTopEducation(entity);
                    if (entity.Employee == null)
                    {
                        entity.Employee = this.Get<Employee>(dbEntity.EmployeeId);
                    }
                    result.Data = entity;

                    this.SaveChanges();
                }
                else
                {
                    this.Detach(entity);
                    if (dbEntity.Employee == null)
                    {
                        dbEntity.Employee = this.Get<Employee>(dbEntity.EmployeeId);
                    }
                    result.Data = dbEntity;
                }
            }
            return result;
        }

        private void UpdateTopEducation(EmployeeEducation entity)
        {
            if ((entity.IsTopDegree.HasValue && entity.IsTopDegree.Value)
                || (entity.IsTopEduLevel.HasValue && entity.IsTopEduLevel.Value))
            {
                var educationEntities = this.GetQuery<EmployeeEducation>(p => p.EmployeeId == entity.EmployeeId && p.ID != entity.ID);
                var employeehr = this.Get<EmployeeHR>(entity.EmployeeId);
                if (entity.IsTopDegree.HasValue && entity.IsTopDegree.Value)
                {
                    foreach (var item in educationEntities)
                    {
                        item.IsTopDegree = false;
                        this.Update(item, false);
                    }
                    if (employeehr != null)
                    {
                        employeehr.DegreeId = entity.DegreeId;
                        this.Update(employeehr, false);
                    }
                }
                if (entity.IsTopEduLevel.HasValue && entity.IsTopEduLevel.Value)
                {
                    foreach (var item in educationEntities)
                    {
                        item.IsTopEduLevel = false;
                        this.Update(item, false);
                    }
                    if (employeehr != null)
                    {
                        employeehr.EducationId = entity.EduLevelId;
                        employeehr.Graduate = entity.SchoolName;
                        this.Update(employeehr, false);
                    }
                }
            }
        }

        /// <summary>
        /// 删除员工学习经历
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeEducation(EmployeeEducation entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeEducation>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeEducation

        #region EmployeeWork

        public BizResult<EmployeeWork> AddEmployeeWork(EmployeeWork entity)
        {
            var result = new BizResult<EmployeeWork>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeWork> UpdateEmployeeWork(EmployeeWork entity)
        {
            var result = new BizResult<EmployeeWork>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeWork>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }
            return result;
        }

        /// <summary>
        /// 删除员工工作经历
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeWork(EmployeeWork entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeWork>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeWork

        #region EmployeeAbroadInfo

        public BizResult<EmployeeAbroadInfo> AddEmployeeAbroadInfo(EmployeeAbroadInfo entity)
        {
            var result = new BizResult<EmployeeAbroadInfo>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeAbroadInfo> UpdateEmployeeAbroadInfo(EmployeeAbroadInfo entity)
        {
            var result = new BizResult<EmployeeAbroadInfo>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeAbroadInfo>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }
            return result;
        }

        /// <summary>
        /// 删除员工出国情况
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeAbroadInfo(EmployeeAbroadInfo entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeAbroadInfo>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeAbroadInfo

        #region EmployeeContract

        public BizResult<EmployeeContract> UpdateNextTimeRemind(EmployeeContract entity)
        {
            var result = new BizResult<EmployeeContract>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeContract>(id);

            if (dbEntity == null)
            {
                result.Error("合同不存在。");
            }
            else
            {
                dbEntity.IsNextTime = true;
                dbEntity.ReminderDate = entity.ReminderDate;
                this.Update(dbEntity);
            }

            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeContract> BatchUpdateNextTimeRemind(EmployeeContract entity, List<Guid> Ids)
        {
            var result = new BizResult<EmployeeContract>();
            if (Ids == null || !Ids.Any())
            {
                result.Error("请选择合同到期的员工");
            }
            else
            {
                var editEmpOContractList = this.GetEntities<EmployeeContract>(p => Ids.Contains(p.ID));
                foreach (var item in editEmpOContractList)
                {
                    item.IsNextTime = true;
                    item.ReminderDate = entity.ReminderDate;
                    this.Update(item);
                }
            }

            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeContract> SendEmailForRenewRemind(EmployeeContract entity)
        {
            var result = new BizResult<EmployeeContract>();
            var mailBll = new MailBll();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeContract>(id);

            var endDate = entity.EndDate.HasValue ? entity.EndDate.Value.ToString("yyyy-MM-dd") : "退休";

            if (dbEntity == null)
            {
                result.Error("合同不存在。");
            }
            else
            {
                //标记已发送提醒
                dbEntity.IsRemind = true;
                this.Update(dbEntity);

                // 领导邮件
                var leader = GetLeader(dbEntity.Employee.Department);
                if (leader != null)
                {
                    var mailTemplateCodeLeader = "Leader";
                    var leaderDatas = new Dictionary<string, string>()
                    {
                        {"UserDisplayName",dbEntity.Employee.DisplayName},
                        {"StartDate",entity.StartDate.AsString("yyyy-MM-dd")},
                        {"EndDate",endDate},
                        {"CurrentDate",SysDateTime.Now.ToString("yyyy-MM-dd")}
                    };
                    mailBll.CreateMail(mailTemplateCodeLeader, leader.Uid + Config.RenjiEmail, leaderDatas, true, nameof(EmployeeContract), "", id, "RenewRemind");
                }

                // 员工邮件
                var mailTemplateCodeStaff = "Staff";
                var staffDatas = new Dictionary<string, string>()
                    {
                        {"StartDate",entity.StartDate.AsString("yyyy-MM-dd")},
                        {"EndDate",endDate},
                        {"CurrentDate",SysDateTime.Now.ToString("yyyy-MM-dd")}
                    };
                mailBll.CreateMail(mailTemplateCodeStaff, dbEntity.Employee.Uid + Config.RenjiEmail, staffDatas, false, nameof(EmployeeContract), "", id, "RenewRemind");
            }

            result.Data = dbEntity;

            return result;
        }

        public BizResult<EmployeeContract> BatchSendEmailForRenewRemind(EmployeeContract entity, List<Guid> Ids)
        {
            var result = new BizResult<EmployeeContract>();
            var mailBll = new MailBll();

            if (Ids == null || !Ids.Any())
            {
                result.Error("请选择合同到期的员工");
            }
            else
            {
                var editEmpOContractList = this.GetEntities<EmployeeContract>(p => Ids.Contains(p.ID));
                var depEmpList = new List<DepartmentEmployee>();
                foreach (var item in editEmpOContractList)
                {
                    //标记已发送提醒
                    item.IsRemind = true;
                    this.Update(item);

                    var leader = GetLeader(item.Employee.Department);
                    if (leader != null)
                    {
                        var depEmp = depEmpList.FirstOrDefault(p => p.Leader == leader.Uid + Config.RenjiEmail);
                        if (depEmp == null)
                        {
                            depEmpList.Add(new DepartmentEmployee { Leader = leader.Uid + Config.RenjiEmail, EmployeeName = item.Employee.DisplayName });
                        }
                        else
                        {
                            depEmp.EmployeeName += "，" + item.Employee.DisplayName;
                        }
                    }

                    // 员工邮件
                    var mailTemplateCodeStaff = "Staff";
                    var staffDatas = new Dictionary<string, string>()
                    {
                        {"StartDate",entity.StartDate.AsString("yyyy-MM-dd")},
                        {"EndDate",entity.EndDate.AsString("yyyy-MM-dd")},
                        {"CurrentDate",SysDateTime.Now.ToString("yyyy-MM-dd")}
                    };
                    mailBll.CreateMail(mailTemplateCodeStaff, item.Employee.Uid + Config.RenjiEmail, staffDatas, false, nameof(EmployeeContract), "", item.ID, "RenewRemind");
                }

                foreach (var item in depEmpList)
                {
                    // 领导邮件
                    var mailTemplateCodeLeader = "Leader";
                    var leaderDatas = new Dictionary<string, string>()
                        {
                            {"UserDisplayName",item.EmployeeName},
                            {"StartDate",entity.StartDate.AsString("yyyy-MM-dd")},
                            {"EndDate",entity.EndDate.AsString("yyyy-MM-dd")},
                            {"CurrentDate",SysDateTime.Now.ToString("yyyy-MM-dd")}
                        };
                    mailBll.CreateMail(mailTemplateCodeLeader, item.Leader, leaderDatas, true, nameof(EmployeeContract), "", item.ID, "RenewRemind");
                }
            }

            return result;
        }

        private Employee? GetLeader(Department? department)
        {
            if (department != null)
            {
                var deptEmp = this.GetEntity<DepartmentEmployee>(p => p.DepartmentID == department.ID);
                if (deptEmp == null)
                {
                    if (department.Parent != null)
                    {
                        GetLeader(department.Parent);
                    }
                }
                else
                {
                    return deptEmp.Employee;
                }
            }
            return null;
        }

        public BizResult<EmployeeContract> AddEmployeeContract(EmployeeContract entity)
        {
            var result = new BizResult<EmployeeContract>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity, false);

            //更新最新合同
            if (entity.IsTheLatest.HasValue && entity.IsTheLatest.Value)
            {
                var employeeContractEntities = this.GetQuery<EmployeeContract>(p => p.EmployeeId == entity.EmployeeId && p.ID != entity.ID && p.IsTheLatest.HasValue && p.IsTheLatest.Value);
                foreach (var item in employeeContractEntities)
                {
                    item.IsTheLatest = false;
                    this.Update(item, false);
                }
            }

            this.SaveChanges();
            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeContract> UpdateEmployeeContract(EmployeeContract entity)
        {
            var result = new BizResult<EmployeeContract>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeContract>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Update(ref entity, true);
                if (result.Succeed)
                {
                    //更新最新合同
                    if (entity.IsTheLatest.HasValue && entity.IsTheLatest.Value)
                    {
                        var employeeContractEntities = this.GetQuery<EmployeeContract>(p => p.EmployeeId == entity.EmployeeId && p.ID != entity.ID && p.IsTheLatest.HasValue && p.IsTheLatest.Value);
                        foreach (var item in employeeContractEntities)
                        {
                            item.IsTheLatest = false;
                            this.Update(item, false);
                        }
                    }

                    this.SaveChanges();
                }
                else
                {
                    this.Detach(entity);
                }
                result.Data = entity;
            }
            return result;
        }

        /// <summary>
        /// 删除员工合同
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeContract(EmployeeContract entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeContract>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeContract

        #region EmployeeTrain

        public BizResult<EmployeeTrain> AddEmployeeTrain(EmployeeTrain entity)
        {
            var result = new BizResult<EmployeeTrain>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeTrain> UpdateEmployeeTrain(EmployeeTrain entity)
        {
            var result = new BizResult<EmployeeTrain>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeTrain>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }
            return result;
        }

        /// <summary>
        /// 删除员工培养计划
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeTrain(EmployeeTrain entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeTrain>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeTrain

        #region EmployeeAssessment

        public BizResult<EmployeeAssessment> AddEmployeeAssessment(EmployeeAssessment entity)
        {
            var result = new BizResult<EmployeeAssessment>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            var existes = this.GetEntities<EmployeeAssessment>(p => p.EmployeeId == entity.EmployeeId && p.Year == entity.Year);

            if (existes.Any())
            {
                result.Error(entity.Year + "年度已考核。");
            }

            if (result.Succeed)
            {
                entity = this.Add(entity);
            }
            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeAssessment> UpdateEmployeeAssessment(EmployeeAssessment entity)
        {
            var result = new BizResult<EmployeeAssessment>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeAssessment>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                var existes = this.GetEntities<EmployeeAssessment>(p => p.EmployeeId == entity.EmployeeId && p.Year == entity.Year && p.ID != entity.ID);

                if (existes.Any())
                {
                    result.Error(entity.Year + "年度已考核。");
                }

                if (result.Succeed)
                {
                    if (this.Update(ref entity))
                    {
                        result.Data = entity;
                    }
                    else
                    {
                        this.Detach(entity);
                        result.Data = dbEntity;
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 删除员工考核
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeAssessment(EmployeeAssessment entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeAssessment>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeAssessment

        #region EmployeeDeduct

        public BizResult<EmployeeDeduct> AddEmployeeDeduct(EmployeeDeduct entity)
        {
            var result = new BizResult<EmployeeDeduct>();

            string procName = (Config.IsDM ? "\"JHR\".usp_AddEmployeeDeductCalculateByYear" : "usp_AddEmployeeDeductCalculateByYear");

            var p1 = this.CreateParameter((Config.IsDM ? "p_Year" : "@Year"), entity.Year);
            var p2 = this.CreateParameter((Config.IsDM ? "p_Remark" : "@Remark"), entity.Remark);
            var p3 = this.CreateParameter((Config.IsDM ? "p_OperatorUser" : "@OperatorUser"), this.OperatorUser?.ID);

            var r = (false, string.Empty);

            using (var reader = this.DbContext.ExecuteReader(CommandType.StoredProcedure, procName, p1, p2, p3))
            {
                while (reader.Read())
                {
                    r = (reader.GetValueExt<bool>("isok"), reader.GetValueExt<string>("msg") ?? "");
                }
            }

            if (r.Item1)
                result.Data = this.GetEntity<EmployeeDeduct>(x => x.Year == entity.Year);
            else
                result.Error(r.Item2);

            return result;
        }

        public BizResult<EmployeeDeduct> UpdateEmployeeDeduct(Guid DeductId)
        {
            var result = new BizResult<EmployeeDeduct>();

            string procName = (Config.IsDM ? "\"JHR\".usp_UpdateEmployeeDeductCalculateByYear" : "usp_UpdateEmployeeDeductCalculateByYear");

            var p1 = this.CreateParameter((Config.IsDM ? "p_DeductId" : "@DeductId"), DeductId);
            var p2 = this.CreateParameter((Config.IsDM ? "p_OperatorUser" : "@OperatorUser"), this.OperatorUser?.ID);

            var r = (false, string.Empty);

            using (var reader = this.DbContext.ExecuteReader(CommandType.StoredProcedure, procName, p1, p2))
            {
                while (reader.Read())
                {
                    r = (reader.GetValueExt<bool>("isok"), reader.GetValueExt<string>("msg") ?? "");
                }
            }

            if (r.Item1)
                result.Data = this.GetEntity<EmployeeDeduct>(x => x.ID == DeductId);
            else
                result.Error(r.Item2);


            return result;
        }


        /// <summary>
        /// 删除员工扣减工龄
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeDeduct(EmployeeDeduct entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeDeduct>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        /// <summary>
        /// 根据工龄计算工龄段
        /// </summary>
        /// <param name="societyAge"></param>
        /// <returns></returns>
        public Guid CalculateSeniorityRange(int societyAge, List<Dict>? dicts)
        {
            var seniorityRange = new List<Dict>();
            if (dicts != null && dicts.Count > 0)
            {
                seniorityRange = dicts.Where(p => p.Parent?.Code == Config.DictCode.AgeRange).ToList();
            }
            else
            {
                seniorityRange = SysCache.GetDicts(Config.DictCode.AgeRange);
            }
            var result = Guid.Empty;
            //工龄段
            foreach (var item in seniorityRange.OrderBy(p => p.Code))
            {
                List<string> list = item.FullName?.Split('-').ToList() ?? new List<string>();

                if (societyAge == 0)
                {
                    result = item.ID;
                    break;
                }
                else if (list.Count == 2 && societyAge >= list[0].As<int>() && societyAge <= list[1].As<int>())
                {
                    result = item.ID;
                    break;
                }
                else if (list.Count == 1 && societyAge >= list[0].As<int>())
                {
                    result = item.ID;
                    break;
                }
            }
            return result;
        }
        #endregion EmployeeDeduct

        #region EmployeeDeductCalculate

        public BizResult<EmployeeDeductCalculate> UpdateEmployeeDeductCalculate(EmployeeDeductCalculate entity)
        {
            var result = new BizResult<EmployeeDeductCalculate>();

            EmployeeDeductCalculate? dbEntity;
            if (entity.ID.IsEmpty())
            {
                dbEntity = GetEntity<EmployeeDeductCalculate>(x => x.DeductId == entity.DeductId && x.EmployeeId == entity.EmployeeId);
            }
            else
            {
                dbEntity = Get<EmployeeDeductCalculate>(entity.ID);
            }

            if (dbEntity == null)
            {
                result.Error("该人员不存在。");
            }
            else
            {
                if (entity.ID.IsEmpty()) 
                    entity.ID = dbEntity.ID;

                entity.SalaryScale = dbEntity.SalaryScale;
                entity.CompanyAge = dbEntity.CompanyAge;
                entity.SocietyAge = dbEntity.SocietyAge;

                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }
            return result;
        }


        /// <summary>
        /// EmployeeDeductCalculate
        /// </summary>
        /// <param name="departmentDatas"></param>
        /// <returns></returns>
        public BizResult ImportEmployeeDeductCalculate(Guid deductId, List<int> uidList, List<EmployeeDeductCalculate> deductCalList)
        {
            var result = new BizResult();

            for (int i = 0; i < uidList.Count; i++)
            {
                int uid = uidList[i];
                var dbdeductCal = this.GetEntities<EmployeeDeductCalculate>(x => x.DeductId == deductId && x.Employee.Uid == uid).FirstOrDefault();
                if (dbdeductCal != null)
                {

                    dbdeductCal.NotCalAge = deductCalList[i].NotCalAge;
                    dbdeductCal.NotCalSalaryScale = deductCalList[i].NotCalSalaryScale;
                    this.Update(dbdeductCal, false);
                }
            }
            
            this.SaveChanges();
            return result;
        }
        public BizResult<EmployeeDeductCalculate> AddEmployeeDeductCalculate(EmployeeDeductCalculate entity)
        {
            var result = new BizResult<EmployeeDeductCalculate>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            var existes = this.GetEntities<EmployeeDeductCalculate>(p => p.EmployeeId == entity.EmployeeId && p.DeductId == entity.DeductId);

            if (existes.Any())
            {
                result.Error("员工添加重复，请重新选择。");
            }

            if (result.Succeed)
            {
                entity = this.Add(entity);
            }
            result.Data = entity;

            return result;
        }

        private Guid? GetEmployeeIdByOperatorUser()
        {
            Employee? employee = null;

            if (this.OperatorUser != null)
            {
                employee = this.GetEntity<Employee>(p => p.Uid == this.OperatorUser.Uid);
            }
            return employee?.ID;
        }

        /// <summary>
        /// 删除员工薪级工龄计算记录
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeDeductCalculate(EmployeeDeductCalculate entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeDeductCalculate>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeDeductCalculate

        #region EmployeeDeductWorkingAge

        public BizResult<EmployeeDeductWorkingAge> AddEmployeeDeductWorkingAge(EmployeeDeductWorkingAge entity)
        {
            var result = new BizResult<EmployeeDeductWorkingAge>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            var existes = this.GetEntities<EmployeeDeductWorkingAge>(p => p.EmployeeId == entity.EmployeeId && p.Year == entity.Year);

            if (existes.Any())
            {
                result.Error(entity.Year + "员工添加重复，请重新选择。");
            }

            if (result.Succeed)
            {
                //entity.RecordEmployeeId = GetEmployeeIdByOperatorUser();

                entity = this.Add(entity);
            }
            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeDeductWorkingAge> UpdateEmployeeDeductWorkingAge(EmployeeDeductWorkingAge entity)
        {
            var result = new BizResult<EmployeeDeductWorkingAge>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeDeductWorkingAge>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                var existes = this.GetEntities<EmployeeDeductWorkingAge>(p => p.EmployeeId == entity.EmployeeId && p.Year == entity.Year && p.ID != entity.ID);

                if (existes.Any())
                {
                    result.Error(entity.Year + "年工龄不能重复扣除！");
                }

                if (result.Succeed)
                {
                    if (this.Update(ref entity))
                    {
                        result.Data = entity;
                    }
                    else
                    {
                        this.Detach(entity);
                        result.Data = dbEntity;
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 删除员工扣减工龄
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeDeductWorkingAge(EmployeeDeductWorkingAge entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeDeductWorkingAge>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeDeductWorkingAge

        #region EmployeeIncentive

        public BizResult<EmployeeIncentive> AddEmployeeIncentive(EmployeeIncentive entity)
        {
            var result = new BizResult<EmployeeIncentive>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeIncentive> UpdateEmployeeIncentive(EmployeeIncentive entity)
        {
            var result = new BizResult<EmployeeIncentive>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeIncentive>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }
            return result;
        }

        /// <summary>
        /// 删除员工奖惩
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeIncentive(EmployeeIncentive entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeIncentive>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeIncentive

        #region EmployeeHealth

        public BizResult<EmployeeHealth> UpdateEmployeeHealth(EmployeeHealth entity)
        {
            var result = new BizResult<EmployeeHealth>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeHealth>(id);

            if (dbEntity == null)
            {
                dbEntity = this.Add(entity);
            }
            else
            {
                dbEntity = this.Update(entity);
            }
            result.Data = dbEntity;

            return result;
        }

        #endregion EmployeeHealth

        #region EmployeeAccident

        public BizResult<EmployeeAccident> AddEmployeeAccident(EmployeeAccident entity)
        {
            var result = new BizResult<EmployeeAccident>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeAccident> UpdateEmployeeAccident(EmployeeAccident entity)
        {
            var result = new BizResult<EmployeeAccident>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeAccident>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }
            return result;
        }

        /// <summary>
        /// 删除员工医疗事故
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeAccident(EmployeeAccident entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeAccident>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeAccident

        #region EmployeeTeach

        public BizResult<EmployeeTeach> AddEmployeeTeach(EmployeeTeach entity)
        {
            var result = new BizResult<EmployeeTeach>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeTeach> UpdateEmployeeTeach(EmployeeTeach entity)
        {
            var result = new BizResult<EmployeeTeach>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeTeach>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }
            return result;
        }

        /// <summary>
        /// 删除员工教学信息
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeTeach(EmployeeTeach entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeTeach>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeTeach

        #region EmployeeRelation

        public BizResult<EmployeeRelation> AddEmployeeRelation(EmployeeRelation entity)
        {
            var result = new BizResult<EmployeeRelation>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeRelation> UpdateEmployeeRelation(EmployeeRelation entity)
        {
            var result = new BizResult<EmployeeRelation>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeRelation>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }
            return result;
        }

        /// <summary>
        /// 删除员工社会关系
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeRelation(EmployeeRelation entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeRelation>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeRelation

        #region EmployeeArticle

        public BizResult<EmployeeArticle> AddEmployeeArticle(EmployeeArticle entity)
        {
            var result = new BizResult<EmployeeArticle>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeArticle> UpdateEmployeeArticle(EmployeeArticle entity)
        {
            var result = new BizResult<EmployeeArticle>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeArticle>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }
            return result;
        }

        /// <summary>
        /// 删除员工论文信息
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeArticle(EmployeeArticle entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeArticle>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeArticle

        #region EmployeeClass

        public BizResult<EmployeeClass> AddEmployeeClass(EmployeeClass entity)
        {
            var result = new BizResult<EmployeeClass>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeClass> UpdateEmployeeClass(EmployeeClass entity)
        {
            var result = new BizResult<EmployeeClass>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeClass>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }
            return result;
        }

        /// <summary>
        /// 删除员工课题信息
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeClass(EmployeeClass entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeClass>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeClass

        #region EmployeePatent

        public BizResult<EmployeePatent> AddEmployeePatent(EmployeePatent entity)
        {
            var result = new BizResult<EmployeePatent>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        public BizResult<EmployeePatent> UpdateEmployeePatent(EmployeePatent entity)
        {
            var result = new BizResult<EmployeePatent>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeePatent>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }
            return result;
        }

        /// <summary>
        /// 删除员工专利信息
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeePatent(EmployeePatent entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeePatent>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeePatent

        #region EmployeeTeacher

        public BizResult<EmployeeTeacher> AddEmployeeTeacher(EmployeeTeacher entity)
        {
            var result = new BizResult<EmployeeTeacher>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeTeacher> UpdateEmployeeTeacher(EmployeeTeacher entity)
        {
            var result = new BizResult<EmployeeTeacher>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeTeacher>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }
            return result;
        }

        /// <summary>
        /// 删除员工导师信息
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeTeacher(EmployeeTeacher entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeTeacher>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeTeacher

        #region EmployeeAward

        public BizResult<EmployeeAward> AddEmployeeAward(EmployeeAward entity)
        {
            var result = new BizResult<EmployeeAward>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeAward> UpdateEmployeeAward(EmployeeAward entity)
        {
            var result = new BizResult<EmployeeAward>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeAward>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }
            return result;
        }

        /// <summary>
        /// 删除员工获奖信息
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeAward(EmployeeAward entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeAward>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        #endregion EmployeeAward

        #region EmployeeHighTalent

        public BizResult<EmployeeHighTalent> AddEmployeeHighTalent(EmployeeHighTalent entity)
        {
            var result = new BizResult<EmployeeHighTalent>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        public BizResult DeleteEmployeeHighTalent(EmployeeHighTalent entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeHighTalent>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Delete(entity);
            }
            return result;
        }

        public BizResult updateEmployeeHighTalent(EmployeeHighTalent entity)
        {
            var result = new BizResult<EmployeeHighTalent>();

            var dbEntity = this.Get<EmployeeHighTalent>(entity.ID);
            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }
            return result;
        }

        #endregion EmployeeHighTalent

        #region EmployeeDeptHistory

        public BizResult<EmployeeDeptHistory> UpdateEmployeeDept(EmployeeDeptHistory entity)
        {
            var result = new BizResult<EmployeeDeptHistory>();

            var id = entity.EmployeeId;
            var dbEntity = this.Get<Employee>(id);

            if (dbEntity == null)
            {
                result.Error("员工不存在");
            }
            else
            {
                var employee = new Entities.Employee()
                {
                    ID = entity.EmployeeId,
                    DeptId = entity.AdjustDeptId
                };

                this.Update<Employee>(employee, false);

                // 增加变更记录
                entity.Remark = JHR.Common.Consts.EmployeeDeptHistory.Remark;
                result = AddEmployeeDeptHistory(entity);
                this.SaveChanges();
            }

            return result;
        }

        public BizResult<EmployeeDeptHistory> BatchUpdateEmployeeDept(EmployeeDeptHistory entity, List<Guid> Ids, ref List<int> uids)
        {
            var result = new BizResult<EmployeeDeptHistory>();

            var id = entity.EmployeeId;
            var dbEntity = this.Get<Employee>(id);

            var currentMonth = DateTime.Now.ToString("yyyy-MM");

            if (Ids == null || !Ids.Any())
            {
                result.Error("请选择需要变更部门的员工");
            }

            var editEmpDeptList = this.GetEntities<Employee>(p => Ids!.Contains(p.ID));

            //foreach (var item in editEmpDeptList)
            //{
            //    //节假日加班费
            //    var holiday = this.GetEntity<AttHolidayOTRecordDetail>(x => x.EmployeeId == item.ID && x.AttHolidayOTRecord.RecordDate.Value.Year == DateTime.Now.Year && x.AttHolidayOTRecord.RecordDate.Value.Month == DateTime.Now.Month && x.AttHolidayOTRecord.EnumStatus != AttHolidayOTRecordStatus.UnKnown);
            //    //中夜班费
            //    var monthShift = this.GetEntity<AttMonthShiftRecordDetail>(x => x.EmployeeId == item.ID && x.AttMonthShiftRecord.RecordMonth == currentMonth && x.AttMonthShiftRecord.EnumStatus != AttMonthShiftRecordStatus.UnCommitted);
            //    //考勤
            //    var dayOff = this.GetEntity<AttDayOffRecordDetail>(x => x.EmployeeId == item.ID && x.AttDayOffRecord.RecordMonth == currentMonth && x.AttDayOffRecord.EnumStatus == AttDayOffRecordStatus.Committed);
            //    if (holiday != null || monthShift != null || dayOff != null)
            //    {
            //        result.Error($"{item.DisplayName}在当月存在已提交的中夜班费/节日加班费/考勤数据");
            //    }
            //}

            if (result.Succeed)
            {
                foreach (var emp in editEmpDeptList)
                {
                    // 增加变更记录
                    var employeeDeptHistory = new EmployeeDeptHistory();
                    employeeDeptHistory.ID = CombGuid.NewGuid();
                    employeeDeptHistory.EmployeeId = emp.ID;
                    employeeDeptHistory.PreDeptId = emp.DeptId;
                    employeeDeptHistory.AdjustDeptId = entity.AdjustDeptId;
                    entity.Remark = JHR.Common.Consts.EmployeeDeptHistory.Remark;
                    result = AddEmployeeDeptHistory(employeeDeptHistory);

                    emp.DeptId = entity.AdjustDeptId;
                    this.Update(emp, false);

                    if (uids.IndexOf(emp.Uid) < 0)
                    {
                        uids.Add(emp.Uid);
                    }
                }

                this.SaveChanges();
            }

            return result;
        }

        public BizResult<EmployeeDeptHistory> AddEmployeeDeptHistory(EmployeeDeptHistory entity)
        {
            var result = new BizResult<EmployeeDeptHistory>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        #endregion EmployeeDeptHistory

        #region EmployeeList

        public DataSet QueryEmployeeList(string sql)
        {
            DataSet ds = new DataSet();
            using (var dataSet = this.DbContext.ExecuteDataSet(CommandType.Text, sql))
            {
                ds = dataSet;
            }

            return ds;
        }

        public DbDataReader QueryEmployeeListReader(string sql)
        {
            return this.ExecuteReader(CommandType.Text, sql);
        }

        public List<object> CreateHeadData(string field, string fieldName)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add(new DataColumn("displayName"));
            dt.Columns.Add(new DataColumn("propName"));

            var fields = field.Split('|');
            var fieldNames = fieldName.Split('|');

            for (int i = 0; i < fields.Length; i++)
            {
                if (string.IsNullOrEmpty(fields[i]))
                {
                    var newrow1 = dt.NewRow();
                    newrow1[0] = fieldName[i];
                    newrow1[1] = fields[i].Substring(fields[i].IndexOf(".") + 1);
                    dt.Rows.Add(newrow1);
                }
            }
            return Utility.ConvertToList(dt);
        }

        public BizResult<EmployeeList> SaveEmployeeList(EmployeeList entity)
        {
            var result = new BizResult<EmployeeList>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
                entity = this.Add(entity);
            }
            else
            {
                entity = this.Update(entity);
            }

            result.Data = entity;

            return result;
        }

        /// <summary>
        /// 删除高级查询
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeList(EmployeeList entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeList>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Remove(dbEntity);
            }
            return result;
        }

        #endregion EmployeeList

        #region OtherEmpManage

        public void DelOtherEmp(OtherEmployeeInfo entity)
        {
            this.Delete(entity);
        }

        public void SaveOtherEmp(OtherEmployeeInfo entity)
        {
            if (entity.ID == default)
            {
                entity.ID = CombGuid.NewGuid();
                this.Add(entity);
            }
            else
            {
                this.Update(entity);
            }
        }

        #endregion OtherEmpManage

        #region PostInformation

        public BizResult<AttDayOffRecordPost> AddAttDayOffRecordPost(AttDayOffRecordPost entity)
        {
            var result = new BizResult<AttDayOffRecordPost>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        public BizResult<Information> AddInformation(Information entity)
        {
            var result = new BizResult<Information>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            result.Data = entity;

            return result;
        }

        #endregion PostInformation

        #region 导入

        public (bool isok, string msg) ImportEmployeeSocialInsurance(DataTable table, string? currentUser)
        {
            bool uidempty = false;
            bool accempty = false;

            foreach (DataRow row in table.Rows)
            {
                if (row.IsNull(2) || row[2].IsEmpty())
                {
                    uidempty = true;
                    break;
                }
                if ((row.IsNull(3) || row[3].IsEmpty()) && (row.IsNull(4) || row[4].IsEmpty()) && (row.IsNull(5) || row[5].IsEmpty()))
                {
                    accempty = true;
                    break;
                }
            }

            if (uidempty || accempty)
            {
                return (false, "导入存在空数据");
            }

            table.Columns.Add("Creator");
            table.Columns.Add("CreateTime", typeof(DateTime));
            table.TableName = "EmployeeSocialInsuranceHistory";
            foreach (DataRow row in table.Rows)
            {
                row["Creator"] = currentUser;
                row["CreateTime"] = SysDateTime.Now;
                int uid;
                if (int.TryParse(row[2].ToString(), out uid))
                {
                    var emp = this.GetEntity<Employee>(o => o.Uid == uid);
                    if (emp != null)
                    {
                        var entity = this.Get<EmployeeSocialInsurance>(emp.ID);
                        if (entity != null)
                        {
                            if (row[3].AsString().Trim().Length > 0)
                                entity.BankAccount1 = row[3].AsString().Trim();
                            if (row[4].AsString().Trim().Length > 0)
                                entity.BankAccount2 = row[4].AsString().Trim();
                            if (row[5].AsString().Trim().Length > 0)
                                entity.BankAccount3 = row[5].AsString().Trim();
                            this.Update(entity, false);
                        }
                        else
                        {
                            entity = new EmployeeSocialInsurance();
                            entity.ID = emp.ID;
                            if (row[3].AsString().Trim().Length > 0)
                                entity.BankAccount1 = row[3].AsString().Trim();
                            if (row[4].AsString().Trim().Length > 0)
                                entity.BankAccount2 = row[4].AsString().Trim();
                            if (row[5].AsString().Trim().Length > 0)
                                entity.BankAccount3 = row[5].AsString().Trim();
                            this.Add(entity, false);
                        }
                    }
                }

                if (Config.IsDM)
                {
                    Guid groupid = Guid.Empty;
                    Guid.TryParse(row["GroupID"].ToString(), out groupid);
                    EmployeeSocialInsuranceHistory his = new EmployeeSocialInsuranceHistory()
                    {
                        GroupID = groupid,
                        Uid = row["Uid"].ToString(),
                        BankAccount1 = row["BankAccount1"].ToString(),
                        BankAccount2 = row["BankAccount2"].ToString(),
                        BankAccount3 = row["BankAccount3"].ToString(),
                        CreateTime = SysDateTime.Now,
                        Creator = currentUser
                    };
                    this.Add(his, false);
                }
            }

            this.SaveChanges();

            if (!Config.IsDM)
            {
                using (var conn = (SqlConnection)this.DbConnection)
                {
                    using (SqlBulkCopy sqlBulkCopy = new SqlBulkCopy(conn))
                    {
                        sqlBulkCopy.DestinationTableName = table.TableName;
                        sqlBulkCopy.WriteToServer(table);
                    }
                }
            }

            return (true, "导入成功");
        }

        public (bool isok, string msg) SqlBulkCopyProc(DataTable table, string procName, Guid groupid)
        {
            var result = (false, string.Empty);
            using (var conn = (SqlConnection)this.DbConnection)
            {
                using (SqlBulkCopy sqlBulkCopy = new SqlBulkCopy(conn))
                {
                    sqlBulkCopy.DestinationTableName = table.TableName;
                    sqlBulkCopy.WriteToServer(table);
                }
                using SqlCommand command = new SqlCommand
                {
                    Connection = conn,
                    CommandType = CommandType.StoredProcedure,
                    CommandText = procName
                };
                command.Parameters.Add(this.CreateParameter("@OperatorUser", this.OperatorUser?.ID));
                command.Parameters.Add(this.CreateParameter("@GroupID", groupid));

                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    result = (reader.GetValueExt<bool>("isok"), reader.GetValueExt<string>("msg") ?? "");
                }

                reader.Close();
                command.Dispose();
            }
            return result;
        }
        public (bool isok, string msg) DmBulkCopyProc(DataTable table, string procName, Guid groupid)
        {
            var result = (false, string.Empty);
            using (var conn = this.DbConnection)
            {
                result = this.InsertDataFromDataTable(table, table.TableName);
                if (result.Item1)
                {
                    var p1 = this.CreateParameter("OperatorUser", this.OperatorUser?.ID);
                    var p2 = this.CreateParameter("GroupID", groupid);

                    using (var reader = this.DbContext.ExecuteReader(System.Data.CommandType.StoredProcedure, procName, p1,p2))
                    {
                        while (reader.Read())
                        {
                            result = (reader.GetValueExt<bool>("isok"), reader.GetValueExt<string>("msg") ?? "");
                        }
                    }
                }
            }
            return result;
        }
        public (bool isOk, string msg) InsertDataFromDataTable(DataTable table, string tableName)
        {
            var result = (isOk: false, msg: string.Empty);

            using (var conn = this.DbConnection)
            {
                try
                {
                    conn.Open();
                    var commandText = $"INSERT INTO \"JHR\".\"{tableName}\" VALUES ({string.Join(", ", Enumerable.Range(0, table.Columns.Count - 1).Select(i => "?"))})";

                    using (var command = this.CreateCommand(CommandType.Text, commandText))
                    {
                        foreach (DataRow row in table.Rows)
                        {
                            for (int i = 1; i < table.Columns.Count; i++)
                            {
                                command.Parameters.Add(this.CreateParameter($"@param{i}", row[i]));
                            }
                            command.ExecuteNonQuery();
                            command.Parameters.Clear();
                        }
                    }
                    result = (true, "Data inserted successfully.");
                }
                catch (Exception ex)
                {
                    result = (false, $"Error inserting data: {ex.Message}");
                }
            }

            return result;
        }
        /// <summary>
        /// 获取表结构
        /// </summary>
        /// <param name="tableName">表明</param>
        /// <param name="groupiddefault"> GroupID 列的默认值 </param>
        /// <returns></returns>
        public DataTable GetDataTable(string tableName, object groupiddefault)
        {
            var dt = new DataTable(tableName);
            string sql = @"SELECT
                                a.[name],
                                b.[name] typename
                                ,C.[value] as columnDescription
                                ,a.column_id
                            FROM sys.columns a
                             inner join sys.objects d on a.object_id = d.object_id  and d.[type]='U' and  d.[name]=@tablename
                             left join sys.types b ON b.user_type_id = a.user_type_id
                             left join sys.extended_properties C on C.major_id = a.object_id and C.minor_id = a.column_id
                             ORDER BY a.column_id  ";

            if (Config.IsDM)
            {
                //  数据库名，读取不到，暂时写死JHR
                sql = @$"select
                              aa.COLUMN_NAME as name,
                              aa.DATA_TYPE as typename,
                              ss.comments as columnDescription,
                              aa.COLUMN_ID as column_id
                            from
                              all_tab_columns aa
                              inner join all_col_comments ss on ss.COLUMN_NAME = aa.COLUMN_NAME
                              and ss.Table_Name = aa.Table_Name
                            where
                              aa.Table_Name = ?
                              and aa.owner = 'JHR'
                              and ss.SCHEMA_NAME = 'JHR'
                            order by
                              aa.column_id";
            }
            var p1 = this.CreateParameter("@tablename", tableName);

            using (var reader = this.DbContext.ExecuteReader(CommandType.Text, sql, p1))
            {
                while (reader.Read())
                {
                    var col = new DataColumn
                    {
                        ColumnName = reader.GetValueExt<string>("name"),
                        DataType = DBTypeToCSharpType(reader.GetValueExt<string>("typename") ?? ""),
                        Caption = reader.IsDBNullExt("columnDescription") ? string.Empty : reader.GetValueExt<string>("columnDescription"),
                    };
                    if (string.Compare(col.ColumnName, "GroupID", StringComparison.OrdinalIgnoreCase) == 0)
                    {
                        col.DefaultValue = groupiddefault;
                    }

                    dt.Columns.Add(col);
                }
            }
            return dt;
        }

        /// <summary>
        /// 将数据库数据类型字符串，转为C#数据类型字符串。
        /// </summary>
        /// <param name="dbType">数据库数据类型字符串。</param>
        /// <returns>C#数据类型字符串。</returns>
        private Type DBTypeToCSharpType(string dbType)
        {
            Type? cSharpType = default;
            switch (dbType.ToLower())
            {
                case "bit":
                    cSharpType = typeof(bool);
                    break;

                case "tinyint":
                    cSharpType = typeof(byte);
                    break;

                case "smallint":
                    cSharpType = typeof(short);
                    break;

                case "int":
                    cSharpType = typeof(int);
                    break;

                case "bigint":
                    cSharpType = typeof(long);
                    break;

                case "real":
                    cSharpType = typeof(float);
                    break;

                case "float":
                    cSharpType = typeof(double);
                    break;

                case "smallmoney":
                case "money":
                case "decimal":
                case "numeric":
                    cSharpType = typeof(decimal);
                    break;

                case "char":
                case "varchar":
                case "nchar":
                case "nvarchar":
                case "text":
                case "ntext":
                    cSharpType = typeof(string);
                    break;

                case "samlltime":
                case "date":
                case "smalldatetime":
                case "datetime":
                case "datetime2":
                case "datetimeoffset":
                    cSharpType = typeof(System.DateTime);
                    break;

                case "timestamp":
                case "image":
                case "binary":
                case "varbinary":
                    cSharpType = typeof(byte[]);
                    break;

                case "uniqueidentifier":
                    cSharpType = typeof(System.Guid);
                    break;

                case "variant":
                case "sql_variant":
                    cSharpType = typeof(object);
                    break;

                default:
                    cSharpType = typeof(string);
                    break;
            }
            return cSharpType;
        }

        #endregion 导入

        #region EmployeeStationCurrent

        public BizResult<EmployeeStationCurrent> AddEmployeeStationCurrent(EmployeeStationCurrent entity)
        {
            var result = new BizResult<EmployeeStationCurrent>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            this.SaveChanges();
            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeStationCurrent> UpdateEmployeeStationCurrent(EmployeeStationCurrent entity)
        {
            var result = new BizResult<EmployeeStationCurrent>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeStationCurrent>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Update(ref entity, true);
                if (result.Succeed)
                {
                    this.SaveChanges();
                }
                else
                {
                    this.Detach(entity);
                }
                result.Data = entity;
            }
            return result;
        }

        #endregion EmployeeStationCurrent

        #region EmployeeOAInsertFail

        public BizResult<EmployeeOAInsertFail> AddEmployeeOAInsertFail(EmployeeOAInsertFail entity)
        {
            var result = new BizResult<EmployeeOAInsertFail>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity = this.Add(entity);

            this.SaveChanges();
            result.Data = entity;

            return result;
        }

        public BizResult<EmployeeOAInsertFail> DeleteEmployeeOAInsertFail(EmployeeOAInsertFail entity)
        {
            var result = new BizResult<EmployeeOAInsertFail>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeOAInsertFail>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                this.Remove(dbEntity);
                result.Data = entity;
            }
            return result;
        }

        #endregion EmployeeOAInsertFail

        #region 工资  EmployeeWage

        public BizResult<EmployeeWage> EditEmployeeWage(EmployeeWage entity, bool IsEditBase)
        {
            var result = new BizResult<EmployeeWage>();

            result = this.CheckedEditEmployeeWage(entity);

            if (result.Succeed)
            {
                var employeeWage = this.Get<EmployeeWage>(entity.ID);

                bool IsAdd = employeeWage == null;

                // 非年薪，把 相关字段清空
                if (!entity.IsAnnualSalary)
                {
                    entity.AnnualSalary = decimal.Zero;
                    entity.HousePoster = decimal.Zero;
                    entity.ExpirationDate = null;
                }

                if (IsAdd)
                {
                    entity.PreviousSocialSecurity = null;
                    entity.PreviousProvidentFund = null;

                    entity = this.Add(entity);
                }
                else
                {
                    // 编辑社保基数和公积金基数
                    if (IsEditBase)
                    {
                        entity.PreviousSocialSecurity = employeeWage?.PreviousSocialSecurity;
                        entity.PreviousProvidentFund = employeeWage?.PreviousProvidentFund;
                    }

                    this.Update(ref entity);
                }

                result.Data = entity;
            }

            return result;
        }
        #endregion

        #region 人力资源数据统计报表

        public BizResult<EmployeeHighTalent> HRStatisticsReport()
        {
            var result = new BizResult<EmployeeHighTalent>();

            var entities = this.GetQuery<ViewPersonnelInformationExport>()
                // 在职状态为“在职”
                .Where(c=> c.JobStatus == "1")
                // 数据作废标志为“正常”
                .Where(c => c.DeleteFlag == "1")
                .Select(c => new
                {
                    c.UserID,
                    // 人员类别
                    c.UserCategory,
                    // 专技职称1（聘任）
                    c.TechnicalTitleCode,
                    // 专技职称1（资格）
                    c.TechnicalTitleCodeZG,
                    // 最高学历
                    c.MaxEducation,
                    // 最高学位
                    c.MaxDegree
                }).ToList();

           entities = entities.Distinct().ToList();
           
            var res = new HRStatisticsReport
            {
                ID = CombGuid.NewGuid(),
                GeneratedTime = DateTime.Now,
                // 员工总数
                TotalNumber = entities.Count(),
                // 住培人数
                ResidentTrainingNumber = entities.Where(c => c.UserCategory == UserCategoryType.Training.GetDesc()).Count(),
                // 卫技人员
                MedicalTechnicalStaffNumber = entities.Where(c =>
                    (
                        c.UserCategory == UserCategoryType.Clinician.GetDesc() ||
                        c.UserCategory == UserCategoryType.Nursing.GetDesc() ||
                        c.UserCategory == UserCategoryType.MedicalTechnicians.GetDesc() ||
                        c.UserCategory == UserCategoryType.Pharmacists.GetDesc()
                    ) ||
                    (
                        c.UserCategory == UserCategoryType.Managers.GetDesc() &&
                        (
                            // 卫生技术人员（医生）
                            c.TechnicalTitleCode == TechnicalTitleType.ChiefPhysician.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.AssociateChiefPhysician.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.AttendingPhysician.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.Physician.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.MedicalAssistant.GetDesc() ||
                            // 卫生技术人员（药剂）
                            c.TechnicalTitleCode == TechnicalTitleType.ChiefPharmacist.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.AssociateChiefPharmacist.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.PharmacySupervisor.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.Pharmacist.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.PharmacyAssistant.GetDesc() ||
                            // 卫生技术人员（护理）
                            c.TechnicalTitleCode == TechnicalTitleType.ChiefNurse.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.AssociateChiefNurse.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.NursingSupervisor.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.Nurse.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.NursingAssistant.GetDesc() ||
                            // 卫生技术人员（医技）
                            c.TechnicalTitleCode == TechnicalTitleType.ChiefTechnologist.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.AssociateChiefTechnologist.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.TechnologistSupervisor.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.Technologist.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.Technician.GetDesc() ||
                            // 卫生技术人员（研究）
                            c.TechnicalTitleCode == TechnicalTitleType.ResearcherMedicalScience.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.AssociateResearcherMedicalScience.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.AssistantResearcherMedicalScience.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.ResearchInternMedicalScience.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.ResearcherHealthCare.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.AssociateResearcherHealthCare.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.AssistantResearcherHospitalManagement.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.ResearchInternHospitalManagement.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.ResearcherClinicalResearchSupport.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.AssociateResearcherClinicalResearchSupport.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.AssistantResearcherClinicalResearchSupport.GetDesc() ||
                            c.TechnicalTitleCode == TechnicalTitleType.ResearchInternClinicalResearchSupport.GetDesc()
                        )
                    )
                ).Count(),
                // 其他专技人员
                OtherSpecializedStaffNumber = entities.Where(c => c.UserCategory == UserCategoryType.OtherProfessional.GetDesc()).Count(),
                // 科研人员
                ScientificResearchStaffNumber = entities.Where(c => c.UserCategory == UserCategoryType.Researchers.GetDesc()).Count(),
                // 管理人员
                ManagementStaffNumber = entities.Where(c=> 
                        c.UserCategory == UserCategoryType.Managers.GetDesc() &&
                        (
                            // 卫生技术人员（医生）
                            c.TechnicalTitleCode != TechnicalTitleType.ChiefPhysician.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.AssociateChiefPhysician.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.AttendingPhysician.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.Physician.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.MedicalAssistant.GetDesc() ||
                            // 卫生技术人员（药剂）
                            c.TechnicalTitleCode != TechnicalTitleType.ChiefPharmacist.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.AssociateChiefPharmacist.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.PharmacySupervisor.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.Pharmacist.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.PharmacyAssistant.GetDesc() ||
                            // 卫生技术人员（护理）
                            c.TechnicalTitleCode != TechnicalTitleType.ChiefNurse.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.AssociateChiefNurse.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.NursingSupervisor.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.Nurse.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.NursingAssistant.GetDesc() ||
                            // 卫生技术人员（医技）
                            c.TechnicalTitleCode != TechnicalTitleType.ChiefTechnologist.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.AssociateChiefTechnologist.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.TechnologistSupervisor.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.Technologist.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.Technician.GetDesc() ||
                            // 卫生技术人员（研究）
                            c.TechnicalTitleCode != TechnicalTitleType.ResearcherMedicalScience.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.AssociateResearcherMedicalScience.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.AssistantResearcherMedicalScience.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.ResearchInternMedicalScience.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.ResearcherHealthCare.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.AssociateResearcherHealthCare.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.AssistantResearcherHospitalManagement.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.ResearchInternHospitalManagement.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.ResearcherClinicalResearchSupport.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.AssociateResearcherClinicalResearchSupport.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.AssistantResearcherClinicalResearchSupport.GetDesc() ||
                            c.TechnicalTitleCode != TechnicalTitleType.ResearchInternClinicalResearchSupport.GetDesc()
                        )).Count(),
                // 工勤人员
                SupportStaffNumber = entities.Where(c => c.UserCategory == UserCategoryType.Worker.GetDesc()).Count(),
                // 文员辅助人员
                AdminAssistantStaffNumber = entities.Where(c => c.UserCategory == UserCategoryType.Clerk.GetDesc()).Count(),
                // 执业医师数
                PracticingPhysicianNumber = entities.Where(c =>
                        c.UserCategory == UserCategoryType.Clinician.GetDesc() ||
                        (
                            c.UserCategory == UserCategoryType.Managers.GetDesc() &&
                            (
                                // 卫生技术人员（医生）
                                c.TechnicalTitleCode == TechnicalTitleType.ChiefPhysician.GetDesc() ||
                                c.TechnicalTitleCode == TechnicalTitleType.AssociateChiefPhysician.GetDesc() ||
                                c.TechnicalTitleCode == TechnicalTitleType.AttendingPhysician.GetDesc() ||
                                c.TechnicalTitleCode == TechnicalTitleType.Physician.GetDesc() ||
                                c.TechnicalTitleCode == TechnicalTitleType.MedicalAssistant.GetDesc()
                             )
                         )).Count(),
                // 执业医师中研究生数
                GraduatePhysicianNumber = entities.Where(c =>
                        (c.MaxEducation == MaxEducationType.DoctoralGraduate.GetDesc() || c.MaxEducation == MaxEducationType.MasterGraduate.GetDesc()) &&
                        (
                            c.UserCategory == UserCategoryType.Clinician.GetDesc() ||
                            (
                                c.UserCategory == UserCategoryType.Managers.GetDesc() &&
                                (
                                    // 卫生技术人员（医生）
                                    c.TechnicalTitleCode == TechnicalTitleType.ChiefPhysician.GetDesc() ||
                                    c.TechnicalTitleCode == TechnicalTitleType.AssociateChiefPhysician.GetDesc() ||
                                    c.TechnicalTitleCode == TechnicalTitleType.AttendingPhysician.GetDesc() ||
                                    c.TechnicalTitleCode == TechnicalTitleType.Physician.GetDesc() ||
                                    c.TechnicalTitleCode == TechnicalTitleType.MedicalAssistant.GetDesc()
                                 )
                             ))
                         ).Count(),
                // 执业医师中博士数
                DoctoratePhysicianNumber = entities.Where(c =>
                        c.MaxDegree == (int)MaxDegreeType.Doctor &&
                        (
                            c.UserCategory == UserCategoryType.Clinician.GetDesc() ||
                            (
                                c.UserCategory == UserCategoryType.Managers.GetDesc() &&
                                (
                                    // 卫生技术人员（医生）
                                    c.TechnicalTitleCode == TechnicalTitleType.ChiefPhysician.GetDesc() ||
                                    c.TechnicalTitleCode == TechnicalTitleType.AssociateChiefPhysician.GetDesc() ||
                                    c.TechnicalTitleCode == TechnicalTitleType.AttendingPhysician.GetDesc() ||
                                    c.TechnicalTitleCode == TechnicalTitleType.Physician.GetDesc() ||
                                    c.TechnicalTitleCode == TechnicalTitleType.MedicalAssistant.GetDesc()
                                 )
                             ))
                         ).Count(),
                // 执业医师中硕士数
                MastersPhysicianNumber = entities.Where(c =>
                        c.MaxDegree == (int)MaxDegreeType.Master &&
                        (
                            c.UserCategory == UserCategoryType.Clinician.GetDesc() ||
                            (
                                c.UserCategory == UserCategoryType.Managers.GetDesc() &&
                                (
                                    // 卫生技术人员（医生）
                                    c.TechnicalTitleCode == TechnicalTitleType.ChiefPhysician.GetDesc() ||
                                    c.TechnicalTitleCode == TechnicalTitleType.AssociateChiefPhysician.GetDesc() ||
                                    c.TechnicalTitleCode == TechnicalTitleType.AttendingPhysician.GetDesc() ||
                                    c.TechnicalTitleCode == TechnicalTitleType.Physician.GetDesc() ||
                                    c.TechnicalTitleCode == TechnicalTitleType.MedicalAssistant.GetDesc()
                                 )
                             ))
                         ).Count(),
                // 注册护士数
                RegisteredNurseNumber = entities.Where(c =>
                    c.UserCategory == UserCategoryType.Nursing.GetDesc() ||
                    (
                        c.UserCategory == UserCategoryType.Managers.GetDesc() &&
                        (
                           // 卫生技术人员（护理）
                           c.TechnicalTitleCode == TechnicalTitleType.ChiefNurse.GetDesc() ||
                           c.TechnicalTitleCode == TechnicalTitleType.AssociateChiefNurse.GetDesc() ||
                           c.TechnicalTitleCode == TechnicalTitleType.NursingSupervisor.GetDesc() ||
                           c.TechnicalTitleCode == TechnicalTitleType.Nurse.GetDesc() ||
                           c.TechnicalTitleCode == TechnicalTitleType.NursingAssistant.GetDesc()
                        )
                    )
                    ).Count(),
                // 注册护士本科以上人数
                BachelorNurseNumber = entities.Where(c =>
                    (
                        c.MaxEducation == MaxEducationType.DoctoralGraduate.GetDesc() ||
                        c.MaxEducation == MaxEducationType.MasterGraduate.GetDesc() ||
                        c.MaxEducation == MaxEducationType.Undergraduate.GetDesc()
                    ) &&
                    (
                        c.UserCategory == UserCategoryType.Nursing.GetDesc() ||
                        (
                            c.UserCategory == UserCategoryType.Managers.GetDesc() &&
                            (
                               // 卫生技术人员（护理）
                               c.TechnicalTitleCode == TechnicalTitleType.ChiefNurse.GetDesc() ||
                               c.TechnicalTitleCode == TechnicalTitleType.AssociateChiefNurse.GetDesc() ||
                               c.TechnicalTitleCode == TechnicalTitleType.NursingSupervisor.GetDesc() ||
                               c.TechnicalTitleCode == TechnicalTitleType.Nurse.GetDesc() ||
                               c.TechnicalTitleCode == TechnicalTitleType.NursingAssistant.GetDesc()
                            )
                        )
                    )
                    ).Count(),
                // 注册护士中大专以上人数
                AssociateNurseNumber = entities.Where(c =>
                    (
                        c.MaxEducation == MaxEducationType.DoctoralGraduate.GetDesc() ||
                        c.MaxEducation == MaxEducationType.MasterGraduate.GetDesc() ||
                        c.MaxEducation == MaxEducationType.Undergraduate.GetDesc() ||
                        c.MaxEducation == MaxEducationType.CollegeSpecialty.GetDesc()
                    ) &&
                    (
                        c.UserCategory == UserCategoryType.Nursing.GetDesc() ||
                        (
                            c.UserCategory == UserCategoryType.Managers.GetDesc() &&
                            (
                               // 卫生技术人员（护理）
                               c.TechnicalTitleCode == TechnicalTitleType.ChiefNurse.GetDesc() ||
                               c.TechnicalTitleCode == TechnicalTitleType.AssociateChiefNurse.GetDesc() ||
                               c.TechnicalTitleCode == TechnicalTitleType.NursingSupervisor.GetDesc() ||
                               c.TechnicalTitleCode == TechnicalTitleType.Nurse.GetDesc() ||
                               c.TechnicalTitleCode == TechnicalTitleType.NursingAssistant.GetDesc()
                            )
                        )
                    )
                    ).Count(),
                // 正高级职称人员数（资格）
                SeniorTitleQualificationNumber = entities.Where(c => 
                    c.TechnicalTitleCodeZG == TechnicalTitleType.ChiefPhysician.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.ChiefPharmacist.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.ChiefNurse.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.ChiefTechnologist.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.ResearcherMedicalScience.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.ResearcherHealthCare.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.ResearcherClinicalResearchSupport.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.Professor.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.ResearcherNaturalScience.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.SeniorChiefEconomist.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.SeniorChiefAccountant.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.SeniorChiefStatistician.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.Archivist.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.ChiefEngineerProfessorLevel.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.SeniorChiefExperimentalist.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.SeniorChiefAuditor.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.SeniorChiefSocialWorker.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.OtherSeniorTitle.GetDesc()
                ).Count(),
                // 副高级职称人员数（资格）
                AssociateSeniorTitleQualificationNumber = entities.Where(c =>
                    c.TechnicalTitleCodeZG == TechnicalTitleType.AssociateChiefPhysician.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.AssociateChiefPharmacist.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.AssociateChiefNurse.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.AssociateChiefTechnologist.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.AssociateResearcherMedicalScience.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.AssociateResearcherHealthCare.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.AssociateResearcherClinicalResearchSupport.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.AssociateProfessor.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.AssociateResearcherNaturalScience.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.SeniorEconomist.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.SeniorAccountant.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.SeniorStatistician.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.Archivist.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.SeniorEngineer.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.SeniorExperimentalist.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.SeniorAuditor.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.SeniorSocialWorker.GetDesc() ||
                    c.TechnicalTitleCodeZG == TechnicalTitleType.OtherAssociateSeniorTitle.GetDesc()
                ).Count(),
                // 正高级职称人员数（聘任）
                SeniorTitleAppointmentNumber = entities.Where(c =>
                    c.TechnicalTitleCode == TechnicalTitleType.ChiefPhysician.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.ChiefPharmacist.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.ChiefNurse.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.ChiefTechnologist.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.ResearcherMedicalScience.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.ResearcherHealthCare.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.ResearcherClinicalResearchSupport.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.Professor.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.ResearcherNaturalScience.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.SeniorChiefEconomist.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.SeniorChiefAccountant.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.SeniorChiefStatistician.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.Archivist.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.ChiefEngineerProfessorLevel.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.SeniorChiefExperimentalist.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.SeniorChiefAuditor.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.SeniorChiefSocialWorker.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.OtherSeniorTitle.GetDesc()
                ).Count(),
                // 副高级职称人员数（聘任）
                AssociateSeniorTitleAppointmentNumber = entities.Where(c =>
                    c.TechnicalTitleCode == TechnicalTitleType.AssociateChiefPhysician.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.AssociateChiefPharmacist.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.AssociateChiefNurse.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.AssociateChiefTechnologist.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.AssociateResearcherMedicalScience.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.AssociateResearcherHealthCare.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.AssociateResearcherClinicalResearchSupport.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.AssociateProfessor.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.AssociateResearcherNaturalScience.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.SeniorEconomist.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.SeniorAccountant.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.SeniorStatistician.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.Archivist.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.SeniorEngineer.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.SeniorExperimentalist.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.SeniorAuditor.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.SeniorSocialWorker.GetDesc() ||
                    c.TechnicalTitleCode == TechnicalTitleType.OtherAssociateSeniorTitle.GetDesc()
                ).Count()
            };

            this.Add<HRStatisticsReport>(res);

            return result;
        }

        #endregion

        #region EmployeeStationNotice

        public BizResult<EmployeeStationNotice> UpdateEmployeeStationNotice(EmployeeStationNotice entity)
        {
            var result = new BizResult<EmployeeStationNotice>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeStationNotice>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在。");
            }
            else
            {
                if (this.Update(ref entity))
                {
                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                    result.Data = dbEntity;
                }
            }
            return result;
        }
        #endregion

        #region 党建

        public BizResult PostPartyInformation(EmployeeHR empHR, List<string> affiliatedBranchs,
            List<string> partyOrganizations, List<string> partyPositions, string partyCode)
        {
            var result = new BizResult();

            // 政治面貌
            var partyDict = this.QueryDictByParentCode(Renji.JHR.Common.Consts.Dicts.PartyCode);

            var part = partyDict.FirstOrDefault(d => d.Remark == partyCode);
            if (part == null)
            {
                result.Error("政治面貌代码不存在。");
            }
            else
            {
                empHR.PartyId = part.ID;

                // 党内职务
                var partyPositionsDict = this.QueryDictByParentCode(Renji.JHR.Common.Consts.Dicts.PartyPositionsCode);
                var empHrDictAdd = new List<EmployeeHRDict>();

                bool errorPartyPosition = false;
                StringBuilder builder = new StringBuilder();

                if (partyOrganizations.Count > partyPositions.Count)
                {
                    for (int i = 0; i < partyOrganizations.Count; i++)
                    {
                        var partyOrganization = partyOrganizations[i];
                        var partyPosition = partyPositions.Count > i ? partyPositions[i] : "";
                        if (partyPosition.Trim().Length > 0)
                        {
                            var partyPositionDict = partyPositionsDict.FirstOrDefault(d => d.Remark.ToLower() == partyPosition.ToLower());
                            if (partyPositionDict == null)
                            {
                                builder.Append(string.Format("当前组织代码{0}，", partyPosition));
                                errorPartyPosition = true;
                            }
                            else
                            {
                                empHrDictAdd.Add(new EmployeeHRDict()
                                {
                                    EmployeeHRId = empHR.ID,
                                    DictId = partyPositionDict.ID,
                                    OrganizationName = partyOrganization.Trim()
                                });
                            }
                        }
                        else
                        {
                            empHrDictAdd.Add(new EmployeeHRDict()
                            {
                                EmployeeHRId = empHR.ID,
                                DictId = Guid.Parse(Renji.JHR.Common.Consts.Dicts.PartyPosition_None),
                                OrganizationName = partyOrganization.Trim()
                            }); ;
                        }
                    }
                }
                else
                {
                    for (int i = 0; i < partyPositions.Count; i++)
                    {
                        var partyPosition = partyPositions[i];
                        var partyOrganization = partyOrganizations.Count > i ? partyOrganizations[i] : "";

                        if (partyPosition.Trim().Length > 0)
                        {
                            var partyPositionDict = partyPositionsDict.FirstOrDefault(d => d.Remark.ToLower() == partyPosition.ToLower());
                            if (partyPositionDict == null)
                            {
                                builder.Append(string.Format("当前组织代码{0}，", partyPosition));
                                errorPartyPosition = true;
                            }
                            else
                            {
                                empHrDictAdd.Add(new EmployeeHRDict()
                                {
                                    EmployeeHRId = empHR.ID,
                                    DictId = partyPositionDict.ID,
                                    OrganizationName = partyOrganization.Trim()
                                }); ;
                            }
                        }
                        else
                        {
                            if (partyOrganization.Trim().Length > 0)
                            {
                                empHrDictAdd.Add(new EmployeeHRDict()
                                {
                                    EmployeeHRId = empHR.ID,
                                    DictId = Guid.Parse(Renji.JHR.Common.Consts.Dicts.PartyPosition_None),
                                    OrganizationName = partyOrganization.Trim()
                                });
                            }
                        }
                    }
                }

                if (errorPartyPosition)
                {
                    builder.Append("不存在。");
                    result.Error(builder.ToString());
                }
                else
                {
                    // 删除当前用户党内职务中间表
                    var empHrDictDelete = this.GetEntities<EmployeeHRDict>(x => x.EmployeeHRId == empHR.ID);
                    foreach (var item in empHrDictDelete)
                    {
                        this.Remove(item, false);
                    }

                    // 拼接党内职务
                    empHR.PartyPositionsIds = "[" + string.Join(",", empHrDictAdd.Select(c => $"\"{c.DictId}\"")) + "]";

                    this.AddRange(empHrDictAdd, false);

                    // 所在支部
                    var empHRAffBraAdd = affiliatedBranchs?
                        .Select(name => new EmployeeHRAffiliatedBranch
                        {
                            EmployeeHRId = empHR.ID,
                            AffiliatedBranch = name
                        })
                        .ToList();

                    // 删除当前用户所在支部
                    var empHRAffBraDelete = this.GetEntities<EmployeeHRAffiliatedBranch>(x => x.EmployeeHRId == empHR.ID);
                    foreach (var item in empHRAffBraDelete)
                    {
                        this.Remove(item, false);
                    }

                    if (empHRAffBraAdd != null)
                    {
                        this.AddRange(empHRAffBraAdd, false);
                    }

                    this.Update(empHR, false);
                    this.SaveChanges();
                }
            }

            

            return result;
        }

        #endregion

        #region 编辑数据检验

        private BizResult<EmployeeWage> CheckedEditEmployeeWage(EmployeeWage entity)
        {
            var result = new BizResult<EmployeeWage>();

            if (entity.ID.IsEmpty())
            {
                result.Error("请先保存基本信息");
            }
            else
            {
                var employee = this.Get<Employee>(entity.ID);
                if (employee == null)
                {
                    result.Error("员工不存在");
                }
            }

            if (entity.ParentStationId.IsEmpty())
            {
                result.Error("岗位大类必选");
            }
            else
            {
                var station = this.BizRepo.GetEntity<Station>(p => p.ID == entity.ParentStationId && !p.ParentId.HasValue && p.IsCategory == true);

                if (station == null)
                {
                    result.Error("岗位大类不存在");
                }
            }

            if (entity.StationId.IsEmpty())
            {
                result.Error("岗位必选");
            }
            else
            {
                var station = this.BizRepo.Get<Station>(entity.StationId);

                if (station == null)
                {
                    result.Error("岗位不存在");
                }
            }

            if (entity.SalaryScaleId.IsEmpty())
            {
                result.Error("薪级工资必选");
            }
            else
            {
                var salaryScale = this.BizRepo.Get<SalaryScale>(entity.SalaryScaleId);

                if (salaryScale == null)
                {
                    result.Error("薪级工资不存在");
                }
            }

            if (entity.StationAllowanceId.IsEmpty())
            {
                result.Error("岗位津贴必选");
            }
            else
            {
                var stationAllowance = this.BizRepo.Get<StationAllowance>(entity.StationAllowanceId);

                if (stationAllowance == null)
                {
                    result.Error("岗位津贴不存在");
                }
            }

            if (entity.WageGroupId.IsEmpty())
            {
                result.Error("工资组必选");
            }
            else
            {
                var dict = this.BizRepo.Get<Dict>(entity.WageGroupId);

                if (dict == null)
                {
                    result.Error("工资组不存在");
                }
            }

            if (entity.SocialSecurity <= 0)
            {
                result.Error("社保基数必须大于零");
            }

            if (entity.ProvidentFund <= 0)
            {
                result.Error("公积金基数必须大于零");
            }

            if (entity.OccupationalPension <= 0)
            {
                result.Error("职业年金必须大于零");
            }

            if (entity.MembershipFees <= 0)
            {
                result.Error("会费必须大于零");
            }

            // 是年薪
            if (entity.IsAnnualSalary)
            {
                if (entity.AnnualSalary <= 0)
                {
                    result.Error("年薪必须大于零");
                }

                if (entity.HousePoster <= 0)
                {
                    result.Error("房贴必须大于零");
                }

                if (!entity.ExpirationDate.HasValue)
                {
                    result.Error("在站到期日不能为空");
                }
                else
                {
                    if (result.Succeed)
                    {
                        var employeeHR = this.Get<EmployeeHR>(entity.ID);
                        if (employeeHR != null && employeeHR.EffHireDate.HasValue && entity.ExpirationDate.Value.Date <= employeeHR.EffHireDate.Value.Date)
                        {
                            result.Error("在站到期日必须大于报到日期");
                        }
                    }
                }
            }

            if (!entity.DutyFreeId.IsEmpty())
            {
                var dict = this.BizRepo.Get<Dict>(entity.DutyFreeId!.Value);

                if (dict == null)
                {
                    result.Error("免税类型不存在");
                }
            }

            if (entity.ICBCAccountNumber.IsEmpty())
            {
                result.Error("工行账号必填");
            }

            return result;
        }

        #endregion 编辑数据检验
    }
}