﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NLog.Filters;
using Renji.JHR.Api.CommonUtils;
using Renji.JHR.Api.Models;
using Renji.JHR.Bll.Caching;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.Caching;
using Shinsoft.Core.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System;
using Renji.JHR.Common.Configration;

namespace Renji.JHR.Api
{
    [Authorize]
    [ApiController]
    [Route("[controller]/[action]")]
    [ApiExplorerSettings(GroupName = "Main")]
    public abstract class BaseApiController<TRepo> : BaseController<TRepo, User, IdentityUser, Identity>
        where TRepo : class, IRepo
    {
        protected BaseApiController(TRepo? repo = null)
            : base(repo)
        {
        }

        protected virtual new UserProvider UserProvider => this.GetRequiredService<IUserProvider, UserProvider>();

        protected virtual SysBll SysBll => this.GetRepo<SysBll>();
        protected virtual FileBll FileBll => this.GetRepo<FileBll>();
        protected virtual LogBll LogBll => this.GetRepo<LogBll>();

        protected virtual MailBll MailBll => this.GetRepo<MailBll>();

        #region Operator User

        public virtual Guid? OperatorUserId => this.OperatorUser?.ID;

        #endregion Operator User

        #region Current User

        /// <summary>
        /// 当前用户ID（操作员工为空时报错）
        /// </summary>
        public virtual Guid CurrentUserId => this.CurrentUser.ID;

        #endregion Current User

        #region SysCache

        protected virtual SysCache SysCache => this.GetRequiredService<SysCache>();

        #endregion

        protected string GetClientIP()
		{
			var ip = HttpContextExtension.GetClientIP(HttpContext);
			return ip;
		}

        protected string GetClientIP2(IHttpContextAccessor accessor)
        {
            var ip = HttpContextExtension.GetClientIP(HttpContext);
            var ip2 = HttpContext?.Connection?.LocalIpAddress?.MapToIPv4().ToString();
            var ip3 = HttpContext?.Connection?.RemoteIpAddress?.MapToIPv4().ToString();
            var ip4 = accessor.HttpContext?.Connection.RemoteIpAddress;
            return ip;
        }

		protected BizResult AnonymousValidation([FromBody] AnonymousValidationModel model)
		{
            var result = new BizResult();

            if (model.Key != Config.WebApi.Key.ToMD5())
            {
                result.Error("Key error!");
            }
            if (model.Domain != Config.WebApi.Domain)
            {
                result.Error("Domain error!");
            }
            if (string.IsNullOrEmpty(model.Time))
            {
                result.Error("Time error!");
            }
            else
            {
                if (!long.TryParse(model.Time, out var ticks))
                {
                    result.Error("Time format error!");
                }

                DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeMilliseconds(ticks);
                // 转换为本地时区时间
                DateTime startDate = dateTimeOffset.LocalDateTime;

                var timeDiff = DateTime.Now - startDate;

                if (timeDiff.TotalMinutes > Config.WebApi.ChkMin)
                {
                    result.Error("Time is too eariler error!");
                }
            }

            return result;

        }

    }
}
