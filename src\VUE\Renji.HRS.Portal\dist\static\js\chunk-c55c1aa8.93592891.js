(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c55c1aa8"],{"4a63":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[r("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.headModel}},[r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"日期"}},[r("el-date-picker",{staticClass:"input_class",attrs:{type:"date",placeholder:"请选择日期","value-format":"yyyy-MM-dd",size:"small",clearable:!1},model:{value:e.headModel.recordDate,callback:function(t){e.$set(e.headModel,"recordDate",t)},expression:"headModel.recordDate"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"部门"}},[r("c-select-tree",{attrs:{options:e.treeData,"tree-props":e.treeProps},model:{value:e.headModel.dept,callback:function(t){e.$set(e.headModel,"dept",t)},expression:"headModel.dept"}})],1)],1)],1),r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"工号"}},[r("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.headModel.empCode,callback:function(t){e.$set(e.headModel,"empCode",t)},expression:"headModel.empCode"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"姓名"}},[r("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.headModel.empName,callback:function(t){e.$set(e.headModel,"empName",t)},expression:"headModel.empName"}})],1)],1)],1),r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:""}},[r("el-button",{attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"仅显示加班数据"}},[r("el-checkbox",{model:{value:e.headModel.onlyOTValue,callback:function(t){e.$set(e.headModel,"onlyOTValue",t)},expression:"headModel.onlyOTValue"}})],1),r("el-form-item",{attrs:{label:"按月份查询"}},[r("el-checkbox",{model:{value:e.headModel.withMonth,callback:function(t){e.$set(e.headModel,"withMonth",t)},expression:"headModel.withMonth"}})],1)],1)],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",attrs:{data:e.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"}},[r("el-table-column",{attrs:{prop:"date",label:"唯一码",align:"center",width:"120",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[r("span",[e._v(e._s(a.empUid))])]}}])}),r("el-table-column",{attrs:{prop:"date",label:"工号",align:"center",width:"120",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[r("span",[e._v(e._s(a.empCode))])]}}])}),r("el-table-column",{attrs:{prop:"date",label:"姓名",align:"center",width:"120",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[r("span",[e._v(e._s(a.empName))])]}}])}),r("el-table-column",{attrs:{prop:"name",label:"部门",align:"center",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[r("span",[e._v(e._s(a.empDept))])]}}])}),r("el-table-column",{attrs:{prop:"name",label:"加班类型",align:"center",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.edit?r("el-select",{attrs:{placeholder:"请选择"},model:{value:a.enumOverTimeType,callback:function(t){e.$set(a,"enumOverTimeType",t)},expression:"row.enumOverTimeType"}},e._l(e.overTimeType,(function(e){return r("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1):r("span",[e._v(e._s(a.enumOverTimeTypeDesc))])]}}])}),r("el-table-column",{attrs:{prop:"name",label:"日期",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[r("span",[e._v(e._s(a.recordDateStr))])]}}])}),r("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.edit?r("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(t){return e.confirmEdit(a)}}},[e._v(" 更新 ")]):e._e(),a.edit?r("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.cancelEdit(a)}}},[e._v(" 取消 ")]):e._e(),a.edit?e._e():r("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(t){return e.Edit(a)}}},[e._v(" 编辑 ")])]}}])})],1),r("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[20,50,100],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.paginationChanged}})]},proxy:!0}])})],1)},o=[],n=(r("99af"),r("d81d"),r("d3b7"),r("ac1f"),r("25f0"),r("4d90"),r("841c"),r("d368")),i=r("cbd2"),c=r("f9ac"),l={components:{},data:function(){return{headModel:{onlyOTValue:!0},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeLoading:!1,treeExpandedKeys:[],currentNode:null,total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,tableData:[],editOringinData:{},overTimeType:[]}},created:function(){this.$set(this.headModel,"recordDate",this.getNowTime()),this.search(),this.loadTree(),this.loadOverTimeType()},methods:{getNowTime:function(){var e=new Date,t=e.getFullYear(),r=e.getMonth(),a=e.getDate();r+=1,r=r.toString().padStart(2,"0");var o="".concat(t,"-").concat(r,"-").concat(a," 00:00:00");return o},loadTree:function(){var e=this;n["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data,e.treeExpandedKeys.push(t.data[0].id)})).catch((function(e){console.log(e)})),this.resertCurrentNode()},loadOverTimeType:function(){var e=this;c["a"].getEnumInfos({enumType:"OverTimeType"}).then((function(t){e.overTimeType=t.data.datas})).catch((function(e){console.log(e)}))},resertCurrentNode:function(){this.currentNode=null},treeNodeClick:function(e){},Edit:function(e){e.edit=!e.edit},cancelEdit:function(e){e.edit=!1,e.enumOverTimeType=e.originalEnumOverTimeType,e.enumOverTimeTypeDesc=e.originalEnumOverTimeTypeDesc},confirmEdit:function(e){var t=this;e.edit=!1,e.oTDate=this.headModel.recordDate,i["a"].updateAttHolidayOTRecordDetail(e).then((function(r){if(r.succeed){var a=r.data;e.enumOverTimeTypeDesc=a.enumOverTimeTypeDesc,e.updator=a.updator,e.originalEnumOverTimeType=a.enumOverTimeType,e.originalEnumOverTimeTypeDesc=a.enumOverTimeTypeDesc}else t.$notice.resultTip(r)})).catch((function(e){console.log(e)}))},search:function(){this.listQuery.pageIndex=1,this.getSearchResult()},getSearchResult:function(){var e=this,t={RecordDate:this.headModel.recordDate,DeptId:this.headModel.dept,EmpCode:this.headModel.empCode,EmpName:this.headModel.empName,OnlyOTValue:this.headModel.onlyOTValue,PageIndex:this.listQuery.pageIndex,PageSize:this.listQuery.pageSize,WithMonth:this.headModel.withMonth};i["a"].searchAttHolidayOTRecordDetail_Update(t).then((function(t){e.listLoading=!1,t.succeed?(e.total=t.data.recordCount,e.tableData=t.data.datas.map((function(t){return e.$set(t,"edit",!1),t.originalEnumOverTimeType=t.enumOverTimeType,t.originalEnumOverTimeTypeDesc=t.enumOverTimeTypeDesc,t}))):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},paginationChanged:function(){this.getSearchResult()}}},d=l,u=r("2877"),f=Object(u["a"])(d,a,o,!1,null,null,null);t["default"]=f.exports},cbd2:function(e,t,r){"use strict";var a=r("cfe3"),o="AttendanceManage",n=new a["a"](o);t["a"]={getAttMonthShiftRecord:function(e){return n.get("GetAttMonthShiftRecord",e)},queryAttMonthShiftRecordDetail:function(e){return n.get("QueryAttMonthShiftRecordDetail",e)},batchConfirmAttMonthShiftRecord:function(e){return n.post("BatchConfirmAttMonthShiftRecord",e)},saveAttMonthShiftRecord:function(e){return n.post("SaveAttMonthShiftRecord",e)},submitAttMonthShiftRecord:function(e){return n.post("SubmitAttMonthShiftRecord",e)},ConfirmAttMonthShiftRecord:function(e){return n.post("ConfirmAttMonthShiftRecord",e)},rejectAttMonthShiftRecord:function(e){return n.post("RejectAttMonthShiftRecord",e)},searchAttMonthShiftRecordDetail:function(e){return n.get("SearchAttMonthShiftRecordDetail",e)},searchAttMonthShiftRecordDetail_Update:function(e){return n.get("SearchAttMonthShiftRecordDetail_Update",e)},updateAttMonthShiftRecordDetail:function(e){return n.post("UpdateAttMonthShiftRecordDetail",e)},getColorDeptTree_MiddleNightShift:function(e){return n.get("GetColorDeptTree_MiddleNightShift",e)},get_MiddleNightShiftReportExcel:function(e){return n.getFile("Get_MiddleNightShiftReportExcel",e)},getAttHolidayOTRecord:function(e){return n.get("GetAttHolidayOTRecord",e)},queryAttHolidayOTRecordDetail:function(e){return n.get("QueryAttHolidayOTRecordDetail",e)},saveAttHolidayOTRecord:function(e){return n.post("SaveAttHolidayOTRecord",e)},batchConfirmAttHolidayOTRecord:function(e){return n.post("BatchConfirmAttHolidayOTRecord",e)},submitAttHolidayOTRecord:function(e){return n.post("SubmitAttHolidayOTRecord",e)},ConfirmAttHolidayOTRecord:function(e){return n.post("ConfirmAttHolidayOTRecord",e)},rejectAttHolidayOTRecord:function(e){return n.post("RejectAttHolidayOTRecord",e)},searchAttHolidayOTRecordDetail:function(e){return n.get("SearchAttHolidayOT",e)},searchAttHolidayOTRecordDetail_Update:function(e){return n.get("SearchAttHolidayOTRecordDetail_Update",e)},updateAttHolidayOTRecordDetail:function(e){return n.post("UpdateAttHolidayOTRecordDetail",e)},getColorDeptTree_HolidayOT:function(e){return n.get("GetColorDeptTree_HolidayOT",e)},getOTReportExcel:function(e){return n.getFile("GetOTReportExcel",e)},getAttDayOffRecord:function(e){return n.get("GetAttDayOffRecord",e)},queryAttDayOffRecordDetail:function(e){return n.get("QueryAttDayOffRecordDetail",e)},saveAttDayOffRecord:function(e){return n.post("SaveAttDayOffRecord",e)},submitAttDayOffRecord:function(e){return n.post("SubmitAttDayOffRecord",e)},updateApproveAttDayOffRecord:function(e){return n.post("UpdateApproveAttDayOffRecord",e)},rejectAttDayOffRecord:function(e){return n.post("RejectAttDayOffRecord",e)},searchAttDayOffRecordDetail:function(e){return n.get("SearchAttDayOffRecordDetail",e)},searchAttDayOffRecordDetail_Update:function(e){return n.get("SearchAttDayOffRecordDetail_Update",e)},updateAttDayOffRecordDetail:function(e){return n.post("UpdateAttDayOffRecordDetail",e)},getColorDeptTree_DayOff:function(e){return n.get("GetColorDeptTree_DayOff",e)},getDayOffReportExcel:function(e){return n.getFile("GetDayOffReportExcel",e)},searchAttDayOffRecordDetail1:function(e){return n.get("SearchAttDayOffRecordDetail1",e)},searchAttMonthWatchRecord:function(e){return n.get("SearchAttMonthWatchRecord",e)},updateAttMonthWatchRecord:function(e){return n.post("UpdateAttMonthWatchRecord",e)},getMonthWatchTReportExcel:function(e){return n.getFile("GetMonthWatchTReportExcel",e)},getAttDayOffRecordDetail1Excel:function(e){return n.getFile("GetAttDayOffRecordDetail1Excel",e)},queryEmployeeList:function(e){return n.get("QueryEmployeeList",e)},searchAttDayOffRecordDetail2:function(e){return n.get("SearchAttDayOffRecordDetail2",e)},getAttDayOffRecordDetail2Excel:function(e){return n.getFile("GetAttDayOffRecordDetail2Excel",e)},searchAttDayOffRecordDetail3:function(e){return n.get("SearchAttDayOffRecordDetail3",e)},getAttDayOffRecordDetail3Excel:function(e){return n.getFile("GetAttDayOffRecordDetail3Excel",e)},queryAttDayOffRecordProphylacticDetail:function(e){return n.get("QueryAttDayOffRecordProphylacticDetail",e)},exportAttDayOffRecordProphylacticDetail:function(e){return n.getFile("ExportAttDayOffRecordProphylacticDetail",e)},getAttDayOffRecordProphylacticCase:function(e){return n.get("GetAttDayOffRecordProphylacticCase",e)},getAttDayOffRecordProphylacticDetail:function(e){return n.get("GetAttDayOffRecordProphylacticDetail",e)},getAttDayOffRecordProphylactic:function(e){return n.get("GetAttDayOffRecordProphylactic",e)},addAttDayOffRecordProphylactic:function(e){return n.post("AddAttDayOffRecordProphylactic",e)},updateAttDayOffRecordProphylactic:function(e){return n.post("UpdateAttDayOffRecordProphylactic",e)},deleteAttDayOffRecordProphylacticDetail:function(e){return n.post("DeleteAttDayOffRecordProphylacticDetail",e)},subjectAttDayOffRecordProphylactic:function(e){return n.post("SubjectAttDayOffRecordProphylactic",e)},queryCheckRecordFilling:function(e){return n.get("QueryCheckRecordFilling",e)},queryPersonnelAttendanceData:function(e){return n.get("QueryPersonnelAttendanceData",e)},queryProphylacticChange:function(e){return n.get("QueryProphylacticChange",e)},exportProphylacticChange:function(e){return n.getFile("ExportProphylacticChange",e)},queryPersonnelPendingApproval:function(e){return n.get("QueryPersonnelPendingApproval",e)},approveAttDayOffRecord:function(e){return n.post("ApproveAttDayOffRecord",e)},getSameDeptEmployeeWithHealthAllowance:function(e){return n.get("GetSameDeptEmployeeWithHealthAllowance",e)}}}}]);