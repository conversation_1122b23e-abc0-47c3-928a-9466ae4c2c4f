using System.ComponentModel;

namespace Renji.JHR.Entities
{
    /// <summary>
    /// 十三薪员工状态
    /// </summary>
    public enum ThirteenthSalaryEmployeeStatus
    {
        /// <summary>
        /// 无
        /// </summary>
        [Description("")]
        None = 0,

        /// <summary>
        /// 累计缺勤<=92天
        /// </summary>
        [Description("累计缺勤<=92天")]
        Normal = 1,

        /// <summary>
        /// 上半年新进职工
        /// </summary>
        [Description("上半年新进职工")]
        NewEmployeeFirstHalf = 2,

        /// <summary>
        /// 下半年新进职工
        /// </summary>
        [Description("下半年新进职工")]
        NewEmployeeSecondHalf = 3,

        /// <summary>
        /// 累计缺勤>92天
        /// </summary>
        [Description("累计缺勤>92天")]
        ExcessiveAbsenteeism = 4,

        /// <summary>
        /// 待退休、残疾不在岗、其他停薪等
        /// </summary>
        [Description("待退休、残疾不在岗、其他停薪等")]
        RetirementPending = 5,

        /// <summary>
        /// 年薪制
        /// </summary>
        [Description("年薪制")]
        YearlySalary = 6,

        /// <summary>
        /// 刑事、行政等处分
        /// </summary>
        [Description("刑事、行政等处分")]
        DisciplinaryAction = 7,

        /// <summary>
        /// 离岗培训
        /// </summary>
        [Description("离岗培训")]
        OffDutyTraining = 8,

        /// <summary>
        /// 考核不合格或无考核结果
        /// </summary>
        [Description("考核不合格或无考核结果")]
        AssessmentIssue = 9,

        /// <summary>
        /// 因公出国
        /// </summary>
        [Description("因公出国")]
        OfficialOverseasTravel = 10,

        /// <summary>
        /// 下半年退休职工
        /// </summary>
        [Description("下半年退休职工")]
        RetiredSecondHalf = 11,

        /// <summary>
        /// 上半年退休职工
        /// </summary>
        [Description("上半年退休职工")]
        RetiredFirstHalf = 12,

    }
}