﻿using Microsoft.EntityFrameworkCore.Diagnostics;
using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Renji.JHR.Entities
{
    public enum AppType
    {
        None = 0,

        /// <summary>
        /// 网页应用
        /// </summary>
        [Description("网页应用")]
        WebApp = 1,

        /// <summary>
        /// 桌面应用
        /// </summary>
        [Description("桌面应用")]
        DesktopApp = 2,

        /// <summary>
        /// 移动应用
        /// </summary>
        [Description("移动应用")]
        [EnumGroup(Visible = false)]
        MobileApp = 3,
    }
}
