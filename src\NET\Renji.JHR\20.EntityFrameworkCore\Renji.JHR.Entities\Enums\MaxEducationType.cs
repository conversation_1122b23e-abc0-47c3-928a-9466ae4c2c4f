﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Renji.JHR.Entities
{
    /// <summary>
    /// 申康字段：最高学历
    /// </summary>
    public enum MaxEducationType
    {
        #region 最高学历
        /// <summary>
        /// 博士研究生
        /// </summary>
        [Description("1")]
        DoctoralGraduate = 1,

        /// <summary>
        /// 硕士研究生
        /// </summary>
        [Description("2")]
        MasterGraduate = 2,

        /// <summary>
        /// 研究生毕业
        /// </summary>
        [Description("3")]
        Postgraduate = 3,

        /// <summary>
        /// 大学本科
        /// </summary>
        [Description("4")]
        Undergraduate = 4,

        /// <summary>
        /// 大学专科
        /// </summary>
        [Description("5")]
        CollegeSpecialty = 5,

        /// <summary>
        /// 其他
        /// </summary>
        [Description("9")]
        Other = 9,
        #endregion
    }
}
