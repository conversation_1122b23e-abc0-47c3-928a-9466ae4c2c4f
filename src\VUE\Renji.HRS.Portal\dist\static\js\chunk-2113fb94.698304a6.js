(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2113fb94"],{3421:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.headModel}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{staticClass:"input_class",attrs:{type:"date",placeholder:"请选择日期","value-format":"yyyy-MM-dd",size:"small",clearable:!1},model:{value:e.headModel.recordDate,callback:function(t){e.$set(e.headModel,"recordDate",t)},expression:"headModel.recordDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"假期天数"}},[a("el-select",{attrs:{placeholder:"请选择"},on:{change:e.dayChanged},model:{value:e.headModel.addDay,callback:function(t){e.$set(e.headModel,"addDay",t)},expression:"headModel.addDay"}},e._l(e.dayOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"部门"}},[a("c-select-tree2",{ref:"treeSelect",attrs:{clearable:!0,multiple:!0,data:e.treeData,props:e.treeProps,"check-strictly":!0},model:{value:e.headModel.dept,callback:function(t){e.$set(e.headModel,"dept",t)},expression:"headModel.dept"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"唯一码"}},[a("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.headModel.empUid,callback:function(t){e.$set(e.headModel,"empUid",t)},expression:"headModel.empUid"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"工号"}},[a("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.headModel.empCode,callback:function(t){e.$set(e.headModel,"empCode",t)},expression:"headModel.empCode"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"员工姓名"}},[a("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.headModel.empName,callback:function(t){e.$set(e.headModel,"empName",t)},expression:"headModel.empName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:""}},[a("el-button",{attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")]),a("el-button",{attrs:{type:"primary"},on:{click:e.exportData}},[e._v("导出")])],1)],1),a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"仅显示加班数据"}},[a("el-checkbox",{model:{value:e.headModel.onlyOTValue,callback:function(t){e.$set(e.headModel,"onlyOTValue",t)},expression:"headModel.onlyOTValue"}})],1)],1)],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"}},[a("el-table-column",{attrs:{prop:"EmpUid",label:"唯一码",align:"center",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.empUid))])]}}])}),a("el-table-column",{attrs:{prop:"EmpCode",label:"工号",align:"center",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.empCode))])]}}])}),a("el-table-column",{attrs:{prop:"EmpName",label:"姓名",align:"center",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.empName))])]}}])}),a("el-table-column",{attrs:{prop:"EmpDept",label:"部门",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.empDept))])]}}])}),a("el-table-column",{attrs:{prop:"HireStyleName",label:"在职方式",align:"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.hireStyleName))])]}}])}),a("el-table-column",{attrs:{prop:"EnumStatusDesc",label:"状态",align:"center",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.enumStatusDesc))])]}}])}),e._l(e.dateDatas,(function(t,o){return a("el-table-column",{key:o,attrs:{label:t,align:"center"}},[a("el-table-column",{attrs:{prop:"Hour_4",label:"4小时班",align:"center",width:"65"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.details[o].hour_4))])]}}],null,!0)}),a("el-table-column",{attrs:{prop:"Hour_8",label:"8小时班",align:"center",width:"65"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.details[o].hour_8))])]}}],null,!0)}),a("el-table-column",{attrs:{prop:"Hour_12",label:"12小时班",align:"center",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.details[o].hour_12))])]}}],null,!0)}),a("el-table-column",{attrs:{prop:"Hour_16",label:"16小时班",align:"center",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.details[o].hour_16))])]}}],null,!0)}),a("el-table-column",{attrs:{prop:"Hour_24",label:"24小时班",align:"center",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.details[o].hour_24))])]}}],null,!0)}),a("el-table-column",{attrs:{prop:"Hour_Doctor24",label:"医生24小时班",align:"center",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.details[o].hour_Doctor24))])]}}],null,!0)})],1)})),a("el-table-column",{attrs:{prop:"SumClass",label:"班次",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.sumClass))])]}}])})],2),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[20,50,100],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.paginationChanged}})]},proxy:!0}])})],1)},r=[],n=(a("99af"),a("d3b7"),a("ac1f"),a("25f0"),a("3ca3"),a("4d90"),a("841c"),a("ddb0"),a("2b3d"),a("d368")),l=a("cbd2"),c={components:{},data:function(){return{dateDatas:[],dayOptions:[{label:"1天",value:"1"},{label:"2天",value:"2"},{label:"3天",value:"3"},{label:"4天",value:"4"},{label:"5天",value:"5"},{label:"6天",value:"6"},{label:"7天",value:"7"}],total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,headModel:{addDay:"1",recordDate:this.getNowTime(),onlyOTValue:!0,dept:[]},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeExpandedKeys:[],currentNode:null,tableData:[],zoomLevel:1}},created:function(){this.loadTree()},methods:{getNowTime:function(){var e=new Date,t=e.getFullYear(),a=e.getMonth(),o=e.getDate();a+=1,a=a.toString().padStart(2,"0"),o=o.toString().padStart(2,"0");var r="".concat(t,"-").concat(a,"-").concat(o);return r},dayChanged:function(e){this.search()},loadTree:function(){var e=this;n["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data,e.treeExpandedKeys.push(t.data[0].id)})).catch((function(e){console.log(e)})),this.resertCurrentNode()},resertCurrentNode:function(){this.currentNode=null},treeNodeClick:function(e){},search:function(){this.listQuery.pageIndex=1,this.getSearchResult()},exportData:function(){var e={RecordDate:this.headModel.recordDate,AddDay:this.headModel.addDay,DeptIds:this.headModel.dept,EmpCode:this.headModel.empCode,EmpUid:this.headModel.empUid,EmpName:this.headModel.empName,OnlyOTValue:this.headModel.onlyOTValue,PageIndex:this.listQuery.pageIndex,PageSize:this.listQuery.pageSize};l["a"].getOTReportExcel(e).then((function(e){console.log(e);var t=new Blob([e],{type:e.type}),a="节日加班明细.xlsx";if(window.navigator&&window.navigator.msSaveOrOpenBlob)window.navigator.msSaveOrOpenBlob(t,a);else{var o=document.createElement("a"),r=window.URL.createObjectURL(t);o.href=r,o.download=a,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(r)}}))},getSearchResult:function(){var e=this,t={RecordDate:this.headModel.recordDate,AddDay:this.headModel.addDay,DeptIds:this.headModel.dept,EmpCode:this.headModel.empCode,EmpUid:this.headModel.empUid,EmpName:this.headModel.empName,OnlyOTValue:this.headModel.onlyOTValue,PageIndex:this.listQuery.pageIndex,PageSize:this.listQuery.pageSize};l["a"].searchAttHolidayOTRecordDetail(t).then((function(t){e.listLoading=!1,t.succeed?(e.tableData=t.data.datas,e.dateDatas=[],t.data.datas&&t.data.datas.length>0&&(e.dateDatas=t.data.datas[0].recordDates),e.total=t.data.recordCount):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},paginationChanged:function(){this.getSearchResult()}}},i=c,d=a("2877"),u=Object(d["a"])(i,o,r,!1,null,null,null);t["default"]=u.exports},cbd2:function(e,t,a){"use strict";var o=a("cfe3"),r="AttendanceManage",n=new o["a"](r);t["a"]={getAttMonthShiftRecord:function(e){return n.get("GetAttMonthShiftRecord",e)},queryAttMonthShiftRecordDetail:function(e){return n.get("QueryAttMonthShiftRecordDetail",e)},batchConfirmAttMonthShiftRecord:function(e){return n.post("BatchConfirmAttMonthShiftRecord",e)},saveAttMonthShiftRecord:function(e){return n.post("SaveAttMonthShiftRecord",e)},submitAttMonthShiftRecord:function(e){return n.post("SubmitAttMonthShiftRecord",e)},ConfirmAttMonthShiftRecord:function(e){return n.post("ConfirmAttMonthShiftRecord",e)},rejectAttMonthShiftRecord:function(e){return n.post("RejectAttMonthShiftRecord",e)},searchAttMonthShiftRecordDetail:function(e){return n.get("SearchAttMonthShiftRecordDetail",e)},searchAttMonthShiftRecordDetail_Update:function(e){return n.get("SearchAttMonthShiftRecordDetail_Update",e)},updateAttMonthShiftRecordDetail:function(e){return n.post("UpdateAttMonthShiftRecordDetail",e)},getColorDeptTree_MiddleNightShift:function(e){return n.get("GetColorDeptTree_MiddleNightShift",e)},get_MiddleNightShiftReportExcel:function(e){return n.getFile("Get_MiddleNightShiftReportExcel",e)},getAttHolidayOTRecord:function(e){return n.get("GetAttHolidayOTRecord",e)},queryAttHolidayOTRecordDetail:function(e){return n.get("QueryAttHolidayOTRecordDetail",e)},saveAttHolidayOTRecord:function(e){return n.post("SaveAttHolidayOTRecord",e)},batchConfirmAttHolidayOTRecord:function(e){return n.post("BatchConfirmAttHolidayOTRecord",e)},submitAttHolidayOTRecord:function(e){return n.post("SubmitAttHolidayOTRecord",e)},ConfirmAttHolidayOTRecord:function(e){return n.post("ConfirmAttHolidayOTRecord",e)},rejectAttHolidayOTRecord:function(e){return n.post("RejectAttHolidayOTRecord",e)},searchAttHolidayOTRecordDetail:function(e){return n.get("SearchAttHolidayOT",e)},searchAttHolidayOTRecordDetail_Update:function(e){return n.get("SearchAttHolidayOTRecordDetail_Update",e)},updateAttHolidayOTRecordDetail:function(e){return n.post("UpdateAttHolidayOTRecordDetail",e)},getColorDeptTree_HolidayOT:function(e){return n.get("GetColorDeptTree_HolidayOT",e)},getOTReportExcel:function(e){return n.getFile("GetOTReportExcel",e)},getAttDayOffRecord:function(e){return n.get("GetAttDayOffRecord",e)},queryAttDayOffRecordDetail:function(e){return n.get("QueryAttDayOffRecordDetail",e)},saveAttDayOffRecord:function(e){return n.post("SaveAttDayOffRecord",e)},submitAttDayOffRecord:function(e){return n.post("SubmitAttDayOffRecord",e)},updateApproveAttDayOffRecord:function(e){return n.post("UpdateApproveAttDayOffRecord",e)},rejectAttDayOffRecord:function(e){return n.post("RejectAttDayOffRecord",e)},searchAttDayOffRecordDetail:function(e){return n.get("SearchAttDayOffRecordDetail",e)},searchAttDayOffRecordDetail_Update:function(e){return n.get("SearchAttDayOffRecordDetail_Update",e)},updateAttDayOffRecordDetail:function(e){return n.post("UpdateAttDayOffRecordDetail",e)},getColorDeptTree_DayOff:function(e){return n.get("GetColorDeptTree_DayOff",e)},getDayOffReportExcel:function(e){return n.getFile("GetDayOffReportExcel",e)},searchAttDayOffRecordDetail1:function(e){return n.get("SearchAttDayOffRecordDetail1",e)},searchAttMonthWatchRecord:function(e){return n.get("SearchAttMonthWatchRecord",e)},updateAttMonthWatchRecord:function(e){return n.post("UpdateAttMonthWatchRecord",e)},getMonthWatchTReportExcel:function(e){return n.getFile("GetMonthWatchTReportExcel",e)},getAttDayOffRecordDetail1Excel:function(e){return n.getFile("GetAttDayOffRecordDetail1Excel",e)},queryEmployeeList:function(e){return n.get("QueryEmployeeList",e)},searchAttDayOffRecordDetail2:function(e){return n.get("SearchAttDayOffRecordDetail2",e)},getAttDayOffRecordDetail2Excel:function(e){return n.getFile("GetAttDayOffRecordDetail2Excel",e)},searchAttDayOffRecordDetail3:function(e){return n.get("SearchAttDayOffRecordDetail3",e)},getAttDayOffRecordDetail3Excel:function(e){return n.getFile("GetAttDayOffRecordDetail3Excel",e)},queryAttDayOffRecordProphylacticDetail:function(e){return n.get("QueryAttDayOffRecordProphylacticDetail",e)},exportAttDayOffRecordProphylacticDetail:function(e){return n.getFile("ExportAttDayOffRecordProphylacticDetail",e)},getAttDayOffRecordProphylacticCase:function(e){return n.get("GetAttDayOffRecordProphylacticCase",e)},getAttDayOffRecordProphylacticDetail:function(e){return n.get("GetAttDayOffRecordProphylacticDetail",e)},getAttDayOffRecordProphylactic:function(e){return n.get("GetAttDayOffRecordProphylactic",e)},addAttDayOffRecordProphylactic:function(e){return n.post("AddAttDayOffRecordProphylactic",e)},updateAttDayOffRecordProphylactic:function(e){return n.post("UpdateAttDayOffRecordProphylactic",e)},deleteAttDayOffRecordProphylacticDetail:function(e){return n.post("DeleteAttDayOffRecordProphylacticDetail",e)},subjectAttDayOffRecordProphylactic:function(e){return n.post("SubjectAttDayOffRecordProphylactic",e)},queryCheckRecordFilling:function(e){return n.get("QueryCheckRecordFilling",e)},queryPersonnelAttendanceData:function(e){return n.get("QueryPersonnelAttendanceData",e)},queryProphylacticChange:function(e){return n.get("QueryProphylacticChange",e)},exportProphylacticChange:function(e){return n.getFile("ExportProphylacticChange",e)},queryPersonnelPendingApproval:function(e){return n.get("QueryPersonnelPendingApproval",e)},approveAttDayOffRecord:function(e){return n.post("ApproveAttDayOffRecord",e)},getSameDeptEmployeeWithHealthAllowance:function(e){return n.get("GetSameDeptEmployeeWithHealthAllowance",e)}}}}]);