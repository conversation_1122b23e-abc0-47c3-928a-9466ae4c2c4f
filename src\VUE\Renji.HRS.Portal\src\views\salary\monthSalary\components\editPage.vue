<template>
  <div>
    <el-dialog :title="title" :visible="showDialog" width="40%" :close-on-press-escape="false"
      :close-on-click-modal="false" @close="closeDialog">
      <el-form ref="dataForm" :model="dataModel" label-width="120px">
        <el-row>
          <el-col :span="10">
            <el-form-item label="薪资年份" prop="RecordMonth">
              <template v-if="isEdit">
                <span>{{ currentYear }}</span>
              </template>
              <template v-else>
                <el-date-picker v-model="year" type="year" placeholder="选择年">
                </el-date-picker>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="月份">
              <template v-if="isEdit">
                <span>{{ selectedMonth }}</span>
              </template>
              <template v-else>
                <el-select v-model="selectedMonth">
                  <el-option v-for="month in months" :key="month" :label="month.toString()" :value="month"></el-option>
                </el-select>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item label="年度社保基数调整">
              <input v-model="dataModel.yearSocialSecurityBaseCorrection" style="width: 16px; height: 16px;"
                type="checkbox">
            </el-form-item>
          </el-col>
          <el-col :span="10" v-if="dataModel.yearSocialSecurityBaseCorrection">
            <el-form-item label="调整月份数" prop="correctionMonths">
              <el-input-number v-model="dataModel.correctionMonths" :min="0" :precision="0" :step="1"
                placeholder="请输入调整月份数" style="width: 100%">
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <!--
        <el-row>
          <el-col>
            <el-form-item label="特殊月份">
              <input v-model="dataModel.specialMonth" style="width: 16px; height: 16px;" type="checkbox">
            </el-form-item>
          </el-col>
        </el-row>
        -->
        <el-row>
          <el-col>
            <el-form-item label="备注">
              <el-input v-model="dataModel.remark" type="textarea" :rows="3" maxlength="500" clearable
                placeholder="备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" :loading="btnSaveLoading" @click="saveDialog">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import salaryApi from '@/api/salary'
export default {
  data() {
    return {
      showDialog: false,
      title: '',
      btnSaveLoading: false,
      isEdit: false,
      dataModel: {},
      year: new Date(),
      currentYear: new Date().getFullYear(),
      selectedMonth: null,
      months: Array.from({ length: 13 }, (_, i) => i + 1),
      rules: {
        correctionMonths: [
          { required: true, message: '请输入调整月份数', trigger: 'blur' },
          { type: 'number', min: 1, message: '调整月份数必须大于0', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    'dataModel.yearSocialSecurityBaseCorrection': {
      handler(newVal) {
        if (newVal && !this.dataModel.correctionMonths) {
          this.dataModel.correctionMonths = 1
        }
      },
      immediate: false
    }
  },
  methods: {
    initDialog(row) {
      this.showDialog = true
      if (!row) {
        this.title = '新增月度薪资'
        this.isEdit = false
        this.getNowTime()

        const month = this.dataModel.month ? (new Date(this.dataModel.month)).getMonth() + 1 : 0
        if (month === 8) {
          this.dataModel.yearSocialSecurityBaseCorrection = true
          this.dataModel.correctionMonths = 1
        }
      } else {
        this.title = '编辑月度薪资'
        this.isEdit = true
        this.getData(row.id)
      }
    },
    getNowTime() {
      this.selectedMonth = new Date().getMonth() + 1
    },
    getData(id) {
      salaryApi.getSalary({ id: id }).then(res => {
        if (res.succeed) {
          this.dataModel = res.data
          const date = new Date(this.dataModel.month)
          this.currentYear = date.getFullYear()

          if (this.dataModel.recordMonth === 13) {
            this.selectedMonth = date.getMonth() + 2
          } else {
            this.selectedMonth = date.getMonth() + 1
          }
        }
      }).catch(res => {
      })
    },
    saveDialog() {
      this.$refs['dataForm'].validate(valid => {
        this.currentYear = new Date(this.year).getFullYear()
        if (valid) {
          this.dataModel.recordYear = new Date(this.year).getFullYear()
          this.dataModel.recordMonth = this.selectedMonth
          if (this.selectedMonth === 13) {
            this.dataModel.month = new Date(this.currentYear, this.dataModel.recordMonth - 2, 31).toISOString()
          } else {
            this.dataModel.month = new Date(this.currentYear, this.dataModel.recordMonth - 1).toISOString()
          }
          this.btnSaveLoading = true
          if (!this.isEdit) {
            salaryApi.addSalary(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '添加成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
              this.closeDialog()
            })
          } else {
            salaryApi.updateSalary(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '修改成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          }
        }
      })
    },
    closeDialog() {
      /*
      this.dataModel = {
        yearSocialSecurityBaseCorrection: false,
        correctionMonths: null
      }
      */
      this.dataModel = {}
      this.showDialog = false
      this.$refs.dataForm.resetFields()
    }
  }
}
</script>

<style>
.numrule.el-input--small .el-input__inner {
  text-align: left;
}
</style>

<style scoped lang="scss">
::v-deep .numrule input::-webkit-outer-spin-button,
::v-deep .numrule input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .numrule input[type="number"] {
  -moz-appearance: textfield !important;
}
</style>
