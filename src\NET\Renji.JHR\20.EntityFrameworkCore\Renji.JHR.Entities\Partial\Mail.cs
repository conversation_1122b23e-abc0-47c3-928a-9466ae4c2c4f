﻿using Shinsoft.Core.Mail;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using System.Xml.Serialization;

namespace Renji.JHR.Entities
{
    public partial class Mail : IMail
    {
        #region IMail

        [NotMapped, XmlIgnore, JsonIgnore]
        object IMail.ID => this.ID;

        [NotMapped, XmlIgnore, JsonIgnore]
        List<IMailAttachment>? IMail.MailAttachments => this.MailAttachments?.Cast<IMailAttachment>().ToList();

        [NotMapped, XmlIgnore, JsonIgnore]
        public List<MailAttachment>? MailAttachments { get; set; }

        #endregion IMail
    }
}