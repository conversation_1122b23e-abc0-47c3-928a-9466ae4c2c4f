﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Shinsoft.Core.Caching;
using System.Net;

namespace Renji.JHR.Api
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true, Inherited = true)]
    public class AuthorizeFilter : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            if (context.ActionDescriptor is ControllerActionDescriptor descriptor)
            {
                //方法上没有定义权限属性，则取Controller类上的定义
                var allowAnonymous = descriptor.MethodInfo.GetActionAttribute<AllowAnonymousAttribute>(true)
                    ?? descriptor.MethodInfo.GetControllerAttribute<AllowAnonymousAttribute>(true);

                if (allowAnonymous == null)
                {
                    int? statusCode = null;
                    string? message = null;

                    var authorize = descriptor.MethodInfo.GetActionAttribute<AuthorizeAttribute>(true)
                        ?? descriptor.MethodInfo.GetControllerAttribute<AuthorizeAttribute>(true);

                    if (authorize != null)
                    {
                        if (context.IsAuthenticated())
                        {
                            //只对非服务账号（正常登录的用户）进行此判断
                            //判断是否要强制用户重新登录（IdentityCode错误）
                            var userProvider = context.GetRequiredService<IUserProvider, UserProvider>();

                            if (!userProvider.ValidateIdentity(context.HttpContext.User.GetIdentityJson()))
                            {
                                statusCode = (int)HttpStatusCode.Unauthorized;
                            }
                        }
                        else
                        {
                            statusCode = (int)HttpStatusCode.Unauthorized;
                        }
                    }

                    if (!statusCode.HasValue)
                    {
                        var allow = true;

                        //方法上没有定义权限属性，则取Controller类上的定义
                        var authAttrs = descriptor.MethodInfo.GetPriorityAttributes<PermissionAttribute>();

                        if (authAttrs.Any() == true)
                        {
                            //已设置了权限属性，需要判断用户是否拥有权限
                            allow = false;

                            if (context.IsAuthenticated())
                            {
                                var user = context.GetOperatorUser<User>();

                                if (user != null)
                                {
                                    allow = user.HasAnyPermission(authAttrs);
                                }
                            }
                        }

                        if (!allow)
                        {
                            statusCode = (int)HttpStatusCode.Forbidden;
                        }
                    }

                    if (statusCode.HasValue)
                    {
                        if (message.IsEmpty())
                        {
                            context.Result = new ContentResult()
                            {
                                StatusCode = statusCode
                            };
                        }
                        else
                        {
                            context.Result = new ContentResult()
                            {
                                StatusCode = statusCode,
                                Content = message
                            };
                        }
                    }
                }
            }

            base.OnActionExecuting(context);
        }

        public override void OnResultExecuting(ResultExecutingContext context)
        {
            if (context.ActionDescriptor is ControllerActionDescriptor descriptor)
            {
                //方法上没有定义权限属性，则取Controller类上的定义
                //var allowAnonymous = descriptor.MethodInfo.GetAttribute<AllowAnonymousAttribute>(true)
                //    ?? descriptor.MethodInfo.DeclaringType?.GetAttribute<AllowAnonymousAttribute>(true);

                var jwtIgnore = descriptor.MethodInfo.GetAttribute<JwtIgnoreAttribute>(true)
                    ?? descriptor.MethodInfo.DeclaringType?.GetAttribute<JwtIgnoreAttribute>(true);

                if (jwtIgnore == null && context.HttpContext.User.IsAuthenticated())
                {
                    //只对非服务账号（正常登录的用户）重新生成jwt
                    var userProvider = context.GetRequiredService<IUserProvider, UserProvider>();

                    var identity = userProvider.DeserializeToIdentity(context.HttpContext.User.GetIdentityJson());

                    var token = $"{JwtBearerDefaults.AuthenticationScheme} {identity.GetJwt()}";

                    var exposHeader = context.HttpContext.Response.Headers["Access-Control-Expose-Headers"].AsString();

                    if (exposHeader.IsEmpty())
                    {
                        exposHeader = "jwt";
                    }
                    else
                    {
                        exposHeader += ",jwt";
                    }

                    context.HttpContext.Response.Headers.Add("jwt", token);
                    context.HttpContext.Response.Headers.Add("Access-Control-Expose-Headers", exposHeader);
                }
            }

            base.OnResultExecuting(context);
        }
    }
}