﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Renji.JHR.Entities
{
    /// <summary>
    /// 中夜班费记录状态
    /// </summary>
    public enum AttMonthShiftRecordStatus
    {
        /// <summary>
        /// 申领表未提交
        /// </summary>
        [Description("申领表未提交")]
        UnCommitted = 0,
        /// <summary>
        /// 等待部门负责人审批
        /// </summary>
        [Description("等待部门负责人审批")]
        WaitLeaderConfirm = 1, 
        /// <summary>
        /// 等待人事部门确认
        /// </summary>
        [Description("等待人事部门确认")]
        WaitHrConfirm = 2,
        /// <summary>
        /// 人事部门已确认
        /// </summary>
        [Description("人事部门已确认")]
        Confirmed = 3

    }
}
