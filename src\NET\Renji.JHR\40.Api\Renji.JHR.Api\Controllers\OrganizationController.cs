﻿using Microsoft.AspNetCore.Mvc;
using Renji.JHR.Api.Models;
using Renji.JHR.Bll;
using Renji.JHR.Common.Utility;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.Mvc;
using Shinsoft.Core.NLog;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using static Renji.JHR.Api.Controllers.HRController;
using System.Collections;
using Renji.JHR.Common;
using Microsoft.AspNetCore.Http;
using System.IO;
using System.Data;
using Microsoft.AspNetCore.Authorization;
using Shinsoft.Core.DynamicQuery;

namespace Renji.JHR.Api.Controllers
{
    /// <summary>
    /// 组织机构管理
    /// </summary>
    [ApiExplorerSettings(GroupName = "组织机构管理")]
    public class OrganizationController : BaseApiController<OrganizationBll>
    {
        #region Position

        /// <summary>
        /// 查询职位
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "查询职位")]
        [Permission(Permissions.OrganizationManage.PositionManage)]
        public QueryResult<PositionQuery> QueryPosition([FromBody] PositionFilter filter)
        {
            var exps = this.NewExps<Position>();

            if (filter.ConditionList != null && filter.ConditionList.Any())
            {
                var expression = new Common.DynamicQuery().GetDynamicQuery<Entities.Position>(filter.ConditionList);
                exps.And(expression);
            }

            if (filter.Order.IsEmpty())
            {
                filter.Order = "Ordinal ,Name asc";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<PositionQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 获取职位
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取职位")]
        [Permission(Permissions.OrganizationManage.PositionManage, Permissions.OrganizationManage.PositionLevelManage)]
        public BizResult<PositionModel> GetPosition([FromQuery, Required] Guid id)
        {
            var result = this.BizResult<PositionModel>();

            var entity = this.Repo.Get<Position>(id);

            if (entity == null)
            {
                result.Error("职位不存在");
            }
            else
            {
                result.Data = entity.Map<PositionModel>();
            }

            return result;
        }

        /// <summary>
        /// 新增职位
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增职位")]
        [Permission(Permissions.OrganizationManage.PositionManage)]
        public BizResult<PositionModel> AddPosition([FromBody] PositionModel model)
        {
            var entity = model.Map<Position>();

            var result = this.Repo.AddPosition(entity);

            return result.Map<PositionModel>();
        }

        /// <summary>
        /// 更新职位
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新职位")]
        [Permission(Permissions.OrganizationManage.PositionManage)]
        public BizResult<PositionModel> UpdatePosition([FromBody] PositionModel model)
        {
            var entity = model.Map<Position>();

            var result = this.Repo.UpdatePosition(entity);

            return result.Map<PositionModel>();
        }

        /// <summary>
        /// 删除职位
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除职位")]
        [Permission(Permissions.OrganizationManage.PositionManage)]
        public BizResult DeletePosition([FromBody] PositionModel model)
        {
            var entity = new Position
            {
                ID = model.ID,
                ConfirmToDelete = model.ConfirmToDelete
            };

            var result = this.Repo.DeletePosition(entity);

            return result;
        }

        #endregion Position

        #region Staion

        /// <summary>
        /// 获取职位
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取职位")]
        [Permission(Permissions.OrganizationManage.PositionLevelManage)]
        public BizResult<StationModel> GetStation([FromQuery, Required] Guid id)
        {
            var result = this.BizResult<StationModel>();

            var entity = this.Repo.Get<Station>(id);

            if (entity == null)
            {
                result.Error("岗位不存在");
            }
            else
            {
                entity.PositionIds = entity.PositionStation.Select(ps => ps.PositionId).ToList();
                result.Data = entity.Map<StationModel>();
            }

            return result;
        }

        /// <summary>
        /// 添加岗位
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "添加岗位")]
        [Permission(Permissions.OrganizationManage.PositionLevelManage)]
        public BizResult<StationModel> AddStation([FromBody] StationModel model)
        {
            var entity = model.Map<Station>();

            var result = this.Repo.AddStation(entity);

            return result.Map<StationModel>();
        }

        /// <summary>
        /// 更新岗位
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新岗位")]
        [Permission(Permissions.OrganizationManage.PositionLevelManage)]
        public BizResult<StationModel> UpdateStation([FromBody] StationModel model)
        {
            var entity = model.Map<Station>();

            var result = this.Repo.UpdateStation(entity);

            return result.Map<StationModel>();
        }

        /// <summary>
        /// 删除岗位
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除岗位")]
        [Permission(Permissions.OrganizationManage.PositionLevelManage)]
        public BizResult DeleteStation([FromBody] StationModel model)
        {
            var entity = new Station
            {
                ID = model.ID,
                ConfirmToDelete = model.ConfirmToDelete
            };
            var result = this.Repo.DeleteStation(entity);

            return result;
        }

        #endregion Staion

        #region PositionStaion

        /// <summary>
        /// 查询PostionStaion
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询PostionStaion")]
        public QueryResult<PositionStationQuery> QueryPositionStation([FromQuery] PositionStationFilter filter)
        {
            var exps = this.NewExps<PositionStation>();

            if (!filter.PositionId.IsEmpty())
            {
                exps.And(p => p.PositionId == filter.PositionId);
            }
            if (!filter.PositionName.IsEmpty())
            {
                exps.And(p => p.PositionName.Contains(filter.PositionName!));
            }

            if (!filter.StationId.IsEmpty())
            {
                exps.And(p => p.StationId == filter.StationId);
            }
            if (!filter.StationName.IsEmpty())
            {
                exps.And(p => p.StationName.Contains(filter.StationName!));
            }

            if (filter.Ordinal.HasValue)
            {
                exps.And(p => p.Ordinal == filter.Ordinal);
            }

            if (!filter.Description.IsEmpty())
            {
                exps.And(p => p.Description != null && p.Description.Contains(filter.Description!));
            }

            if (filter.Order.IsEmpty())
            {
                filter.Order = "Ordinal +,Name +";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<PositionStationQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 查询岗位-职位结构树
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询岗位-职位结构树")]
        [Permission(Permissions.OrganizationManage.PositionLevelManage)]
        public BizResult<List<PositionStationTreeQuery>> QueryPositionStationTree([FromQuery] PositionStationFilter filter)
        {
            //查询chilren为自己的表这类的可以不用贪婪加载，贪婪加载外键其他表的可以
            var staEntities = new List<Station>();
            var staAllEntities = this.Repo.GetEntities<Station>();
            if (!string.IsNullOrEmpty(filter.StationName))
            {
                var staFilEntities = staAllEntities.Where(c => c.Name.Contains(filter.StationName)).ToList();
                foreach (var staEntity in staFilEntities)
                {
                    AddStationParents(staEntities, staEntity, staAllEntities);
                    AddStationChildren(staEntities, staEntity, staAllEntities);
                }
            }
            else
            {
                staEntities = staAllEntities;
            }
            var posEntities = this.Repo.GetEntities<Position>();
            var posStaEntities = this.Repo.GetEntities<PositionStation>();
            var models = new List<PositionStationTreeQuery>();

            foreach (var staEntity in staEntities.Where(s => !s.ParentId.HasValue).OrderBy(s => s.Number))
            {
                var model = new PositionStationTreeQuery();
                model.ID = staEntity.ID;
                model.Name = staEntity.Name;
                model.Type = "gw";
                model.Icon = "岗位";
                model.ParentId = staEntity.ParentId;
                model.Children = SetPositionStationTreeChild(staEntity, staEntities, posEntities, posStaEntities);
                models.Add(model);
            }
            return this.BizResult(models);
        }

        private void AddStationParents(List<Station> staEntities, Station currentStation, List<Station> staAllEntities)
        {
            if (staEntities.IndexOf(currentStation) < 0)
            {
                staEntities.Add(currentStation);
            }

            if (currentStation.ParentId.HasValue)
            {
                foreach (var parentStation in staAllEntities.Where(c => c.ID == currentStation.ParentId))
                {
                    AddStationParents(staEntities, parentStation, staAllEntities);
                }
            }
        }

        private void AddStationChildren(List<Station> staEntities, Station currentStation, List<Station> staAllEntities)
        {
            if (staEntities.IndexOf(currentStation) < 0)
            {
                staEntities.Add(currentStation);
            }

            if (!currentStation.ParentId.HasValue)
            {
                foreach (var childStation in staAllEntities.Where(c => c.ParentId == currentStation.ID))
                {
                    AddStationChildren(staEntities, childStation, staAllEntities);
                }
            }
        }

        private List<PositionStationTreeQuery> SetPositionStationTreeChild(Station station, List<Station> staEntities, List<Position> posEntities, List<PositionStation> posStaEntities)
        {
            var childs = new List<PositionStationTreeQuery>();
            //var childStaion = station.Children;
            var childStaions = staEntities.Where(p => p.ParentId == station.ID).ToList();
            if (childStaions.Any()) //有子岗位
            {
                foreach (var sta in childStaions.OrderBy(s => s.Number))
                {
                    var child = new PositionStationTreeQuery();
                    child.ID = sta.ID;
                    child.Name = sta.Name;
                    child.Type = "gw";
                    child.Icon = "岗位";
                    child.ParentId = sta.ParentId; //岗位有父子级关系，一个岗位可以有0个或0个以上的子岗位
                    child.Children = SetPositionStationTreeChild(sta, childStaions, posEntities, posStaEntities); //先一路添加子岗位，再对每个子岗位添加关联的职位
                    childs.Add(child);
                }
            }
            //关联的职位
            var child_ps = new List<PositionStationTreeQuery>();
            var posStas = posStaEntities.Where(p => p.StationId == station.ID);
            if (posStas.Any()) //有关联职位
            {
                foreach (var ps in posStas.OrderBy(p => p.Position.Name))
                {
                    var position = posEntities.First(p => p.ID == ps.PositionId);
                    var child_p = new PositionStationTreeQuery();
                    child_p.ID = position.ID;
                    child_p.Name = position.Name;
                    child_p.Type = "zw";
                    child_p.Icon = "职位";
                    child_p.ParentId = station.ID; //职位本身没有父子级关系，但与岗位有关联（每个职位下可以有多个不同岗位）
                    child_ps.Add(child_p);
                }
                childs.AddRange(child_ps);
            }

            return childs;
        }

        /// <summary>
        /// 分配职位
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "分配职位")]
        [Permission(Permissions.OrganizationManage.PositionLevelManage)]
        public BizResult AllocatePosition([FromBody] AllocatePositionRequest request)
        {
            var result = new BizResult();
            var entities = new List<PositionStation>();

            if (request.PositionIds != null && request.PositionIds.Count > 0)
            {
                foreach (var positionId in request.PositionIds)
                {
                    var positionStation = new PositionStation()
                    {
                        StationId = request.StationId,
                        PositionId = positionId
                    };
                    entities.Add(positionStation);
                }
                result = this.Repo.AllocatePosition(entities);
            }
            else
            {
                this.Repo.ClearPositionStation(request.StationId, ref result);
            }
            return result;
        }

        /// <summary>
        /// 删除PositionStation
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除PositionStation")]
        [Permission(Permissions.OrganizationManage.PositionLevelManage)]
        public BizResult DeletePositionStation([FromBody] PositionStationModel model)
        {
            var entity = new PositionStation
            {
                StationId = model.StationId,
                PositionId = model.PositionId
            };
            var result = this.Repo.DeletePositionStation(entity);

            return result;
        }

        #endregion PositionStaion

        #region Organization

        /// <summary>
        /// 查询组织架构
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询组织架构")]
        public BizResult<List<OrganizationQuery>> QueryOrganization([FromQuery] DepartmentFilter filter)
        {
            var depEntities = new List<Department>();
            var depFilEntities = new List<Department>();
            var depAllEntities = this.Repo.GetEntities<Department>();

            bool allDepts = true;
            if (!filter.Code.IsEmpty() && !filter.Name.IsEmpty())
            {
                depFilEntities = depAllEntities.Where(c => c.Code.Contains(filter.Code!)).Where(c => c.Name.Contains(filter.Name!)).ToList();
                allDepts = false;
            }
            else if (!filter.Code.IsEmpty())
            {
                depFilEntities = depAllEntities.Where(c => c.Code.Contains(filter.Code!)).ToList();
                allDepts = false;
            }
            else if (!filter.Name.IsEmpty())
            {
                depFilEntities = depAllEntities.Where(c => c.Name.Contains(filter.Name!)).ToList();
                allDepts = false;
            }
            else
            {
                depEntities = depAllEntities;
            }

            if (!allDepts)
            {
                foreach (var depEntity in depFilEntities)
                {
                    if (depEntities.IndexOf(depEntity) < 0)
                    {
                        depEntities.Add(depEntity);
                    }

                    var children = depAllEntities.Where(c => c.UidPath.Contains(depEntity.Uid.ToString())).ToList();
                    foreach (var child in children)
                    {
                        if (depEntities.IndexOf(child) < 0)
                        {
                            depEntities.Add(child);
                        }
                    }
                }
            }

            var all = depEntities.Maps<OrganizationQuery>();

            SemiNumericComparer comp = new SemiNumericComparer();

            foreach (var model in all)
            {
                var childern = all.Where(p => p.ParentId == model.ID).OrderBy(p => p.Sequence.ToString(), comp).ToList();
                if (childern != null && childern.Any())
                {
                    model.Children = childern;
                }
            }
            List<OrganizationQuery> models;

            if (allDepts)
            {
                models = all
                    .Where(p => !p.ParentId.HasValue)
                    .OrderBy(p => p.Sequence.ToString(), comp)
                    .ToList();
            }
            else
            {
                models = all
                        .OrderBy(p => p.Sequence.ToString(), comp)
                        .ToList();
            }

            return this.BizResult(models);
        }

        private void AddDepartmentChildren(List<Department> staEntities, Department currentStation, List<Department> staAllEntities)
        {
            if (staEntities.IndexOf(currentStation) < 0)
            {
                staEntities.Add(currentStation);
            }

            foreach (var childStation in staAllEntities.Where(c => c.ParentId == currentStation.ID))
            {
                AddDepartmentChildren(staEntities, childStation, staAllEntities);
            }
        }

        /// <summary>
        /// 查询组织架构
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询组织架构")]
        public BizResult<List<OrganizationQuery>> QueryOrganizationHiddenTop()
        {
            var exps = this.NewExps<Department>();
            exps.And(p => p.Level > 3);

            var entities = this.Repo.GetEntities<Department>(exps);

            var all = entities.Maps<OrganizationQuery>();

            SemiNumericComparer comp = new SemiNumericComparer();

            foreach (var model in all)
            {
                var childern = all.Where(p => p.ParentId == model.ID).OrderBy(p => p.Sequence.ToString(), comp).ToList();
                if (childern != null && childern.Any())
                {
                    model.Children = childern;
                }
            }

            List<OrganizationQuery> models;

            models = all
                .OrderBy(p => p.Sequence.ToString(), comp)
                .ToList();

            return this.BizResult(models);
        }

        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询当前用户的组织架构")]
        public BizResult<List<OrganizationQuery>> QueryDeptByUser()
        {
            bool fullcontrol = this.Repo.GetFullControlDeptByCurrentUser();
            IList<Department> entities;
            List<OrganizationQuery> all;
            SemiNumericComparer comp = new SemiNumericComparer();
            List<OrganizationQuery> models;

            if (fullcontrol)
            {
                entities = this.Repo.GetDeptByCurrentUser();

                all = entities.Maps<OrganizationQuery>();

                foreach (var model in all)
                {
                    var childern = all.Where(p => p.ParentId == model.ID).OrderBy(p => p.Sequence.ToString(), comp).ToList();
                    if (childern != null && childern.Any())
                    {
                        model.Children = childern;
                    }
                }

                models = all
                    .Where(p => !p.ParentId.HasValue)
                    .OrderBy(p => p.Sequence.ToString(), comp)
                    .ToList();
            }
            else
            {
                entities = this.Repo.GetRealDeptByCurrentUser();

                all = entities.Maps<OrganizationQuery>();

                List<int> indexes = new List<int>();
                foreach (var model in all)
                {
                    var childern = all.Where(p => p.ParentId == model.ID).OrderBy(p => p.Sequence.ToString(), comp).ToList();
                    if (childern.Any())
                    {
                        model.Children = childern;
                    }

                    foreach (var chd in childern)
                    {
                        indexes.Add(all.IndexOf(chd));
                    }
                }

                indexes.Sort();
                for (int i = indexes.Count - 1; i >= 0; i--)
                {
                    all.RemoveAt(indexes[i]);
                }

                models = all
                    //.Where(p => !p.ParentId.HasValue)
                    .OrderBy(p => p.Sequence.ToString(), comp)
                    .ToList();
            }

            return this.BizResult(models);
        }

        #endregion Organization

        #region Department

        /// <summary>
        /// 查询科室
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询科室")]
        [Permission(Permissions.OrganizationManage.Organization)]
        public QueryResult<DepartmentQuery> QueryDepartment([FromQuery] DepartmentFilter filter)
        {
            var exps = this.NewExps<Department>();

            if (!filter.Dept.IsEmpty())
            {
                exps.And(p => p.Code.Contains(filter.Dept!) || p.Name.Contains(filter.Dept!));
            }

            if (!filter.Description.IsEmpty())
            {
                exps.And(p => p.Description != null && p.Description.Contains(filter.Description!));
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<DepartmentQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        /// <summary>
        /// 获取科室
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取科室")]
        [Permission(Permissions.OrganizationManage.Organization)]
        public BizResult<DepartmentModel> GetDepartment([FromQuery, Required] Guid id)
        {
            var result = this.BizResult<DepartmentModel>();

            var entity = this.Repo.Get<Department>(id);

            if (entity == null)
            {
                result.Error("科室不存在");
            }
            else
            {
                result.Data = entity.Map<DepartmentModel>();
            }

            return result;
        }

        /// <summary>
        /// 新增科室
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增科室")]
        [Permission(Permissions.OrganizationManage.Organization)]
        public BizResult<DepartmentModel> AddDepartment([FromBody] DepartmentModel model)
        {
            var entity = model.Map<Department>();

            var result = this.Repo.AddDepartment(entity);

            return result.Map<DepartmentModel>();
        }

        /// <summary>
        /// 更新科室
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "更新科室")]
        [Permission(Permissions.OrganizationManage.Organization)]
        public BizResult<DepartmentModel> UpdateDepartment([FromBody] DepartmentModel model)
        {
            var entity = model.Map<Department>();

            var result = this.Repo.UpdateDepartment(entity);

            return result.Map<DepartmentModel>();
        }

        /// <summary>
        /// 删除科室
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除科室")]
        [Permission(Permissions.OrganizationManage.Organization)]
        public BizResult DeleteDepartment([FromBody] DepartmentModel model)
        {
            var entity = new Department
            {
                ID = model.ID,
                ConfirmToDelete = model.ConfirmToDelete
            };

            var result = this.Repo.DeleteDepartment(entity);

            return result;
        }

        /// <summary>
        /// 移动科室
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "移动科室")]
        [Permission(Permissions.OrganizationManage.Organization)]
        public BizResult MoveDepartment(MoveDeptRequest request)
        {
            var entity = new Department()
            {
                OriginDept = request.OriginDept ?? "",
                ToParentDept = request.ToParentDept ?? ""
            };
            var result = this.Repo.MoveDepartment(entity);
            if (result.Succeed)
            {
                var parent = this.Repo.Get<Department>(request.ToParentDept?.As<Guid>());
                this.PostUpdateDocumentInformationByCode(request.OriginDept ?? "", parent?.OrgSerial ?? "");
            }

            return result;
        }

        /// <summary>
        /// 合并科室
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "合并科室")]
        [Permission(Permissions.OrganizationManage.Organization)]
        public BizResult MergeDepartment(MergeDeptRequest request)
        {
            var entity = new Department()
            {
                OriginDept = request.OriginDept ?? "",
                MergeToDept = request.MergeToDept ?? ""
            };
            var origin = this.Repo.Get<Department>(request.OriginDept?.As<Guid>());
            var result = this.Repo.MergeDepartment(entity);
            if (result.Succeed)
            {
                this.PostDeleteDocumentInformationByCode(origin);
            }

            return result;
        }

        #endregion Department

        #region 提取数据写入第三方

        private BizResult PostUpdateDocumentInformationByCode(string deptId, string parentOrgserial)
        {
            var bizresult = new BizResult();
            try
            {
                //string deptName, string orgserial, string parentorgserial, string hostitalArea
                Guid id = Guid.Parse(deptId);
                var model = this.Repo.Get<Department>(id);
                if (model != null && !string.IsNullOrEmpty(model.OrgSerial))
                {
                    var dict = this.Repo.Get<Entities.Dict>(model.HospitalAreaId);
                    string hostitalArea;
                    if (dict != null)
                    {
                        hostitalArea = dict.Name;
                    }
                    else
                    {
                        hostitalArea = "东院";
                    }

                    //地址：http://************:1413/services/HSBHR?wsdl   方法名：SaveOrganization
                    Hashtable ht = new Hashtable();
                    ht.Add("key", "ezOFFICE12.4".ToMD5());
                    ht.Add("domain", "0");
                    ht.Add("serviceKey", "webappKey");
                    ht.Add("verificationType", "0");
                    ht.Add("userKey", "sys");
                    ht.Add("userKeyType", "0");
                    var time = SysDateTime.Now.TimeOfDay.Milliseconds;
                    ht.Add("time", time);
                    ht.Add("md5key", ("webappKey0sys0" + time.ToString() + "webapp").ToMD5());
                    ht.Add("cmd", "updateOrganizationNew");
                    ht.Add("name", model.Name);
                    ht.Add("orgsimplename", model.Name);
                    ht.Add("orgserial", model.OrgSerial);
                    ht.Add("description", "");
                    ht.Add("orgordercode", "500000");
                    ht.Add("orgtype", "1");
                    ht.Add("hasChanged", "0");
                    ht.Add("parentorgserial", parentOrgserial);
                    ht.Add("targetOrgserial", "");
                    ht.Add("sort", "0");
                    ht.Add("chargeLeaderAccounts", "");
                    ht.Add("deptLeaderAccounts", "");
                    ht.Add("hospitalArea", hostitalArea);

                    var result = WebServiceCaller.QuerySoapWebService("http://************:1413/services/HSBHR", "SaveOrganization", ht, true);
                    Information info = new Information()
                    {
                        Key1 = "PostUpdateDocumentInformationByCode",
                        Key2 = $"{model.Uid}",
                        Remark = result.InnerText
                    };
                    this.Repo.AddInformation(info);
                    if (!result.InnerText.Contains("<result>1</result>"))
                    {
                        bizresult.Error("上传部门信息失败");
                        return bizresult;
                    }
                }
            }
            catch (Exception)
            {
                bizresult.Error("上传部门信息异常");
            }
            return bizresult;
        }

        private BizResult PostDeleteDocumentInformationByCode(Department? model)
        {
            var bizresult = new BizResult();
            try
            {
                //string orgserial;
                if (model != null && !string.IsNullOrEmpty(model.OrgSerial))
                {
                    //地址：http://************:1413/services/HSBHR?wsdl   方法名：SaveOrganization
                    Hashtable ht = new Hashtable();

                    ht.Add("key", "ezOFFICE12.4".ToMD5());
                    ht.Add("domain", "0");
                    ht.Add("serviceKey", "webappKey");
                    ht.Add("verificationType", "0");
                    ht.Add("userKey", "sys");
                    ht.Add("userKeyType", "0");
                    var time = SysDateTime.Now.TimeOfDay.Milliseconds;
                    ht.Add("time", time);
                    ht.Add("md5key", ("webappKey0sys0" + time.ToString() + "webapp").ToMD5());
                    ht.Add("cmd", "deleteOrganizationByOrgIdNew");
                    ht.Add("orgserial", model.OrgSerial);

                    var result = WebServiceCaller.QuerySoapWebService("http://************:1413/services/HSBHR", "SaveOrganization", ht, true);
                    Information info = new Information()
                    {
                        Key1 = "PostDeleteDocumentInformationByCode",
                        Key2 = $"{model.Uid}",
                        Remark = result.InnerText
                    };
                    this.Repo.AddInformation(info);
                    if (!result.InnerText.Contains("<result>1</result>"))
                    {
                        bizresult.Error("上传部门信息失败");
                        return bizresult;
                    }
                }
            }
            catch (Exception)
            {
                bizresult.Error("上传部门信息异常");
            }
            return bizresult;
        }

        #endregion 提取数据写入第三方

        #region 岗位津贴

        /// <summary>
        /// 岗位大类
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "岗位大类")]
        public QueryResult<SelectModel> QueryOneLevelStation()
        {
            QueryResult<SelectModel> queryResult = new QueryResult<SelectModel>();
            var exps = this.NewExps<Station>();
            exps.And(p => p.IsCategory.HasValue && !p.ParentId.HasValue);
            var entities = this.Repo.GetEntities<Station>(exps);
            var models = entities.Select(s => new SelectModel { Label = s.Name, Value = s.ID.ToString() }).ToList();
            //var all = entities.Maps<StationModel>();
            queryResult.Datas = models;
            return queryResult;
        }

        /// <summary>
        /// 岗位级别
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "岗位级别")]
        public QueryResult<SelectModel> QueryTwoLevelStation([FromQuery] Guid id)
        {
            QueryResult<SelectModel> queryResult = new QueryResult<SelectModel>();
            var exps = this.NewExps<Station>();
            exps.And(p => p.ParentId == id);
            var entities = this.Repo.GetEntities<Station>(exps);
            //var all = entities.Maps<StationModel>();
            var models = entities.OrderBy(c => c.Number).Select(s => new SelectModel { Label = s.Name, Value = s.ID.ToString() }).ToList();
            queryResult.Datas = models;
            return queryResult;
        }

        /// <summary>
        /// 查询工龄下拉
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "岗位级别")]
        public QueryResult<SelectModel> QuerySenioritySelect()
        {
            QueryResult<SelectModel> queryResult = new QueryResult<SelectModel>();
            var entities = this.Repo.GetEntities<Seniority>();
            var models = entities.Select(s => new SelectModel { Label = s.WorkAge.ToString(), Value = s.ID.ToString() }).ToList();
            queryResult.Datas = models;
            return queryResult;
        }

        /// <summary>
        /// 查询岗位津贴
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询岗位津贴")]
        [Permission(Permissions.OrganizationManage.JobSubsidy)]
        public QueryResult<StationAllowanceQuery> QueryStationAllowance([FromQuery] StationAllowanceFilter filter)
        {
            var exps = this.NewExps<StationAllowance>();
            var result = this.Repo.GetDynamicQuery<StationAllowance, StationAllowanceQuery>(filter, exps);
            return result;
        }

        /// <summary>
        /// 获取岗位津贴
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取岗位津贴")]
        [Permission(Permissions.OrganizationManage.JobSubsidy)]
        public BizResult<StationAllowanceQuery> GetStationAllowance([FromQuery] Guid id)
        {
            var result = new BizResult<StationAllowanceQuery>();

            var entity = this.Repo.Get<StationAllowance>(id);

            if (entity == null)
            {
                result.Error("岗位津贴不存在");
            }
            else
            {
                var model = entity.Map<StationAllowanceQuery>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增岗位津贴
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增岗位津贴")]
        [Permission(Permissions.OrganizationManage.JobSubsidy)]
        public BizResult<StationAllowanceModel> AddStationAllowance(StationAllowanceModel model)
        {
            var result = new BizResult<StationAllowance>();
            var entity = model.Map<StationAllowance>();
            result = this.Repo.AddStationAllowance(entity);

            return result.Map<StationAllowanceModel>();
        }

        /// <summary>
        /// 修改岗位津贴
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "修改岗位津贴")]
        [Permission(Permissions.OrganizationManage.JobSubsidy)]
        public BizResult<StationAllowanceModel> UpdateStationAllowance(StationAllowanceModel model)
        {
            var entity = model.Map<StationAllowance>();

            var result = this.Repo.UpdateStationAllowance(entity);

            return result.Map<StationAllowanceModel>();
        }

        /// <summary>
        /// 下载岗位津贴模板
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "下载岗位津贴模板")]
        [Permission(Permissions.OrganizationManage.Seniority)]
        public IActionResult DownloadStationAllowanceTemplate()
        {
            List<DataTable> listTable = new List<DataTable>();
            var parentStations = this.Repo.GetEntities<Station>(p => !p.ParentId.HasValue && p.IsCategory == true);
            foreach (var parent in parentStations)
            {
                DataTable dt = new DataTable(parent.Name);
                dt.Columns.Add(new DataColumn("工龄", typeof(int)));
                var stations = this.Repo.GetEntities<Station>(p => p.ParentId == parent.ID).OrderBy(p => p.Number);
                foreach (var st in stations)
                {
                    dt.Columns.Add(new DataColumn(st.Name, typeof(decimal)));
                }

                listTable.Add(dt);
            }
            var bytes = Excel.WriteToExcelBytes(listTable);
            return this.File(bytes, ConstDefinition.Common.Export_Excel, "岗位津贴模板" + SysDateTime.Now.ToString("yyyy-MM-dd") + ConstDefinition.Common.ExtensionOfHighVersionExcel);
        }

        /// <summary>
        /// 导入岗位津贴
        /// </summary>
        /// <param name="form"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "导入岗位津贴")]
        [Permission(Permissions.OrganizationManage.JobSubsidy)]
        public BizResult ImportStationAllowance([FromForm] IFormCollection form)
        {
            var result = new BizResult();
            var ms = new MemoryStream();
            try
            {
                var file = form.Files["file"];

                if (file == null)
                {
                    result.Error("请上传文件");
                }
                else
                {
                    var stationAllowances = new List<StationAllowance>();

                    file.CopyTo(ms);
                    var ds = new DataSet();
                    var fileInfo = new FileInfo(file.FileName);
                    ds = Excel.ReadTemplateFromExcelToDataSet(ms, fileInfo.Extension);

                    var senioritys = this.Repo.GetEntities<Seniority>();
                    var parentStations = this.Repo.GetEntities<Station>(p => !p.ParentId.HasValue && p.IsCategory == true);
                    foreach (DataTable dt in ds.Tables)
                    {
                        var tabName = dt.TableName;
                        var parent = parentStations.Where(p => p.Name == tabName).FirstOrDefault();
                        if (parent == null)
                        {
                            result.Error("没有岗位大类：" + tabName);
                        }
                        var parentId = parent?.ID;
                        var stations = this.Repo.GetEntities<Station>(p => p.ParentId == parentId);
                        var errorMessage = string.Empty;
                        int rowIndex = 1;
                        int unitWorkAge;
                        foreach (DataRow row in dt.Rows)
                        {
                            var seniority = new Seniority();
                            DataColumn? columnWorkAge = dt.Columns["工龄"];
                            if (columnWorkAge == null)
                            {
                                result.Error(parent?.Name + "工龄");
                            }

                            var workAgeColumn = row["工龄"].ToString();
                            unitWorkAge = 0;
                            if (workAgeColumn.IsEmpty())
                            {
                                errorMessage += "工龄不能为空;";
                            }
                            else
                            if (!int.TryParse(workAgeColumn, out unitWorkAge))
                            {
                                errorMessage += "工龄不正确;";
                            }
                            else if (unitWorkAge <= 0)
                            {
                                errorMessage += "工龄贴必须大于0;";
                            }
                            else
                            {
                                seniority = senioritys.Where(p => p.WorkAge == unitWorkAge).FirstOrDefault();
                                if (seniority == null)
                                {
                                    errorMessage += "工龄不存在;";
                                }
                            }
                            //先判断工龄是否存在
                            if (!errorMessage.IsEmpty())
                            {
                                errorMessage = $"{parent?.Name}内第{rowIndex}行：{errorMessage}";
                                result.Error(errorMessage);
                            }

                            if (result.Succeed)
                            {
                                foreach (DataColumn column in dt.Columns)
                                {
                                    var columnName = column.ColumnName;
                                    if (columnName == "工龄")
                                        continue;
                                    var station = stations.Where(p => p.Name == columnName).FirstOrDefault();
                                    if (station == null)
                                    {
                                        result.Error(parent?.Name + "没有岗位级别：" + columnName);
                                    }
                                    if (result.Succeed)
                                    {
                                        StationAllowance stationAllowance = new StationAllowance();
                                        var allowance = row[columnName].ToString();
                                        if (allowance == "")
                                            continue;
                                        //if (allowance.IsEmpty())
                                        //{
                                        //    errorMessage += "工作量津贴不能为空;";
                                        //}
                                        else if (!decimal.TryParse(allowance, out decimal unitAllowance))
                                        {
                                            errorMessage += "工作量津贴不正确;";
                                        }
                                        else if (unitAllowance < 0)
                                        {
                                            errorMessage += "工作量津贴必须大于等于0;";
                                        }
                                        else
                                        {
                                            stationAllowance.Allowance = decimal.Parse(unitAllowance.ToString("#.00"));
                                        }

                                        stationAllowance.ParentStationId = parent!.ID;
                                        stationAllowance.StationId = station!.ID;
                                        stationAllowance.WorkAge = unitWorkAge;

                                        if (errorMessage.IsEmpty())
                                        {
                                            stationAllowances.Add(stationAllowance);
                                        }
                                        else
                                        {
                                            errorMessage = $"{parent.Name}内第{rowIndex}行：{errorMessage}";
                                            result.Error(errorMessage);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // 数据正确 写数据
                    if (result.Succeed)
                    {
                        result = this.Repo.ImportStationAllowance(stationAllowances);
                    }
                }

                return result;
            }
            finally
            {
                ms.Dispose();
            }
        }

        /// <summary>
        /// 导出岗位津贴
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "导出岗位津贴")]
        [Permission(Permissions.OrganizationManage.JobSubsidy)]
        public IActionResult ExportStationAllowance(StationAllowanceFilter filter)
        {
            var exps = filter.GetDynamicQueryExp<StationAllowance>();
            filter.Order = "+ParentStationId,+StationId,+WorkAge";
            //查询所有数据
            var entities = exps == null
                ? this.Repo.GetEntities<StationAllowance>(filter.Order)
                : this.Repo.GetEntities(exps, filter.Order);

            var allStationAllowances = entities.Maps<StationAllowanceQuery>();

            //查询工龄
            //var senioritys = this.Repo.GetEntities<Seniority>();
            var expsSeniority = this.NewExps<Seniority>();
            if (filter.WorkAge.HasValue)
            {
                expsSeniority.And(p => p.WorkAge == filter.WorkAge);
            }
            var senioritys = this.Repo.GetEntities(expsSeniority);

            List<DataTable> dataTables = new List<DataTable>();

            var expsParentStation = this.NewExps<Station>();
            expsParentStation.And(p => p.IsCategory == true && !p.ParentId.HasValue);

            if (!filter.ParentStationId.IsEmpty())
            {
                expsParentStation.And(p => p.ID == filter.ParentStationId);
            }
            var parentStations = this.Repo.GetEntities(expsParentStation);
            //循环岗位大类
            foreach (var parent in parentStations)
            {
                DataTable dt = new(parent.Name);
                var expsStation = this.NewExps<Station>();
                expsStation.And(p => p.ParentId == parent.ID);

                if (!filter.StationId.IsEmpty())
                {
                    expsStation.And(p => p.ID == filter.StationId);
                }
                var stations = this.Repo.GetEntities(expsStation, "+Number");
                //var stations = this.Repo.GetEntities<Station>(p => p.ParentId == parent.ID).OrderBy(p=>p.Number);
                dt.Columns.Add(new DataColumn("工龄", typeof(int)));
                //创建列
                foreach (var station in stations)
                {
                    dt.Columns.Add(new DataColumn(station.Name, typeof(decimal)));
                }

                var stationAllowances = allStationAllowances.Where(p => p.ParentStationId == parent.ID);

                //循环工龄
                foreach (var seniority in senioritys)
                {
                    DataRow dr = dt.NewRow();
                    dr["工龄"] = seniority.WorkAge;
                    //循环列
                    foreach (var station in stations)
                    {
                        var d = allStationAllowances.Where(p => p.WorkAge == seniority.WorkAge && p.ParentStationId == parent.ID && p.StationId == station.ID).FirstOrDefault();
                        //var d = allStationAllowances.Where(p => p.ParentStationId == parent.ID && p.StationId == station.ID).FirstOrDefault();
                        if (d == null)
                            continue;
                        dr[station.Name] = d.Allowance;
                    }
                    dt.Rows.Add(dr);
                }
                dataTables.Add(dt);
            }

            var bytes = Excel.WriteToExcelBytes(dataTables);
            return this.File(bytes, ConstDefinition.Common.Export_Excel, "岗位津贴" + SysDateTime.Now.ToString("yyyy-MM-dd") + ConstDefinition.Common.ExtensionOfHighVersionExcel);
        }

        private DataTable FormatStationAllowanceTable(List<StationAllowanceQuery> seniorities)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add(new DataColumn("工龄", typeof(string)));
            dt.Columns.Add(new DataColumn("工作量津贴", typeof(decimal)));
            dt.Columns.Add(new DataColumn("护龄工资", typeof(decimal)));

            foreach (var senioritie in seniorities ?? new List<StationAllowanceQuery>())
            {
                var dr = dt.NewRow();
                //dr["工龄"] = senioritie.WorkAge;
                //dr["工作量津贴"] = senioritie.WorkAllowance;
                //dr["护龄工资"] = senioritie.NursingAgeWage;

                dt.Rows.Add(dr);
            }

            return dt;
        }

        #endregion 岗位津贴

        #region 工龄津贴

        /// <summary>
        /// 查询工龄津贴
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询工龄津贴")]
        [Permission(Permissions.OrganizationManage.Seniority)]
        public QueryResult<SeniorityModel> QuerySeniority([FromQuery] SeniorityFilter filter)
        {
            var exps = this.NewExps<Seniority>();
            var result = this.Repo.GetDynamicQuery<Seniority, SeniorityModel>(filter, exps);
            return result;
        }

        /// <summary>
        /// 获取工龄津贴
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取工龄津贴")]
        [Permission(Permissions.OrganizationManage.Seniority)]
        public BizResult<SeniorityModel> GetSeniority([FromQuery] Guid id)
        {
            var result = new BizResult<SeniorityModel>();

            var entity = this.Repo.Get<Seniority>(id);

            if (entity == null)
            {
                result.Error("工龄津贴不存在");
            }
            else
            {
                var model = entity.Map<SeniorityModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增工龄津贴
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增工龄津贴")]
        [Permission(Permissions.OrganizationManage.Seniority)]
        public BizResult<SeniorityModel> AddSeniority(SeniorityModel model)
        {
            var entity = model.Map<Seniority>();

            var result = this.Repo.AddSeniority(entity);

            return result.Map<SeniorityModel>();
        }

        /// <summary>
        /// 修改工龄津贴
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "修改工龄津贴")]
        [Permission(Permissions.OrganizationManage.Seniority)]
        public BizResult<SeniorityModel> UpdateSeniority(SeniorityModel model)
        {
            var entity = model.Map<Seniority>();

            var result = this.Repo.UpdateSeniority(entity);

            return result.Map<SeniorityModel>();
        }

        /// <summary>
        /// 导入工龄津贴
        /// </summary>
        /// <param name="form"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "导入工龄津贴")]
        [Permission(Permissions.OrganizationManage.Seniority)]
        public BizResult ImportSeniority([FromForm] IFormCollection form)
        {
            var result = new BizResult();
            var ms = new MemoryStream();
            try
            {
                var file = form.Files["file"];

                if (file == null)
                {
                    result.Error("请上传文件");
                }
                else
                {
                    var senioritys = new List<Seniority>();

                    file.CopyTo(ms);
                    var dt = new DataTable();
                    var fileInfo = new FileInfo(file.FileName);
                    dt = Excel.ReadTemplateFromExcelToDataTable(ms, fileInfo.Extension);

                    if (dt.Rows.Count == 0)
                    {
                        result.Error("导入列表不可为空");
                    }
                    else
                    {
                        //检查列头是否符合格式要求
                        var lackColumns = DataTableHelper.CheckColumns(dt, ImportMapping.SeniorityMapping);
                        if (lackColumns.Any())
                        {
                            result.Error($"模板中缺少如下列：{string.Join('、', lackColumns)}。");
                        }
                    }

                    if (result.Succeed)
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            var row = dt.Rows[i];
                            var seniority = new Seniority();
                            var errorMessage = string.Empty;

                            // 取值
                            var workAge = row["工龄"].AsString().AsTrim();
                            var workAllowance = row["工作量津贴"].AsString().AsTrim();
                            var nursingAgeWage = row["护龄工资"].AsString().AsTrim();
                            seniority.Memo = row["备注"].AsString().AsTrim();

                            #region 判断 验证 取值

                            if (workAge.IsEmpty())
                            {
                                errorMessage += "工龄不能为空;";
                            }
                            else if (!int.TryParse(workAge, out int workAgeNumber))
                            {
                                errorMessage += "工龄不正确，必须为数字;";
                            }
                            else if (workAgeNumber <= 0)
                            {
                                errorMessage += "工龄必须大于0;";
                            }
                            else
                            {
                                seniority.WorkAge = workAgeNumber;
                            }

                            if (workAllowance.IsEmpty())
                            {
                                errorMessage += "工作量津贴不能为空;";
                            }
                            else if (!decimal.TryParse(workAllowance, out decimal unitworkAllowance))
                            {
                                errorMessage += "工作量津贴不正确;";
                            }
                            else if (unitworkAllowance <= 0)
                            {
                                errorMessage += "工作量津贴必须大于0;";
                            }
                            else
                            {
                                seniority.WorkAllowance = decimal.Parse(unitworkAllowance.ToString("#.00"));
                            }

                            if (nursingAgeWage.IsEmpty())
                            {
                                errorMessage += "护龄工资不能为空;";
                            }
                            else if (!decimal.TryParse(nursingAgeWage, out decimal unitnursingAgeWage))
                            {
                                errorMessage += "护龄工资不正确;";
                            }
                            else if (unitnursingAgeWage < 0)
                            {
                                errorMessage += "护龄工资必须大于等于0;";
                            }
                            else
                            {
                                seniority.NursingAgeWage = decimal.Parse(unitnursingAgeWage.ToString("#.00"));
                            }

                            #endregion 判断 验证 取值

                            if (errorMessage.IsEmpty())
                            {
                                if (senioritys.Any(p => p.WorkAge == seniority.WorkAge))
                                {
                                    errorMessage += "文件中有与当前行工龄一致的重复数据;";
                                }
                            }

                            if (errorMessage.IsEmpty())
                            {
                                senioritys.Add(seniority);
                            }
                            else
                            {
                                errorMessage = $"第{i + 2}行：{errorMessage}";
                                result.Error(errorMessage);
                            }
                        }
                    }

                    // 数据正确 写数据
                    if (result.Succeed)
                    {
                        result = this.Repo.ImportSeniority(senioritys);
                    }
                }

                return result;
            }
            finally
            {
                ms.Dispose();
            }
        }

        /// <summary>
        /// 下载模板
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "下载模板")]
        [Permission(Permissions.OrganizationManage.Seniority)]
        public IActionResult DownloadSeniorityTemplate()
        {
            List<DataTable> listTable = new List<DataTable>();

            DataTable dt = new DataTable("sheet1");
            dt.Columns.Add(new DataColumn("工龄", typeof(string)));
            dt.Columns.Add(new DataColumn("工作量津贴", typeof(decimal)));
            dt.Columns.Add(new DataColumn("护龄工资", typeof(decimal)));
            dt.Columns.Add(new DataColumn("备注", typeof(string)));
            listTable.Add(dt);
            var bytes = Excel.WriteToExcelBytes(listTable);
            return this.File(bytes, ConstDefinition.Common.Export_Excel, "工龄津贴模板" + SysDateTime.Now.ToString("yyyy-MM-dd") + ConstDefinition.Common.ExtensionOfHighVersionExcel);
        }

        /// <summary>
        /// 导出工龄津贴
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "导出工龄津贴")]
        [Permission(Permissions.OrganizationManage.Seniority)]
        public IActionResult ExportSeniority(SeniorityFilter filter)
        {
            var exps = filter.GetDynamicQueryExp<Seniority>();

            if (!filter.WorkAge.HasValue)
            {
                filter.Order = "+WorkAge";
            }

            var seniorities = exps == null
                ? this.Repo.GetEntities<Seniority>(filter.Order).Maps<SeniorityQuery>()
                : this.Repo.GetEntities(exps, filter.Order).Maps<SeniorityQuery>();

            var tables = this.FormatSeniorityTable(seniorities);
            var bytes = Excel.WriteToExcelBytes(tables, "工龄津贴");
            return this.File(bytes, ConstDefinition.Common.Export_Excel, "工龄津贴" + SysDateTime.Now.ToString("yyyy-MM-dd") + ConstDefinition.Common.ExtensionOfHighVersionExcel);
        }

        private DataTable FormatSeniorityTable(List<SeniorityQuery> seniorities)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add(new DataColumn("工龄", typeof(string)));
            dt.Columns.Add(new DataColumn("工作量津贴", typeof(decimal)));
            dt.Columns.Add(new DataColumn("护龄工资", typeof(decimal)));
            dt.Columns.Add(new DataColumn("备注", typeof(string)));

            foreach (var senioritie in seniorities ?? new List<SeniorityQuery>())
            {
                var dr = dt.NewRow();
                dr["工龄"] = senioritie.WorkAge;
                dr["工作量津贴"] = senioritie.WorkAllowance;
                dr["护龄工资"] = senioritie.NursingAgeWage;
                dr["备注"] = senioritie.Memo;

                dt.Rows.Add(dr);
            }

            return dt;
        }

        #endregion 工龄津贴

        #region 薪级工资管理

        /// <summary>
        /// 查询薪级工资
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询薪级工资")]
        [Permission(Permissions.OrganizationManage.SalaryScaleManage)]
        public QueryResult<SalaryScaleQuery> QuerySalaryScale([FromQuery] SalaryScaleFilter filter)
        {
            var exps = this.NewExps<SalaryScale>();

            var result = this.Repo.GetDynamicQuery<SalaryScale, SalaryScaleQuery>(filter, exps);

            return result;
        }

        /// <summary>
        /// 根据岗位大类查询薪级工资
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "根据岗位大类查询薪级工资")]
        public BizResult<List<SalaryScaleModel>> QuerySalaryScaleByStationId([FromQuery] Guid id)
        {
            var entities = this.Repo.GetEntities<SalaryScale>(p => p.StationId == id);

            return new BizResult<List<SalaryScaleModel>>(entities.Maps<SalaryScaleModel>());
        }

        /// <summary>
        /// 导出薪级工资
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "导出薪级工资")]
        [Permission(Permissions.OrganizationManage.SalaryScaleManage)]
        public IActionResult ExportSalaryScale(SalaryScaleFilter filter)
        {
            var exps = filter.GetDynamicQueryExp<SalaryScale>();

            if (filter.StationId.IsEmpty() && !filter.Scale.HasValue)
            {
                filter.Order = "+Scale";
            }

            var salaryScale = exps == null
                ? this.Repo.GetEntities<SalaryScale>(filter.Order).Maps<SalaryScaleQuery>()
                : this.Repo.GetEntities(exps, filter.Order).Maps<SalaryScaleQuery>();
            var tables = this.FormatSalaryScaleTable(salaryScale);
            var bytes = Excel.WriteToExcelBytes(tables, "薪级工资");
            return this.File(bytes, ConstDefinition.Common.Export_Excel, "薪级工资" + SysDateTime.Now.ToString("yyyy-MM-dd") + ConstDefinition.Common.ExtensionOfHighVersionExcel);
        }

        private DataTable FormatSalaryScaleTable(List<SalaryScaleQuery> salaryScales)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add(new DataColumn("岗位大类", typeof(string)));
            dt.Columns.Add(new DataColumn("薪级", typeof(int)));
            dt.Columns.Add(new DataColumn("薪级工资", typeof(decimal)));
            dt.Columns.Add(new DataColumn("备注", typeof(string)));

            foreach (var salaryScale in salaryScales ?? new List<SalaryScaleQuery>())
            {
                var dr = dt.NewRow();
                dr["岗位大类"] = salaryScale.StationName;
                dr["薪级"] = salaryScale.Scale;
                dr["薪级工资"] = salaryScale.Wage;
                dr["备注"] = salaryScale.Memo;

                dt.Rows.Add(dr);
            }

            return dt;
        }

        /// <summary>
        /// 获取薪级工资
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取薪级工资")]
        [Permission(Permissions.OrganizationManage.SalaryScaleManage)]
        public BizResult<SalaryScaleModel> GetSalaryScale([FromQuery] Guid id)
        {
            var result = new BizResult<SalaryScaleModel>();

            var entity = this.Repo.Get<SalaryScale>(id);

            if (entity == null)
            {
                result.Error("薪级工资不存在");
            }
            else
            {
                var model = entity.Map<SalaryScaleModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增薪级工资
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增薪级工资")]
        [Permission(Permissions.OrganizationManage.SalaryScaleManage)]
        public BizResult<SalaryScaleModel> AddSalaryScale(SalaryScaleModel model)
        {
            var entity = model.Map<SalaryScale>();

            var result = this.Repo.AddSalaryScale(entity);

            return result.Map<SalaryScaleModel>();
        }

        /// <summary>
        /// 修改薪级工资
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "修改薪级工资")]
        [Permission(Permissions.OrganizationManage.SalaryScaleManage)]
        public BizResult<SalaryScaleModel> UpdateSalaryScale(SalaryScaleModel model)
        {
            var entity = model.Map<SalaryScale>();

            var result = this.Repo.UpdateSalaryScale(entity);

            return result.Map<SalaryScaleModel>();
        }

        /// <summary>
        /// 删除薪级工资
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除薪级工资")]
        [Permission(Permissions.OrganizationManage.SalaryScaleManage)]
        public BizResult DeleteSalaryScale(SalaryScaleModel model)
        {
            return this.Repo.DeleteSalaryScale(model.ID);
        }

        /// <summary>
        /// 导入薪级工资
        /// </summary>
        /// <param name="form"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "导入薪级工资")]
        [Permission(Permissions.OrganizationManage.SalaryScaleManage)]
        public BizResult ImportSalaryScale([FromForm] IFormCollection form)
        {
            var result = new BizResult();
            var ms = new MemoryStream();
            try
            {
                var file = form.Files["file"];

                if (file == null)
                {
                    result.Error("请上传文件");
                }
                else
                {
                    var salaryScales = new List<SalaryScale>();

                    file.CopyTo(ms);
                    var dt = new DataTable();
                    var fileInfo = new FileInfo(file.FileName);
                    dt = Excel.ReadTemplateFromExcelToDataTable(ms, fileInfo.Extension);

                    if (dt.Rows.Count == 0)
                    {
                        result.Error("导入列表不可为空");
                    }
                    else
                    {
                        //检查列头是否符合格式要求
                        var lackColumns = DataTableHelper.CheckColumns(dt, ImportMapping.BudgetingActualMapping);
                        if (lackColumns.Any())
                        {
                            result.Error($"模板中缺少如下列：{string.Join('、', lackColumns)}。");
                        }
                    }

                    if (result.Succeed)
                    {
                        var stations = this.Repo.GetEntities<Station>(p => !p.ParentId.HasValue && p.IsCategory == true);

                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            var row = dt.Rows[i];
                            var salaryScale = new SalaryScale();
                            var errorMessage = string.Empty;

                            // 取值
                            var typeName = row["岗位类型"].AsString().AsTrim();
                            var number = row["薪级"].AsString().AsTrim();
                            var wage = row["工资标准"].AsString().AsTrim();
                            var description = row["备注"].AsString().AsTrim();

                            #region 判断 验证 取值

                            if (typeName.IsEmpty())
                            {
                                errorMessage += "岗位类型不能为空;";
                            }
                            else
                            {
                                var station = stations.FirstOrDefault(p => p.Name == typeName);
                                if (station == null)
                                {
                                    errorMessage += $"岗位类型[{typeName}]不存在;";
                                }
                                else
                                {
                                    salaryScale.StationId = station.ID;
                                }
                            }

                            if (number.IsEmpty())
                            {
                                errorMessage += "薪级不能为空;";
                            }
                            else if (!int.TryParse(number, out int unitNumber))
                            {
                                errorMessage += "薪级不正确，必须为数字;";
                            }
                            else if (unitNumber <= 0)
                            {
                                errorMessage += "薪级必须大于0;";
                            }
                            else
                            {
                                salaryScale.Scale = unitNumber;
                            }

                            if (wage.IsEmpty())
                            {
                                errorMessage += "工资标准不能为空;";
                            }
                            else if (!decimal.TryParse(wage, out decimal unitWage))
                            {
                                errorMessage += "工资标准不正确;";
                            }
                            else if (unitWage <= 0)
                            {
                                errorMessage += "工资标准必须大于0;";
                            }
                            else
                            {
                                salaryScale.Wage = decimal.Parse(unitWage.ToString("#.00"));
                            }

                            #endregion 判断 验证 取值

                            if (errorMessage.IsEmpty())
                            {
                                if (salaryScales.Any(p => p.StationId == salaryScale.StationId && p.Scale == salaryScale.Scale))
                                {
                                    errorMessage += "文件中有与当前行岗位大类、薪级一致的重复数据;";
                                }
                            }

                            if (errorMessage.IsEmpty())
                            {
                                salaryScale.Memo = description;

                                salaryScales.Add(salaryScale);
                            }
                            else
                            {
                                errorMessage = $"第{i + 2}行：{errorMessage}";
                                result.Error(errorMessage);
                            }
                        }
                    }

                    // 数据正确 写数据
                    if (result.Succeed)
                    {
                        result = this.Repo.ImportSalaryScale(salaryScales);
                    }
                }

                return result;
            }
            finally
            {
                ms.Dispose();
            }
        }

        #endregion 薪级工资管理

        #region 电话费

        /// <summary>
        /// 查询电话费
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询电话费")]
        [Permission(Permissions.OrganizationManage.TelephoneFee)]
        public QueryResult<TelephoneFeeModel> QueryTelephoneFee([FromQuery] TelephoneFeeFilter filter)
        {
            var exps = this.NewExps<TelephoneFee>();
            var result = this.Repo.GetDynamicQuery<TelephoneFee, TelephoneFeeModel>(filter, exps);
            return result;
        }

        /// <summary>
        /// 获取电话费
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取电话费")]
        [Permission(Permissions.OrganizationManage.TelephoneFee)]
        public BizResult<TelephoneFeeModel> GetTelephoneFee([FromQuery] Guid id)
        {
            var result = new BizResult<TelephoneFeeModel>();

            var entity = this.Repo.Get<TelephoneFee>(id);

            if (entity == null)
            {
                result.Error("电话费不存在");
            }
            else
            {
                var model = entity.Map<TelephoneFeeModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增电话费
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增电话费")]
        [Permission(Permissions.OrganizationManage.TelephoneFee)]
        public BizResult<TelephoneFeeModel> AddTelephoneFee(TelephoneFeeModel model)
        {
            var entity = model.Map<TelephoneFee>();

            var result = this.Repo.AddTelephoneFee(entity);

            return result.Map<TelephoneFeeModel>();
        }

        /// <summary>
        /// 修改电话费
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "修改电话费")]
        [Permission(Permissions.OrganizationManage.TelephoneFee)]
        public BizResult<TelephoneFeeModel> UpdateTelephoneFee(TelephoneFeeModel model)
        {
            var entity = model.Map<TelephoneFee>();

            var result = this.Repo.UpdateTelephoneFee(entity);

            return result.Map<TelephoneFeeModel>();
        }

        /// <summary>
        /// 删除电话费
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除电话费")]
        [Permission(Permissions.OrganizationManage.TelephoneFee)]
        public BizResult DeleteTelephoneFee([FromBody] TelephoneFeeModel model)
        {
            var entity = model.Map<TelephoneFee>();

            var result = this.Repo.DeleteTelephoneFee(entity);

            return result;
        }

        #endregion 电话费

        #region 公车补贴

        /// <summary>
        /// 查询公车补贴
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询公车补贴")]
        [Permission(Permissions.OrganizationManage.CarSubsidy)]
        public QueryResult<CarSubsidyModel> QueryCarSubsidy([FromQuery] CarSubsidyFilter filter)
        {
            var exps = this.NewExps<CarSubsidy>();
            var result = this.Repo.GetDynamicQuery<CarSubsidy, CarSubsidyModel>(filter, exps);
            return result;
        }

        /// <summary>
        /// 获取公车补贴
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取公车补贴")]
        [Permission(Permissions.OrganizationManage.CarSubsidy)]
        public BizResult<CarSubsidyModel> GetCarSubsidy([FromQuery] Guid id)
        {
            var result = new BizResult<CarSubsidyModel>();

            var entity = this.Repo.Get<CarSubsidy>(id);

            if (entity == null)
            {
                result.Error("公车补贴不存在");
            }
            else
            {
                var model = entity.Map<CarSubsidyModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增公车补贴
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增公车补贴")]
        [Permission(Permissions.OrganizationManage.CarSubsidy)]
        public BizResult<CarSubsidyModel> AddCarSubsidy(CarSubsidyModel model)
        {
            var entity = model.Map<CarSubsidy>();

            var result = this.Repo.AddCarSubsidy(entity);

            return result.Map<CarSubsidyModel>();
        }

        /// <summary>
        /// 修改公车补贴
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "修改公车补贴")]
        [Permission(Permissions.OrganizationManage.CarSubsidy)]
        public BizResult<CarSubsidyModel> UpdateCarSubsidy(CarSubsidyModel model)
        {
            var entity = model.Map<CarSubsidy>();

            var result = this.Repo.UpdateCarSubsidy(entity);

            return result.Map<CarSubsidyModel>();
        }

        /// <summary>
        /// 删除公车补贴
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除公车补贴")]
        [Permission(Permissions.OrganizationManage.CarSubsidy)]
        public BizResult DeleteCarSubsidy([FromBody] CarSubsidyModel model)
        {
            var entity = model.Map<CarSubsidy>();

            var result = this.Repo.DeleteCarSubsidy(entity);

            return result;
        }

        #endregion 公车补贴
    }
}