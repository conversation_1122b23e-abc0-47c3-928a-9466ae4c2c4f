﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Renji.JHR.Entities
{
    /// <summary>
    /// 薪资类型
    /// </summary>
    public enum SalaryType
    {
        [Description("未知")]
        [EnumGroup(Visible = true)]
        None = 0,

        /// <summary>
        /// 月薪
        /// </summary>       
        [Description("月薪")]
        MonthlySalary = 1,

        /// <summary>
        /// 博士后
        /// </summary>
        [Description("博士后")]
        Postdoctor = 2,

        /// <summary>
        /// 年薪
        /// </summary>
        [Description("年薪")]
        Finance = 3,

        /// <summary>
        /// 返聘
        /// </summary>
        [Description("返聘")]
        RehireAfterRetirement = 4
    }
}
