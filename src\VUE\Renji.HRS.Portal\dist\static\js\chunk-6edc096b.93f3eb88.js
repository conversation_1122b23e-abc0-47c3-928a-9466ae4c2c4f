(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6edc096b"],{1939:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.headModel}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"月份"}},[a("el-date-picker",{attrs:{type:"month",placeholder:"请选择月份","value-format":"yyyy-MM",clearable:!1},model:{value:e.headModel.recordMonth,callback:function(t){e.$set(e.headModel,"recordMonth",t)},expression:"headModel.recordMonth"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"至"}},[a("el-date-picker",{attrs:{type:"month",placeholder:"请选择月份","value-format":"yyyy-MM"},model:{value:e.headModel.endRecordMonth,callback:function(t){e.$set(e.headModel,"endRecordMonth",t)},expression:"headModel.endRecordMonth"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"部门"}},[a("c-select-tree2",{ref:"treeSelect",attrs:{clearable:!0,multiple:!0,data:e.treeData,props:e.treeProps,"check-strictly":!0},model:{value:e.headModel.dept,callback:function(t){e.$set(e.headModel,"dept",t)},expression:"headModel.dept"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"唯一码"}},[a("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.headModel.empUid,callback:function(t){e.$set(e.headModel,"empUid",t)},expression:"headModel.empUid"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"工号"}},[a("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.headModel.empCode,callback:function(t){e.$set(e.headModel,"empCode",t)},expression:"headModel.empCode"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"姓名"}},[a("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.headModel.empName,callback:function(t){e.$set(e.headModel,"empName",t)},expression:"headModel.empName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"假期"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.headModel.holiday,callback:function(t){e.$set(e.headModel,"holiday",t)},expression:"headModel.holiday"}},e._l(e.holidayOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1),a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.headModel.operation,callback:function(t){e.$set(e.headModel,"operation",t)},expression:"headModel.operation"}},e._l(e.operationOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1),a("el-input",{staticClass:"holidayValue",attrs:{size:"small"},model:{value:e.headModel.holidayValue,callback:function(t){e.$set(e.headModel,"holidayValue",t)},expression:"headModel.holidayValue"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:""}},[a("el-button",{attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")]),a("el-button",{attrs:{type:"primary"},on:{click:e.exportData}},[e._v("导出")])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"显示有假期项"}},[a("el-checkbox",{model:{value:e.headModel.onlyDayOffValue,callback:function(t){e.$set(e.headModel,"onlyDayOffValue",t)},expression:"headModel.onlyDayOffValue"}})],1),a("el-form-item",{attrs:{label:"显示有卫贴项"}},[a("el-checkbox",{model:{value:e.headModel.onlyH1Value,callback:function(t){e.$set(e.headModel,"onlyH1Value",t)},expression:"headModel.onlyH1Value"}})],1)],1)],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",attrs:{data:e.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"}},[a("el-table-column",{attrs:{label:"唯一码",align:"center",width:"120",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.empUid))])]}}])}),a("el-table-column",{attrs:{label:"工号",align:"center",width:"120",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.empCode))])]}}])}),a("el-table-column",{attrs:{label:"姓名",align:"center",width:"120",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.empName))])]}}])}),a("el-table-column",{attrs:{label:"部门",align:"center",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.empDept))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"应发公休",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(0===o.generalHoliday?"":o.generalHoliday))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"剩余公休",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(0===o.historyH12?"":o.historyH12))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"公休",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.h12))])]}}])}),a("el-table-column",{directives:[{name:"show",rawName:"v-show",value:e.showSubsidies,expression:"showSubsidies"}],attrs:{label:"上月卫贴标准",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.preMonthH1))])]}}])}),a("el-table-column",{directives:[{name:"show",rawName:"v-show",value:e.showSubsidies,expression:"showSubsidies"}],attrs:{label:"本月卫贴标准",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.h1))])]}}])}),a("el-table-column",{attrs:{label:"病假",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.h2))])]}}])}),a("el-table-column",{attrs:{label:"事假",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.h3))])]}}])}),a("el-table-column",{attrs:{label:"产假",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.h4))])]}}])}),a("el-table-column",{attrs:{label:"哺乳假",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.h5))])]}}])}),a("el-table-column",{attrs:{label:"探亲假",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.h6))])]}}])}),a("el-table-column",{attrs:{label:"计生假",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.h7))])]}}])}),a("el-table-column",{attrs:{label:"婚丧假",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.h8))])]}}])}),a("el-table-column",{attrs:{label:"脱产读研",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.h9))])]}}])}),a("el-table-column",{attrs:{label:"因公出国",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.h10))])]}}])}),a("el-table-column",{attrs:{label:"因私出国",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.h11))])]}}])}),a("el-table-column",{attrs:{label:"假期合计",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.sumHoliday))])]}}])}),a("el-table-column",{directives:[{name:"show",rawName:"v-show",value:e.showSubsidies,expression:"showSubsidies"}],attrs:{label:"卫贴",align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.subsidies))])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[20,50,100],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.paginationChanged}})]},proxy:!0}])})],1)},l=[],r=(a("99af"),a("d3b7"),a("25f0"),a("3ca3"),a("4d90"),a("ddb0"),a("2b3d"),a("d368")),n=a("cbd2"),i={components:{},data:function(){return{headModel:{recordMonth:this.getNowTime(),onlyDayOffValue:!0,onlyH1Value:!0,dept:[]},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeLoading:!1,treeExpandedKeys:[],currentNode:null,total:1,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,tableData:[],allData:[],statusoptions:[{label:"全部",value:"-1"},{label:"未提交",value:"0"},{label:"已提交",value:"1"}],holidayOptions:[{label:"病假",value:"H2"},{label:"事假",value:"H3"},{label:"产假",value:"H4"},{label:"哺乳假",value:"H5"},{label:"探亲假",value:"H6"},{label:"计生假",value:"H7"},{label:"婚丧假",value:"H8"},{label:"脱产研读",value:"H9"},{label:"因公出国",value:"H10"},{label:"因私出国",value:"H11"},{label:"公休",value:"H12"}],operationOptions:[{label:"等于",value:"10"},{label:"大于",value:"20"},{label:"大于等于",value:"25"},{label:"小于",value:"30"},{label:"小于等于",value:"35"}],showSubsidies:!1}},created:function(){this.loadTree()},methods:{getNowTime:function(){var e=new Date,t=e.getFullYear(),a=e.getMonth();a=a.toString().padStart(2,"0"),"00"===a&&(t-=1,a="12");var o="".concat(t,"-").concat(a);return o},loadTree:function(){var e=this;r["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data,e.treeExpandedKeys.push(t.data[0].id)})).catch((function(e){console.log(e)})),this.resertCurrentNode()},resertCurrentNode:function(){this.currentNode=null},treeNodeClick:function(e){},search:function(){this.listQuery.pageIndex=1,this.getSearchResult()},getSearchResult:function(){var e=this;this.headModel.recordMonth&&!this.headModel.endRecordMonth||this.headModel.recordMonth===this.headModel.endRecordMonth?this.showSubsidies=!0:this.showSubsidies=!1;var t={RecordMonth:this.headModel.recordMonth,EndRecordMonth:this.headModel.endRecordMonth,Statue:this.headModel.statue,Operation:this.headModel.operation,Holiday:this.headModel.holiday,HolidayValue:this.headModel.holidayValue,DeptIds:this.headModel.dept,EmpCode:this.headModel.empCode,EmpUid:this.headModel.empUid,EmpName:this.headModel.empName,OnlyDayOffValue:this.headModel.onlyDayOffValue,OnlyH1Value:this.headModel.onlyH1Value,PageIndex:this.listQuery.pageIndex,PageSize:this.listQuery.pageSize};n["a"].searchAttDayOffRecordDetail1(t).then((function(t){e.listLoading=!1,t.succeed?(e.tableData=t.data.datas,e.total=t.data.recordCount):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},paginationChanged:function(){this.getSearchResult()},exportData:function(){var e={RecordMonth:this.headModel.recordMonth,EndRecordMonth:this.headModel.endRecordMonth,Statue:this.headModel.statue,Operation:this.headModel.operation,Holiday:this.headModel.holiday,HolidayValue:this.headModel.holidayValue,DeptIds:this.headModel.dept,EmpCode:this.headModel.empCode,EmpUid:this.headModel.empUid,EmpName:this.headModel.empName,OnlyDayOffValue:this.headModel.onlyDayOffValue,OnlyH1Value:this.headModel.onlyH1Value,PageIndex:this.listQuery.pageIndex,PageSize:this.listQuery.pageSize};n["a"].getAttDayOffRecordDetail1Excel(e).then((function(e){var t=new Blob([e],{type:e.type}),a="考勤数据1.xlsx";if(window.navigator&&window.navigator.msSaveOrOpenBlob)window.navigator.msSaveOrOpenBlob(t,a);else{var o=document.createElement("a"),l=window.URL.createObjectURL(t);o.href=l,o.download=a,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(l)}}))}}},d=i,c=(a("a9b4"),a("2877")),u=Object(c["a"])(d,o,l,!1,null,null,null);t["default"]=u.exports},3276:function(e,t,a){},a9b4:function(e,t,a){"use strict";var o=a("3276"),l=a.n(o);l.a},cbd2:function(e,t,a){"use strict";var o=a("cfe3"),l="AttendanceManage",r=new o["a"](l);t["a"]={getAttMonthShiftRecord:function(e){return r.get("GetAttMonthShiftRecord",e)},queryAttMonthShiftRecordDetail:function(e){return r.get("QueryAttMonthShiftRecordDetail",e)},batchConfirmAttMonthShiftRecord:function(e){return r.post("BatchConfirmAttMonthShiftRecord",e)},saveAttMonthShiftRecord:function(e){return r.post("SaveAttMonthShiftRecord",e)},submitAttMonthShiftRecord:function(e){return r.post("SubmitAttMonthShiftRecord",e)},ConfirmAttMonthShiftRecord:function(e){return r.post("ConfirmAttMonthShiftRecord",e)},rejectAttMonthShiftRecord:function(e){return r.post("RejectAttMonthShiftRecord",e)},searchAttMonthShiftRecordDetail:function(e){return r.get("SearchAttMonthShiftRecordDetail",e)},searchAttMonthShiftRecordDetail_Update:function(e){return r.get("SearchAttMonthShiftRecordDetail_Update",e)},updateAttMonthShiftRecordDetail:function(e){return r.post("UpdateAttMonthShiftRecordDetail",e)},getColorDeptTree_MiddleNightShift:function(e){return r.get("GetColorDeptTree_MiddleNightShift",e)},get_MiddleNightShiftReportExcel:function(e){return r.getFile("Get_MiddleNightShiftReportExcel",e)},getAttHolidayOTRecord:function(e){return r.get("GetAttHolidayOTRecord",e)},queryAttHolidayOTRecordDetail:function(e){return r.get("QueryAttHolidayOTRecordDetail",e)},saveAttHolidayOTRecord:function(e){return r.post("SaveAttHolidayOTRecord",e)},batchConfirmAttHolidayOTRecord:function(e){return r.post("BatchConfirmAttHolidayOTRecord",e)},submitAttHolidayOTRecord:function(e){return r.post("SubmitAttHolidayOTRecord",e)},ConfirmAttHolidayOTRecord:function(e){return r.post("ConfirmAttHolidayOTRecord",e)},rejectAttHolidayOTRecord:function(e){return r.post("RejectAttHolidayOTRecord",e)},searchAttHolidayOTRecordDetail:function(e){return r.get("SearchAttHolidayOT",e)},searchAttHolidayOTRecordDetail_Update:function(e){return r.get("SearchAttHolidayOTRecordDetail_Update",e)},updateAttHolidayOTRecordDetail:function(e){return r.post("UpdateAttHolidayOTRecordDetail",e)},getColorDeptTree_HolidayOT:function(e){return r.get("GetColorDeptTree_HolidayOT",e)},getOTReportExcel:function(e){return r.getFile("GetOTReportExcel",e)},getAttDayOffRecord:function(e){return r.get("GetAttDayOffRecord",e)},queryAttDayOffRecordDetail:function(e){return r.get("QueryAttDayOffRecordDetail",e)},saveAttDayOffRecord:function(e){return r.post("SaveAttDayOffRecord",e)},submitAttDayOffRecord:function(e){return r.post("SubmitAttDayOffRecord",e)},updateApproveAttDayOffRecord:function(e){return r.post("UpdateApproveAttDayOffRecord",e)},rejectAttDayOffRecord:function(e){return r.post("RejectAttDayOffRecord",e)},searchAttDayOffRecordDetail:function(e){return r.get("SearchAttDayOffRecordDetail",e)},searchAttDayOffRecordDetail_Update:function(e){return r.get("SearchAttDayOffRecordDetail_Update",e)},updateAttDayOffRecordDetail:function(e){return r.post("UpdateAttDayOffRecordDetail",e)},getColorDeptTree_DayOff:function(e){return r.get("GetColorDeptTree_DayOff",e)},getDayOffReportExcel:function(e){return r.getFile("GetDayOffReportExcel",e)},searchAttDayOffRecordDetail1:function(e){return r.get("SearchAttDayOffRecordDetail1",e)},searchAttMonthWatchRecord:function(e){return r.get("SearchAttMonthWatchRecord",e)},updateAttMonthWatchRecord:function(e){return r.post("UpdateAttMonthWatchRecord",e)},getMonthWatchTReportExcel:function(e){return r.getFile("GetMonthWatchTReportExcel",e)},getAttDayOffRecordDetail1Excel:function(e){return r.getFile("GetAttDayOffRecordDetail1Excel",e)},queryEmployeeList:function(e){return r.get("QueryEmployeeList",e)},searchAttDayOffRecordDetail2:function(e){return r.get("SearchAttDayOffRecordDetail2",e)},getAttDayOffRecordDetail2Excel:function(e){return r.getFile("GetAttDayOffRecordDetail2Excel",e)},searchAttDayOffRecordDetail3:function(e){return r.get("SearchAttDayOffRecordDetail3",e)},getAttDayOffRecordDetail3Excel:function(e){return r.getFile("GetAttDayOffRecordDetail3Excel",e)},queryAttDayOffRecordProphylacticDetail:function(e){return r.get("QueryAttDayOffRecordProphylacticDetail",e)},exportAttDayOffRecordProphylacticDetail:function(e){return r.getFile("ExportAttDayOffRecordProphylacticDetail",e)},getAttDayOffRecordProphylacticCase:function(e){return r.get("GetAttDayOffRecordProphylacticCase",e)},getAttDayOffRecordProphylacticDetail:function(e){return r.get("GetAttDayOffRecordProphylacticDetail",e)},getAttDayOffRecordProphylactic:function(e){return r.get("GetAttDayOffRecordProphylactic",e)},addAttDayOffRecordProphylactic:function(e){return r.post("AddAttDayOffRecordProphylactic",e)},updateAttDayOffRecordProphylactic:function(e){return r.post("UpdateAttDayOffRecordProphylactic",e)},deleteAttDayOffRecordProphylacticDetail:function(e){return r.post("DeleteAttDayOffRecordProphylacticDetail",e)},subjectAttDayOffRecordProphylactic:function(e){return r.post("SubjectAttDayOffRecordProphylactic",e)},queryCheckRecordFilling:function(e){return r.get("QueryCheckRecordFilling",e)},queryPersonnelAttendanceData:function(e){return r.get("QueryPersonnelAttendanceData",e)},queryProphylacticChange:function(e){return r.get("QueryProphylacticChange",e)},exportProphylacticChange:function(e){return r.getFile("ExportProphylacticChange",e)},queryPersonnelPendingApproval:function(e){return r.get("QueryPersonnelPendingApproval",e)},approveAttDayOffRecord:function(e){return r.post("ApproveAttDayOffRecord",e)},getSameDeptEmployeeWithHealthAllowance:function(e){return r.get("GetSameDeptEmployeeWithHealthAllowance",e)}}}}]);