﻿using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.EntityFrameworkCore;
using System;
using System.Linq;
using System.Text.RegularExpressions;

namespace Renji.JHR.Bll
{
    public class HomeBll : BaseBll
    {
        #region Constructs

        public HomeBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public HomeBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public HomeBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public HomeBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        #region UserProfile

        public BizResult<User> UpdateUserProfile(User entity)
        {
            var result = new BizResult<User>();

            var id = entity.ID;

            var dbEntity = this.Get<User>(id);

            if (dbEntity == null)
            {
                result.Error("用户不存在");
            }
            else
            {
                entity.RemoveChangedColumn(User.Columns.Uid);
                entity.RemoveChangedColumn(User.Columns.EnumPwdType);
                entity.RemoveChangedColumn(User.Columns.PwdText);
                entity.RemoveChangedColumn(User.Columns.EnumStatus);

                if (dbEntity.EnumStatus == UserStatus.Invalid)
                {
                    result.Error("用户已被禁用");
                }

                if (result.Succeed)
                {
                    this.Update(ref entity);

                    result.Data = entity;
                }
            }

            return result;
        }

        public BizResult<Employee> UpdateUserProfile(Employee entity)
        {
            var result = new BizResult<Employee>();

            var id = entity.ID;

            var dbEntity = this.Get<Employee>(id);

            if (dbEntity == null)
            {
                result.Error("用户不存在");
            }
            else
            {
                entity.RemoveChangedColumn(Employee.Columns.Uid);
                //entity.RemoveChangedColumn(Employee.Columns.JobNo);
                //entity.RemoveChangedColumn(Employee.Columns.MajorStationId);
                entity.RemoveChangedColumn(Employee.Columns.EnumStatus);

                switch (dbEntity.EnumStatus)
                {
                    case EmployeeStatus.InService:
                    case EmployeeStatus.Probation:
                        break;

                    default:
                        result.Error("员工已被禁用");
                        break;
                }

                if (result.Succeed)
                {
                    this.Update(ref entity, false);

                    //var user = new User();

                    //user.ID = id;

                    //user.CopyFrom(entity);

                    //this.Update(ref user, false);

                    //this.SaveChanges();

                    result.Data = entity;
                }
            }

            return result;
        }

        public BizResult UpdateUserPwd(User entity)
        {
            var result = new BizResult();

            if (entity.OldPwdText.IsEmpty())
            {
                result.Error("原密码不可以为空");
            }

            if (entity.NewPwdText.IsEmpty())
            {
                result.Error("新密码不可以为空");
            }
            Regex regex = new Regex(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%\*-\+=:,\\?\[\]\{}]).{8}");
            if (!regex.IsMatch(entity.NewPwdText))
            {
                result.Error("新密码 必须是大写字母，小写字母，数字，字符组成，且长度不少于八位！");
                return result;
            }

            var dbEntity = this.Get<User>(entity.ID);

            if (dbEntity == null)
            {
                result.Error("用户不存在");
            }
            else
            {
                var oldPwd = dbEntity.EnumPwdType switch
                {
                    PwdType.MD5 => entity.OldPwdText.ToMD5(),
                    PwdType.SHA1 => entity.OldPwdText.ToSHA1(),
                    _ => entity.OldPwdText,
                };
                if (oldPwd != dbEntity.PwdText)
                {
                    result.Error("原密码不正确");
                }

                if (result.Succeed)
                {
                    dbEntity.EnumPwdType = PwdType.MD5;
                    dbEntity.PwdText = entity.NewPwdText.ToMD5();

                    this.Update(dbEntity);
                }
            }
            return result;
        }

        #endregion UserProfile
    }
}