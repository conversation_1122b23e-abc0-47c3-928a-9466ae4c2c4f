﻿using Microsoft.AspNetCore.Mvc.Filters;
using Shinsoft.Core.Mvc.Filters;
using Shinsoft.Core.NLog;

namespace Renji.JHR.Api
{
    public class ApiOperateFilter : BaseApiOperateFilter
    {
        protected override void WriteSpecialLog(string specialCode, ApiLogEvent logEvent, ActionExecutedContext context)
        {
        }

        protected override void WriteLog(ApiLogEvent logEvent, ActionExecutedContext context)
        {
            base.WriteLog(logEvent, context);
        }
    }
}