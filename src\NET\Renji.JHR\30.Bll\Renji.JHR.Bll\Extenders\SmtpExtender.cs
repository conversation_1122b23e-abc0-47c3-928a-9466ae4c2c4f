﻿using MailKit.Net.Smtp;
using MailKit.Security;
using MimeKit;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.Configuration;
using Shinsoft.Core.Json;
using Shinsoft.Core.Mail;
using Shinsoft.Core.NLog;
using System;
using System.Collections.Generic;
using System.Security.Authentication;
using System.Threading;

namespace Renji.JHR.Bll
{
    public static class SmtpExtender
    {
        public static SmtpClient GetSmtpClient(this IMailServer server)
        {
            ISmtpServer smtp = server.SmtpServer!;

            var client = new SmtpClient();

            if (smtp.EnumSmtpSecure == SmtpSecure.Ssl)
            {
                client.Connect(smtp.Host, smtp.Port, true);
            }
            else
            {
                client.Connect(smtp.Host, smtp.Port, false);
            }

            client.Authenticate(smtp.Username, smtp.Password);

            return client;
        }

        public static MimeMessage GetMimeMessage<TMail>(this IMailServer server, TMail mail, List<MailAttachment>? attachments)
            where TMail : class, IMail
        {
            //# MimeMessage代表⼀封电⼦邮件的对象
            var message = new MimeMessage();

            //# 添加发件⼈地址 Name 发件⼈名字 sender 发件⼈邮箱
            if (server.From.IsEmpty())
            {
                message.From.Add(mail.From.ToMailboxAddress());
            }
            else
            {
                message.From.Add(server.From.ToMailboxAddress());

                mail.RealFrom = server.From;
            }

            //# 添加收件⼈地址
            if (BaseConfig.Mail.Debug)
            {
                if (!string.IsNullOrWhiteSpace(server.DebugTo))
                {
                    foreach (string to in server.DebugTo.Split(';', ','))
                    {
                        if (!string.IsNullOrWhiteSpace(to))
                        {
                            message.To.Add(to.ToMailboxAddress());
                        }
                    }

                    mail.DebugTo = server.DebugTo;
                }
            }
            else
            {
                if (!string.IsNullOrWhiteSpace(mail.To))
                {
                    foreach (string to in mail.To.Split(';', ','))
                    {
                        if (!string.IsNullOrWhiteSpace(to))
                        {
                            message.To.Add(to.ToMailboxAddress());
                        }
                    }
                }

                if (!string.IsNullOrWhiteSpace(mail.Cc))
                {
                    foreach (string cc in mail.Cc.Split(';', ','))
                    {
                        if (!string.IsNullOrWhiteSpace(cc))
                        {
                            message.Cc.Add(cc.ToMailboxAddress());
                        }
                    }
                }

                if (!string.IsNullOrWhiteSpace(mail.Bcc))
                {
                    foreach (string bcc in mail.Bcc.Split(';', ','))
                    {
                        if (!string.IsNullOrWhiteSpace(bcc))
                        {
                            message.Bcc.Add(bcc.ToMailboxAddress());
                        }
                    }
                }
            }

            //# 设置邮件主题信息
            message.Subject = mail.Subject;

            //# 设置邮件内容
            var bodyBuilder = new BodyBuilder();

            if (mail.IsHtmlBody)
            {
                bodyBuilder.HtmlBody = mail.Content;
            }
            else
            {
                bodyBuilder.TextBody = mail.Content;
            }

            if (attachments?.Any() == true)
            {
                const string charset = "GB18030";

                foreach (var mailAttachment in attachments)
                {
                    var fileStream = mailAttachment.FileStream;

                    if (fileStream != null)
                    {
                        var contentType = mailAttachment.GetContentType();

                        var mimePart = new MimePart(contentType)
                        {
                            Content = new MimeContent(fileStream),
                            ContentDisposition = new ContentDisposition(ContentDisposition.Attachment),
                            ContentTransferEncoding = ContentEncoding.Base64,
                        };

                        mimePart.ContentType.Parameters.Add(charset, "name", mailAttachment.FileName);
                        mimePart.ContentDisposition.Parameters.Add(charset, "filename", mailAttachment.FileName);

                        foreach (var param in mimePart.ContentDisposition.Parameters)
                        {
                            param.EncodingMethod = ParameterEncodingMethod.Rfc2047;
                        }
                        foreach (var param in mimePart.ContentType.Parameters)
                        {
                            param.EncodingMethod = ParameterEncodingMethod.Rfc2047;
                        }

                        bodyBuilder.Attachments.Add(mimePart);
                    }
                }
            }

            message.Body = bodyBuilder.ToMessageBody();

            return message;
        }

        public static ContentType GetContentType(this MailAttachment attachment)
        {
            if (!ContentType.TryParse(attachment.ContentType, out ContentType contentType))
            {
                var fileType = MimeTypes.GetMimeType(attachment.FileName);
                var array = fileType.Split('/');
                contentType = new ContentType(array[0], array[1]);
            }

            return contentType;
        }

        public static (bool success, string message) SendMail(this SmtpClient client, MimeMessage mimeMessage)
        {
            bool success;
            string message;

            try
            {
                message = client.Send(mimeMessage);
                success = true;
            }
            catch (Exception ex)
            {
                success = false;
                message = ex.Message;
            }

            return (success, message);
        }

        internal static bool SendMail<TMail>(this SmtpClient client, TMail mail, IMailServer server, List<MailAttachment> attachments, int skipFrames = 1)
            where TMail : class, IMail
        {
            var mimeMessage = server.GetMimeMessage(mail, attachments);

            bool success = false;
            string message = string.Empty;

            int maxRetry = server.MaxRetry <= 0
                ? 3
                : server.MaxRetry;
            int retry = 0;

            while (retry < maxRetry)
            {
                (success, message) = client.SendMail(mimeMessage);

                if (success)
                {
                    break;
                }
                else
                {
                    retry++;
                    if (retry >= maxRetry)
                    {
                        Dictionary<string, object> dict = new()
                        {
                            { "MailServerCode", server.Code },
                            { "MailId", mail.ID },
                            { "MailSubject", mail.Subject }
                        };

                        NLogHelper.Error("发送邮件(SMTP)", message, mail.Subject, typeof(TMail).Name, mail.ID, remark: dict.ToJson(), skipFrames: skipFrames + 1);

                        break;
                    }
                    else if (server.RetryInterval > 0)
                    {
                        Thread.Sleep(server.RetryInterval);
                    }
                }
            }

            mail.SendCount++;
            mail.SendTime = SysDateTime.Now;
            mail.SendMessage = message;

            if (success)
            {
                mail.EnumStatus = MailStatus.Success;
            }
            else
            {
                if (mail.SendCount >= server.MaxResend || mail.EnumStatus == MailStatus.Immediately)
                {
                    mail.EnumStatus = MailStatus.Failed;
                }
                else
                {
                    mail.EnumStatus = MailStatus.Resend;
                }
            }

            return success;
        }
    }
}