<template>
  <div>
    <el-dialog :title="title" :visible="showDialog" width="90%" :top="'10vh'" :close-on-press-escape="false" :close-on-click-modal="false" @close="closeDialog">
      <el-form ref="dataForm" :rules="rules" :model="dataModel" label-width="100px">
        <el-card style="margin-top: 3px;">
          <div slot="header">
            <span>基础信息</span>
          </div>
          <el-row>
            <el-col :span="6">
              <el-form-item label="员工姓名" label-width="120px">
                <div>
                  <span>
                    {{ dataModel.employeeModel.empName }}
                  </span>
                  <el-button v-if="!isEdit" style="margin-left: 15px;" type="primary" title="选择员工" @click="selectEmployeeDialog">选 择</el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="唯一码" label-width="120px">
                {{ dataModel.employeeModel.empUid }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="工号" label-width="120px">
                {{ dataModel.employeeModel.empCode }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="性别" label-width="120px">
                {{ dataModel.employeeModel.genderDesc }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="工龄" label-width="120px">
                {{ dataModel.employeeModel.societyAge }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        <el-card style="margin-top: 3px;">
          <div slot="header">
            <span>零星补病产假信息</span>
          </div>
          <el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item label="补扣金额" prop="backPayDeduction" label-width="120px">
                  <el-input-number v-model="dataModel.backPayDeduction"  :min="0" :precision="2" :controls="false" placeholder="补扣金额" style="width: 60%" maxlength="5" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="备注" label-width="120px">
                <el-input v-model="dataModel.remark" type="textarea" :rows="3" maxlength="300" placeholder="备注" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" :loading="btnSaveLoading" @click="saveDialog">保 存</el-button>
      </div>
    </el-dialog>

    <selectUserComponent ref="selectEmployee" @selectRow="setEmployee" />

  </div>
</template>
<script>
import salaryApi from '@/api/salary'
import sysManageApi from '@/api/sysManage'
import selectUserComponent from '@/views/salary/employeeSalary/components/selectUser'

export default {
  components: {
    selectUserComponent
  },
  data() {
    var validateMoney = (rule, value, callback) => {
      if (!(/^(\d|[1-9]\d+)(\.\d{1,2})?$/).test(value)) {
        callback(new Error('请输入正确格式数字,小数不超过2位'))
      } else {
        callback()
      }
    }
    return {
      showDialog: false,
      title: '',
      rules: {
        backPayDeduction: [
          { required: true, message: '补扣金额必填', trigger: 'blur' },
          { validator: validateMoney, trigger: 'blur' }
        ],
        remark: [
          { required: true, message: '备注必填', trigger: 'change' }
        ]
      },
      btnSaveLoading: false,
      leaveDaysTypeList: [],
      isEdit: false,
      maxDaysOfMonth: 0,
      salaryMonth: '',
      dataModel: {
        employeeModel: {}
      },
      isShowBase: true
    }
  },
  methods: {
    initDialog(row) {
      if (!row) {
        this.title = '新增零星补病产假'
        this.isEdit = false
      } else {
        this.title = '编辑零星补病产假'
        this.isEdit = true
        this.getData(row.id)
      }
      this.dataModel.salaryId = this.salaryId
      this.showDialog = true
      this.loadLeaveDaysType()
      this.getMaxDaysOfMonth(this.salaryId)
    },
    getData(id) {
      salaryApi.getSporadicSalaryLeave({ id: id }).then(res => {
        if (res.succeed) {
          this.dataModel = res.data
          this.setEmployee(res.data.employee)
        }
      }).catch(res => {
      })
    },
    getMaxDaysOfMonth(salaryId) {
      salaryApi.getSalary({ id: salaryId }).then(res => {
        if (res.succeed) {
          const salaryData = res.data;
          const recordMonth = salaryData.month;
          const [year, month] = recordMonth.split('-').map(Number);
          
          // 计算两个月前的月份和年份
          let targetMonth = month - 2;
          let targetYear = year;
          
          // 处理月份为负数的情况（跨年）
          if (targetMonth <= 0) {
            targetMonth += 12; // 调整为正数月份
            targetYear -= 1;   // 年份减1
          }
          
          // 获取目标月份的最后一天
          const daysInTargetMonth = new Date(targetYear, targetMonth, 0).getDate();
          
          this.salaryMonth = res.data
          this.maxDaysOfMonth = daysInTargetMonth;
        }
      }).catch(res => {
      })
    },
    selectEmployeeDialog() {
      this.$refs.selectEmployee.loadTree()
      this.$refs.selectEmployee.showEmployee = true
    },
    saveDialog() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.btnSaveLoading = true
          if (!this.isEdit) {
            salaryApi.addSporadicSalaryLeave(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '添加成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          } else {
            salaryApi.updateSporadicSalaryLeave(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '修改成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          }
        }
      })
    },
    closeDialog() {
      this.dataModel = {
        employeeModel: {}
      }
      this.showDialog = false
      this.$refs.dataForm.resetFields()
    },
    setEmployee(emp) {
      this.dataModel.employeeId = emp.id
      this.dataModel.salaryId = this.salaryId
      this.$set(this.dataModel, 'employeeModel',
        {
          employeeId: emp.id,
          empUid: emp.uid,
          empCode: emp.empCode,
          empName: emp.displayName,
          genderDesc: emp.enumGenderDesc,
          empDept: emp.deptName,
          hospitalAreaNameText: emp.hospitalAreaNameText,
          identityNumber: emp.identityNumber,
          hireStyleName: emp.employeeHR.hireStyleName,
          leaveStyleName: emp.employeeHR.leaveStyleName,
          deadDate: emp.employeeHR.deadDate,
          societyAge: emp.employeeHR.societyAge
        })
    }
  }
}
</script>

<style>
</style>
