(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3801c3f8"],{1452:function(e,t,a){"use strict";var l=a("4493"),i=a.n(l);i.a},2508:function(e,t,a){},"34fb":function(e,t,a){"use strict";var l=a("cf3d"),i=a.n(l);i.a},4493:function(e,t,a){},"5e46":function(e,t,a){},"895b":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container "},[a("el-row",{staticStyle:{background:"#e5e9f2","border-bottom":"3px solid white",height:"50px"},attrs:{type:"flex",align:"middle"}},[a("el-col",{attrs:{span:3}},[a("span",{staticStyle:{"margin-left":"10px","font-weight":"600"}},[e._v("薪资月份：")]),a("span",[e._v(" "+e._s(e.salaryData.month&&e.$moment(e.salaryData.month).isSame(e.$moment(e.salaryData.month).endOf("year"),"day")?e.$moment(e.salaryData.month).year()+"-13":null==e.salaryData.month||void 0==e.salaryData.month?"":e.$moment(e.salaryData.month).format("YYYY-MM"))+" ")])]),a("el-col",{attrs:{span:3}},[a("span",{staticStyle:{"font-weight":"600"}},[e._v("状态：")]),a("span",[e._v(e._s(e.salaryData.enumStatusDesc))])]),a("el-col",{attrs:{span:3}},[a("span",{staticStyle:{"font-weight":"600"}},[e._v("员工数量：")]),a("span",[e._v(e._s(0==e.salaryData.employeeCount||null==e.salaryData.employeeCount||void 0==e.salaryData.employeeCount?"-":e.salaryData.employeeCount))])]),a("el-col",{attrs:{span:3}},[a("span",{staticStyle:{"font-weight":"600"}},[e._v("总薪资：")]),a("span",[e._v(e._s(e._f("numberToCurrencyNo")(e.salaryData.totalSalary)))])]),a("el-col",{attrs:{span:6}},[2==e.salaryData.enumStatus?a("el-button",{attrs:{type:"warning"},on:{click:e.approvalReturn}},[e._v("审批退回")]):e._e(),2==e.salaryData.enumStatus?a("el-button",{attrs:{type:"success"},on:{click:e.approvalPass}},[e._v("审批确认")]):e._e()],1)],1),a("el-tabs",{attrs:{type:"border-card"},on:{"tab-click":e.tabClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"财务数据",name:"financeSalaryTab"}},[e.isThirteenthMonth?a("thirteenthFinanceSalary",{ref:"thirteenthFinanceSalary",attrs:{"salary-id":e.salaryId}}):a("financeSalary",{ref:"financeSalary",attrs:{"salary-id":e.salaryId}})],1),e.isThirteenthMonth?e._e():a("el-tab-pane",{attrs:{label:"退休库",name:"retireSalaryTab"}},[a("retireSalary",{ref:"retireSalary",attrs:{"salary-id":e.salaryId}})],1),e.isThirteenthMonth?e._e():a("el-tab-pane",{attrs:{label:"四金不够扣名单",name:"fourGoldInsufficientSalaryTab"}},[a("fourGoldInsufficientSalary",{ref:"fourGoldInsufficientSalary",attrs:{"salary-id":e.salaryId}})],1),e.isThirteenthMonth?e._e():a("el-tab-pane",{attrs:{label:"最低工资补助",name:"minimumWageSubsidyTab"}},[a("minimumWageSubsidy",{ref:"minimumWageSubsidy",attrs:{"salary-id":e.salaryId,"salary-data":e.salaryData}})],1),e.isThirteenthMonth?e._e():a("el-tab-pane",{attrs:{label:"通用补发/补扣",name:"universalReissueDeductionTab"}},[a("universalReissueDeduction",{ref:"universalReissueDeduction",attrs:{"salary-id":e.salaryId,"group-type-name":"2"}})],1)],1)],1)},i=[],n=(a("b0c0"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container "},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"唯一码"},model:{value:e.listQuery.uid,callback:function(t){e.$set(e.listQuery,"uid",t)},expression:"listQuery.uid"}})],1),a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQuery.displayName,callback:function(t){e.$set(e.listQuery,"displayName",t)},expression:"listQuery.displayName"}})],1),a("el-form-item",[a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",clearable:"",placeholder:"请选择字段"},model:{value:e.listQuery.entityColumn,callback:function(t){e.$set(e.listQuery,"entityColumn",t)},expression:"listQuery.entityColumn"}},e._l(e.dataColumns,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e}})})),1)],1),a("el-form-item",[a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"请选择条件"},model:{value:e.listQuery.queryCondition.EnumOperation,callback:function(t){e.$set(e.listQuery.queryCondition,"EnumOperation",t)},expression:"listQuery.queryCondition.EnumOperation"}},e._l(e.selectConditionOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),a("el-form-item",[a("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",placeholder:"请输入关键字"},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.exportData}},[e._v("导出财务数据")])],1),a("el-form-item",[a("el-upload",{attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{slot:"trigger",icon:"el-icon-upload",type:"primary"},slot:"trigger"},[e._v("导入")])],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-user-solid"},on:{click:e.employeeSalaryRecord}},[e._v("员工薪资状态记录")])],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.exportHRData}},[e._v("导出人事数据")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticClass:"my-table",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{label:"上月数据",width:"100px",align:"center",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[l.id.includes("_lastMonth")?e._e():a("input",{staticClass:"choose",attrs:{id:"choose",type:"checkbox"},on:{input:function(t){return e.sarechLastMonthData(l)}}}),l.id.includes("_lastMonth")?a("span",[e._v(e._s(e.lastMonthData.salaryMonth))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"证件号",sortable:"custom","min-width":e.getStringWidth("证件号"),prop:"Employee.IdentityNumber"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"identityNumber")},[e._v(e._s(l.identityNumber))])]}}])}),a("el-table-column",{attrs:{label:"手机号",sortable:"custom","min-width":e.getStringWidth("手机号"),prop:"Employee.Mobile"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"mobile")},[e._v(e._s(l.mobile))])]}}])}),a("el-table-column",{attrs:{label:"在职方式",sortable:"custom","min-width":e.getStringWidth("在职方式"),prop:"Employee.EmployeeHR.HireStyle.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"hireStyleName")},[e._v(e._s(l.hireStyleName))])]}}])}),a("el-table-column",{attrs:{label:"受雇年份",sortable:"custom","min-width":e.getStringWidth("受雇年份"),prop:"EmployYear"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employYear")},[e._v(e._s(l.employYear))])]}}])}),a("el-table-column",{attrs:{label:"受雇月份",sortable:"custom","min-width":e.getStringWidth("受雇月份"),prop:"EmployMonth"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employMonth")},[e._v(e._s(l.employMonth))])]}}])}),a("el-table-column",{attrs:{label:"本年度受雇月数",sortable:"custom","min-width":e.getStringWidth("本年度受雇月数"),prop:"EmployMonthNumber"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employMonthNumber")},[e._v(e._s(l.employMonthNumber))])]}}])}),a("el-table-column",{attrs:{label:"备注1",sortable:"custom","min-width":e.getStringWidth("备注1"),prop:"Memo1"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"memo1")},[e._v(e._s(l.memo1))])]}}])}),a("el-table-column",{attrs:{label:"备注2",sortable:"custom","min-width":e.getStringWidth("备注2"),prop:"Memo2"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"memo2")},[e._v(e._s(l.memo2))])]}}])}),a("el-table-column",{attrs:{label:"备注3",sortable:"custom","min-width":e.getStringWidth("备注3"),prop:"Memo3"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"memo3")},[e._v(e._s(l.memo3))])]}}])}),a("el-table-column",{attrs:{label:"四金不够扣标记",sortable:"custom","min-width":e.getStringWidth("四金不够扣标记"),align:"center",prop:"FourGoldDeficiencySign"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"fourGoldDeficiencySign")},[e._v(e._s(l.fourGoldDeficiencySign?"有":"无"))])]}}])}),a("el-table-column",{attrs:{label:"社保基数",sortable:"custom","min-width":e.getStringWidth("社保基数"),"header-align":"left",align:"right",prop:"SocialSecurityBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"socialSecurityBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.socialSecurityBase)))])]}}])}),a("el-table-column",{attrs:{label:"公积金基数",sortable:"custom","min-width":e.getStringWidth("公积金基数"),"header-align":"left",align:"right",prop:"HousingFundBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"housingFundBase")},[e._v(e._s(parseFloat(l.housingFundBase).toFixed(0)))])]}}])}),a("el-table-column",{attrs:{label:"岗位级别",sortable:"custom","min-width":e.getStringWidth("岗位级别"),align:"center",prop:"Station.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationName")},[e._v(e._s(l.stationName))])]}}])}),a("el-table-column",{attrs:{label:"薪级",sortable:"custom","min-width":e.getStringWidth("薪级"),align:"center",prop:"SalaryScale.scale"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"scale")},[e._v(e._s(l.scale))])]}}])}),a("el-table-column",{attrs:{label:"工龄",sortable:"custom","min-width":e.getStringWidth("工龄"),align:"center",prop:"SocietyAge"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"societyAge")},[e._v(e._s(l.societyAge))])]}}])}),a("el-table-column",{attrs:{label:"工龄段",sortable:"custom","min-width":e.getStringWidth("工龄段"),align:"center",prop:"SeniorityRangeId"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"seniorityRangeName")},[e._v(e._s(l.seniorityRangeName))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金标记",sortable:"custom","min-width":e.getStringWidth("补充公积金标记"),align:"center",prop:"SupplementaryHousingFundFlag"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFundFlag")},[e._v(e._s(l.supplementaryHousingFundFlag?"有":"无"))])]}}])}),a("el-table-column",{attrs:{label:"职员年金标记",sortable:"custom","min-width":e.getStringWidth("职员年金标记"),align:"center",prop:"EmployeePensionFlag"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employeePensionFlag")},[e._v(e._s(l.employeePensionFlag?"有":"无"))])]}}])}),a("el-table-column",{attrs:{label:"病产假天数",sortable:"custom","min-width":e.getStringWidth("病产假天数"),align:"center",prop:"SickLeaveDays"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"sickLeaveDays")},[e._v(e._s(e.getDecimalValueOrDefault(l.sickLeaveDays)))])]}}])}),a("el-table-column",{attrs:{label:"休假类型",sortable:"custom","min-width":"300px",align:"center",prop:"SalaryLeaveDesc"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"salaryLeaveDesc")},[e._v(e._s(l.salaryLeaveDesc))])]}}])}),a("el-table-column",{attrs:{label:"基本工资小计",sortable:"custom","min-width":e.getStringWidth("基本工资小计"),"header-align":"left",align:"right",prop:"BasicSalarySubtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicSalarySubtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicSalarySubtotal)))])]}}])}),a("el-table-column",{attrs:{label:"岗资基数",sortable:"custom","min-width":e.getStringWidth("岗资基数"),"header-align":"left",align:"right",prop:"StationWageBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationWageBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationWageBase)))])]}}])}),a("el-table-column",{attrs:{label:"岗资",sortable:"custom","min-width":e.getStringWidth("岗资"),"header-align":"left",align:"right",prop:"StationWage"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationWage")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationWage)))])]}}])}),a("el-table-column",{attrs:{label:"薪资基数",sortable:"custom","min-width":e.getStringWidth("薪资基数"),"header-align":"left",align:"right",prop:"SalaryBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"salaryBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.salaryBase)))])]}}])}),a("el-table-column",{attrs:{label:"薪资",sortable:"custom","min-width":e.getStringWidth("薪资"),"header-align":"left",align:"right",prop:"SalaryMoney"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"salaryMoney")},[e._v(e._s(e.getDecimalValueOrDefault(l.salaryMoney)))])]}}])}),a("el-table-column",{attrs:{label:"基本工资其它加",sortable:"custom","min-width":e.getStringWidth("基本工资其它加"),"header-align":"left",align:"right",prop:"BasicSalaryOtherAdd"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicSalaryOtherAdd")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicSalaryOtherAdd)))])]}}])}),a("el-table-column",{attrs:{label:"津补贴小计",sortable:"custom","min-width":e.getStringWidth("津补贴小计"),"header-align":"left",align:"right",prop:"AllowanceSubtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"allowanceSubtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.allowanceSubtotal)))])]}}])}),a("el-table-column",{attrs:{label:"粮油补贴",sortable:"custom","min-width":e.getStringWidth("粮油补贴"),"header-align":"left",align:"right",prop:"GrainOilSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"grainOilSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.grainOilSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"上下班交通费基数",sortable:"custom","min-width":e.getStringWidth("上下班交通费基数"),"header-align":"left",align:"right",prop:"CommuteSubsidyBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"commuteSubsidyBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.commuteSubsidyBase)))])]}}])}),a("el-table-column",{attrs:{label:"上下班交通费",sortable:"custom","min-width":e.getStringWidth("上下班交通费"),"header-align":"left",align:"right",prop:"CommuteSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"commuteSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.commuteSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"护龄基数",sortable:"custom","min-width":e.getStringWidth("护龄基数"),"header-align":"left",align:"right",prop:"NursingBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"nursingBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.nursingBase)))])]}}])}),a("el-table-column",{attrs:{label:"护龄",sortable:"custom","min-width":e.getStringWidth("护龄"),align:"center",prop:"Nursing"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"nursing")},[e._v(e._s(e.getDecimalValueOrDefault(l.nursing)))])]}}])}),a("el-table-column",{attrs:{label:"独子",sortable:"custom","min-width":e.getStringWidth("独子"),align:"center",prop:"OnlyChild"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"onlyChild")},[e._v(e._s(e.getDecimalValueOrDefault(l.onlyChild)))])]}}])}),a("el-table-column",{attrs:{label:"独子补发",sortable:"custom","min-width":e.getStringWidth("独子补发"),"header-align":"left",align:"right",prop:"BackPayOnlyChild"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backPayOnlyChild")},[e._v(e._s(e.getDecimalValueOrDefault(l.backPayOnlyChild)))])]}}])}),a("el-table-column",{attrs:{label:"独子合计",sortable:"custom","min-width":e.getStringWidth("独子合计"),"header-align":"left",align:"right",prop:"OnlyChildTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"onlyChildTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.onlyChildTotal)))])]}}])}),a("el-table-column",{attrs:{label:"援外津贴",sortable:"custom","min-width":e.getStringWidth("援外津贴"),"header-align":"left",align:"right",prop:"ForeignAidAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"foreignAidAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.foreignAidAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"津补贴其它加",sortable:"custom","min-width":e.getStringWidth("津补贴其它加"),"header-align":"left",align:"right",prop:"AllowanceOtherAdd"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"allowanceOtherAdd")},[e._v(e._s(e.getDecimalValueOrDefault(l.allowanceOtherAdd)))])]}}])}),a("el-table-column",{attrs:{label:"绩效小计",sortable:"custom","min-width":e.getStringWidth("绩效小计"),"header-align":"left",align:"right",prop:"PerformanceSubtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"performanceSubtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.performanceSubtotal)))])]}}])}),a("el-table-column",{attrs:{label:"岗位津贴基数",sortable:"custom","min-width":e.getStringWidth("岗位津贴基数"),"header-align":"left",align:"right",prop:"StationAllowanceBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationAllowanceBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationAllowanceBase)))])]}}])}),a("el-table-column",{attrs:{label:"岗位津贴",sortable:"custom","min-width":e.getStringWidth("岗位津贴"),"header-align":"left",align:"right",prop:"StationAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"工作量津贴",sortable:"custom","min-width":e.getStringWidth("工作量津贴"),"header-align":"left",align:"right",prop:"WorkloadAllowance1"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"workloadAllowance1")},[e._v(e._s(e.getDecimalValueOrDefault(l.workloadAllowance1)))])]}}])}),a("el-table-column",{attrs:{label:"工作量津贴合计",sortable:"custom","min-width":e.getStringWidth("工作量津贴合计"),"header-align":"left",align:"right",prop:"WorkloadAllowance2Subtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"workloadAllowance2Subtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.workloadAllowance2Subtotal)))])]}}])}),a("el-table-column",{attrs:{label:"停车补贴",sortable:"custom","min-width":e.getStringWidth("停车补贴"),"header-align":"left",align:"right",prop:"ParkingSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"parkingSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.parkingSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"公派车贴补贴",sortable:"custom","min-width":e.getStringWidth("公派车贴补贴"),"header-align":"left",align:"right",prop:"OfficialCarAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"officialCarAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.officialCarAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"电话费",sortable:"custom","min-width":e.getStringWidth("电话费"),"header-align":"left",align:"right",prop:"TelephoneFee"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"telephoneFee")},[e._v(e._s(e.getDecimalValueOrDefault(l.telephoneFee)))])]}}])}),a("el-table-column",{attrs:{label:"电话费合计",sortable:"custom","min-width":e.getStringWidth("电话费合计"),"header-align":"left",align:"right",prop:"TelephoneFeeTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"telephoneFeeTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.telephoneFeeTotal)))])]}}])}),a("el-table-column",{attrs:{label:"长病假职工最低工资补助",sortable:"custom","min-width":e.getStringWidth("长病假职工最低工资补助"),"header-align":"left",align:"right",prop:"LongSickLeaveMinimumWageSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"longSickLeaveMinimumWageSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.longSickLeaveMinimumWageSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"博士后房帖",sortable:"custom","min-width":e.getStringWidth("博士后房帖"),"header-align":"left",align:"right",prop:"PostdoctoralHousingAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"postdoctoralHousingAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.postdoctoralHousingAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"卫生津贴",sortable:"custom","min-width":e.getStringWidth("卫生津贴"),"header-align":"left",align:"right",prop:"HealthAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"healthAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.healthAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"零星补节假日加班绩效",sortable:"custom","min-width":e.getStringWidth("零星补节假日加班绩效"),"header-align":"left",align:"right",prop:"OccasionalHolidayOvertimePerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occasionalHolidayOvertimePerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.occasionalHolidayOvertimePerformance)))])]}}])}),a("el-table-column",{attrs:{label:"节假日加班绩效",sortable:"custom","min-width":e.getStringWidth("节假日加班绩效"),"header-align":"left",align:"right",prop:"HolidayOvertimePerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"holidayOvertimePerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.holidayOvertimePerformance)))])]}}])}),a("el-table-column",{attrs:{label:"中夜班绩效",sortable:"custom","min-width":e.getStringWidth("中夜班绩效"),"header-align":"left",align:"right",prop:"MidnightShiftPerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"midnightShiftPerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.midnightShiftPerformance)))])]}}])}),a("el-table-column",{attrs:{label:"行政值班绩效",sortable:"custom","min-width":e.getStringWidth("行政值班绩效"),"header-align":"left",align:"right",prop:"AdministrativeDutyPerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"administrativeDutyPerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.administrativeDutyPerformance)))])]}}])}),a("el-table-column",{attrs:{label:"一二值班绩效",sortable:"custom","min-width":e.getStringWidth("一二值班绩效"),"header-align":"left",align:"right",prop:"FirstSecondDutyPerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"firstSecondDutyPerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.firstSecondDutyPerformance)))])]}}])}),a("el-table-column",{attrs:{label:"其它加",sortable:"custom","min-width":e.getStringWidth("其它加"),"header-align":"left",align:"right",prop:"OtherAdd"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"otherAdd")},[e._v(e._s(e.getDecimalValueOrDefault(l.otherAdd)))])]}}])}),a("el-table-column",{attrs:{label:"行政聘任补发",sortable:"custom","min-width":e.getStringWidth("行政聘任补发"),"header-align":"left",align:"right",prop:"WorkChangeReissue"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"workChangeReissue")},[e._v(e._s(e.getDecimalValueOrDefault(l.workChangeReissue)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣养扣",sortable:"custom","min-width":e.getStringWidth("退还多扣养扣"),"header-align":"left",align:"right",prop:"ReturnOverDeductPension"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductPension")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductPension)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣医疗",sortable:"custom","min-width":e.getStringWidth("退还多扣医疗"),"header-align":"left",align:"right",prop:"ReturnOverDeductMedical"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductMedical")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductMedical)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣失业",sortable:"custom","min-width":e.getStringWidth("退还多扣失业"),"header-align":"left",align:"right",prop:"ReturnOverDeductUnemployment"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductUnemployment")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductUnemployment)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣公扣",sortable:"custom","min-width":e.getStringWidth("退还多扣公扣"),"header-align":"left",align:"right",prop:"ReturnOverDeductPublic"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductPublic")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductPublic)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣补充公积金",sortable:"custom","min-width":e.getStringWidth("退还多扣补充公积金"),"header-align":"left",align:"right",prop:"ReturnOverDeductSupplementaryHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductSupplementaryHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductSupplementaryHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣年金",sortable:"custom","min-width":e.getStringWidth("退还多扣年金"),"header-align":"left",align:"right",prop:"ReturnOverDeductOccupationalAnnuity"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductOccupationalAnnuity")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductOccupationalAnnuity)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣出国人员扣款",sortable:"custom","min-width":e.getStringWidth("退还多扣出国人员扣款"),"header-align":"left",align:"right",prop:"ReturnOverDeductDeductionForForeigners"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductDeductionForForeigners")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductDeductionForForeigners)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣病假工资",sortable:"custom","min-width":e.getStringWidth("退还多扣病假工资"),"header-align":"left",align:"right",prop:"ReturnOverDeductSickLeaveSalary"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductSickLeaveSalary")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductSickLeaveSalary)))])]}}])}),a("el-table-column",{attrs:{label:"定级补发",sortable:"custom","min-width":e.getStringWidth("定级补发"),"header-align":"left",align:"right",prop:"BackPayGrading"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backPayGrading")},[e._v(e._s(e.getDecimalValueOrDefault(l.backPayGrading)))])]}}])}),a("el-table-column",{attrs:{label:"应发工资",sortable:"custom","min-width":e.getStringWidth("应发工资"),"header-align":"left",align:"right",prop:"GrossSalary"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"grossSalary")},[e._v(e._s(e.getDecimalValueOrDefault(l.grossSalary)))])]}}])}),a("el-table-column",{attrs:{label:"应发工资（申报个税用）",sortable:"custom","min-width":e.getStringWidth("应发工资（申报个税用）"),"header-align":"left",align:"right",prop:"GrossSalaryForTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"grossSalaryForTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.grossSalaryForTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"养扣",sortable:"custom","min-width":e.getStringWidth("养扣"),"header-align":"left",align:"right",prop:"PensionDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"补扣养扣",sortable:"custom","min-width":e.getStringWidth("补扣养扣"),"header-align":"left",align:"right",prop:"BackDeductionPension"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionPension")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionPension)))])]}}])}),a("el-table-column",{attrs:{label:"养扣合计",sortable:"custom","min-width":e.getStringWidth("养扣合计"),"header-align":"left",align:"right",prop:"PensionDeductionTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionDeductionTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionDeductionTotal)))])]}}])}),a("el-table-column",{attrs:{label:"医疗保险",sortable:"custom","min-width":e.getStringWidth("医疗保险"),"header-align":"left",align:"right",prop:"MedicalInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"补扣医疗保险",sortable:"custom","min-width":e.getStringWidth("补扣医疗保险"),"header-align":"left",align:"right",prop:"BackDeductionMedicalInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionMedicalInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionMedicalInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"医疗保险合计",sortable:"custom","min-width":e.getStringWidth("医疗保险合计"),"header-align":"left",align:"right",prop:"MedicalInsuranceTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalInsuranceTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalInsuranceTotal)))])]}}])}),a("el-table-column",{attrs:{label:"失业保险",sortable:"custom","min-width":e.getStringWidth("失业保险"),"header-align":"left",align:"right",prop:"UnemploymentInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"补扣失业保险",sortable:"custom","min-width":e.getStringWidth("补扣失业保险"),"header-align":"left",align:"right",prop:"BackDeductionUnemploymentInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionUnemploymentInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionUnemploymentInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"失业保险合计",sortable:"custom","min-width":e.getStringWidth("失业保险合计"),"header-align":"left",align:"right",prop:"UnemploymentInsuranceTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentInsuranceTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentInsuranceTotal)))])]}}])}),a("el-table-column",{attrs:{label:"基本公积金",sortable:"custom","min-width":e.getStringWidth("基本公积金"),"header-align":"left",align:"right",prop:"BasicHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"补扣基本公积金",sortable:"custom","min-width":e.getStringWidth("补扣基本公积金"),"header-align":"left",align:"right",prop:"BackDeductionBasicHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionBasicHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionBasicHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"基本公积金合计",sortable:"custom","min-width":e.getStringWidth("基本公积金合计"),"header-align":"left",align:"right",prop:"HousingFundTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"housingFundTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.housingFundTotal)))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金",sortable:"custom","min-width":e.getStringWidth("补充公积金"),"header-align":"left",align:"right",prop:"SupplementaryHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFund")},[e._v(e._s(parseFloat(l.supplementaryHousingFund).toFixed(0)))])]}}])}),a("el-table-column",{attrs:{label:"补扣补充公积金",sortable:"custom","min-width":e.getStringWidth("补扣补充公积金"),"header-align":"left",align:"right",prop:"BackDeductionSupplementaryHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionSupplementaryHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionSupplementaryHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金合计",sortable:"custom","min-width":e.getStringWidth("补充公积金合计"),"header-align":"left",align:"right",prop:"SupplementaryHousingFundTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFundTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.supplementaryHousingFundTotal)))])]}}])}),a("el-table-column",{attrs:{label:"职业年金",sortable:"custom","min-width":e.getStringWidth("职业年金"),"header-align":"left",align:"right",prop:"OccupationalAnnuity"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occupationalAnnuity")},[e._v(e._s(e.getDecimalValueOrDefault(l.occupationalAnnuity)))])]}}])}),a("el-table-column",{attrs:{label:"补扣职业年金",sortable:"custom","min-width":e.getStringWidth("补扣职业年金"),"header-align":"left",align:"right",prop:"BackDeductionOccupationalAnnuity"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionOccupationalAnnuity")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionOccupationalAnnuity)))])]}}])}),a("el-table-column",{attrs:{label:"职业年金合计",sortable:"custom","min-width":e.getStringWidth("职业年金合计"),"header-align":"left",align:"right",prop:"OccupationalAnnuityTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occupationalAnnuityTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.occupationalAnnuityTotal)))])]}}])}),a("el-table-column",{attrs:{label:"会费",sortable:"custom","min-width":e.getStringWidth("会费"),"header-align":"left",align:"right",prop:"MembershipFee"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"membershipFee")},[e._v(e._s(parseFloat(l.membershipFee).toFixed(1)))])]}}])}),a("el-table-column",{attrs:{label:"补扣会费",sortable:"custom","min-width":e.getStringWidth("补扣会费"),"header-align":"left",align:"right",prop:"BackDeductionMembershipFee"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionMembershipFee")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionMembershipFee)))])]}}])}),a("el-table-column",{attrs:{label:"会费合计",sortable:"custom","min-width":e.getStringWidth("会费合计"),"header-align":"left",align:"right",prop:"MembershipFeeTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"membershipFeeTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.membershipFeeTotal)))])]}}])}),a("el-table-column",{attrs:{label:"房租",sortable:"custom","min-width":e.getStringWidth("房租"),"header-align":"left",align:"right",prop:"Rent"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"rent")},[e._v(e._s(e.getDecimalValueOrDefault(l.rent)))])]}}])}),a("el-table-column",{attrs:{label:"代扣税金",sortable:"custom","min-width":e.getStringWidth("代扣税金"),"header-align":"left",align:"right",prop:"TaxWithholding"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"taxWithholding")},[e._v(e._s(e.getDecimalValueOrDefault(l.taxWithholding)))])]}}])}),a("el-table-column",{attrs:{label:"其它扣",sortable:"custom","min-width":e.getStringWidth("其它扣"),"header-align":"left",align:"right",prop:"OtherDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"otherDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.otherDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"长病假补扣",sortable:"custom","min-width":e.getStringWidth("长病假补扣"),"header-align":"left",align:"right",prop:"BackDeductionLongSickLeave"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionLongSickLeave")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionLongSickLeave)))])]}}])}),a("el-table-column",{attrs:{label:"扣出国人员扣款",sortable:"custom","min-width":e.getStringWidth("扣出国人员扣款"),"header-align":"left",align:"right",prop:"DeductionForForeigners"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"deductionForForeigners")},[e._v(e._s(e.getDecimalValueOrDefault(l.deductionForForeigners)))])]}}])}),a("el-table-column",{attrs:{label:"扣款合计",sortable:"custom","min-width":e.getStringWidth("扣款合计"),"header-align":"left",align:"right",prop:"DeductionTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"deductionTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.deductionTotal)))])]}}])}),a("el-table-column",{attrs:{label:"实发工资",sortable:"custom","min-width":e.getStringWidth("实发工资"),"header-align":"left",align:"right",prop:"NetSalary"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"netSalary")},[e._v(e._s(e.getDecimalValueOrDefault(l.netSalary)))])]}}])}),a("el-table-column",{attrs:{label:"养扣基础数据",sortable:"custom","min-width":e.getStringWidth("养扣基础数据"),"header-align":"left",align:"right",prop:"BasicDataDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicDataDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicDataDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"医疗保险基础数据",sortable:"custom","min-width":e.getStringWidth("医疗保险基础数据"),"header-align":"left",align:"right",prop:"MedicalInsuranceBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalInsuranceBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalInsuranceBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"失业保险基础数据",sortable:"custom","min-width":e.getStringWidth("失业保险基础数据"),"header-align":"left",align:"right",prop:"UnemploymentInsuranceBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentInsuranceBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentInsuranceBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"公积金基础数据",sortable:"custom","min-width":e.getStringWidth("公积金基础数据"),"header-align":"left",align:"right",prop:"HousingFundBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"housingFundBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.housingFundBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金基础数据",sortable:"custom","min-width":e.getStringWidth("补充公积金基础数据"),"header-align":"left",align:"right",prop:"SupplementaryHousingFundBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFundBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.supplementaryHousingFundBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"年金基础数据",sortable:"custom","min-width":e.getStringWidth("年金基础数据"),"header-align":"left",align:"right",prop:"PensionBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"养老（申报个税用）",sortable:"custom","min-width":e.getStringWidth("养老（申报个税用）"),"header-align":"left",align:"right",prop:"PensionTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"医疗（申报个税用）",sortable:"custom","min-width":e.getStringWidth("医疗（申报个税用）"),"header-align":"left",align:"right",prop:"MedicalTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"失业（申报个税用）",sortable:"custom","min-width":e.getStringWidth("失业（申报个税用）"),"header-align":"left",align:"right",prop:"UnemploymentTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"公扣（申报个税用）",sortable:"custom","min-width":e.getStringWidth("公扣（申报个税用）"),"header-align":"left",align:"right",prop:"PublicTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"publicTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.publicTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"年金（申报个税用）",sortable:"custom","min-width":e.getStringWidth("年金（申报个税用）"),"header-align":"left",align:"right",prop:"OccupationalAnnuityTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occupationalAnnuityTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.occupationalAnnuityTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"申报个税四金合计",sortable:"custom","min-width":e.getStringWidth("申报个税四金合计"),"header-align":"left",align:"right",prop:"TaxDeclarationFourGold"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"taxDeclarationFourGold")},[e._v(e._s(e.getDecimalValueOrDefault(l.taxDeclarationFourGold)))])]}}])}),a("el-table-column",{attrs:{label:"基本工资合计",sortable:"custom","min-width":e.getStringWidth("基本工资合计"),"header-align":"left",align:"right",prop:"BasicSalaryTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicSalaryTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicSalaryTotal)))])]}}])}),a("el-table-column",{attrs:{label:"津贴补贴合计",sortable:"custom","min-width":e.getStringWidth("津贴补贴合计"),"header-align":"left",align:"right",prop:"AllowanceTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"allowanceTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.allowanceTotal)))])]}}])}),a("el-table-column",{attrs:{label:"对个人及家庭补助",sortable:"custom","min-width":e.getStringWidth("对个人及家庭补助"),"header-align":"left",align:"right",prop:"OnlyChildTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"onlyChildTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.onlyChildTotal)))])]}}])}),a("el-table-column",{attrs:{label:"奖金合计",sortable:"custom","min-width":e.getStringWidth("奖金合计"),"header-align":"left",align:"right",prop:"BonusTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"bonusTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.bonusTotal)))])]}}])}),a("el-table-column",{attrs:{label:"年终一次性奖励",sortable:"custom","min-width":e.getStringWidth("年终一次性奖励"),"header-align":"left",align:"right",prop:"YearEndBonus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"yearEndBonus")},[e._v(e._s(e.getDecimalValueOrDefault(l.yearEndBonus)))])]}}])}),a("el-table-column",{attrs:{label:"其他各项奖金",sortable:"custom","min-width":e.getStringWidth("其他各项奖金"),"header-align":"left",align:"right",prop:"OtherBonusesTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"otherBonusesTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.otherBonusesTotal)))])]}}])}),a("el-table-column",{attrs:{label:"实发奖金",sortable:"custom","min-width":e.getStringWidth("实发奖金"),"header-align":"left",align:"right",prop:"ActualBonus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"actualBonus")},[e._v(e._s(e.getDecimalValueOrDefault(l.actualBonus)))])]}}])}),a("el-table-column",{attrs:{label:"税前奖金",sortable:"custom","min-width":e.getStringWidth("税前奖金"),"header-align":"left",align:"right",prop:"PreTaxBonus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"preTaxBonus")},[e._v(e._s(e.getDecimalValueOrDefault(l.preTaxBonus)))])]}}])}),a("el-table-column",{attrs:{label:"奖金扣税",sortable:"custom","min-width":e.getStringWidth("奖金扣税"),"header-align":"left",align:"right",prop:"BonusTax"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"bonusTax")},[e._v(e._s(e.getDecimalValueOrDefault(l.bonusTax)))])]}}])}),a("el-table-column",{attrs:{label:"累计收入额",sortable:"custom","min-width":e.getStringWidth("累计收入额"),"header-align":"left",align:"right",prop:"AccumulatedIncome"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedIncome")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedIncome)))])]}}])}),a("el-table-column",{attrs:{label:"累计专项扣除",sortable:"custom","min-width":e.getStringWidth("累计专项扣除"),"header-align":"left",align:"right",prop:"AccumulatedDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"累计专项附加扣除",sortable:"custom","min-width":e.getStringWidth("累计专项附加扣除"),"header-align":"left",align:"right",prop:"AccumulatedAdditionalDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedAdditionalDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedAdditionalDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"累计应纳税所得额",sortable:"custom","min-width":e.getStringWidth("累计应纳税所得额"),"header-align":"left",align:"right",prop:"AccumulatedTaxableIncome"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedTaxableIncome")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedTaxableIncome)))])]}}])}),a("el-table-column",{attrs:{label:"累计已预扣税额",sortable:"custom","min-width":e.getStringWidth("累计已预扣税额"),"header-align":"left",align:"right",prop:"AccumulatedWithheldTax"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedWithheldTax")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedWithheldTax)))])]}}])}),a("el-table-column",{attrs:{label:"计税",sortable:"custom","min-width":e.getStringWidth("计税"),"header-align":"left",align:"right",prop:"TaxCalculation"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"taxCalculation")},[e._v(e._s(l.taxCalculation))])]}}])}),a("el-table-column",{attrs:{label:"应发总计",sortable:"custom","min-width":e.getStringWidth("应发总计"),"header-align":"left",align:"right",prop:"TotalPayable"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"totalPayable")},[e._v(e._s(e.getDecimalValueOrDefault(l.totalPayable)))])]}}])}),a("el-table-column",{attrs:{label:"工行帐号",sortable:"custom","min-width":e.getStringWidth("工行帐号"),align:"left",prop:"ICBCAccount"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"iCBCAccount")},[e._v(e._s(l.iCBCAccount))])]}}])}),a("el-table-column",{attrs:{fixed:"right",label:"操作",align:"left","header-align":"center",width:"150","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[e.isEdit?a("el-button",{staticStyle:{"margin-left":"35px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.editEmployeeSalaryDetailDialog(l,!0)}}},[e._v(" 编辑 ")]):e._e(),e.isEdit?e._e():a("el-button",{staticStyle:{"margin-left":"35px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-view",size:"mini"},on:{click:function(t){return e.editEmployeeSalaryDetailDialog(l,!1)}}},[e._v(" 显示 ")])]}}])}),a("employeeTableColumns")],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),a("editDialog",{ref:"editDialog",on:{refreshData:e.getPageList}}),a("employeeSalaryRecordDialog",{ref:"employeeSalaryRecordDialog"}),a("socialSecurityWithhold",{ref:"socialSecurityWithhold"})],1)}),o=[],s=(a("7db0"),a("c740"),a("4160"),a("caad"),a("c975"),a("a434"),a("b680"),a("d3b7"),a("ac1f"),a("2532"),a("841c"),a("1276"),a("159b"),a("5530")),r=a("2efc"),u=a("f9ac"),c=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{title:e.title,visible:e.showDialog,width:"90%",top:"10vh","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:e.closeDialog}},[a("el-form",{ref:"dataForm",staticClass:"el-dialogform",attrs:{rules:e.rules,model:e.dataModel,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"唯一码"}},[a("span",[e._v(e._s(e.dataModel.empUid))])])],1),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"工号"}},[a("span",[e._v(e._s(e.dataModel.empCode))])])],1),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"员工姓名"}},[a("span",[e._v(e._s(e.dataModel.empName))])])],1),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"应发总计"}},[a("span",[e._v(e._s(e.dataModel.totalPayable))])])],1)],1),a("el-menu",{staticClass:"el-menu-demo",attrs:{"default-active":e.activeIndex,mode:"horizontal"},on:{select:e.handleSelect}},[a("el-menu-item",{attrs:{index:"0"}},[e._v("人事数据")]),a("el-menu-item",{attrs:{index:"1"}},[e._v("基础信息")]),a("el-menu-item",{attrs:{index:"2"}},[e._v("工资清单")]),a("el-menu-item",{attrs:{index:"3"}},[e._v("调整")])],1),"0"===e.activeIndex?a("div",{staticStyle:{"margin-top":"20px"},attrs:{title:"人事数据",name:"0"}},[a("el-card",[e.showDialog?a("hrSalary",{attrs:{"employee-id":e.employeeId,"finance-salary-id":e.salaryId}}):e._e()],1)],1):e._e(),"1"===e.activeIndex?a("div",{staticStyle:{"margin-top":"20px"},attrs:{title:"基础信息",name:"1"}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"证件号","label-width":"170px"}},[e._v(" "+e._s(e.dataModel.identityNumber)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"手机号","label-width":"170px"}},[e._v(" "+e._s(e.dataModel.mobile)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"在职方式","label-width":"170px"}},[e._v(" "+e._s(e.dataModel.hireStyleName)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"合同签订方","label-width":"170px"}},[e._v(" "+e._s(e.dataModel.contractNo)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"受雇时间",prop:"employTime","label-width":"170px"}},[a("el-date-picker",{staticStyle:{width:"70%"},attrs:{type:"date",placeholder:"日期","value-format":"yyyy-MM-dd",disabled:e.isPageEdit},model:{value:e.dataModel.employTime,callback:function(t){e.$set(e.dataModel,"employTime",t)},expression:"dataModel.employTime"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"受雇年份",prop:"employTime","label-width":"170px"}},[e._v(" "+e._s(e.getYear(e.dataModel.employTime))+"年 ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"受雇月份",prop:"employTime","label-width":"170px"}},[e._v(" "+e._s(e.getMonth(e.dataModel.employTime))+"月 ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"本年度受雇月数",prop:"employMonthNumber","label-width":"170px"}},[[e._v(" "+e._s(e.dataModel.employMonthNumber)+" ")]],2)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"编制",prop:"staffing","label-width":"170px"}},[a("el-switch",{attrs:{disabled:e.isPageEdit,"active-color":"#13ce66"},model:{value:e.dataModel.staffing,callback:function(t){e.$set(e.dataModel,"staffing",t)},expression:"dataModel.staffing"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"四金不够扣标记",prop:"fourGoldDeficiencySign","label-width":"170px"}},[a("el-switch",{attrs:{disabled:e.isPageEdit,"active-color":"#13ce66"},model:{value:e.dataModel.fourGoldDeficiencySign,callback:function(t){e.$set(e.dataModel,"fourGoldDeficiencySign",t)},expression:"dataModel.fourGoldDeficiencySign"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"岗位级别",prop:"stationId","label-width":"170px"}},[a("el-cascader",{staticStyle:{width:"70%"},attrs:{disabled:e.isPageEdit,"show-all-levels":!1,options:e.stationTrees,placeholder:"岗位级别",props:{expandTrigger:"hover",value:"id",label:"name",emitPath:!1}},on:{change:e.stationTreeChange},model:{value:e.dataModel.stationId,callback:function(t){e.$set(e.dataModel,"stationId",t)},expression:"dataModel.stationId"}})],1)],1),1===e.dataModel.enumSalaryType?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"薪级",prop:"salaryScaleId"}},[a("el-select",{staticStyle:{width:"70%"},attrs:{placeholder:"薪级",disabled:e.isPageEdit},on:{change:e.salaryScaleChange},model:{value:e.dataModel.salaryScaleId,callback:function(t){e.$set(e.dataModel,"salaryScaleId",t)},expression:"dataModel.salaryScaleId"}},e._l(e.salaryScales,(function(e){return a("el-option",{key:e.id,attrs:{label:e.scale,value:e.id}})})),1)],1)],1):e._e(),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"工龄",prop:"societyAge","label-width":"170px"}},[a("el-input",{staticStyle:{width:"70%"},attrs:{type:"number",disabled:e.isPageEdit},model:{value:e.dataModel.societyAge,callback:function(t){e.$set(e.dataModel,"societyAge",t)},expression:"dataModel.societyAge"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"工龄段",prop:"seniorityRangeId","label-width":"170px"}},[a("el-select",{staticStyle:{width:"70%"},attrs:{placeholder:"工龄段",disabled:e.isPageEdit},model:{value:e.dataModel.seniorityRangeId,callback:function(t){e.$set(e.dataModel,"seniorityRangeId",t)},expression:"dataModel.seniorityRangeId"}},e._l(e.partySocietyAgeList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"补充公积金标记",prop:"supplementaryHousingFundFlag","label-width":"170px"}},[a("el-switch",{attrs:{disabled:e.isPageEdit,"active-color":"#13ce66"},model:{value:e.dataModel.supplementaryHousingFundFlag,callback:function(t){e.$set(e.dataModel,"supplementaryHousingFundFlag",t)},expression:"dataModel.supplementaryHousingFundFlag"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"职员年金标记",prop:"employeePensionFlag","label-width":"170px"}},[a("el-switch",{attrs:{disabled:e.isPageEdit,"active-color":"#13ce66"},model:{value:e.dataModel.employeePensionFlag,callback:function(t){e.$set(e.dataModel,"employeePensionFlag",t)},expression:"dataModel.employeePensionFlag"}})],1)],1)],1):e._e(),"2"===e.activeIndex?a("div",{staticStyle:{"margin-top":"20px"},attrs:{title:"工资清单",name:"2"}},[e.isNoSalary?e._e():a("el-row",[a("el-col",{attrs:{span:6}},[a("el-row",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"应发工资","label-width":"170px"}},[e._v(" "+e._s(e.dataModel.grossSalary)+" ")])],1),a("el-row",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"实发工资","label-width":"170px"}},[e._v(" "+e._s(e.dataModel.computeTakeHomePay)+" ")])],1)],1),a("el-col",{attrs:{span:18}},[a("el-row",[a("el-col",{attrs:{span:21,offset:2}},[a("el-table",{staticStyle:{"font-size":"14px",width:"100%"},attrs:{data:e.salaryDataTable,"row-key":"id","tree-props":{children:"children",hasChildren:"hasChildren"}}},[a("el-table-column",{attrs:{prop:"text",label:"薪资项","min-width":"180"}}),a("el-table-column",{attrs:{prop:"base",label:"基数","min-width":"180"}}),a("el-table-column",{attrs:{prop:"backPay",label:"补发","min-width":"180"}}),a("el-table-column",{attrs:{prop:"backDeduction",label:"补扣","min-width":"180"}}),a("el-table-column",{attrs:{prop:"netSalary",label:"合计","min-width":"180"}})],1)],1)],1)],1)],1),e.isNoSalary?a("el-row",[a("el-col",{attrs:{span:6}},[a("el-row",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"工资状态","label-width":"170px"}},[e._v(" "+e._s(e.isNoSalaryText)+" ")])],1),a("el-row",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"应发工资","label-width":"170px"}},[e._v(" 0 ")])],1),a("el-row",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"实发工资","label-width":"170px"}},[e._v(" 0 ")])],1)],1),a("el-col",{attrs:{span:18}},[a("el-row",[a("el-col",{attrs:{span:21,offset:2}},[a("el-table",{staticStyle:{"font-size":"14px",width:"100%"},attrs:{data:e.salaryDataTable,"row-key":"id","tree-props":{children:"children",hasChildren:"hasChildren"}}},[a("el-table-column",{attrs:{prop:"text",label:"薪资项","min-width":"180"}}),a("el-table-column",{attrs:{prop:"base",label:"基数","min-width":"180"}}),a("el-table-column",{attrs:{prop:"backPay",label:"补发","min-width":"180"}}),a("el-table-column",{attrs:{prop:"backDeduction",label:"补扣","min-width":"180"}}),a("el-table-column",{attrs:{prop:"netSalary",label:"合计","min-width":"180"}})],1)],1)],1)],1)],1):e._e()],1):e._e(),"3"===e.activeIndex?a("div",{staticStyle:{"margin-top":"20px"},attrs:{title:"调整",name:"10"}},[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.dataModel,"label-width":"100px"}},[a("el-card",{staticStyle:{"margin-top":"3px"}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("其他补扣/补发")])]),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"长病假职工最低工资补助",prop:"longSickLeaveMinimumWageSubsidy","label-width":"170px"}},[a("el-input-number",{staticStyle:{width:"70%"},attrs:{disabled:!e.isPageEdit,min:0,precision:2,controls:!1,placeholder:""},model:{value:e.dataModel.longSickLeaveMinimumWageSubsidy,callback:function(t){e.$set(e.dataModel,"longSickLeaveMinimumWageSubsidy",t)},expression:"dataModel.longSickLeaveMinimumWageSubsidy"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"双薪代扣",prop:"thirteenthMonthTaxWithholding","label-width":"170px"}},[a("el-input-number",{staticStyle:{width:"70%"},attrs:{disabled:!e.isPageEdit,min:0,precision:2,controls:!1,placeholder:""},model:{value:e.dataModel.thirteenthMonthTaxWithholding,callback:function(t){e.$set(e.dataModel,"thirteenthMonthTaxWithholding",t)},expression:"dataModel.thirteenthMonthTaxWithholding"}})],1)],1)],1)],1),a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.dataModel,"label-width":"100px"}},[a("el-card",{staticStyle:{"margin-top":"3px"}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("奖金相关")])]),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"奖金合计",prop:"bonusTotal","label-width":"170px"}},[a("el-input-number",{staticStyle:{width:"70%"},attrs:{disabled:!e.isPageEdit,min:0,precision:2,controls:!1,placeholder:""},model:{value:e.dataModel.bonusTotal,callback:function(t){e.$set(e.dataModel,"bonusTotal",t)},expression:"dataModel.bonusTotal"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"其他各项奖金",prop:"otherBonusesTotal","label-width":"170px"}},[a("el-input-number",{staticStyle:{width:"70%"},attrs:{disabled:!e.isPageEdit,min:0,precision:2,controls:!1,placeholder:""},model:{value:e.dataModel.otherBonusesTotal,callback:function(t){e.$set(e.dataModel,"otherBonusesTotal",t)},expression:"dataModel.otherBonusesTotal"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"实发奖金",prop:"actualBonus","label-width":"170px"}},[a("el-input-number",{staticStyle:{width:"70%"},attrs:{disabled:!e.isPageEdit,min:0,precision:2,controls:!1,placeholder:""},model:{value:e.dataModel.actualBonus,callback:function(t){e.$set(e.dataModel,"actualBonus",t)},expression:"dataModel.actualBonus"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"税前奖金",prop:"preTaxBonus","label-width":"170px"}},[a("el-input-number",{staticStyle:{width:"70%"},attrs:{disabled:!e.isPageEdit,min:0,precision:2,controls:!1,placeholder:""},model:{value:e.dataModel.preTaxBonus,callback:function(t){e.$set(e.dataModel,"preTaxBonus",t)},expression:"dataModel.preTaxBonus"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"奖金扣税",prop:"bonusTax","label-width":"170px"}},[a("el-input-number",{staticStyle:{width:"70%"},attrs:{disabled:!e.isPageEdit,min:0,precision:2,controls:!1,placeholder:""},model:{value:e.dataModel.bonusTax,callback:function(t){e.$set(e.dataModel,"bonusTax",t)},expression:"dataModel.bonusTax"}})],1)],1)],1)],1),a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.dataModel,"label-width":"100px"}},[a("el-card",{staticStyle:{"margin-top":"3px"}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("个税相关")])]),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"应发工资（申报个税用）",prop:"grossSalaryForTaxDeclaration","label-width":"170px"}},[a("el-input-number",{staticStyle:{width:"70%"},attrs:{disabled:!e.isPageEdit,min:0,precision:2,controls:!1,placeholder:""},model:{value:e.dataModel.grossSalaryForTaxDeclaration,callback:function(t){e.$set(e.dataModel,"grossSalaryForTaxDeclaration",t)},expression:"dataModel.grossSalaryForTaxDeclaration"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"代扣税金",prop:"taxWithholding","label-width":"170px"}},[a("el-input-number",{staticStyle:{width:"70%"},attrs:{disabled:!e.isPageEdit,min:0,precision:2,controls:!1,placeholder:""},model:{value:e.dataModel.taxWithholding,callback:function(t){e.$set(e.dataModel,"taxWithholding",t)},expression:"dataModel.taxWithholding"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"累计收入额",prop:"accumulatedIncome","label-width":"170px"}},[a("el-input-number",{staticStyle:{width:"70%"},attrs:{disabled:!e.isPageEdit,min:0,precision:2,controls:!1,placeholder:""},model:{value:e.dataModel.accumulatedIncome,callback:function(t){e.$set(e.dataModel,"accumulatedIncome",t)},expression:"dataModel.accumulatedIncome"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"累计专项扣除",prop:"accumulatedDeduction","label-width":"170px"}},[a("el-input-number",{staticStyle:{width:"70%"},attrs:{disabled:!e.isPageEdit,min:0,precision:2,controls:!1,placeholder:""},model:{value:e.dataModel.accumulatedDeduction,callback:function(t){e.$set(e.dataModel,"accumulatedDeduction",t)},expression:"dataModel.accumulatedDeduction"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"累计专项附加扣除",prop:"accumulatedAdditionalDeduction","label-width":"170px"}},[a("el-input-number",{staticStyle:{width:"70%"},attrs:{disabled:!e.isPageEdit,min:0,precision:2,controls:!1,placeholder:""},model:{value:e.dataModel.accumulatedAdditionalDeduction,callback:function(t){e.$set(e.dataModel,"accumulatedAdditionalDeduction",t)},expression:"dataModel.accumulatedAdditionalDeduction"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"累计应纳税所得额",prop:"accumulatedTaxableIncome","label-width":"170px"}},[a("el-input-number",{staticStyle:{width:"70%"},attrs:{disabled:!e.isPageEdit,min:0,precision:2,controls:!1,placeholder:""},model:{value:e.dataModel.accumulatedTaxableIncome,callback:function(t){e.$set(e.dataModel,"accumulatedTaxableIncome",t)},expression:"dataModel.accumulatedTaxableIncome"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"累计已预扣税额",prop:"accumulatedWithheldTax","label-width":"170px"}},[a("el-input-number",{staticStyle:{width:"70%"},attrs:{disabled:!e.isPageEdit,min:0,precision:2,controls:!1,placeholder:""},model:{value:e.dataModel.accumulatedWithheldTax,callback:function(t){e.$set(e.dataModel,"accumulatedWithheldTax",t)},expression:"dataModel.accumulatedWithheldTax"}})],1)],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"备注","label-width":"170px"}},[a("el-input",{attrs:{type:"textarea",rows:3,maxlength:"300",placeholder:"备注",disabled:!e.isPageEdit},model:{value:e.dataModel.memo1,callback:function(t){e.$set(e.dataModel,"memo1",t)},expression:"dataModel.memo1"}})],1)],1)],1)],1)],1),a("el-row")],1):e._e(),"4"===e.activeIndex?a("div",{staticStyle:{"margin-top":"20px"},attrs:{title:"特殊列",name:"10"}},[a("el-form",{ref:"salaryExtendedDetailsDataForm",attrs:{model:e.salaryExtendedDetailsDataModel,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"特殊列",prop:"salaryExtendedColumnId"}},[a("el-select",{attrs:{clearable:"",placeholder:"特殊列"},on:{change:function(t){return e.groupRests()}},model:{value:e.salaryExtendedDetailsDataModel.salaryExtendedColumnId,callback:function(t){e.$set(e.salaryExtendedDetailsDataModel,"salaryExtendedColumnId",t)},expression:"salaryExtendedDetailsDataModel.salaryExtendedColumnId"}},[e._l(e.groupList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),a("el-option",{key:"00000000-0000-0000-0000-000000000000",attrs:{label:"其他",value:"00000000-0000-0000-0000-000000000000"}})],2)],1)],1)],1),a("el-row",[e.isRests?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"特殊列名称"}},[a("el-input",{staticStyle:{width:"60%"},attrs:{placeholder:""},model:{value:e.salaryExtendedDetailsDataModel.groupModel.name,callback:function(t){e.$set(e.salaryExtendedDetailsDataModel.groupModel,"name",t)},expression:"salaryExtendedDetailsDataModel.groupModel.name"}})],1)],1):e._e()],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"补发/补扣",prop:"reissueDeduction"}},[a("el-input-number",{staticStyle:{width:"60%"},attrs:{min:0,precision:2,controls:!1,placeholder:"",maxlength:"5"},model:{value:e.salaryExtendedDetailsDataModel.reissueDeduction,callback:function(t){e.$set(e.salaryExtendedDetailsDataModel,"reissueDeduction",t)},expression:"salaryExtendedDetailsDataModel.reissueDeduction"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"计税",prop:"taxCalculation","label-width":"170px"}},[a("el-switch",{attrs:{"active-color":"#13ce66"},model:{value:e.salaryExtendedDetailsDataModel.taxCalculation,callback:function(t){e.$set(e.salaryExtendedDetailsDataModel,"taxCalculation",t)},expression:"salaryExtendedDetailsDataModel.taxCalculation"}})],1)],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",rows:3,placeholder:"备注"},model:{value:e.salaryExtendedDetailsDataModel.remark,callback:function(t){e.$set(e.salaryExtendedDetailsDataModel,"remark",t)},expression:"salaryExtendedDetailsDataModel.remark"}})],1)],1)],1),a("div",[a("el-button",{staticStyle:{"margin-top":"20px"},attrs:{type:"primary",loading:e.btnSaveLoading},on:{click:e.saveSalaryExtendedDetailsDialog}},[e._v("保 存")])],1)],1)],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticStyle:{"margin-top":"20px"},on:{click:e.closeDialog}},[e._v("取 消")]),a("el-button",{staticStyle:{"margin-top":"20px"},attrs:{type:"primary",loading:e.btnSaveLoading},on:{click:e.saveDialog}},[e._v("保 存")])],1)],1)],1)},d=[],m=(a("d81d"),a("e44c")),g=a("68b5"),p={components:{hrSalary:g["default"]},data:function(){return{inputNumberProps:{max:999999999,min:-999999999,disabled:!this.isPageEdit,precision:2,controls:!1,placeholder:"",style:"width: 70%"},activeNames:["1"],activeIndex:"1",salaryId:"",employeeId:"",showDialog:!1,isPageEdit:!0,title:"",isRests:!1,btnSaveLoading:!1,isEdit:!1,groupEditShow:!1,stationTrees:[],partySocietyAgeList:[],partPositionList:[],partHolidayTypeList:[],partPositionTypeList:[],salaryScales:[],groupList:[],extendedColumn:{},dataModel:{},salaryExtendedDetailsDataModel:{employeeModel:{},groupModel:{}},rules:{},selectOptions:[{value:1,label:"补扣"},{value:2,label:"补发"}],salaryDataTable:[],salaryDataTableId:1,salaryBonusDataTable:[],isNoSalary:!1,isNoSalaryText:""}},created:function(){this.getGroup(),this.loadPartySocietyAgeList(),this.loadcPositionList(),this.loadcPositionTypeList()},methods:{initDialog:function(e,t){this.employeeId=e.employeeId,t?(this.title="编辑月度员工薪资",this.isEdit=!0,this.isPageEdit=!0):(this.title="显示月度员工薪资",this.isEdit=!1,this.isPageEdit=!1),this.getData(e.id),this.queryStationTree(),this.isRests=!1,this.dataModel.salaryId=this.salaryId,this.salaryExtendedDetailsDataModel.salaryId=this.salaryId,this.showDialog=!0},getData:function(e){var t=this;r["a"].getSalaryDetail({id:e}).then((function(e){e.succeed&&(t.dataModel=e.data,"string"===typeof t.dataModel.salaryLeaveDesc&&""!==t.dataModel.salaryLeaveDesc&&t.dataModel.salaryLeaveDesc.includes("产假")?(t.isNoSalary=!0,t.isNoSalaryText="产假停发工资"):20===t.dataModel.employeeSalary.enumEmployeeSalaryStatus&&(t.isNoSalary=!0,t.isNoSalaryText="停发工资"),t.setSalaryDataTable(),t.setSalaryBonusDataTable(),t.querySalaryScaleSelector(e.data.parentStationId))})).catch((function(e){console.log(e)}))},getSalaryExtendedDetailsData:function(e){var t=this;r["a"].getSalaryExtendedDetails({id:e}).then((function(e){e.succeed&&(t.salaryExtendedDetailsDataModel=e.data,t.setEmployee(e.data.employee))})).catch((function(e){}))},groupRests:function(){"00000000-0000-0000-0000-000000000000"===this.salaryExtendedDetailsDataModel.salaryExtendedColumnId?this.isRests=!0:this.isRests=!1},setSalaryBonusDataTable:function(){this.salaryBonusDataTable=[{id:this.salaryDataTableId++,text:"年终一次性奖励",base:this.dataModel.yearEndBonus},{id:this.salaryDataTableId++,text:"其他各项奖金",base:this.dataModel.otherBonusesTotal},{id:this.salaryDataTableId++,text:"奖金扣税",base:this.dataModel.bonusTax}]},setSalaryDataTable:function(){this.salaryDataTable=[{id:this.salaryDataTableId++,netSalary:parseFloat(this.dataModel.basicSalarySubtotal).toFixed(2),text:"基本工资",children:[{id:this.salaryDataTableId++,text:"岗资",base:this.dataModel.stationWage},{id:this.salaryDataTableId++,text:"薪资",base:this.dataModel.salaryMoney},{id:this.salaryDataTableId++,text:"基本工资其它加",base:this.dataModel.basicSalaryOtherAdd}]},{id:this.salaryDataTableId++,text:"绩效小计",netSalary:parseFloat(this.dataModel.performanceSubtotal).toFixed(2),children:[{id:this.salaryDataTableId++,text:"岗位津贴",base:this.dataModel.stationAllowance},{id:this.salaryDataTableId++,text:"工作量津贴",base:this.dataModel.workloadAllowance1},{id:this.salaryDataTableId++,text:"工作量津贴小计",base:this.dataModel.workloadAllowance2Subtotal},{id:this.salaryDataTableId++,text:"其他加",base:this.dataModel.otherAdd}]},{id:this.salaryDataTableId++,text:"津补贴小计",netSalary:parseFloat(this.dataModel.allowanceSubtotal).toFixed(2),children:[{id:this.salaryDataTableId++,text:"粮油补贴",base:this.dataModel.grainOilSubsidy},{id:this.salaryDataTableId++,text:"上下班交通费",base:this.dataModel.commuteSubsidy},{id:this.salaryDataTableId++,text:"护龄",base:this.dataModel.nursing},{id:this.salaryDataTableId++,text:"独子",base:this.dataModel.onlyChild,backPay:this.dataModel.backPayOnlyChild,netSalary:this.dataModel.onlyChildTotal},{id:this.salaryDataTableId++,text:"援外津贴",base:this.dataModel.foreignAidAllowance},{id:this.salaryDataTableId++,text:"津补贴其它加",base:this.dataModel.allowanceOtherAdd}]},{id:this.salaryDataTableId++,text:"工作量津贴合计",netSalary:parseFloat(this.dataModel.workloadAllowance2Subtotal).toFixed(2),children:[{id:this.salaryDataTableId++,text:"停车补贴",base:this.dataModel.parkingSubsidy},{id:this.salaryDataTableId++,text:"公派车贴补贴",base:this.dataModel.officialCarAllowance},{id:this.salaryDataTableId++,text:"电话费",base:this.dataModel.telephoneFee},{id:this.salaryDataTableId++,text:"博士后房帖",base:this.dataModel.postdoctoralHousingAllowance},{id:this.salaryDataTableId++,text:"卫生津贴",base:this.dataModel.healthAllowance},{id:this.salaryDataTableId++,text:"零星补节假日加班绩效",base:this.dataModel.occasionalHolidayOvertimePerformance},{id:this.salaryDataTableId++,text:"节假日加班绩效",base:this.dataModel.holidayOvertimePerformance},{id:this.salaryDataTableId++,text:"中夜班绩效",base:this.dataModel.midnightShiftPerformance},{id:this.salaryDataTableId++,text:"行政值班绩效",base:this.dataModel.administrativeDutyPerformance},{id:this.salaryDataTableId++,text:"一二值班绩效",base:this.dataModel.firstSecondDutyPerformance}]},{id:this.salaryDataTableId++,text:"其他加",netSalary:parseFloat(this.dataModel.otherAdd).toFixed(2),children:[{id:this.salaryDataTableId++,text:"退还多扣养扣",base:this.dataModel.returnOverDeductPension},{id:this.salaryDataTableId++,text:"退还多扣医疗",base:this.dataModel.returnOverDeductMedical},{id:this.salaryDataTableId++,text:"退还多扣失业",base:this.dataModel.returnOverDeductUnemployment},{id:this.salaryDataTableId++,text:"退还多扣公扣",base:this.dataModel.returnOverDeductPublic},{id:this.salaryDataTableId++,text:"退还多扣补充公积金",base:this.dataModel.returnOverDeductSupplementaryHousingFund},{id:this.salaryDataTableId++,text:"退还多扣年金",base:this.dataModel.returnOverDeductOccupationalAnnuity},{id:this.salaryDataTableId++,text:"退还多扣出国人员扣款",base:this.dataModel.returnOverDeductDeductionForForeigners},{id:this.salaryDataTableId++,text:"退还多扣病假工资",base:this.dataModel.returnOverDeductSickLeaveSalary}]},{id:this.salaryDataTableId++,text:"扣款合计",netSalary:parseFloat(this.dataModel.deductionTotal).toFixed(2),children:[{id:this.salaryDataTableId++,text:"养老保险",base:null!=this.dataModel.pensionDeduction?parseFloat(this.dataModel.pensionDeduction).toFixed(2):"",backDeduction:this.dataModel.backDeductionPension,netSalary:this.dataModel.pensionDeductionTotal},{id:this.salaryDataTableId++,text:"医疗保险",base:null!=this.dataModel.medicalInsurance?parseFloat(this.dataModel.medicalInsurance).toFixed(2):"",backDeduction:this.dataModel.backDeductionMedicalInsurance,netSalary:this.dataModel.medicalInsuranceTotal},{id:this.salaryDataTableId++,text:"失业保险",base:null!=this.dataModel.unemploymentInsurance?parseFloat(this.dataModel.unemploymentInsurance).toFixed(2):"",backDeduction:this.dataModel.backDeductionUnemploymentInsurance,netSalary:this.dataModel.unemploymentInsuranceTotal},{id:this.salaryDataTableId++,text:"基本公积金",base:null!=this.dataModel.basicHousingFund?parseFloat(this.dataModel.basicHousingFund).toFixed(0):"",backDeduction:this.dataModel.backDeductionBasicHousingFund,netSalary:this.dataModel.housingFundTotal},{id:this.salaryDataTableId++,text:"补充公积金",base:null!=this.dataModel.supplementaryHousingFund?parseFloat(this.dataModel.supplementaryHousingFund).toFixed(0):"",backDeduction:this.dataModel.backDeductionSupplementaryHousingFund,netSalary:this.dataModel.supplementaryHousingFundTotal},{id:this.salaryDataTableId++,text:"职业年金",base:null!=this.dataModel.occupationalAnnuity?parseFloat(this.dataModel.occupationalAnnuity).toFixed(2):"",backDeduction:this.dataModel.backDeductionOccupationalAnnuity,netSalary:this.dataModel.occupationalAnnuityTotal},{id:this.salaryDataTableId++,text:"会费",base:null!=this.dataModel.membershipFee?parseFloat(this.dataModel.membershipFee).toFixed(1):"",backDeduction:this.dataModel.backDeductionMembershipFee,netSalary:this.dataModel.membershipFeeTotal}]},{id:this.salaryDataTableId++,text:"申报个税四金合计",netSalary:parseFloat(this.dataModel.taxDeclarationFourGold).toFixed(2),children:[{id:this.salaryDataTableId++,text:"养扣基础数据",base:this.dataModel.basicDataDeduction},{id:this.salaryDataTableId++,text:"医疗保险基础数据",base:this.dataModel.medicalInsuranceBasicData},{id:this.salaryDataTableId++,text:"失业保险基础数据",base:this.dataModel.unemploymentInsuranceBasicData},{id:this.salaryDataTableId++,text:"公积金基础数据",base:this.dataModel.housingFundBasicData},{id:this.salaryDataTableId++,text:"补充公积金基础数据",base:this.dataModel.supplementaryHousingFundBasicData},{id:this.salaryDataTableId++,text:"年金基础数据",base:this.dataModel.pensionBasicData},{id:this.salaryDataTableId++,text:"养老（申报个税用）",base:this.dataModel.pensionTaxDeclaration},{id:this.salaryDataTableId++,text:"医疗（申报个税用）",base:this.dataModel.medicalTaxDeclaration},{id:this.salaryDataTableId++,text:"失业（申报个税用）",base:this.dataModel.unemploymentTaxDeclaration},{id:this.salaryDataTableId++,text:"公扣（申报个税用）",base:this.dataModel.publicTaxDeclaration},{id:this.salaryDataTableId++,text:"年金（申报个税用）",base:this.dataModel.occupationalAnnuityTaxDeclaration}]}]},saveDialog:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.btnSaveLoading=!0,e.isEdit&&r["a"].updateSalaryDetail(e.dataModel).then((function(t){t.succeed&&(e.$message({message:"修改成功",type:"success"}),e.btnSaveLoading=!1,e.$emit("refreshData"),e.closeDialog())})).catch((function(t){e.btnSaveLoading=!1})))}))},saveSalaryExtendedDetailsDialog:function(){var e=this;this.salaryExtendedDetailsDataModel.salaryId=this.salaryId,this.salaryExtendedDetailsDataModel.employeeModel.employeeId=this.dataModel.employeeId,this.salaryExtendedDetailsDataModel.groupModel.EnumGroupType=2,this.$refs["salaryExtendedDetailsDataForm"].validate((function(t){t&&(e.btnSaveLoading=!0,r["a"].addSalaryExtendedDetails(e.salaryExtendedDetailsDataModel).then((function(t){t.succeed&&(e.$message({message:"添加成功",type:"success"}),e.btnSaveLoading=!1,e.closesalaryExtendedDetailsDialog())})).catch((function(t){e.btnSaveLoading=!1})))}))},getGroup:function(){var e=this;r["a"].getSalaryExtendedColumn({type:2}).then((function(t){t.succeed&&(e.groupList=t.data)})).catch((function(e){}))},closeDialog:function(){this.showDialog=!1,this.$refs.dataForm.resetFields(),this.activeNames=["1"]},closesalaryExtendedDetailsDialog:function(){this.salaryExtendedDetailsDataModel={}},loadPartySocietyAgeList:function(){var e=this;m["a"].queryDictByParentCode({code:"00078"}).then((function(t){e.partySocietyAgeList=t.data.datas})).catch((function(e){console.log(e)}))},loadcPositionList:function(){var e=this;m["a"].queryDictByParentCode({code:"00048"}).then((function(t){e.partPositionList=t.data.datas})).catch((function(e){console.log(e)}))},loadcPositionTypeList:function(){var e=this;m["a"].queryDictByParentCode({code:"00039"}).then((function(t){e.partPositionTypeList=t.data.datas})).catch((function(e){console.log(e)}))},loadcHolidayTypeList:function(){var e=this;m["a"].queryDictByParentCode({code:"00056"}).then((function(t){e.partHolidayTypeList=t.data.datas})).catch((function(e){console.log(e)}))},queryStationTree:function(){var e=this;u["a"].queryStationTree().then((function(t){e.stationTrees=t.data})).catch((function(e){console.log(e)}))},stationTreeChange:function(){var e=this;this.stationTrees.forEach((function(t){if(null!=t.children){var a=t.children.find((function(t){return t.id===e.dataModel.stationId}));a&&(e.dataModel.newStationWage=a.wage)}}))},salaryScaleChange:function(e){var t=this.salaryScales.find((function(t){return t.id===e}));t&&this.salaryData&&(this.salaryData=this.salaryData.map((function(a){return"薪级工资"===a.content&&(a.paramId=e,a.allowance=t.wage),a})))},querySalaryScaleSelector:function(e){var t=this;u["a"].querySalaryScaleSelector({id:e}).then((function(e){t.salaryScales=e.data})).catch((function(e){console.log(e)}))},getYear:function(e){return e?("string"===typeof e&&(e=new Date(e)),e.getFullYear()):null},getMonth:function(e){return e?("string"===typeof e&&(e=new Date(e)),e.getMonth()+1):null},handleSelect:function(e){this.activeIndex=e},setEmployee:function(e){this.salaryExtendedDetailsDataModel.employeeId=e.id,this.salaryExtendedDetailsDataModel.salaryId=this.salaryId,this.$set(this.salaryExtendedDetailsDataModel,"employeeModel",{employeeId:e.id,empUid:e.uid,empCode:e.empCode,empName:e.displayName,genderDesc:e.enumGenderDesc,empDept:e.deptName,hospitalAreaNameText:e.hospitalAreaNameText,identityNumber:e.identityNumber,hireStyleName:e.employeeHR.hireStyleName,leaveStyleName:e.employeeHR.leaveStyleName,deadDate:e.employeeHR.deadDate})}}},y=p,h=a("2877"),f=Object(h["a"])(y,c,d,!1,null,null,null),b=f.exports,S=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{title:e.title,visible:e.showDialog,width:"90%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:e.closeDialog}},[a("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"唯一码"},model:{value:e.listQuery.uid,callback:function(t){e.$set(e.listQuery,"uid",t)},expression:"listQuery.uid"}})],1),a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQuery.displayName,callback:function(t){e.$set(e.listQuery,"displayName",t)},expression:"listQuery.displayName"}})],1),a("el-form-item",[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"原状态"},model:{value:e.listQuery.oldEnumEmployeeSalaryStatus,callback:function(t){e.$set(e.listQuery,"oldEnumEmployeeSalaryStatus",t)},expression:"listQuery.oldEnumEmployeeSalaryStatus"}},e._l(e.employeeSalaryStatusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),a("el-form-item",[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"新状态"},model:{value:e.listQuery.newEnumEmployeeSalaryStatus,callback:function(t){e.$set(e.listQuery,"newEnumEmployeeSalaryStatus",t)},expression:"listQuery.newEnumEmployeeSalaryStatus"}},e._l(e.employeeSalaryStatusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.exportData}},[e._v("导出")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","default-sort":{prop:"Month",order:"descending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{label:"部门",prop:"Employee.Department.Name",sortable:"custom"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.deptName))])]}}])}),a("el-table-column",{attrs:{label:"在职方式",prop:"Employee.EmployeeHR.HireStyle.Name",sortable:"custom","min-width":100},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.hireStyleName))])]}}])}),a("el-table-column",{attrs:{label:"原薪资状态",sortable:"custom",prop:"OldEmployeeSalaryStatus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.oldEnumEmployeeSalaryStatusDesc))])]}}])}),a("el-table-column",{attrs:{label:"新薪资状态",sortable:"custom",prop:"NewEmployeeSalaryStatus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.newEnumEmployeeSalaryStatusDesc))])]}}])}),a("el-table-column",{attrs:{label:"更新时间",sortable:"custom",prop:"UpdateTime"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.updateTime))])]}}])}),a("employeeTableColumns")],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.closeDialog}},[e._v("取 消")])],1)],1)],1)},D=[],v=a("2579"),_={components:{employeeTableColumns:v["a"]},data:function(){return{showDialog:!1,title:"员工薪资状态记录查询",btnSaveLoading:!1,listLoading:!1,total:0,employeeSalaryStatusList:[],salaryId:"",dataList:[],listQuery:{pageIndex:1,pageSize:10}}},methods:{initDialog:function(e){this.salaryId=e,this.listQuery.salaryId=e,this.showDialog=!0,this.getPageList(),this.loadSalaryStatus()},loadSalaryStatus:function(){var e=this;u["a"].getEnumInfos({enumType:"EmployeeSalaryStatus"}).then((function(t){e.employeeSalaryStatusList=t.data.datas})).catch((function(e){console.log(e)}))},search:function(){this.listQuery.pageIndex=1,this.getPageList()},getPageList:function(){var e=this;this.listLoading=!0,r["a"].queryEmployeeSalaryStatus(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.dataList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var l="";"descending"===e.order&&(l="-"),"ascending"===e.order&&(l="+"),this.listQuery.order=l+e.prop,this.getPageList()},exportData:function(){var e=this;r["a"].exportEmployeeSalaryRecord(this.listQuery).then((function(t){var l=a("19de"),i="员工薪资状态记录"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?l(t.data,i):l(t,i)}))},closeDialog:function(){this.dataModel={uniqueCode:null,employeeNumber:null,name:null,salaryStatus:null,time:null},this.showDialog=!1}}},w=_,x=Object(h["a"])(w,S,D,!1,null,null,null),k=x.exports,L=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{title:e.title,visible:e.showDialog,width:"80%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:e.closeDialog}},[a("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[a("el-form-item",[a("el-input",{attrs:{clearable:"",placeholder:"唯一码"},model:{value:e.listQuery.uid,callback:function(t){e.$set(e.listQuery,"uid",t)},expression:"listQuery.uid"}})],1),a("el-form-item",[a("el-input",{attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),a("el-form-item",[a("el-input",{attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQuery.displayName,callback:function(t){e.$set(e.listQuery,"displayName",t)},expression:"listQuery.displayName"}})],1),a("el-form-item",[a("el-input",{attrs:{clearable:"",placeholder:"部门"},model:{value:e.listQuery.deptName,callback:function(t){e.$set(e.listQuery,"deptName",t)},expression:"listQuery.deptName"}})],1),a("el-form-item",[a("el-input",{attrs:{clearable:"",placeholder:"职别"},model:{value:e.listQuery.officialRankName,callback:function(t){e.$set(e.listQuery,"officialRankName",t)},expression:"listQuery.officialRankName"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{label:"部门",sortable:"custom",prop:"Employee.Department.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.deptName))])]}}])}),a("el-table-column",{attrs:{label:"职别",sortable:"custom",prop:"Employee.EmployeeHR.OfficialRank.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.officialRankName))])]}}])}),a("el-table-column",{attrs:{label:"挂账月份",sortable:"custom",prop:"WithholdMonth","header-align":"left",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.withholdMonth?new Date(l.withholdMonth).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"挂账金额",sortable:"custom",prop:"WithholdAmount","header-align":"left",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(e._f("formatMoney2")(l.withholdAmount)))])]}}])}),a("el-table-column",{attrs:{label:"不够扣原因",sortable:"custom",prop:"InsuffDeductReason","header-align":"left",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.insuffDeductReason))])]}}])}),a("el-table-column",{attrs:{label:"备注","min-width":"150px",sortable:"custom",prop:"Remark"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.remark))])]}}])}),a("employeeTableColumns")],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})],1)],1)},O=[],T={components:{employeeTableColumns:v["a"]},data:function(){return{showDialog:!1,title:"",btnSaveLoading:!1,isEdit:!1,listLoading:!1,dataModel:{},listQuery:{total:1,pageIndex:1,pageSize:10},pageList:[]}},methods:{initDialog:function(){this.title="查看社保代扣",this.showDialog=!0,this.getPageList()},search:function(){this.listQuery.pageIndex=1,this.getPageList()},getPageList:function(){var e=this;r["a"].querySocialSecurityWithhold(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},closeDialog:function(){this.dataModel={},this.showDialog=!1,this.$refs.dataForm.resetFields()},sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.$delete(this.listQuery,"order"),this.search()}}},$=T,N=(a("ab72"),a("d975"),Object(h["a"])($,L,O,!1,null,"db814d50",null)),W=N.exports,F={components:{editDialog:b,employeeSalaryRecordDialog:k,employeeTableColumns:v["a"],socialSecurityWithhold:W},data:function(){return{isSalaryMonthComplete:!0,showDialog:!1,isEdit:!1,title:"财务数据",pageList:[],groupList:[],salaryData:{},lastMonthData:{},salaryId:"",listQuery:{queryCondition:{},salaryId:"",total:1,pageIndex:1,pageSize:10},searchList:[{id:""}],lastMonth:!1,listLoading:!1,dialogEditEmpSalaryVisible:!1,dataColumns:[{value:"1",label:"唯一码",type:"System.String",columnName:"Uid"},{value:"2",label:"工号",type:"System.String",columnName:"EmpCode"},{value:"3",label:"姓名",type:"System.String",columnName:"DisplayName"},{value:"4",label:"证件号",type:"System.String",columnName:"IdentityNumber"},{value:"5",label:"手机号",type:"System.String",columnName:"Mobile"},{value:"6",label:"在职方式",type:"System.String",columnName:"HireStyle"},{value:"7",label:"受雇年份",type:"System.String",columnName:"EmployYear"},{value:"8",label:"受雇月份",type:"System.String",columnName:"EmployMonth"},{value:"9",label:"本年度受雇月数",type:"System.String",columnName:"EmployMonthNumber"},{value:"10",label:"四金不够扣标记",type:"System.String",columnName:"FourGoldDeficiencySign"},{value:"11",label:"社保基数",type:"System.Decimal",columnName:"SocialSecurityBase"},{value:"12",label:"公积金基数",type:"System.Decimal",columnName:"HousingFundBase"},{value:"13",label:"岗位级别",type:"System.String",columnName:"StationLevel"},{value:"14",label:"薪级",type:"System.String",columnName:"SalaryScale"},{value:"15",label:"工龄",type:"System.Int32",columnName:"SocietyAge"},{value:"16",label:"工龄段",type:"System.String",columnName:"SeniorityRange"},{value:"17",label:"补充公积金标记",type:"System.Boolean",columnName:"SupplementaryHousingFundFlag"},{value:"18",label:"职员年金标记",type:"System.Boolean",columnName:"EmployeePensionFlag"},{value:"19",label:"病产假天数",type:"System.Decimal",columnName:"SickLeaveDays"},{value:"20",label:"休假类型",type:"System.String",columnName:"SalaryLeaveDesc"},{value:"21",label:"基本工资小计",type:"System.Decimal",columnName:"BasicSalarySubtotal"},{value:"22",label:"岗资基数",type:"System.Decimal",columnName:"StationWageBase"},{value:"23",label:"岗资",type:"System.Decimal",columnName:"StationWage"},{value:"24",label:"薪资基数",type:"System.Decimal",columnName:"SalaryBase"},{value:"25",label:"薪资",type:"System.Decimal",columnName:"SalaryMoney"},{value:"26",label:"基本工资其它加",type:"System.Decimal",columnName:"BasicSalaryOtherAdd"},{value:"27",label:"津补贴小计",type:"System.Decimal",columnName:"AllowanceSubtotal"},{value:"28",label:"粮油补贴",type:"System.Decimal",columnName:"GrainOilSubsidy"},{value:"29",label:"上下班交通费基数",type:"System.Decimal",columnName:"CommuteSubsidyBase"},{value:"30",label:"上下班交通费",type:"System.Decimal",columnName:"CommuteSubsidy"},{value:"31",label:"护龄基数",type:"System.Decimal",columnName:"NursingBase"},{value:"32",label:"护龄",type:"System.Decimal",columnName:"Nursing"},{value:"33",label:"独子",type:"System.Decimal",columnName:"OnlyChild"},{value:"34",label:"独子补发",type:"System.Decimal",columnName:"BackPayOnlyChild"},{value:"35",label:"独子合计",type:"System.Decimal",columnName:"OnlyChildTotal"},{value:"36",label:"援外津贴",type:"System.Decimal",columnName:"ForeignAidAllowance"},{value:"37",label:"津补贴其它加",type:"System.Decimal",columnName:"AllowanceOtherAdd"},{value:"38",label:"绩效小计",type:"System.Decimal",columnName:"PerformanceSubtotal"},{value:"39",label:"岗位津贴基数",type:"System.Decimal",columnName:"StationAllowanceBase"},{value:"40",label:"岗位津贴",type:"System.Decimal",columnName:"StationAllowance"},{value:"41",label:"工作量津贴",type:"System.Decimal",columnName:"WorkloadAllowance1"},{value:"42",label:"工作量津贴合计",type:"System.Decimal",columnName:"WorkloadAllowance2Subtotal"},{value:"43",label:"停车补贴",type:"System.Decimal",columnName:"ParkingSubsidy"},{value:"44",label:"公派车贴补贴",type:"System.Decimal",columnName:"OfficialCarAllowance"},{value:"45",label:"电话费",type:"System.Decimal",columnName:"TelephoneFee"},{value:"46",label:"电话费合计",type:"System.Decimal",columnName:"TelephoneFeeTotal"},{value:"47",label:"长病假职工最低工资补助",type:"System.Decimal",columnName:"LongSickLeaveMinimumWageSubsidy"},{value:"48",label:"博士后房帖",type:"System.Decimal",columnName:"PostdoctoralHousingAllowance"},{value:"49",label:"卫生津贴",type:"System.Decimal",columnName:"HealthAllowance"},{value:"50",label:"零星补节假日加班绩效",type:"System.Decimal",columnName:"OccasionalHolidayOvertimePerformance"},{value:"51",label:"节假日加班绩效",type:"System.Decimal",columnName:"HolidayOvertimePerformance"},{value:"52",label:"中夜班绩效",type:"System.Decimal",columnName:"MidnightShiftPerformance"},{value:"53",label:"行政值班绩效",type:"System.Decimal",columnName:"AdministrativeDutyPerformance"},{value:"54",label:"一二值班绩效",type:"System.Decimal",columnName:"FirstSecondDutyPerformance"},{value:"55",label:"其它加",type:"System.Decimal",columnName:"OtherAdd"},{value:"56",label:"行政聘任补发",type:"System.Decimal",columnName:"AdministrativeAppointmentReissue"},{value:"57",label:"退还多扣养扣",type:"System.Decimal",columnName:"ReturnOverDeductPension"},{value:"58",label:"退还多扣医疗",type:"System.Decimal",columnName:"ReturnOverDeductMedical"},{value:"59",label:"退还多扣失业",type:"System.Decimal",columnName:"ReturnOverDeductUnemployment"},{value:"60",label:"退还多扣公扣",type:"System.Decimal",columnName:"ReturnOverDeductPublic"},{value:"61",label:"退还多扣补充公积金",type:"System.Decimal",columnName:"ReturnOverDeductSupplementaryHousingFund"},{value:"62",label:"退还多扣年金",type:"System.Decimal",columnName:"ReturnOverDeductOccupationalAnnuity"},{value:"63",label:"退还多扣出国人员扣款",type:"System.Decimal",columnName:"ReturnOverDeductDeductionForForeigners"},{value:"64",label:"退还多扣病假工资",type:"System.Decimal",columnName:"ReturnOverDeductSickLeaveSalary"},{value:"65",label:"定级补发",type:"System.Decimal",columnName:"BackPayGrading"},{value:"66",label:"应发工资",type:"System.Decimal",columnName:"GrossSalary"},{value:"67",label:"应发工资（申报个税用）",type:"System.Decimal",columnName:"GrossSalaryForTaxDeclaration"},{value:"68",label:"养扣",type:"System.Decimal",columnName:"PensionDeduction"},{value:"69",label:"补扣养扣",type:"System.Decimal",columnName:"BackDeductionPension"},{value:"70",label:"养扣合计",type:"System.Decimal",columnName:"PensionDeductionTotal"},{value:"71",label:"医疗保险",type:"System.Decimal",columnName:"MedicalInsurance"},{value:"72",label:"补扣医疗保险",type:"System.Decimal",columnName:"BackDeductionMedicalInsurance"},{value:"73",label:"医疗保险合计",type:"System.Decimal",columnName:"MedicalInsuranceTotal"},{value:"74",label:"失业保险",type:"System.Decimal",columnName:"UnemploymentInsurance"},{value:"75",label:"补扣失业保险",type:"System.Decimal",columnName:"BackDeductionUnemploymentInsurance"},{value:"76",label:"失业保险合计",type:"System.Decimal",columnName:"UnemploymentInsuranceTotal"},{value:"77",label:"基本公积金",type:"System.Decimal",columnName:"BasicHousingFund"},{value:"78",label:"补扣基本公积金",type:"System.Decimal",columnName:"BackDeductionBasicHousingFund"},{value:"79",label:"基本公积金合计",type:"System.Decimal",columnName:"HousingFundTotal"},{value:"80",label:"补充公积金",type:"System.Decimal",columnName:"SupplementaryHousingFund"},{value:"81",label:"补扣补充公积金",type:"System.Decimal",columnName:"BackDeductionSupplementaryHousingFund"},{value:"82",label:"补充公积金合计",type:"System.Decimal",columnName:"SupplementaryHousingFundTotal"},{value:"83",label:"职业年金",type:"System.Decimal",columnName:"OccupationalAnnuity"},{value:"84",label:"补扣职业年金",type:"System.Decimal",columnName:"BackDeductionOccupationalAnnuity"},{value:"85",label:"职业年金合计",type:"System.Decimal",columnName:"OccupationalAnnuityTotal"},{value:"86",label:"会费",type:"System.Decimal",columnName:"MembershipFee"},{value:"87",label:"补扣会费",type:"System.Decimal",columnName:"BackDeductionMembershipFee"},{value:"88",label:"会费合计",type:"System.Decimal",columnName:"MembershipFeeTotal"},{value:"89",label:"病假工资",type:"System.Decimal",columnName:"SickLeaveSalary"},{value:"90",label:"补扣病假",type:"System.Decimal",columnName:"BackDeductionSickLeave"},{value:"91",label:"病假工资合计",type:"System.Decimal",columnName:"SickLeaveSalaryTotal"},{value:"92",label:"代扣税金",type:"System.Decimal",columnName:"TaxWithholding"},{value:"93",label:"其它扣",type:"System.Decimal",columnName:"OtherDeduction"},{value:"94",label:"长病假补扣",type:"System.Decimal",columnName:"BackDeductionLongSickLeave"},{value:"95",label:"扣款合计",type:"System.Decimal",columnName:"DeductionTotal"},{value:"96",label:"实发工资",type:"System.Decimal",columnName:"NetSalary"},{value:"97",label:"养扣基础数据",type:"System.Decimal",columnName:"BasicDataDeduction"},{value:"98",label:"医疗保险基础数据",type:"System.Decimal",columnName:"MedicalInsuranceBasicData"},{value:"99",label:"失业保险基础数据",type:"System.Decimal",columnName:"UnemploymentInsuranceBasicData"},{value:"100",label:"公积金基础数据",type:"System.Decimal",columnName:"HousingFundBasicData"},{value:"101",label:"补充公积金基础数据",type:"System.Decimal",columnName:"SupplementaryHousingFundBasicData"},{value:"102",label:"年金基础数据",type:"System.Decimal",columnName:"PensionBasicData"},{value:"103",label:"养老（申报个税用）",type:"System.Decimal",columnName:"PensionTaxDeclaration"},{value:"104",label:"医疗（申报个税用）",type:"System.Decimal",columnName:"MedicalTaxDeclaration"},{value:"105",label:"失业（申报个税用）",type:"System.Decimal",columnName:"UnemploymentTaxDeclaration"},{value:"106",label:"公扣（申报个税用）",type:"System.Decimal",columnName:"PublicTaxDeclaration"},{value:"107",label:"年金（申报个税用）",type:"System.Decimal",columnName:"OccupationalAnnuityTaxDeclaration"},{value:"108",label:"申报个税四金合计",type:"System.Decimal",columnName:"TaxDeclarationFourGold"},{value:"109",label:"基本工资合计",type:"System.Decimal",columnName:"BasicSalaryTotal"},{value:"110",label:"津贴补贴合计",type:"System.Decimal",columnName:"AllowanceTotal"},{value:"111",label:"加班工资",type:"System.Decimal",columnName:"OvertimePay"},{value:"112",label:"奖金合计",type:"System.Decimal",columnName:"BonusTotal"},{value:"113",label:"年终一次性奖励",type:"System.Decimal",columnName:"YearEndBonus"},{value:"114",label:"其他各项奖金",type:"System.Decimal",columnName:"OtherBonusesTotal"},{value:"115",label:"实发奖金",type:"System.Decimal",columnName:"ActualBonus"},{value:"116",label:"税前奖金",type:"System.Decimal",columnName:"PreTaxBonus"},{value:"117",label:"奖金扣税",type:"System.Decimal",columnName:"BonusTax"},{value:"118",label:"累计收入额",type:"System.Decimal",columnName:"AccumulatedIncome"},{value:"119",label:"累计专项扣除",type:"System.Decimal",columnName:"AccumulatedDeduction"},{value:"120",label:"累计专项附加扣除",type:"System.Decimal",columnName:"AccumulatedAdditionalDeduction"},{value:"121",label:"累计应纳税所得额",type:"System.Decimal",columnName:"AccumulatedTaxableIncome"},{value:"122",label:"累计已预扣税额",type:"System.Decimal",columnName:"AccumulatedWithheldTax"},{value:"123",label:"应发总计",type:"System.Decimal",columnName:"TotalPayable"},{value:"124",label:"工行帐号",type:"System.String",columnName:"ICBCAccount"}],selectConditionOptions:[],signDropdown:[{value:!0,label:"有"},{value:!1,label:"无"}]}},created:function(){this.initDialog()},mounted:function(){},methods:{initDialog:function(){this.salaryId=this.$route.query.salaryId,this.salaryId&&(this.showDialog=!0,this.listQuery.salaryId=this.salaryId,this.getSalary(),this.getPageList(),this.loadConditions())},employeeSalaryRecord:function(){this.$refs.employeeSalaryRecordDialog.initDialog(this.salaryId)},loadConditions:function(){var e=this;u["a"].getEnumInfos({enumType:"Operations"}).then((function(t){e.selectConditionOptions=t.data.datas})).catch((function(e){console.log(e)}))},getSalary:function(){var e=this;r["a"].getSalary({id:this.listQuery.salaryId}).then((function(t){t.succeed?(e.salaryData=t.data,2===e.salaryData.enumStatus?e.isEdit=!0:e.isEdit=!1):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))},closeDialog:function(){this.pageList=[],this.showDialog=!1},search:function(){this.listQuery.pageIndex=1,this.getPageList(),this.clearCheckboxes()},sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.$delete(this.listQuery,"order"),this.search()},getPageList:function(){var e=this;this.searchList.length=0,this.pageList=[],this.listLoading=!0,this.listQuery.entityColumn&&""!==this.listQuery.entityColumn&&this.listQuery.queryCondition.EnumOperation?(this.listQuery.queryCondition.Keywords||(this.listQuery.queryCondition.Keywords=""),this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.columnName,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.ConditionList=[this.listQuery.queryCondition]):(this.listQuery.ConditionList=[],this.listQuery.queryCondition={}),r["a"].querySalaryDetail(this.listQuery).then((function(t){t.succeed?(e.pageList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex,console.log(e.pageList)):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1}))},clearCheckboxes:function(){this.pageList.forEach((function(e){if(!e.id.includes("_lastMonth")){var t=document.getElementById("choose");t&&(t.checked=!1)}}))},sarechLastMonthData:function(e){var t=this;if(this.searchList.includes(e.id)){this.searchList.pop(e.id);var a=this.pageList.findIndex((function(t){return t.id.includes("_lastMonth")&&t.id.split("_lastMonth")[0]===e.id}));-1!==a&&this.pageList.splice(a,1)}else r["a"].queryLastMonthEmployeeSalary({id:e.id}).then((function(a){if(a.succeed){t.lastMonthData=a.data,t.searchList.push(e.id);var l=Object(s["a"])({},e);Object.assign(l,t.lastMonthData),l.id=e.id+"_lastMonth";var i=t.pageList.indexOf(e);t.pageList.splice(i+1,0,l)}else t.$notice.resultTip(a)})).catch((function(e){console.log(e)}))},editEmployeeSalaryDetailDialog:function(e,t){this.$refs.editDialog.salaryId=this.salaryId,this.$refs.editDialog.initDialog(e,t)},onRefresh:function(){this.isEdit&&this.getPageList(),this.dialogEditEmpSalaryVisible=!1},exportData:function(){var e=this;this.searchList.length=0,this.pageList=[],this.listQuery.entityColumn&&""!==this.listQuery.entityColumn&&this.listQuery.queryCondition.EnumOperation?(this.listQuery.queryCondition.Keywords||(this.listQuery.queryCondition.Keywords=""),this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.columnName,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.ConditionList=[this.listQuery.queryCondition]):(this.listQuery.ConditionList=[],this.listQuery.queryCondition={}),r["a"].exportSalaryDetail(this.listQuery).then((function(t){var l=a("19de"),i="员工薪资"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?l(t.data,i):l(t,i),e.getPageList()}))},importExcel:function(e){var t=this,a=e.file,l=new FormData;r["a"].importSalaryDetail(a,l,this.listQuery.salaryId).then((function(e){e.succeed&&(t.$message({message:"导入成功",type:"success"}),t.search())})).catch((function(e){t.search()}))},exportHRData:function(){var e=this;this.searchList.length=0,this.pageList=[],r["a"].exportHRData(this.listQuery).then((function(t){var l=a("19de"),i="人事数据"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?l(t.data,i):l(t,i),e.getPageList()}))},getReissueDeduction:function(e,t){var a=e.find((function(e){return e.groupId===t}));return a?a.reissueDeduction:""},getDecimalValueOrDefault:function(e){return 0===e||void 0===e?"":e<0?Math.abs(e).toFixed(2):e.toFixed(2)},getStyle:function(e,t,a){if(void 0!==e&&void 0!==t){var l=e[a],i=t[a],n=e.uid,o=t.uid;if(n===o&&void 0!==l&&void 0!==i&&l!==i)return{color:"red"}}},getStringWidth:function(e){var t=0;return e.length<3?"auto":3===e.length?(t=30*e.length,t+"px"):4===e.length||5===e.length?(t=25*e.length,t+"px"):e.length>5?(t=20*e.length,t+"px"):void 0}}},M=F,B=(a("34fb"),Object(h["a"])(M,n,o,!1,null,"7e5b5618",null)),C=B.exports,A=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container "},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"唯一码"},model:{value:e.listQuery.uid,callback:function(t){e.$set(e.listQuery,"uid",t)},expression:"listQuery.uid"}})],1),a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQuery.displayName,callback:function(t){e.$set(e.listQuery,"displayName",t)},expression:"listQuery.displayName"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.exportThirteenthFinanceData}},[e._v("导出十三月工资财务数据")])],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-user-solid"},on:{click:e.employeeSalaryRecord}},[e._v("员工薪资状态记录")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticClass:"my-table",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{label:"十三月工资应发",sortable:"custom","min-width":e.getStringWidth("十三月工资应发"),"header-align":"left",align:"right",prop:"ThirteenthMonthSalary"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"thirteenthMonthSalary")},[e._v(e._s(e.getDecimalValueOrDefault(l.thirteenthMonthSalary)))])]}}])}),a("el-table-column",{attrs:{label:"十三月工资代扣税金",sortable:"custom","min-width":e.getStringWidth("十三月工资代扣税金"),"header-align":"left",align:"right",prop:"ThirteenthMonthTaxWithholding"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"thirteenthMonthTaxWithholding")},[e._v(e._s(e.getDecimalValueOrDefault(l.thirteenthMonthTaxWithholding)))])]}}])}),a("el-table-column",{attrs:{label:"十三月工资实发",sortable:"custom","min-width":e.getStringWidth("十三月工资实发"),"header-align":"left",align:"right",prop:"ThirteenthMonthSalaryNet"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"thirteenthMonthSalaryNet")},[e._v(e._s(e.getDecimalValueOrDefault(l.thirteenthMonthSalaryNet)))])]}}])}),a("employeeTableColumns")],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),a("employeeSalaryRecordDialog",{ref:"employeeSalaryRecordDialog"})],1)},I=[],V={components:{employeeTableColumns:v["a"]},props:{salaryId:{type:String,default:"",required:!0}},data:function(){return{pageList:[],listQuery:{total:1,pageIndex:1,pageSize:10,uid:"",empCode:"",displayName:"",keywords:"",entityColumn:null,queryCondition:{EnumOperation:null,Keywords:""}},listLoading:!1,dataColumns:[],selectConditionOptions:[]}},created:function(){this.getPageList()},methods:{init:function(){this.getPageList()},getPageList:function(){var e=this;this.pageList=[],this.listLoading=!0,this.listQuery.salaryId=this.salaryId,r["a"].querySalaryDetail(this.listQuery).then((function(t){t.succeed?(e.pageList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1}))},search:function(){this.listQuery.pageIndex=1,this.getPageList()},sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.listQuery.order=null,this.getPageList()},getStringWidth:function(e){return Math.max(12*e.length+40,100)+"px"},getDecimalValueOrDefault:function(e){return null!=e&&e>0?parseFloat(e).toFixed(2):""},getStyle:function(e,t,a){return{}},exportThirteenthFinanceData:function(){var e=this;this.pageList=[],this.listLoading=!0,r["a"].exportThirteenthFinanceData(this.listQuery).then((function(t){var l=a("19de"),i="员工薪资"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?l(t.data,i):l(t,i),e.getPageList()}))}}},P=V,Q=(a("a48c"),Object(h["a"])(P,A,I,!1,null,"92f7b6e8",null)),E=Q.exports,H=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container "},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"唯一码"},model:{value:e.listQuery.uid,callback:function(t){e.$set(e.listQuery,"uid",t)},expression:"listQuery.uid"}})],1),a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQuery.displayName,callback:function(t){e.$set(e.listQuery,"displayName",t)},expression:"listQuery.displayName"}})],1),a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"关键字"},model:{value:e.listQuery.keywords,callback:function(t){e.$set(e.listQuery,"keywords",t)},expression:"listQuery.keywords"}})],1),a("el-form-item",[a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",clearable:"",placeholder:"请选择字段"},model:{value:e.listQuery.entityColumn,callback:function(t){e.$set(e.listQuery,"entityColumn",t)},expression:"listQuery.entityColumn"}},e._l(e.dataColumns,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e}})})),1)],1),a("el-form-item",[a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"请选择条件"},model:{value:e.listQuery.queryCondition.EnumOperation,callback:function(t){e.$set(e.listQuery.queryCondition,"EnumOperation",t)},expression:"listQuery.queryCondition.EnumOperation"}},e._l(e.selectConditionOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),a("el-form-item",[a("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",placeholder:"请输入关键字"},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1),e._e(),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.downloadexceltemplate}},[e._v("模板下载")])],1),a("el-form-item",[a("el-upload",{attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{slot:"trigger",icon:"el-icon-upload",type:"primary"},slot:"trigger"},[e._v("导入")])],1)],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticClass:"my-table",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{label:"上月数据",width:"100px",align:"center",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[l.id.includes("_lastMonth")?e._e():a("input",{staticClass:"choose",attrs:{id:"choose",type:"checkbox"},on:{input:function(t){return e.sarechLastMonthData(l)}}}),l.id.includes("_lastMonth")?a("span",[e._v(e._s(e.lastMonthData.salaryMonth))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"证件号",sortable:"custom","min-width":e.getStringWidth("证件号"),prop:"Employee.IdentityNumber"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"identityNumber")},[e._v(e._s(l.identityNumber))])]}}])}),a("el-table-column",{attrs:{label:"手机号",sortable:"custom","min-width":e.getStringWidth("手机号"),prop:"Employee.Mobile"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"mobile")},[e._v(e._s(l.mobile))])]}}])}),a("el-table-column",{attrs:{label:"在职方式",sortable:"custom","min-width":e.getStringWidth("在职方式"),prop:"Employee.EmployeeHR.HireStyle.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"hireStyleName")},[e._v(e._s(l.hireStyleName))])]}}])}),a("el-table-column",{attrs:{label:"受雇年份",sortable:"custom","min-width":e.getStringWidth("受雇年份"),prop:"EmployYear"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employYear")},[e._v(e._s(l.employYear))])]}}])}),a("el-table-column",{attrs:{label:"受雇月份",sortable:"custom","min-width":e.getStringWidth("受雇月份"),prop:"EmployMonth"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employMonth")},[e._v(e._s(l.employMonth))])]}}])}),a("el-table-column",{attrs:{label:"本年度受雇月数",sortable:"custom","min-width":e.getStringWidth("本年度受雇月数"),prop:"EmployMonthNumber"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employMonthNumber")},[e._v(e._s(l.employMonthNumber))])]}}])}),a("el-table-column",{attrs:{label:"备注1",sortable:"custom","min-width":e.getStringWidth("备注1"),prop:"Memo1"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"memo1")},[e._v(e._s(l.memo1))])]}}])}),a("el-table-column",{attrs:{label:"备注2",sortable:"custom","min-width":e.getStringWidth("备注2"),prop:"Memo2"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"memo2")},[e._v(e._s(l.memo2))])]}}])}),a("el-table-column",{attrs:{label:"备注3",sortable:"custom","min-width":e.getStringWidth("备注3"),prop:"Memo3"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"memo3")},[e._v(e._s(l.memo3))])]}}])}),a("el-table-column",{attrs:{label:"四金不够扣标记",sortable:"custom","min-width":e.getStringWidth("四金不够扣标记"),align:"center",prop:"FourGoldDeficiencySign"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"fourGoldDeficiencySign")},[e._v(e._s(l.fourGoldDeficiencySign?"有":"无"))])]}}])}),a("el-table-column",{attrs:{label:"社保基数",sortable:"custom","min-width":e.getStringWidth("社保基数"),"header-align":"left",align:"right",prop:"SocialSecurityBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"socialSecurityBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.socialSecurityBase)))])]}}])}),a("el-table-column",{attrs:{label:"公积金基数",sortable:"custom","min-width":e.getStringWidth("公积金基数"),"header-align":"left",align:"right",prop:"HousingFundBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"housingFundBase")},[e._v(e._s(parseFloat(l.housingFundBase).toFixed(0)))])]}}])}),a("el-table-column",{attrs:{label:"岗位级别",sortable:"custom","min-width":e.getStringWidth("岗位级别"),align:"center",prop:"Station.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationName")},[e._v(e._s(l.stationName))])]}}])}),a("el-table-column",{attrs:{label:"薪级",sortable:"custom","min-width":e.getStringWidth("薪级"),align:"center",prop:"SalaryScale.scale"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"scale")},[e._v(e._s(l.scale))])]}}])}),a("el-table-column",{attrs:{label:"工龄",sortable:"custom","min-width":e.getStringWidth("工龄"),align:"center",prop:"SocietyAge"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"societyAge")},[e._v(e._s(l.societyAge))])]}}])}),a("el-table-column",{attrs:{label:"工龄段",sortable:"custom","min-width":e.getStringWidth("工龄段"),align:"center",prop:"SeniorityRangeId"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"seniorityRangeName")},[e._v(e._s(l.seniorityRangeName))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金标记",sortable:"custom","min-width":e.getStringWidth("补充公积金标记"),align:"center",prop:"SupplementaryHousingFundFlag"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFundFlag")},[e._v(e._s(l.supplementaryHousingFundFlag?"有":"无"))])]}}])}),a("el-table-column",{attrs:{label:"职员年金标记",sortable:"custom","min-width":e.getStringWidth("职员年金标记"),align:"center",prop:"EmployeePensionFlag"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employeePensionFlag")},[e._v(e._s(l.employeePensionFlag?"有":"无"))])]}}])}),a("el-table-column",{attrs:{label:"病产假天数",sortable:"custom","min-width":e.getStringWidth("病产假天数"),align:"center",prop:"SickLeaveDays"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"sickLeaveDays")},[e._v(e._s(e.getDecimalValueOrDefault(l.sickLeaveDays)))])]}}])}),a("el-table-column",{attrs:{label:"休假类型",sortable:"custom","min-width":"300px",align:"center",prop:"SalaryLeaveDesc"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"salaryLeaveDesc")},[e._v(e._s(l.salaryLeaveDesc))])]}}])}),a("el-table-column",{attrs:{label:"基本工资小计",sortable:"custom","min-width":e.getStringWidth("基本工资小计"),"header-align":"left",align:"right",prop:"BasicSalarySubtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicSalarySubtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicSalarySubtotal)))])]}}])}),a("el-table-column",{attrs:{label:"岗资基数",sortable:"custom","min-width":e.getStringWidth("岗资基数"),"header-align":"left",align:"right",prop:"StationWageBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationWageBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationWageBase)))])]}}])}),a("el-table-column",{attrs:{label:"岗资",sortable:"custom","min-width":e.getStringWidth("岗资"),"header-align":"left",align:"right",prop:"StationWage"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationWage")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationWage)))])]}}])}),a("el-table-column",{attrs:{label:"薪资基数",sortable:"custom","min-width":e.getStringWidth("薪资基数"),"header-align":"left",align:"right",prop:"SalaryBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"salaryBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.salaryBase)))])]}}])}),a("el-table-column",{attrs:{label:"薪资",sortable:"custom","min-width":e.getStringWidth("薪资"),"header-align":"left",align:"right",prop:"SalaryMoney"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"salaryMoney")},[e._v(e._s(e.getDecimalValueOrDefault(l.salaryMoney)))])]}}])}),a("el-table-column",{attrs:{label:"基本工资其它加",sortable:"custom","min-width":e.getStringWidth("基本工资其它加"),"header-align":"left",align:"right",prop:"BasicSalaryOtherAdd"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicSalaryOtherAdd")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicSalaryOtherAdd)))])]}}])}),a("el-table-column",{attrs:{label:"津补贴小计",sortable:"custom","min-width":e.getStringWidth("津补贴小计"),"header-align":"left",align:"right",prop:"AllowanceSubtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"allowanceSubtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.allowanceSubtotal)))])]}}])}),a("el-table-column",{attrs:{label:"粮油补贴",sortable:"custom","min-width":e.getStringWidth("粮油补贴"),"header-align":"left",align:"right",prop:"GrainOilSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"grainOilSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.grainOilSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"上下班交通费基数",sortable:"custom","min-width":e.getStringWidth("上下班交通费基数"),"header-align":"left",align:"right",prop:"CommuteSubsidyBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"commuteSubsidyBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.commuteSubsidyBase)))])]}}])}),a("el-table-column",{attrs:{label:"上下班交通费",sortable:"custom","min-width":e.getStringWidth("上下班交通费"),"header-align":"left",align:"right",prop:"CommuteSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"commuteSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.commuteSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"护龄基数",sortable:"custom","min-width":e.getStringWidth("护龄基数"),"header-align":"left",align:"right",prop:"NursingBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"nursingBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.nursingBase)))])]}}])}),a("el-table-column",{attrs:{label:"护龄",sortable:"custom","min-width":e.getStringWidth("护龄"),align:"center",prop:"Nursing"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"nursing")},[e._v(e._s(e.getDecimalValueOrDefault(l.nursing)))])]}}])}),a("el-table-column",{attrs:{label:"独子",sortable:"custom","min-width":e.getStringWidth("独子"),align:"center",prop:"OnlyChild"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"onlyChild")},[e._v(e._s(e.getDecimalValueOrDefault(l.onlyChild)))])]}}])}),a("el-table-column",{attrs:{label:"独子补发",sortable:"custom","min-width":e.getStringWidth("独子补发"),"header-align":"left",align:"right",prop:"BackPayOnlyChild"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backPayOnlyChild")},[e._v(e._s(e.getDecimalValueOrDefault(l.backPayOnlyChild)))])]}}])}),a("el-table-column",{attrs:{label:"独子合计",sortable:"custom","min-width":e.getStringWidth("独子合计"),"header-align":"left",align:"right",prop:"OnlyChildTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"onlyChildTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.onlyChildTotal)))])]}}])}),a("el-table-column",{attrs:{label:"援外津贴",sortable:"custom","min-width":e.getStringWidth("援外津贴"),"header-align":"left",align:"right",prop:"ForeignAidAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"foreignAidAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.foreignAidAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"津补贴其它加",sortable:"custom","min-width":e.getStringWidth("津补贴其它加"),"header-align":"left",align:"right",prop:"AllowanceOtherAdd"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"allowanceOtherAdd")},[e._v(e._s(e.getDecimalValueOrDefault(l.allowanceOtherAdd)))])]}}])}),a("el-table-column",{attrs:{label:"绩效小计",sortable:"custom","min-width":e.getStringWidth("绩效小计"),"header-align":"left",align:"right",prop:"PerformanceSubtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"performanceSubtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.performanceSubtotal)))])]}}])}),a("el-table-column",{attrs:{label:"岗位津贴基数",sortable:"custom","min-width":e.getStringWidth("岗位津贴基数"),"header-align":"left",align:"right",prop:"StationAllowanceBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationAllowanceBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationAllowanceBase)))])]}}])}),a("el-table-column",{attrs:{label:"岗位津贴",sortable:"custom","min-width":e.getStringWidth("岗位津贴"),"header-align":"left",align:"right",prop:"StationAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"工作量津贴",sortable:"custom","min-width":e.getStringWidth("工作量津贴"),"header-align":"left",align:"right",prop:"WorkloadAllowance1"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"workloadAllowance1")},[e._v(e._s(e.getDecimalValueOrDefault(l.workloadAllowance1)))])]}}])}),a("el-table-column",{attrs:{label:"工作量津贴合计",sortable:"custom","min-width":e.getStringWidth("工作量津贴合计"),"header-align":"left",align:"right",prop:"WorkloadAllowance2Subtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"workloadAllowance2Subtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.workloadAllowance2Subtotal)))])]}}])}),a("el-table-column",{attrs:{label:"停车补贴",sortable:"custom","min-width":e.getStringWidth("停车补贴"),"header-align":"left",align:"right",prop:"ParkingSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"parkingSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.parkingSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"公派车贴补贴",sortable:"custom","min-width":e.getStringWidth("公派车贴补贴"),"header-align":"left",align:"right",prop:"OfficialCarAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"officialCarAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.officialCarAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"电话费",sortable:"custom","min-width":e.getStringWidth("电话费"),"header-align":"left",align:"right",prop:"TelephoneFee"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"telephoneFee")},[e._v(e._s(e.getDecimalValueOrDefault(l.telephoneFee)))])]}}])}),a("el-table-column",{attrs:{label:"电话费合计",sortable:"custom","min-width":e.getStringWidth("电话费合计"),"header-align":"left",align:"right",prop:"TelephoneFeeTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"telephoneFeeTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.telephoneFeeTotal)))])]}}])}),a("el-table-column",{attrs:{label:"长病假职工最低工资补助",sortable:"custom","min-width":e.getStringWidth("长病假职工最低工资补助"),"header-align":"left",align:"right",prop:"LongSickLeaveMinimumWageSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"longSickLeaveMinimumWageSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.longSickLeaveMinimumWageSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"博士后房帖",sortable:"custom","min-width":e.getStringWidth("博士后房帖"),"header-align":"left",align:"right",prop:"PostdoctoralHousingAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"postdoctoralHousingAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.postdoctoralHousingAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"卫生津贴",sortable:"custom","min-width":e.getStringWidth("卫生津贴"),"header-align":"left",align:"right",prop:"HealthAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"healthAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.healthAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"零星补节假日加班绩效",sortable:"custom","min-width":e.getStringWidth("零星补节假日加班绩效"),"header-align":"left",align:"right",prop:"OccasionalHolidayOvertimePerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occasionalHolidayOvertimePerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.occasionalHolidayOvertimePerformance)))])]}}])}),a("el-table-column",{attrs:{label:"节假日加班绩效",sortable:"custom","min-width":e.getStringWidth("节假日加班绩效"),"header-align":"left",align:"right",prop:"HolidayOvertimePerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"holidayOvertimePerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.holidayOvertimePerformance)))])]}}])}),a("el-table-column",{attrs:{label:"中夜班绩效",sortable:"custom","min-width":e.getStringWidth("中夜班绩效"),"header-align":"left",align:"right",prop:"MidnightShiftPerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"midnightShiftPerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.midnightShiftPerformance)))])]}}])}),a("el-table-column",{attrs:{label:"行政值班绩效",sortable:"custom","min-width":e.getStringWidth("行政值班绩效"),"header-align":"left",align:"right",prop:"AdministrativeDutyPerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"administrativeDutyPerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.administrativeDutyPerformance)))])]}}])}),a("el-table-column",{attrs:{label:"一二值班绩效",sortable:"custom","min-width":e.getStringWidth("一二值班绩效"),"header-align":"left",align:"right",prop:"FirstSecondDutyPerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"firstSecondDutyPerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.firstSecondDutyPerformance)))])]}}])}),a("el-table-column",{attrs:{label:"其它加",sortable:"custom","min-width":e.getStringWidth("其它加"),"header-align":"left",align:"right",prop:"OtherAdd"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"otherAdd")},[e._v(e._s(e.getDecimalValueOrDefault(l.otherAdd)))])]}}])}),a("el-table-column",{attrs:{label:"行政聘任补发",sortable:"custom","min-width":e.getStringWidth("行政聘任补发"),"header-align":"left",align:"right",prop:"WorkChangeReissue"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"workChangeReissue")},[e._v(e._s(e.getDecimalValueOrDefault(l.workChangeReissue)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣养扣",sortable:"custom","min-width":e.getStringWidth("退还多扣养扣"),"header-align":"left",align:"right",prop:"ReturnOverDeductPension"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductPension")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductPension)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣医疗",sortable:"custom","min-width":e.getStringWidth("退还多扣医疗"),"header-align":"left",align:"right",prop:"ReturnOverDeductMedical"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductMedical")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductMedical)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣失业",sortable:"custom","min-width":e.getStringWidth("退还多扣失业"),"header-align":"left",align:"right",prop:"ReturnOverDeductUnemployment"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductUnemployment")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductUnemployment)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣公扣",sortable:"custom","min-width":e.getStringWidth("退还多扣公扣"),"header-align":"left",align:"right",prop:"ReturnOverDeductPublic"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductPublic")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductPublic)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣补充公积金",sortable:"custom","min-width":e.getStringWidth("退还多扣补充公积金"),"header-align":"left",align:"right",prop:"ReturnOverDeductSupplementaryHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductSupplementaryHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductSupplementaryHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣年金",sortable:"custom","min-width":e.getStringWidth("退还多扣年金"),"header-align":"left",align:"right",prop:"ReturnOverDeductOccupationalAnnuity"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductOccupationalAnnuity")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductOccupationalAnnuity)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣出国人员扣款",sortable:"custom","min-width":e.getStringWidth("退还多扣出国人员扣款"),"header-align":"left",align:"right",prop:"ReturnOverDeductDeductionForForeigners"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductDeductionForForeigners")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductDeductionForForeigners)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣病假工资",sortable:"custom","min-width":e.getStringWidth("退还多扣病假工资"),"header-align":"left",align:"right",prop:"ReturnOverDeductSickLeaveSalary"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductSickLeaveSalary")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductSickLeaveSalary)))])]}}])}),a("el-table-column",{attrs:{label:"定级补发",sortable:"custom","min-width":e.getStringWidth("定级补发"),"header-align":"left",align:"right",prop:"BackPayGrading"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backPayGrading")},[e._v(e._s(e.getDecimalValueOrDefault(l.backPayGrading)))])]}}])}),a("el-table-column",{attrs:{label:"应发工资",sortable:"custom","min-width":e.getStringWidth("应发工资"),"header-align":"left",align:"right",prop:"GrossSalary"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"grossSalary")},[e._v(e._s(e.getDecimalValueOrDefault(l.grossSalary)))])]}}])}),a("el-table-column",{attrs:{label:"应发工资（申报个税用）",sortable:"custom","min-width":e.getStringWidth("应发工资（申报个税用）"),"header-align":"left",align:"right",prop:"GrossSalaryForTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"grossSalaryForTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.grossSalaryForTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"养扣",sortable:"custom","min-width":e.getStringWidth("养扣"),"header-align":"left",align:"right",prop:"PensionDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"补扣养扣",sortable:"custom","min-width":e.getStringWidth("补扣养扣"),"header-align":"left",align:"right",prop:"BackDeductionPension"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionPension")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionPension)))])]}}])}),a("el-table-column",{attrs:{label:"养扣合计",sortable:"custom","min-width":e.getStringWidth("养扣合计"),"header-align":"left",align:"right",prop:"PensionDeductionTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionDeductionTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionDeductionTotal)))])]}}])}),a("el-table-column",{attrs:{label:"医疗保险",sortable:"custom","min-width":e.getStringWidth("医疗保险"),"header-align":"left",align:"right",prop:"MedicalInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"补扣医疗保险",sortable:"custom","min-width":e.getStringWidth("补扣医疗保险"),"header-align":"left",align:"right",prop:"BackDeductionMedicalInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionMedicalInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionMedicalInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"医疗保险合计",sortable:"custom","min-width":e.getStringWidth("医疗保险合计"),"header-align":"left",align:"right",prop:"MedicalInsuranceTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalInsuranceTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalInsuranceTotal)))])]}}])}),a("el-table-column",{attrs:{label:"失业保险",sortable:"custom","min-width":e.getStringWidth("失业保险"),"header-align":"left",align:"right",prop:"UnemploymentInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"补扣失业保险",sortable:"custom","min-width":e.getStringWidth("补扣失业保险"),"header-align":"left",align:"right",prop:"BackDeductionUnemploymentInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionUnemploymentInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionUnemploymentInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"失业保险合计",sortable:"custom","min-width":e.getStringWidth("失业保险合计"),"header-align":"left",align:"right",prop:"UnemploymentInsuranceTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentInsuranceTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentInsuranceTotal)))])]}}])}),a("el-table-column",{attrs:{label:"基本公积金",sortable:"custom","min-width":e.getStringWidth("基本公积金"),"header-align":"left",align:"right",prop:"BasicHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"补扣基本公积金",sortable:"custom","min-width":e.getStringWidth("补扣基本公积金"),"header-align":"left",align:"right",prop:"BackDeductionBasicHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionBasicHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionBasicHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"基本公积金合计",sortable:"custom","min-width":e.getStringWidth("基本公积金合计"),"header-align":"left",align:"right",prop:"HousingFundTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"housingFundTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.housingFundTotal)))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金",sortable:"custom","min-width":e.getStringWidth("补充公积金"),"header-align":"left",align:"right",prop:"SupplementaryHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFund")},[e._v(e._s(parseFloat(l.supplementaryHousingFund).toFixed(0)))])]}}])}),a("el-table-column",{attrs:{label:"补扣补充公积金",sortable:"custom","min-width":e.getStringWidth("补扣补充公积金"),"header-align":"left",align:"right",prop:"BackDeductionSupplementaryHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionSupplementaryHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionSupplementaryHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金合计",sortable:"custom","min-width":e.getStringWidth("补充公积金合计"),"header-align":"left",align:"right",prop:"SupplementaryHousingFundTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFundTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.supplementaryHousingFundTotal)))])]}}])}),a("el-table-column",{attrs:{label:"职业年金",sortable:"custom","min-width":e.getStringWidth("职业年金"),"header-align":"left",align:"right",prop:"OccupationalAnnuity"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occupationalAnnuity")},[e._v(e._s(e.getDecimalValueOrDefault(l.occupationalAnnuity)))])]}}])}),a("el-table-column",{attrs:{label:"补扣职业年金",sortable:"custom","min-width":e.getStringWidth("补扣职业年金"),"header-align":"left",align:"right",prop:"BackDeductionOccupationalAnnuity"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionOccupationalAnnuity")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionOccupationalAnnuity)))])]}}])}),a("el-table-column",{attrs:{label:"职业年金合计",sortable:"custom","min-width":e.getStringWidth("职业年金合计"),"header-align":"left",align:"right",prop:"OccupationalAnnuityTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occupationalAnnuityTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.occupationalAnnuityTotal)))])]}}])}),a("el-table-column",{attrs:{label:"会费",sortable:"custom","min-width":e.getStringWidth("会费"),"header-align":"left",align:"right",prop:"MembershipFee"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"membershipFee")},[e._v(e._s(parseFloat(l.membershipFee).toFixed(1)))])]}}])}),a("el-table-column",{attrs:{label:"补扣会费",sortable:"custom","min-width":e.getStringWidth("补扣会费"),"header-align":"left",align:"right",prop:"BackDeductionMembershipFee"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionMembershipFee")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionMembershipFee)))])]}}])}),a("el-table-column",{attrs:{label:"会费合计",sortable:"custom","min-width":e.getStringWidth("会费合计"),"header-align":"left",align:"right",prop:"MembershipFeeTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"membershipFeeTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.membershipFeeTotal)))])]}}])}),a("el-table-column",{attrs:{label:"房租",sortable:"custom","min-width":e.getStringWidth("房租"),"header-align":"left",align:"right",prop:"Rent"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"rent")},[e._v(e._s(e.getDecimalValueOrDefault(l.rent)))])]}}])}),a("el-table-column",{attrs:{label:"代扣税金",sortable:"custom","min-width":e.getStringWidth("代扣税金"),"header-align":"left",align:"right",prop:"TaxWithholding"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"taxWithholding")},[e._v(e._s(e.getDecimalValueOrDefault(l.taxWithholding)))])]}}])}),a("el-table-column",{attrs:{label:"其它扣",sortable:"custom","min-width":e.getStringWidth("其它扣"),"header-align":"left",align:"right",prop:"OtherDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"otherDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.otherDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"长病假补扣",sortable:"custom","min-width":e.getStringWidth("长病假补扣"),"header-align":"left",align:"right",prop:"BackDeductionLongSickLeave"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionLongSickLeave")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionLongSickLeave)))])]}}])}),a("el-table-column",{attrs:{label:"扣出国人员扣款",sortable:"custom","min-width":e.getStringWidth("扣出国人员扣款"),"header-align":"left",align:"right",prop:"DeductionForForeigners"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"deductionForForeigners")},[e._v(e._s(e.getDecimalValueOrDefault(l.deductionForForeigners)))])]}}])}),a("el-table-column",{attrs:{label:"扣款合计",sortable:"custom","min-width":e.getStringWidth("扣款合计"),"header-align":"left",align:"right",prop:"DeductionTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"deductionTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.deductionTotal)))])]}}])}),a("el-table-column",{attrs:{label:"实发工资",sortable:"custom","min-width":e.getStringWidth("实发工资"),"header-align":"left",align:"right",prop:"NetSalary"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"netSalary")},[e._v(e._s(e.getDecimalValueOrDefault(l.netSalary)))])]}}])}),a("el-table-column",{attrs:{label:"养扣基础数据",sortable:"custom","min-width":e.getStringWidth("养扣基础数据"),"header-align":"left",align:"right",prop:"BasicDataDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicDataDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicDataDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"医疗保险基础数据",sortable:"custom","min-width":e.getStringWidth("医疗保险基础数据"),"header-align":"left",align:"right",prop:"MedicalInsuranceBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalInsuranceBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalInsuranceBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"失业保险基础数据",sortable:"custom","min-width":e.getStringWidth("失业保险基础数据"),"header-align":"left",align:"right",prop:"UnemploymentInsuranceBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentInsuranceBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentInsuranceBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"公积金基础数据",sortable:"custom","min-width":e.getStringWidth("公积金基础数据"),"header-align":"left",align:"right",prop:"HousingFundBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"housingFundBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.housingFundBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金基础数据",sortable:"custom","min-width":e.getStringWidth("补充公积金基础数据"),"header-align":"left",align:"right",prop:"SupplementaryHousingFundBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFundBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.supplementaryHousingFundBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"年金基础数据",sortable:"custom","min-width":e.getStringWidth("年金基础数据"),"header-align":"left",align:"right",prop:"PensionBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"养老（申报个税用）",sortable:"custom","min-width":e.getStringWidth("养老（申报个税用）"),"header-align":"left",align:"right",prop:"PensionTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"医疗（申报个税用）",sortable:"custom","min-width":e.getStringWidth("医疗（申报个税用）"),"header-align":"left",align:"right",prop:"MedicalTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"失业（申报个税用）",sortable:"custom","min-width":e.getStringWidth("失业（申报个税用）"),"header-align":"left",align:"right",prop:"UnemploymentTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"公扣（申报个税用）",sortable:"custom","min-width":e.getStringWidth("公扣（申报个税用）"),"header-align":"left",align:"right",prop:"PublicTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"publicTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.publicTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"年金（申报个税用）",sortable:"custom","min-width":e.getStringWidth("年金（申报个税用）"),"header-align":"left",align:"right",prop:"OccupationalAnnuityTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occupationalAnnuityTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.occupationalAnnuityTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"申报个税四金合计",sortable:"custom","min-width":e.getStringWidth("申报个税四金合计"),"header-align":"left",align:"right",prop:"TaxDeclarationFourGold"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"taxDeclarationFourGold")},[e._v(e._s(e.getDecimalValueOrDefault(l.taxDeclarationFourGold)))])]}}])}),a("el-table-column",{attrs:{label:"基本工资合计",sortable:"custom","min-width":e.getStringWidth("基本工资合计"),"header-align":"left",align:"right",prop:"BasicSalaryTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicSalaryTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicSalaryTotal)))])]}}])}),a("el-table-column",{attrs:{label:"津贴补贴合计",sortable:"custom","min-width":e.getStringWidth("津贴补贴合计"),"header-align":"left",align:"right",prop:"AllowanceTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"allowanceTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.allowanceTotal)))])]}}])}),a("el-table-column",{attrs:{label:"对个人及家庭补助",sortable:"custom","min-width":e.getStringWidth("对个人及家庭补助"),"header-align":"left",align:"right",prop:"OnlyChildTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"onlyChildTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.onlyChildTotal)))])]}}])}),a("el-table-column",{attrs:{label:"奖金合计",sortable:"custom","min-width":e.getStringWidth("奖金合计"),"header-align":"left",align:"right",prop:"BonusTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"bonusTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.bonusTotal)))])]}}])}),a("el-table-column",{attrs:{label:"年终一次性奖励",sortable:"custom","min-width":e.getStringWidth("年终一次性奖励"),"header-align":"left",align:"right",prop:"YearEndBonus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"yearEndBonus")},[e._v(e._s(e.getDecimalValueOrDefault(l.yearEndBonus)))])]}}])}),a("el-table-column",{attrs:{label:"其他各项奖金",sortable:"custom","min-width":e.getStringWidth("其他各项奖金"),"header-align":"left",align:"right",prop:"OtherBonusesTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"otherBonusesTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.otherBonusesTotal)))])]}}])}),a("el-table-column",{attrs:{label:"实发奖金",sortable:"custom","min-width":e.getStringWidth("实发奖金"),"header-align":"left",align:"right",prop:"ActualBonus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"actualBonus")},[e._v(e._s(e.getDecimalValueOrDefault(l.actualBonus)))])]}}])}),a("el-table-column",{attrs:{label:"税前奖金",sortable:"custom","min-width":e.getStringWidth("税前奖金"),"header-align":"left",align:"right",prop:"PreTaxBonus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"preTaxBonus")},[e._v(e._s(e.getDecimalValueOrDefault(l.preTaxBonus)))])]}}])}),a("el-table-column",{attrs:{label:"奖金扣税",sortable:"custom","min-width":e.getStringWidth("奖金扣税"),"header-align":"left",align:"right",prop:"BonusTax"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"bonusTax")},[e._v(e._s(e.getDecimalValueOrDefault(l.bonusTax)))])]}}])}),a("el-table-column",{attrs:{label:"累计收入额",sortable:"custom","min-width":e.getStringWidth("累计收入额"),"header-align":"left",align:"right",prop:"AccumulatedIncome"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedIncome")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedIncome)))])]}}])}),a("el-table-column",{attrs:{label:"累计专项扣除",sortable:"custom","min-width":e.getStringWidth("累计专项扣除"),"header-align":"left",align:"right",prop:"AccumulatedDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"累计专项附加扣除",sortable:"custom","min-width":e.getStringWidth("累计专项附加扣除"),"header-align":"left",align:"right",prop:"AccumulatedAdditionalDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedAdditionalDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedAdditionalDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"累计应纳税所得额",sortable:"custom","min-width":e.getStringWidth("累计应纳税所得额"),"header-align":"left",align:"right",prop:"AccumulatedTaxableIncome"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedTaxableIncome")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedTaxableIncome)))])]}}])}),a("el-table-column",{attrs:{label:"累计已预扣税额",sortable:"custom","min-width":e.getStringWidth("累计已预扣税额"),"header-align":"left",align:"right",prop:"AccumulatedWithheldTax"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedWithheldTax")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedWithheldTax)))])]}}])}),a("el-table-column",{attrs:{label:"计税",sortable:"custom","min-width":e.getStringWidth("计税"),"header-align":"left",align:"right",prop:"TaxCalculation"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"taxCalculation")},[e._v(e._s(l.taxCalculation))])]}}])}),a("el-table-column",{attrs:{label:"应发总计",sortable:"custom","min-width":e.getStringWidth("应发总计"),"header-align":"left",align:"right",prop:"TotalPayable"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"totalPayable")},[e._v(e._s(e.getDecimalValueOrDefault(l.totalPayable)))])]}}])}),a("el-table-column",{attrs:{label:"工行帐号",sortable:"custom","min-width":e.getStringWidth("工行帐号"),align:"left",prop:"ICBCAccount"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"iCBCAccount")},[e._v(e._s(l.iCBCAccount))])]}}])}),a("el-table-column",{attrs:{fixed:"right",label:"操作",align:"left","header-align":"center",width:"150","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[e.isEdit?a("el-button",{staticStyle:{"margin-left":"35px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.editEmployeeSalaryDetailDialog(l,!0)}}},[e._v(" 编辑 ")]):e._e(),e.isEdit?e._e():a("el-button",{staticStyle:{"margin-left":"35px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-view",size:"mini"},on:{click:function(t){return e.editEmployeeSalaryDetailDialog(l,!1)}}},[e._v(" 显示 ")])]}}])}),a("employeeTableColumns")],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),a("editDialog",{ref:"editDialog",on:{refreshData:e.getPageList}}),a("employeeSalaryRecordDialog",{ref:"employeeSalaryRecordDialog"})],1)},R=[],q={components:{editDialog:b,employeeSalaryRecordDialog:k,employeeTableColumns:v["a"]},data:function(){return{isSalaryMonthComplete:!0,showDialog:!1,isEdit:!1,title:"财务数据",pageList:[],groupList:[],salaryData:{},lastMonthData:{},salaryId:"",listQuery:{queryCondition:{},salaryId:"",total:1,pageIndex:1,pageSize:10},searchList:[{id:""}],lastMonth:!1,listLoading:!1,dialogEditEmpSalaryVisible:!1,dataColumns:[{value:"1",label:"旧社保基数",type:"System.Decimal",columnName:"OldSocialSecurityBase"},{value:"2",label:"社保基数",type:"System.Decimal",columnName:"SocialSecurityBase"},{value:"3",label:"旧公积金基数",type:"System.Decimal",columnName:"OldHousingFundBase"},{value:"4",label:"公积金基数",type:"System.Decimal",columnName:"HousingFundBase"},{value:"5",label:"岗位津贴",type:"System.Decimal",columnName:"StationAllowance"},{value:"7",label:"工龄",type:"System.Int32",columnName:"SocietyAge"},{value:"8",label:"补充公积金标记",type:"System.Boolean",columnName:"SupplementaryHousingFundFlag"},{value:"9",label:"职员年金标记",type:"System.Boolean",columnName:"EmployeePensionFlag"},{value:"10",label:"病假天数",type:"System.Decimal",columnName:"SickLeaveDays"},{value:"11",label:"休假类型",type:"System.String",columnName:"SalaryLeaveDesc"},{value:"12",label:"基本工资小计",type:"System.Decimal",columnName:"BasicSalarySubtotal"},{value:"13",label:"岗资基数",type:"System.Decimal",columnName:"StationWageBase"},{value:"14",label:"岗资",type:"System.Decimal",columnName:"StationWage"},{value:"15",label:"薪资基数",type:"System.Decimal",columnName:"SalaryBase"},{value:"16",label:"薪资",type:"System.Decimal",columnName:"SalaryMoney"},{value:"17",label:"基本工资其它加",type:"System.Decimal",columnName:"BasicSalaryOtherAdd"},{value:"18",label:"津补贴小计",type:"System.Decimal",columnName:"AllowanceSubtotal"},{value:"19",label:"粮油补贴",type:"System.Decimal",columnName:"GrainOilSubsidy"},{value:"20",label:"上下班交通费基数",type:"System.Decimal",columnName:"CommuteSubsidyBase"},{value:"21",label:"上下班交通费",type:"System.Decimal",columnName:"CommuteSubsidy"},{value:"22",label:"护龄基数",type:"System.Decimal",columnName:"NursingBase"},{value:"23",label:"护龄",type:"System.Decimal",columnName:"Nursing"},{value:"24",label:"独子",type:"System.Decimal",columnName:"OnlyChild"},{value:"25",label:"独子补发",type:"System.Decimal",columnName:"BackPayOnlyChild"},{value:"26",label:"独子合计",type:"System.Decimal",columnName:"OnlyChildTotal"},{value:"27",label:"援外津贴",type:"System.Decimal",columnName:"ForeignAidAllowance"},{value:"28",label:"援滇津贴",type:"System.Decimal",columnName:"YunnanAidAllowance"},{value:"29",label:"津补贴其它加",type:"System.Decimal",columnName:"AllowanceOtherAdd"},{value:"30",label:"绩效小计",type:"System.Decimal",columnName:"PerformanceSubtotal"},{value:"31",label:"岗位津贴基数",type:"System.Decimal",columnName:"StationAllowanceBase"},{value:"32",label:"工作量津贴1",type:"System.Decimal",columnName:"WorkloadAllowance1"},{value:"33",label:"工作量津贴2小计",type:"System.Decimal",columnName:"WorkloadAllowance2Subtotal"},{value:"34",label:"停车补贴",type:"System.Decimal",columnName:"ParkingSubsidy"},{value:"35",label:"公派车贴补贴",type:"System.Decimal",columnName:"OfficialCarAllowance"},{value:"36",label:"电话费",type:"System.Decimal",columnName:"TelephoneFee"},{value:"37",label:"补发电话费",type:"System.Decimal",columnName:"BackPayTelephoneFee"},{value:"38",label:"电话费合计",type:"System.Decimal",columnName:"TelephoneFeeTotal"},{value:"39",label:"长病假职工最低工资补助",type:"System.Decimal",columnName:"LongSickLeaveMinimumWageSubsidy"},{value:"40",label:"博士后房帖",type:"System.Decimal",columnName:"PostdoctoralHousingAllowance"},{value:"41",label:"卫生津贴",type:"System.Decimal",columnName:"HealthAllowance"},{value:"42",label:"零星补节假日加班绩效",type:"System.Decimal",columnName:"OccasionalHolidayOvertimePerformance"},{value:"43",label:"节假日加班绩效",type:"System.Decimal",columnName:"HolidayOvertimePerformance"},{value:"44",label:"中夜班绩效",type:"System.Decimal",columnName:"MidnightShiftPerformance"},{value:"45",label:"行政值班绩效",type:"System.Decimal",columnName:"AdministrativeDutyPerformance"},{value:"46",label:"一二值班绩效",type:"System.Decimal",columnName:"FirstSecondDutyPerformance"},{value:"47",label:"急诊拖后值班绩效",type:"System.Decimal",columnName:"EmergencyDelayDutyPerformance"},{value:"48",label:"其它加",type:"System.Decimal",columnName:"OtherAdd"},{value:"52",label:"工改补发",type:"System.Decimal",columnName:"WorkChangeReissue"},{value:"53",label:"退还多扣养扣",type:"System.Decimal",columnName:"ReturnOverDeductPension"},{value:"54",label:"退还多扣医疗",type:"System.Decimal",columnName:"ReturnOverDeductMedical"},{value:"55",label:"退还多扣失业",type:"System.Decimal",columnName:"ReturnOverDeductUnemployment"},{value:"56",label:"退还多扣公扣",type:"System.Decimal",columnName:"ReturnOverDeductPublic"},{value:"57",label:"退还多扣补充公积金",type:"System.Decimal",columnName:"ReturnOverDeductSupplementaryHousingFund"},{value:"58",label:"退还多扣年金",type:"System.Decimal",columnName:"ReturnOverDeductOccupationalAnnuity"},{value:"59",label:"退还多扣出国人员扣款",type:"System.Decimal",columnName:"ReturnOverDeductDeductionForForeigners"},{value:"60",label:"退还多扣病假工资",type:"System.Decimal",columnName:"ReturnOverDeductSickLeaveSalary"},{value:"61",label:"长病假、待退休绩效奖",type:"System.Decimal",columnName:"LongSickLeaveRetirementPerformanceBonus"},{value:"62",label:"定级补发",type:"System.Decimal",columnName:"BackPayGrading"},{value:"63",label:"应发工资",type:"System.Decimal",columnName:"GrossSalary"},{value:"64",label:"应发工资（申报个税用）",type:"System.Decimal",columnName:"GrossSalaryForTaxDeclaration"},{value:"65",label:"养扣",type:"System.Decimal",columnName:"PensionDeduction"},{value:"66",label:"补扣养扣",type:"System.Decimal",columnName:"BackDeductionPension"},{value:"67",label:"养扣合计",type:"System.Decimal",columnName:"PensionDeductionTotal"},{value:"68",label:"医疗保险",type:"System.Decimal",columnName:"MedicalInsurance"},{value:"69",label:"补扣医疗保险",type:"System.Decimal",columnName:"BackDeductionMedicalInsurance"},{value:"70",label:"医疗保险合计",type:"System.Decimal",columnName:"MedicalInsuranceTotal"},{value:"71",label:"失业保险",type:"System.Decimal",columnName:"UnemploymentInsurance"},{value:"72",label:"补扣失业保险",type:"System.Decimal",columnName:"BackDeductionUnemploymentInsurance"},{value:"73",label:"失业保险合计",type:"System.Decimal",columnName:"UnemploymentInsuranceTotal"},{value:"74",label:"基本公积金",type:"System.Decimal",columnName:"BasicHousingFund"},{value:"75",label:"补扣基本公积金",type:"System.Decimal",columnName:"BackDeductionBasicHousingFund"},{value:"76",label:"基本公积金合计",type:"System.Decimal",columnName:"HousingFundTotal"},{value:"77",label:"补充公积金",type:"System.Decimal",columnName:"SupplementaryHousingFund"},{value:"78",label:"补扣补充公积金",type:"System.Decimal",columnName:"BackDeductionSupplementaryHousingFund"},{value:"79",label:"补充公积金合计",type:"System.Decimal",columnName:"SupplementaryHousingFundTotal"},{value:"80",label:"职业年金",type:"System.Decimal",columnName:"OccupationalAnnuity"},{value:"81",label:"补扣职业年金",type:"System.Decimal",columnName:"BackDeductionOccupationalAnnuity"},{value:"82",label:"职业年金合计",type:"System.Decimal",columnName:"OccupationalAnnuityTotal"},{value:"83",label:"会费",type:"System.Decimal",columnName:"MembershipFee"},{value:"84",label:"补扣会费",type:"System.Decimal",columnName:"BackDeductionMembershipFee"},{value:"85",label:"会费合计",type:"System.Decimal",columnName:"MembershipFeeTotal"},{value:"86",label:"房租",type:"System.Decimal",columnName:"Rent"},{value:"87",label:"病假工资",type:"System.Decimal",columnName:"SickLeaveSalary"},{value:"88",label:"补扣病假",type:"System.Decimal",columnName:"BackDeductionSickLeave"},{value:"89",label:"病假工资合计",type:"System.Decimal",columnName:"SickLeaveSalaryTotal"},{value:"90",label:"代扣税金",type:"System.Decimal",columnName:"TaxWithholding"},{value:"91",label:"其它扣",type:"System.Decimal",columnName:"OtherDeduction"},{value:"92",label:"长病假补扣",type:"System.Decimal",columnName:"BackDeductionLongSickLeave"},{value:"93",label:"综合险",type:"System.Decimal",columnName:"ComprehensiveInsurance"},{value:"94",label:"户口挂靠费",type:"System.Decimal",columnName:"HukouHostingFee"},{value:"100",label:"扣款合计",type:"System.Decimal",columnName:"DeductionTotal"},{value:"101",label:"实发工资",type:"System.Decimal",columnName:"NetSalary"},{value:"102",label:"房贴2016",type:"System.Decimal",columnName:"HousingAllowance2016"},{value:"103",label:"养扣基础数据",type:"System.Decimal",columnName:"BasicDataDeduction"},{value:"104",label:"医疗保险基础数据",type:"System.Decimal",columnName:"MedicalInsuranceBasicData"},{value:"105",label:"失业保险基础数据",type:"System.Decimal",columnName:"UnemploymentInsuranceBasicData"},{value:"106",label:"公积金基础数据",type:"System.Decimal",columnName:"HousingFundBasicData"},{value:"107",label:"补充公积金基础数据",type:"System.Decimal",columnName:"SupplementaryHousingFundBasicData"},{value:"108",label:"年金基础数据",type:"System.Decimal",columnName:"PensionBasicData"},{value:"109",label:"养老（申报个税用）",type:"System.Decimal",columnName:"PensionTaxDeclaration"},{value:"110",label:"医疗（申报个税用）",type:"System.Decimal",columnName:"MedicalTaxDeclaration"},{value:"111",label:"失业（申报个税用）",type:"System.Decimal",columnName:"UnemploymentTaxDeclaration"},{value:"112",label:"公扣（申报个税用）",type:"System.Decimal",columnName:"PublicTaxDeclaration"},{value:"113",label:"年金（申报个税用）",type:"System.Decimal",columnName:"OccupationalAnnuityTaxDeclaration"},{value:"114",label:"申报个税四金合计",type:"System.Decimal",columnName:"TaxDeclarationFourGold"},{value:"115",label:"基本工资合计",type:"System.Decimal",columnName:"BasicSalaryTotal"},{value:"116",label:"津贴补贴合计",type:"System.Decimal",columnName:"AllowanceTotal"},{value:"117",label:"对个人及家庭补助",type:"System.Decimal",columnName:"PersonalAndFamilySubsidy"},{value:"118",label:"加班工资",type:"System.Decimal",columnName:"OvertimePay"},{value:"119",label:"奖金合计",type:"System.Decimal",columnName:"BonusTotal"},{value:"120",label:"十三月工资（应发）",type:"System.Decimal",columnName:"ThirteenthMonthSalary"},{value:"121",label:"计税工资2（12月计税工资与十三月工资应发之和）",type:"System.Decimal",columnName:"TaxableWage2"},{value:"122",label:"代扣税金2（以计税工资2计算出的代扣税金）",type:"System.Decimal",columnName:"TaxWithholding2"},{value:"123",label:"十三月工资代扣税金",type:"System.Decimal",columnName:"ThirteenthMonthTaxWithholding"},{value:"124",label:"十三月工资（实发）",type:"System.Decimal",columnName:"ThirteenthMonthSalaryNet"},{value:"125",label:"年终一次性奖励",type:"System.Decimal",columnName:"YearEndBonus"},{value:"126",label:"其他各项奖金",type:"System.Decimal",columnName:"OtherBonusesTotal"},{value:"127",label:"实发奖金",type:"System.Decimal",columnName:"ActualBonus"},{value:"128",label:"税前奖金",type:"System.Decimal",columnName:"PreTaxBonus"},{value:"129",label:"奖金扣税",type:"System.Decimal",columnName:"BonusTax"},{value:"130",label:"累计收入额",type:"System.Decimal",columnName:"AccumulatedIncome"},{value:"131",label:"累计专项扣除",type:"System.Decimal",columnName:"AccumulatedDeduction"},{value:"132",label:"累计专项附加扣除",type:"System.Decimal",columnName:"AccumulatedAdditionalDeduction"},{value:"133",label:"累计应纳税所得额",type:"System.Decimal",columnName:"AccumulatedTaxableIncome"},{value:"134",label:"累计已预扣税额",type:"System.Decimal",columnName:"AccumulatedWithheldTax"},{value:"135",label:"待个人汇算时由税务退还个税",type:"System.Decimal",columnName:"TaxToBeReturnedUponPersonalSettlementByTaxAuthority"},{value:"136",label:"计税工资",type:"System.Decimal",columnName:"TaxableWage"},{value:"137",label:"其他工资",type:"System.Decimal",columnName:"OtherWages"}],selectConditionOptions:[],signDropdown:[{value:!0,label:"有"},{value:!1,label:"无"}]}},created:function(){this.initDialog()},mounted:function(){},methods:{initDialog:function(){this.salaryId=this.$route.query.salaryId,this.salaryId&&(this.showDialog=!0,this.listQuery.salaryId=this.salaryId,this.getSalary(),this.getPageList(),this.loadConditions())},employeeSalaryRecord:function(){this.$refs.employeeSalaryRecordDialog.initDialog(this.salaryId)},loadConditions:function(){var e=this;u["a"].getEnumInfos({enumType:"Operations"}).then((function(t){e.selectConditionOptions=t.data.datas})).catch((function(e){console.log(e)}))},getSalary:function(){var e=this;r["a"].getSalary({id:this.listQuery.salaryId}).then((function(t){t.succeed?(e.salaryData=t.data,2===e.salaryData.enumStatus?e.isEdit=!0:e.isEdit=!1):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))},closeDialog:function(){this.pageList=[],this.showDialog=!1},search:function(){this.listQuery.pageIndex=1,this.getPageList(),this.clearCheckboxes()},sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.$delete(this.listQuery,"order"),this.search()},getPageList:function(){var e=this;this.searchList.length=0,this.pageList=[],this.listLoading=!0,this.listQuery.entityColumn&&""!==this.listQuery.entityColumn&&this.listQuery.queryCondition.EnumOperation?(this.listQuery.queryCondition.Keywords||(this.listQuery.queryCondition.Keywords=""),this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.columnName,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.ConditionList=[this.listQuery.queryCondition]):(this.listQuery.ConditionList=[],this.listQuery.queryCondition={}),this.listQuery.enumSalaryStatusType=2,r["a"].querySalaryDetail(this.listQuery).then((function(t){t.succeed?(e.pageList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1}))},clearCheckboxes:function(){this.pageList.forEach((function(e){if(!e.id.includes("_lastMonth")){var t=document.getElementById("choose");t&&(t.checked=!1)}}))},sarechLastMonthData:function(e){var t=this;if(this.searchList.includes(e.id)){this.searchList.pop(e.id);var a=this.pageList.findIndex((function(t){return t.id.includes("_lastMonth")&&t.id.split("_lastMonth")[0]===e.id}));-1!==a&&this.pageList.splice(a,1)}else r["a"].queryLastMonthEmployeeSalary({id:e.id}).then((function(a){if(a.succeed){t.lastMonthData=a.data,t.searchList.push(e.id);var l=Object(s["a"])({},e);Object.assign(l,t.lastMonthData),l.id=e.id+"_lastMonth";var i=t.pageList.indexOf(e);t.pageList.splice(i+1,0,l)}else t.$notice.resultTip(a)})).catch((function(e){console.log(e)}))},editEmployeeSalaryDetailDialog:function(e,t){this.$refs.editDialog.salaryId=this.salaryId,this.$refs.editDialog.initDialog(e,t)},onRefresh:function(){this.isEdit&&this.getPageList(),this.dialogEditEmpSalaryVisible=!1},exportData:function(){var e=this;this.searchList.length=0,this.pageList=[],this.listLoading=!0,this.listQuery.entityColumn&&""!==this.listQuery.entityColumn&&this.listQuery.queryCondition.EnumOperation?(this.listQuery.queryCondition.Keywords||(this.listQuery.queryCondition.Keywords=""),this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.columnName,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.ConditionList=[this.listQuery.queryCondition]):(this.listQuery.ConditionList=[],this.listQuery.queryCondition={}),r["a"].exportSalaryDetail(this.listQuery).then((function(t){var l=a("19de"),i="员工薪资"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?l(t.data,i):l(t,i),e.getPageList()}))},exportHRData:function(){var e=this;this.searchList.length=0,this.pageList=[],this.listLoading=!0,r["a"].exportHRData(this.listQuery).then((function(t){var l=a("19de"),i="人事数据"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?l(t.data,i):l(t,i),e.getPageList()}))},getReissueDeduction:function(e,t){var a=e.find((function(e){return e.groupId===t}));return a?a.reissueDeduction:""},getDecimalValueOrDefault:function(e){return 0===e||void 0===e?"":e<0?Math.abs(e).toFixed(2):e.toFixed(2)},getStyle:function(e,t,a){if(void 0!==e&&void 0!==t){var l=e[a],i=t[a],n=e.uid,o=t.uid;if(n===o&&void 0!==l&&void 0!==i&&l!==i)return{color:"red"}}},approvalReturn:function(){var e=this;this.$confirm("确定退回人事吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.salaryData.enumStatus=1,r["a"].approvalSalary(e.salaryData).then((function(t){t.succeed?(e.$notice.message("退回成功","success"),window.close(),e.$emit("refreshData")):t.succeed||e.$notice.message("退回失败，请联系管理员","info")})).catch((function(e){console.log(e)}))})).catch((function(e){console.log(e)}))},approvalPass:function(){var e=this;this.$confirm("确定审批通过吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.salaryData.enumStatus=3,r["a"].approvalSalary(e.salaryData).then((function(t){t.succeed?(e.$notice.message("审批成功","success"),window.close(),e.$emit("refreshData")):t.succeed||e.$notice.message("审批失败，请联系管理员","info")})).catch((function(e){console.log(e)}))})).catch((function(e){console.log(e)}))},getStringWidth:function(e){var t=0;return e.length<3?"auto":3===e.length?(t=30*e.length,t+"px"):4===e.length||5===e.length?(t=25*e.length,t+"px"):e.length>5?(t=20*e.length,t+"px"):void 0},downloadexceltemplate:function(){hRManageApi.downlodaImportExcelTemplate({type:"importSalaryExtendedDetails"}).then((function(e){var t=a("19de"),l="SalaryExtendedDetailsTemplate.xlsx";e.data?t(e.data,l):t(e,l)})).catch((function(e){}))},importExcel:function(e){var t=this,a=e.file,l=new FormData;r["a"].importSalaryExtendedDetails(a,l,this.salaryId,2).then((function(e){e.succeed&&(t.$message({message:"导入成功",type:"success"}),t.search())})).catch((function(e){t.search()}))}}},G=q,U=(a("1452"),Object(h["a"])(G,H,R,!1,null,"680dc53a",null)),Y=U.exports,z=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container "},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"唯一码"},model:{value:e.listQuery.uid,callback:function(t){e.$set(e.listQuery,"uid",t)},expression:"listQuery.uid"}})],1),a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQuery.displayName,callback:function(t){e.$set(e.listQuery,"displayName",t)},expression:"listQuery.displayName"}})],1),a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"关键字"},model:{value:e.listQuery.keywords,callback:function(t){e.$set(e.listQuery,"keywords",t)},expression:"listQuery.keywords"}})],1),a("el-form-item",[a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",clearable:"",placeholder:"请选择字段"},model:{value:e.listQuery.entityColumn,callback:function(t){e.$set(e.listQuery,"entityColumn",t)},expression:"listQuery.entityColumn"}},e._l(e.dataColumns,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e}})})),1)],1),a("el-form-item",[a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"请选择条件"},model:{value:e.listQuery.queryCondition.EnumOperation,callback:function(t){e.$set(e.listQuery.queryCondition,"EnumOperation",t)},expression:"listQuery.queryCondition.EnumOperation"}},e._l(e.selectConditionOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),a("el-form-item",[a("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",placeholder:"请输入关键字"},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1),e._e(),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.openSocialSecurityWithhold}},[e._v("社保代扣")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticClass:"my-table",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{label:"上月数据",width:"100px",align:"center",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[l.id.includes("_lastMonth")?e._e():a("input",{staticClass:"choose",attrs:{id:"choose",type:"checkbox"},on:{input:function(t){return e.sarechLastMonthData(l)}}}),l.id.includes("_lastMonth")?a("span",[e._v(e._s(e.lastMonthData.salaryMonth))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"证件号",sortable:"custom","min-width":e.getStringWidth("证件号"),prop:"Employee.IdentityNumber"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"identityNumber")},[e._v(e._s(l.identityNumber))])]}}])}),a("el-table-column",{attrs:{label:"手机号",sortable:"custom","min-width":e.getStringWidth("手机号"),prop:"Employee.Mobile"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"mobile")},[e._v(e._s(l.mobile))])]}}])}),a("el-table-column",{attrs:{label:"在职方式",sortable:"custom","min-width":e.getStringWidth("在职方式"),prop:"Employee.EmployeeHR.HireStyle.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"hireStyleName")},[e._v(e._s(l.hireStyleName))])]}}])}),a("el-table-column",{attrs:{label:"受雇年份",sortable:"custom","min-width":e.getStringWidth("受雇年份"),prop:"EmployYear"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employYear")},[e._v(e._s(l.employYear))])]}}])}),a("el-table-column",{attrs:{label:"受雇月份",sortable:"custom","min-width":e.getStringWidth("受雇月份"),prop:"EmployMonth"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employMonth")},[e._v(e._s(l.employMonth))])]}}])}),a("el-table-column",{attrs:{label:"本年度受雇月数",sortable:"custom","min-width":e.getStringWidth("本年度受雇月数"),prop:"EmployMonthNumber"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employMonthNumber")},[e._v(e._s(l.employMonthNumber))])]}}])}),a("el-table-column",{attrs:{label:"备注1",sortable:"custom","min-width":e.getStringWidth("备注1"),prop:"Memo1"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"memo1")},[e._v(e._s(l.memo1))])]}}])}),a("el-table-column",{attrs:{label:"备注2",sortable:"custom","min-width":e.getStringWidth("备注2"),prop:"Memo2"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"memo2")},[e._v(e._s(l.memo2))])]}}])}),a("el-table-column",{attrs:{label:"备注3",sortable:"custom","min-width":e.getStringWidth("备注3"),prop:"Memo3"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"memo3")},[e._v(e._s(l.memo3))])]}}])}),a("el-table-column",{attrs:{label:"四金不够扣标记",sortable:"custom","min-width":e.getStringWidth("四金不够扣标记"),align:"center",prop:"FourGoldDeficiencySign"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"fourGoldDeficiencySign")},[e._v(e._s(l.fourGoldDeficiencySign?"有":"无"))])]}}])}),a("el-table-column",{attrs:{label:"社保基数",sortable:"custom","min-width":e.getStringWidth("社保基数"),"header-align":"left",align:"right",prop:"SocialSecurityBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"socialSecurityBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.socialSecurityBase)))])]}}])}),a("el-table-column",{attrs:{label:"公积金基数",sortable:"custom","min-width":e.getStringWidth("公积金基数"),"header-align":"left",align:"right",prop:"HousingFundBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"housingFundBase")},[e._v(e._s(parseFloat(l.housingFundBase).toFixed(0)))])]}}])}),a("el-table-column",{attrs:{label:"岗位级别",sortable:"custom","min-width":e.getStringWidth("岗位级别"),align:"center",prop:"Station.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationName")},[e._v(e._s(l.stationName))])]}}])}),a("el-table-column",{attrs:{label:"薪级",sortable:"custom","min-width":e.getStringWidth("薪级"),align:"center",prop:"SalaryScale.scale"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"scale")},[e._v(e._s(l.scale))])]}}])}),a("el-table-column",{attrs:{label:"工龄",sortable:"custom","min-width":e.getStringWidth("工龄"),align:"center",prop:"SocietyAge"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"societyAge")},[e._v(e._s(l.societyAge))])]}}])}),a("el-table-column",{attrs:{label:"工龄段",sortable:"custom","min-width":e.getStringWidth("工龄段"),align:"center",prop:"SeniorityRangeId"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"seniorityRangeName")},[e._v(e._s(l.seniorityRangeName))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金标记",sortable:"custom","min-width":e.getStringWidth("补充公积金标记"),align:"center",prop:"SupplementaryHousingFundFlag"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFundFlag")},[e._v(e._s(l.supplementaryHousingFundFlag?"有":"无"))])]}}])}),a("el-table-column",{attrs:{label:"职员年金标记",sortable:"custom","min-width":e.getStringWidth("职员年金标记"),align:"center",prop:"EmployeePensionFlag"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employeePensionFlag")},[e._v(e._s(l.employeePensionFlag?"有":"无"))])]}}])}),a("el-table-column",{attrs:{label:"病产假天数",sortable:"custom","min-width":e.getStringWidth("病产假天数"),align:"center",prop:"SickLeaveDays"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"sickLeaveDays")},[e._v(e._s(e.getDecimalValueOrDefault(l.sickLeaveDays)))])]}}])}),a("el-table-column",{attrs:{label:"休假类型",sortable:"custom","min-width":"300px",align:"center",prop:"SalaryLeaveDesc"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"salaryLeaveDesc")},[e._v(e._s(l.salaryLeaveDesc))])]}}])}),a("el-table-column",{attrs:{label:"基本工资小计",sortable:"custom","min-width":e.getStringWidth("基本工资小计"),"header-align":"left",align:"right",prop:"BasicSalarySubtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicSalarySubtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicSalarySubtotal)))])]}}])}),a("el-table-column",{attrs:{label:"岗资基数",sortable:"custom","min-width":e.getStringWidth("岗资基数"),"header-align":"left",align:"right",prop:"StationWageBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationWageBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationWageBase)))])]}}])}),a("el-table-column",{attrs:{label:"岗资",sortable:"custom","min-width":e.getStringWidth("岗资"),"header-align":"left",align:"right",prop:"StationWage"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationWage")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationWage)))])]}}])}),a("el-table-column",{attrs:{label:"薪资基数",sortable:"custom","min-width":e.getStringWidth("薪资基数"),"header-align":"left",align:"right",prop:"SalaryBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"salaryBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.salaryBase)))])]}}])}),a("el-table-column",{attrs:{label:"薪资",sortable:"custom","min-width":e.getStringWidth("薪资"),"header-align":"left",align:"right",prop:"SalaryMoney"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"salaryMoney")},[e._v(e._s(e.getDecimalValueOrDefault(l.salaryMoney)))])]}}])}),a("el-table-column",{attrs:{label:"基本工资其它加",sortable:"custom","min-width":e.getStringWidth("基本工资其它加"),"header-align":"left",align:"right",prop:"BasicSalaryOtherAdd"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicSalaryOtherAdd")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicSalaryOtherAdd)))])]}}])}),a("el-table-column",{attrs:{label:"津补贴小计",sortable:"custom","min-width":e.getStringWidth("津补贴小计"),"header-align":"left",align:"right",prop:"AllowanceSubtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"allowanceSubtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.allowanceSubtotal)))])]}}])}),a("el-table-column",{attrs:{label:"粮油补贴",sortable:"custom","min-width":e.getStringWidth("粮油补贴"),"header-align":"left",align:"right",prop:"GrainOilSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"grainOilSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.grainOilSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"上下班交通费基数",sortable:"custom","min-width":e.getStringWidth("上下班交通费基数"),"header-align":"left",align:"right",prop:"CommuteSubsidyBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"commuteSubsidyBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.commuteSubsidyBase)))])]}}])}),a("el-table-column",{attrs:{label:"上下班交通费",sortable:"custom","min-width":e.getStringWidth("上下班交通费"),"header-align":"left",align:"right",prop:"CommuteSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"commuteSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.commuteSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"护龄基数",sortable:"custom","min-width":e.getStringWidth("护龄基数"),"header-align":"left",align:"right",prop:"NursingBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"nursingBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.nursingBase)))])]}}])}),a("el-table-column",{attrs:{label:"护龄",sortable:"custom","min-width":e.getStringWidth("护龄"),align:"center",prop:"Nursing"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"nursing")},[e._v(e._s(e.getDecimalValueOrDefault(l.nursing)))])]}}])}),a("el-table-column",{attrs:{label:"独子",sortable:"custom","min-width":e.getStringWidth("独子"),align:"center",prop:"OnlyChild"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"onlyChild")},[e._v(e._s(e.getDecimalValueOrDefault(l.onlyChild)))])]}}])}),a("el-table-column",{attrs:{label:"独子补发",sortable:"custom","min-width":e.getStringWidth("独子补发"),"header-align":"left",align:"right",prop:"BackPayOnlyChild"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backPayOnlyChild")},[e._v(e._s(e.getDecimalValueOrDefault(l.backPayOnlyChild)))])]}}])}),a("el-table-column",{attrs:{label:"独子合计",sortable:"custom","min-width":e.getStringWidth("独子合计"),"header-align":"left",align:"right",prop:"OnlyChildTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"onlyChildTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.onlyChildTotal)))])]}}])}),a("el-table-column",{attrs:{label:"援外津贴",sortable:"custom","min-width":e.getStringWidth("援外津贴"),"header-align":"left",align:"right",prop:"ForeignAidAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"foreignAidAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.foreignAidAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"津补贴其它加",sortable:"custom","min-width":e.getStringWidth("津补贴其它加"),"header-align":"left",align:"right",prop:"AllowanceOtherAdd"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"allowanceOtherAdd")},[e._v(e._s(e.getDecimalValueOrDefault(l.allowanceOtherAdd)))])]}}])}),a("el-table-column",{attrs:{label:"绩效小计",sortable:"custom","min-width":e.getStringWidth("绩效小计"),"header-align":"left",align:"right",prop:"PerformanceSubtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"performanceSubtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.performanceSubtotal)))])]}}])}),a("el-table-column",{attrs:{label:"岗位津贴基数",sortable:"custom","min-width":e.getStringWidth("岗位津贴基数"),"header-align":"left",align:"right",prop:"StationAllowanceBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationAllowanceBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationAllowanceBase)))])]}}])}),a("el-table-column",{attrs:{label:"岗位津贴",sortable:"custom","min-width":e.getStringWidth("岗位津贴"),"header-align":"left",align:"right",prop:"StationAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"工作量津贴",sortable:"custom","min-width":e.getStringWidth("工作量津贴"),"header-align":"left",align:"right",prop:"WorkloadAllowance1"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"workloadAllowance1")},[e._v(e._s(e.getDecimalValueOrDefault(l.workloadAllowance1)))])]}}])}),a("el-table-column",{attrs:{label:"工作量津贴合计",sortable:"custom","min-width":e.getStringWidth("工作量津贴合计"),"header-align":"left",align:"right",prop:"WorkloadAllowance2Subtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"workloadAllowance2Subtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.workloadAllowance2Subtotal)))])]}}])}),a("el-table-column",{attrs:{label:"停车补贴",sortable:"custom","min-width":e.getStringWidth("停车补贴"),"header-align":"left",align:"right",prop:"ParkingSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"parkingSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.parkingSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"公派车贴补贴",sortable:"custom","min-width":e.getStringWidth("公派车贴补贴"),"header-align":"left",align:"right",prop:"OfficialCarAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"officialCarAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.officialCarAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"电话费",sortable:"custom","min-width":e.getStringWidth("电话费"),"header-align":"left",align:"right",prop:"TelephoneFee"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"telephoneFee")},[e._v(e._s(e.getDecimalValueOrDefault(l.telephoneFee)))])]}}])}),a("el-table-column",{attrs:{label:"电话费合计",sortable:"custom","min-width":e.getStringWidth("电话费合计"),"header-align":"left",align:"right",prop:"TelephoneFeeTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"telephoneFeeTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.telephoneFeeTotal)))])]}}])}),a("el-table-column",{attrs:{label:"长病假职工最低工资补助",sortable:"custom","min-width":e.getStringWidth("长病假职工最低工资补助"),"header-align":"left",align:"right",prop:"LongSickLeaveMinimumWageSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"longSickLeaveMinimumWageSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.longSickLeaveMinimumWageSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"博士后房帖",sortable:"custom","min-width":e.getStringWidth("博士后房帖"),"header-align":"left",align:"right",prop:"PostdoctoralHousingAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"postdoctoralHousingAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.postdoctoralHousingAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"卫生津贴",sortable:"custom","min-width":e.getStringWidth("卫生津贴"),"header-align":"left",align:"right",prop:"HealthAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"healthAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.healthAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"零星补节假日加班绩效",sortable:"custom","min-width":e.getStringWidth("零星补节假日加班绩效"),"header-align":"left",align:"right",prop:"OccasionalHolidayOvertimePerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occasionalHolidayOvertimePerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.occasionalHolidayOvertimePerformance)))])]}}])}),a("el-table-column",{attrs:{label:"节假日加班绩效",sortable:"custom","min-width":e.getStringWidth("节假日加班绩效"),"header-align":"left",align:"right",prop:"HolidayOvertimePerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"holidayOvertimePerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.holidayOvertimePerformance)))])]}}])}),a("el-table-column",{attrs:{label:"中夜班绩效",sortable:"custom","min-width":e.getStringWidth("中夜班绩效"),"header-align":"left",align:"right",prop:"MidnightShiftPerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"midnightShiftPerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.midnightShiftPerformance)))])]}}])}),a("el-table-column",{attrs:{label:"行政值班绩效",sortable:"custom","min-width":e.getStringWidth("行政值班绩效"),"header-align":"left",align:"right",prop:"AdministrativeDutyPerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"administrativeDutyPerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.administrativeDutyPerformance)))])]}}])}),a("el-table-column",{attrs:{label:"一二值班绩效",sortable:"custom","min-width":e.getStringWidth("一二值班绩效"),"header-align":"left",align:"right",prop:"FirstSecondDutyPerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"firstSecondDutyPerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.firstSecondDutyPerformance)))])]}}])}),a("el-table-column",{attrs:{label:"其它加",sortable:"custom","min-width":e.getStringWidth("其它加"),"header-align":"left",align:"right",prop:"OtherAdd"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"otherAdd")},[e._v(e._s(e.getDecimalValueOrDefault(l.otherAdd)))])]}}])}),a("el-table-column",{attrs:{label:"行政聘任补发",sortable:"custom","min-width":e.getStringWidth("行政聘任补发"),"header-align":"left",align:"right",prop:"WorkChangeReissue"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"workChangeReissue")},[e._v(e._s(e.getDecimalValueOrDefault(l.workChangeReissue)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣养扣",sortable:"custom","min-width":e.getStringWidth("退还多扣养扣"),"header-align":"left",align:"right",prop:"ReturnOverDeductPension"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductPension")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductPension)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣医疗",sortable:"custom","min-width":e.getStringWidth("退还多扣医疗"),"header-align":"left",align:"right",prop:"ReturnOverDeductMedical"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductMedical")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductMedical)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣失业",sortable:"custom","min-width":e.getStringWidth("退还多扣失业"),"header-align":"left",align:"right",prop:"ReturnOverDeductUnemployment"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductUnemployment")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductUnemployment)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣公扣",sortable:"custom","min-width":e.getStringWidth("退还多扣公扣"),"header-align":"left",align:"right",prop:"ReturnOverDeductPublic"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductPublic")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductPublic)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣补充公积金",sortable:"custom","min-width":e.getStringWidth("退还多扣补充公积金"),"header-align":"left",align:"right",prop:"ReturnOverDeductSupplementaryHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductSupplementaryHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductSupplementaryHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣年金",sortable:"custom","min-width":e.getStringWidth("退还多扣年金"),"header-align":"left",align:"right",prop:"ReturnOverDeductOccupationalAnnuity"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductOccupationalAnnuity")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductOccupationalAnnuity)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣出国人员扣款",sortable:"custom","min-width":e.getStringWidth("退还多扣出国人员扣款"),"header-align":"left",align:"right",prop:"ReturnOverDeductDeductionForForeigners"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductDeductionForForeigners")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductDeductionForForeigners)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣病假工资",sortable:"custom","min-width":e.getStringWidth("退还多扣病假工资"),"header-align":"left",align:"right",prop:"ReturnOverDeductSickLeaveSalary"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductSickLeaveSalary")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductSickLeaveSalary)))])]}}])}),a("el-table-column",{attrs:{label:"定级补发",sortable:"custom","min-width":e.getStringWidth("定级补发"),"header-align":"left",align:"right",prop:"BackPayGrading"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backPayGrading")},[e._v(e._s(e.getDecimalValueOrDefault(l.backPayGrading)))])]}}])}),a("el-table-column",{attrs:{label:"应发工资",sortable:"custom","min-width":e.getStringWidth("应发工资"),"header-align":"left",align:"right",prop:"GrossSalary"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"grossSalary")},[e._v(e._s(e.getDecimalValueOrDefault(l.grossSalary)))])]}}])}),a("el-table-column",{attrs:{label:"应发工资（申报个税用）",sortable:"custom","min-width":e.getStringWidth("应发工资（申报个税用）"),"header-align":"left",align:"right",prop:"GrossSalaryForTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"grossSalaryForTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.grossSalaryForTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"养扣",sortable:"custom","min-width":e.getStringWidth("养扣"),"header-align":"left",align:"right",prop:"PensionDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"补扣养扣",sortable:"custom","min-width":e.getStringWidth("补扣养扣"),"header-align":"left",align:"right",prop:"BackDeductionPension"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionPension")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionPension)))])]}}])}),a("el-table-column",{attrs:{label:"养扣合计",sortable:"custom","min-width":e.getStringWidth("养扣合计"),"header-align":"left",align:"right",prop:"PensionDeductionTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionDeductionTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionDeductionTotal)))])]}}])}),a("el-table-column",{attrs:{label:"医疗保险",sortable:"custom","min-width":e.getStringWidth("医疗保险"),"header-align":"left",align:"right",prop:"MedicalInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"补扣医疗保险",sortable:"custom","min-width":e.getStringWidth("补扣医疗保险"),"header-align":"left",align:"right",prop:"BackDeductionMedicalInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionMedicalInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionMedicalInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"医疗保险合计",sortable:"custom","min-width":e.getStringWidth("医疗保险合计"),"header-align":"left",align:"right",prop:"MedicalInsuranceTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalInsuranceTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalInsuranceTotal)))])]}}])}),a("el-table-column",{attrs:{label:"失业保险",sortable:"custom","min-width":e.getStringWidth("失业保险"),"header-align":"left",align:"right",prop:"UnemploymentInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"补扣失业保险",sortable:"custom","min-width":e.getStringWidth("补扣失业保险"),"header-align":"left",align:"right",prop:"BackDeductionUnemploymentInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionUnemploymentInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionUnemploymentInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"失业保险合计",sortable:"custom","min-width":e.getStringWidth("失业保险合计"),"header-align":"left",align:"right",prop:"UnemploymentInsuranceTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentInsuranceTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentInsuranceTotal)))])]}}])}),a("el-table-column",{attrs:{label:"基本公积金",sortable:"custom","min-width":e.getStringWidth("基本公积金"),"header-align":"left",align:"right",prop:"BasicHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"补扣基本公积金",sortable:"custom","min-width":e.getStringWidth("补扣基本公积金"),"header-align":"left",align:"right",prop:"BackDeductionBasicHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionBasicHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionBasicHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"基本公积金合计",sortable:"custom","min-width":e.getStringWidth("基本公积金合计"),"header-align":"left",align:"right",prop:"HousingFundTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"housingFundTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.housingFundTotal)))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金",sortable:"custom","min-width":e.getStringWidth("补充公积金"),"header-align":"left",align:"right",prop:"SupplementaryHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFund")},[e._v(e._s(parseFloat(l.supplementaryHousingFund).toFixed(0)))])]}}])}),a("el-table-column",{attrs:{label:"补扣补充公积金",sortable:"custom","min-width":e.getStringWidth("补扣补充公积金"),"header-align":"left",align:"right",prop:"BackDeductionSupplementaryHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionSupplementaryHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionSupplementaryHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金合计",sortable:"custom","min-width":e.getStringWidth("补充公积金合计"),"header-align":"left",align:"right",prop:"SupplementaryHousingFundTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFundTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.supplementaryHousingFundTotal)))])]}}])}),a("el-table-column",{attrs:{label:"职业年金",sortable:"custom","min-width":e.getStringWidth("职业年金"),"header-align":"left",align:"right",prop:"OccupationalAnnuity"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occupationalAnnuity")},[e._v(e._s(e.getDecimalValueOrDefault(l.occupationalAnnuity)))])]}}])}),a("el-table-column",{attrs:{label:"补扣职业年金",sortable:"custom","min-width":e.getStringWidth("补扣职业年金"),"header-align":"left",align:"right",prop:"BackDeductionOccupationalAnnuity"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionOccupationalAnnuity")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionOccupationalAnnuity)))])]}}])}),a("el-table-column",{attrs:{label:"职业年金合计",sortable:"custom","min-width":e.getStringWidth("职业年金合计"),"header-align":"left",align:"right",prop:"OccupationalAnnuityTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occupationalAnnuityTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.occupationalAnnuityTotal)))])]}}])}),a("el-table-column",{attrs:{label:"会费",sortable:"custom","min-width":e.getStringWidth("会费"),"header-align":"left",align:"right",prop:"MembershipFee"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"membershipFee")},[e._v(e._s(parseFloat(l.membershipFee).toFixed(1)))])]}}])}),a("el-table-column",{attrs:{label:"补扣会费",sortable:"custom","min-width":e.getStringWidth("补扣会费"),"header-align":"left",align:"right",prop:"BackDeductionMembershipFee"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionMembershipFee")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionMembershipFee)))])]}}])}),a("el-table-column",{attrs:{label:"会费合计",sortable:"custom","min-width":e.getStringWidth("会费合计"),"header-align":"left",align:"right",prop:"MembershipFeeTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"membershipFeeTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.membershipFeeTotal)))])]}}])}),a("el-table-column",{attrs:{label:"房租",sortable:"custom","min-width":e.getStringWidth("房租"),"header-align":"left",align:"right",prop:"Rent"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"rent")},[e._v(e._s(e.getDecimalValueOrDefault(l.rent)))])]}}])}),a("el-table-column",{attrs:{label:"代扣税金",sortable:"custom","min-width":e.getStringWidth("代扣税金"),"header-align":"left",align:"right",prop:"TaxWithholding"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"taxWithholding")},[e._v(e._s(e.getDecimalValueOrDefault(l.taxWithholding)))])]}}])}),a("el-table-column",{attrs:{label:"其它扣",sortable:"custom","min-width":e.getStringWidth("其它扣"),"header-align":"left",align:"right",prop:"OtherDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"otherDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.otherDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"长病假补扣",sortable:"custom","min-width":e.getStringWidth("长病假补扣"),"header-align":"left",align:"right",prop:"BackDeductionLongSickLeave"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionLongSickLeave")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionLongSickLeave)))])]}}])}),a("el-table-column",{attrs:{label:"扣出国人员扣款",sortable:"custom","min-width":e.getStringWidth("扣出国人员扣款"),"header-align":"left",align:"right",prop:"DeductionForForeigners"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"deductionForForeigners")},[e._v(e._s(e.getDecimalValueOrDefault(l.deductionForForeigners)))])]}}])}),a("el-table-column",{attrs:{label:"扣款合计",sortable:"custom","min-width":e.getStringWidth("扣款合计"),"header-align":"left",align:"right",prop:"DeductionTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"deductionTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.deductionTotal)))])]}}])}),a("el-table-column",{attrs:{label:"实发工资",sortable:"custom","min-width":e.getStringWidth("实发工资"),"header-align":"left",align:"right",prop:"NetSalary"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"netSalary")},[e._v(e._s(e.getDecimalValueOrDefault(l.netSalary)))])]}}])}),a("el-table-column",{attrs:{label:"养扣基础数据",sortable:"custom","min-width":e.getStringWidth("养扣基础数据"),"header-align":"left",align:"right",prop:"BasicDataDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicDataDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicDataDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"医疗保险基础数据",sortable:"custom","min-width":e.getStringWidth("医疗保险基础数据"),"header-align":"left",align:"right",prop:"MedicalInsuranceBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalInsuranceBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalInsuranceBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"失业保险基础数据",sortable:"custom","min-width":e.getStringWidth("失业保险基础数据"),"header-align":"left",align:"right",prop:"UnemploymentInsuranceBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentInsuranceBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentInsuranceBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"公积金基础数据",sortable:"custom","min-width":e.getStringWidth("公积金基础数据"),"header-align":"left",align:"right",prop:"HousingFundBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"housingFundBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.housingFundBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金基础数据",sortable:"custom","min-width":e.getStringWidth("补充公积金基础数据"),"header-align":"left",align:"right",prop:"SupplementaryHousingFundBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFundBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.supplementaryHousingFundBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"年金基础数据",sortable:"custom","min-width":e.getStringWidth("年金基础数据"),"header-align":"left",align:"right",prop:"PensionBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"养老（申报个税用）",sortable:"custom","min-width":e.getStringWidth("养老（申报个税用）"),"header-align":"left",align:"right",prop:"PensionTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"医疗（申报个税用）",sortable:"custom","min-width":e.getStringWidth("医疗（申报个税用）"),"header-align":"left",align:"right",prop:"MedicalTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"失业（申报个税用）",sortable:"custom","min-width":e.getStringWidth("失业（申报个税用）"),"header-align":"left",align:"right",prop:"UnemploymentTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"公扣（申报个税用）",sortable:"custom","min-width":e.getStringWidth("公扣（申报个税用）"),"header-align":"left",align:"right",prop:"PublicTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"publicTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.publicTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"年金（申报个税用）",sortable:"custom","min-width":e.getStringWidth("年金（申报个税用）"),"header-align":"left",align:"right",prop:"OccupationalAnnuityTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occupationalAnnuityTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.occupationalAnnuityTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"申报个税四金合计",sortable:"custom","min-width":e.getStringWidth("申报个税四金合计"),"header-align":"left",align:"right",prop:"TaxDeclarationFourGold"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"taxDeclarationFourGold")},[e._v(e._s(e.getDecimalValueOrDefault(l.taxDeclarationFourGold)))])]}}])}),a("el-table-column",{attrs:{label:"基本工资合计",sortable:"custom","min-width":e.getStringWidth("基本工资合计"),"header-align":"left",align:"right",prop:"BasicSalaryTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicSalaryTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicSalaryTotal)))])]}}])}),a("el-table-column",{attrs:{label:"津贴补贴合计",sortable:"custom","min-width":e.getStringWidth("津贴补贴合计"),"header-align":"left",align:"right",prop:"AllowanceTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"allowanceTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.allowanceTotal)))])]}}])}),a("el-table-column",{attrs:{label:"对个人及家庭补助",sortable:"custom","min-width":e.getStringWidth("对个人及家庭补助"),"header-align":"left",align:"right",prop:"OnlyChildTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"onlyChildTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.onlyChildTotal)))])]}}])}),a("el-table-column",{attrs:{label:"奖金合计",sortable:"custom","min-width":e.getStringWidth("奖金合计"),"header-align":"left",align:"right",prop:"BonusTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"bonusTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.bonusTotal)))])]}}])}),a("el-table-column",{attrs:{label:"年终一次性奖励",sortable:"custom","min-width":e.getStringWidth("年终一次性奖励"),"header-align":"left",align:"right",prop:"YearEndBonus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"yearEndBonus")},[e._v(e._s(e.getDecimalValueOrDefault(l.yearEndBonus)))])]}}])}),a("el-table-column",{attrs:{label:"其他各项奖金",sortable:"custom","min-width":e.getStringWidth("其他各项奖金"),"header-align":"left",align:"right",prop:"OtherBonusesTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"otherBonusesTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.otherBonusesTotal)))])]}}])}),a("el-table-column",{attrs:{label:"实发奖金",sortable:"custom","min-width":e.getStringWidth("实发奖金"),"header-align":"left",align:"right",prop:"ActualBonus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"actualBonus")},[e._v(e._s(e.getDecimalValueOrDefault(l.actualBonus)))])]}}])}),a("el-table-column",{attrs:{label:"税前奖金",sortable:"custom","min-width":e.getStringWidth("税前奖金"),"header-align":"left",align:"right",prop:"PreTaxBonus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"preTaxBonus")},[e._v(e._s(e.getDecimalValueOrDefault(l.preTaxBonus)))])]}}])}),a("el-table-column",{attrs:{label:"奖金扣税",sortable:"custom","min-width":e.getStringWidth("奖金扣税"),"header-align":"left",align:"right",prop:"BonusTax"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"bonusTax")},[e._v(e._s(e.getDecimalValueOrDefault(l.bonusTax)))])]}}])}),a("el-table-column",{attrs:{label:"累计收入额",sortable:"custom","min-width":e.getStringWidth("累计收入额"),"header-align":"left",align:"right",prop:"AccumulatedIncome"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedIncome")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedIncome)))])]}}])}),a("el-table-column",{attrs:{label:"累计专项扣除",sortable:"custom","min-width":e.getStringWidth("累计专项扣除"),"header-align":"left",align:"right",prop:"AccumulatedDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"累计专项附加扣除",sortable:"custom","min-width":e.getStringWidth("累计专项附加扣除"),"header-align":"left",align:"right",prop:"AccumulatedAdditionalDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedAdditionalDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedAdditionalDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"累计应纳税所得额",sortable:"custom","min-width":e.getStringWidth("累计应纳税所得额"),"header-align":"left",align:"right",prop:"AccumulatedTaxableIncome"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedTaxableIncome")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedTaxableIncome)))])]}}])}),a("el-table-column",{attrs:{label:"累计已预扣税额",sortable:"custom","min-width":e.getStringWidth("累计已预扣税额"),"header-align":"left",align:"right",prop:"AccumulatedWithheldTax"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedWithheldTax")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedWithheldTax)))])]}}])}),a("el-table-column",{attrs:{label:"计税",sortable:"custom","min-width":e.getStringWidth("计税"),"header-align":"left",align:"right",prop:"TaxCalculation"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"taxCalculation")},[e._v(e._s(l.taxCalculation))])]}}])}),a("el-table-column",{attrs:{label:"应发总计",sortable:"custom","min-width":e.getStringWidth("应发总计"),"header-align":"left",align:"right",prop:"TotalPayable"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"totalPayable")},[e._v(e._s(e.getDecimalValueOrDefault(l.totalPayable)))])]}}])}),a("el-table-column",{attrs:{label:"工行帐号",sortable:"custom","min-width":e.getStringWidth("工行帐号"),align:"left",prop:"ICBCAccount"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"iCBCAccount")},[e._v(e._s(l.iCBCAccount))])]}}])}),a("el-table-column",{attrs:{fixed:"right",label:"操作",align:"left","header-align":"center",width:"150","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[e.isEdit?a("el-button",{staticStyle:{"margin-left":"35px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.editEmployeeSalaryDetailDialog(l,!0)}}},[e._v(" 编辑 ")]):e._e(),e.isEdit?e._e():a("el-button",{staticStyle:{"margin-left":"35px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-view",size:"mini"},on:{click:function(t){return e.editEmployeeSalaryDetailDialog(l,!1)}}},[e._v(" 显示 ")])]}}])}),a("employeeTableColumns")],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),a("editDialog",{ref:"editDialog",on:{refreshData:e.getPageList}}),a("employeeSalaryRecordDialog",{ref:"employeeSalaryRecordDialog"}),a("socialSecurityWithhold",{ref:"socialSecurityWithhold"})],1)},K=[],j={components:{editDialog:b,employeeSalaryRecordDialog:k,employeeTableColumns:v["a"],socialSecurityWithhold:W},data:function(){return{isSalaryMonthComplete:!0,showDialog:!1,isEdit:!1,title:"财务数据",pageList:[],groupList:[],salaryData:{},lastMonthData:{},salaryId:"",listQuery:{queryCondition:{},salaryId:"",total:1,pageIndex:1,pageSize:10},searchList:[{id:""}],lastMonth:!1,listLoading:!1,dialogEditEmpSalaryVisible:!1,dataColumns:[{value:"1",label:"旧社保基数",type:"System.Decimal",columnName:"OldSocialSecurityBase"},{value:"2",label:"社保基数",type:"System.Decimal",columnName:"SocialSecurityBase"},{value:"3",label:"旧公积金基数",type:"System.Decimal",columnName:"OldHousingFundBase"},{value:"4",label:"公积金基数",type:"System.Decimal",columnName:"HousingFundBase"},{value:"5",label:"岗位津贴",type:"System.Decimal",columnName:"StationAllowance"},{value:"7",label:"工龄",type:"System.Int32",columnName:"SocietyAge"},{value:"8",label:"补充公积金标记",type:"System.Boolean",columnName:"SupplementaryHousingFundFlag"},{value:"9",label:"职员年金标记",type:"System.Boolean",columnName:"EmployeePensionFlag"},{value:"10",label:"病假天数",type:"System.Decimal",columnName:"SickLeaveDays"},{value:"11",label:"休假类型",type:"System.String",columnName:"SalaryLeaveDesc"},{value:"12",label:"基本工资小计",type:"System.Decimal",columnName:"BasicSalarySubtotal"},{value:"13",label:"岗资基数",type:"System.Decimal",columnName:"StationWageBase"},{value:"14",label:"岗资",type:"System.Decimal",columnName:"StationWage"},{value:"15",label:"薪资基数",type:"System.Decimal",columnName:"SalaryBase"},{value:"16",label:"薪资",type:"System.Decimal",columnName:"SalaryMoney"},{value:"17",label:"基本工资其它加",type:"System.Decimal",columnName:"BasicSalaryOtherAdd"},{value:"18",label:"津补贴小计",type:"System.Decimal",columnName:"AllowanceSubtotal"},{value:"19",label:"粮油补贴",type:"System.Decimal",columnName:"GrainOilSubsidy"},{value:"20",label:"上下班交通费基数",type:"System.Decimal",columnName:"CommuteSubsidyBase"},{value:"21",label:"上下班交通费",type:"System.Decimal",columnName:"CommuteSubsidy"},{value:"22",label:"护龄基数",type:"System.Decimal",columnName:"NursingBase"},{value:"23",label:"护龄",type:"System.Decimal",columnName:"Nursing"},{value:"24",label:"独子",type:"System.Decimal",columnName:"OnlyChild"},{value:"25",label:"独子补发",type:"System.Decimal",columnName:"BackPayOnlyChild"},{value:"26",label:"独子合计",type:"System.Decimal",columnName:"OnlyChildTotal"},{value:"27",label:"援外津贴",type:"System.Decimal",columnName:"ForeignAidAllowance"},{value:"28",label:"援滇津贴",type:"System.Decimal",columnName:"YunnanAidAllowance"},{value:"29",label:"津补贴其它加",type:"System.Decimal",columnName:"AllowanceOtherAdd"},{value:"30",label:"绩效小计",type:"System.Decimal",columnName:"PerformanceSubtotal"},{value:"31",label:"岗位津贴基数",type:"System.Decimal",columnName:"StationAllowanceBase"},{value:"32",label:"工作量津贴1",type:"System.Decimal",columnName:"WorkloadAllowance1"},{value:"33",label:"工作量津贴2小计",type:"System.Decimal",columnName:"WorkloadAllowance2Subtotal"},{value:"34",label:"停车补贴",type:"System.Decimal",columnName:"ParkingSubsidy"},{value:"35",label:"公派车贴补贴",type:"System.Decimal",columnName:"OfficialCarAllowance"},{value:"36",label:"电话费",type:"System.Decimal",columnName:"TelephoneFee"},{value:"37",label:"补发电话费",type:"System.Decimal",columnName:"BackPayTelephoneFee"},{value:"38",label:"电话费合计",type:"System.Decimal",columnName:"TelephoneFeeTotal"},{value:"39",label:"长病假职工最低工资补助",type:"System.Decimal",columnName:"LongSickLeaveMinimumWageSubsidy"},{value:"40",label:"博士后房帖",type:"System.Decimal",columnName:"PostdoctoralHousingAllowance"},{value:"41",label:"卫生津贴",type:"System.Decimal",columnName:"HealthAllowance"},{value:"42",label:"零星补节假日加班绩效",type:"System.Decimal",columnName:"OccasionalHolidayOvertimePerformance"},{value:"43",label:"节假日加班绩效",type:"System.Decimal",columnName:"HolidayOvertimePerformance"},{value:"44",label:"中夜班绩效",type:"System.Decimal",columnName:"MidnightShiftPerformance"},{value:"45",label:"行政值班绩效",type:"System.Decimal",columnName:"AdministrativeDutyPerformance"},{value:"46",label:"一二值班绩效",type:"System.Decimal",columnName:"FirstSecondDutyPerformance"},{value:"47",label:"急诊拖后值班绩效",type:"System.Decimal",columnName:"EmergencyDelayDutyPerformance"},{value:"48",label:"其它加",type:"System.Decimal",columnName:"OtherAdd"},{value:"52",label:"工改补发",type:"System.Decimal",columnName:"WorkChangeReissue"},{value:"53",label:"退还多扣养扣",type:"System.Decimal",columnName:"ReturnOverDeductPension"},{value:"54",label:"退还多扣医疗",type:"System.Decimal",columnName:"ReturnOverDeductMedical"},{value:"55",label:"退还多扣失业",type:"System.Decimal",columnName:"ReturnOverDeductUnemployment"},{value:"56",label:"退还多扣公扣",type:"System.Decimal",columnName:"ReturnOverDeductPublic"},{value:"57",label:"退还多扣补充公积金",type:"System.Decimal",columnName:"ReturnOverDeductSupplementaryHousingFund"},{value:"58",label:"退还多扣年金",type:"System.Decimal",columnName:"ReturnOverDeductOccupationalAnnuity"},{value:"59",label:"退还多扣出国人员扣款",type:"System.Decimal",columnName:"ReturnOverDeductDeductionForForeigners"},{value:"60",label:"退还多扣病假工资",type:"System.Decimal",columnName:"ReturnOverDeductSickLeaveSalary"},{value:"61",label:"长病假、待退休绩效奖",type:"System.Decimal",columnName:"LongSickLeaveRetirementPerformanceBonus"},{value:"62",label:"定级补发",type:"System.Decimal",columnName:"BackPayGrading"},{value:"63",label:"应发工资",type:"System.Decimal",columnName:"GrossSalary"},{value:"64",label:"应发工资（申报个税用）",type:"System.Decimal",columnName:"GrossSalaryForTaxDeclaration"},{value:"65",label:"养扣",type:"System.Decimal",columnName:"PensionDeduction"},{value:"66",label:"补扣养扣",type:"System.Decimal",columnName:"BackDeductionPension"},{value:"67",label:"养扣合计",type:"System.Decimal",columnName:"PensionDeductionTotal"},{value:"68",label:"医疗保险",type:"System.Decimal",columnName:"MedicalInsurance"},{value:"69",label:"补扣医疗保险",type:"System.Decimal",columnName:"BackDeductionMedicalInsurance"},{value:"70",label:"医疗保险合计",type:"System.Decimal",columnName:"MedicalInsuranceTotal"},{value:"71",label:"失业保险",type:"System.Decimal",columnName:"UnemploymentInsurance"},{value:"72",label:"补扣失业保险",type:"System.Decimal",columnName:"BackDeductionUnemploymentInsurance"},{value:"73",label:"失业保险合计",type:"System.Decimal",columnName:"UnemploymentInsuranceTotal"},{value:"74",label:"基本公积金",type:"System.Decimal",columnName:"BasicHousingFund"},{value:"75",label:"补扣基本公积金",type:"System.Decimal",columnName:"BackDeductionBasicHousingFund"},{value:"76",label:"基本公积金合计",type:"System.Decimal",columnName:"HousingFundTotal"},{value:"77",label:"补充公积金",type:"System.Decimal",columnName:"SupplementaryHousingFund"},{value:"78",label:"补扣补充公积金",type:"System.Decimal",columnName:"BackDeductionSupplementaryHousingFund"},{value:"79",label:"补充公积金合计",type:"System.Decimal",columnName:"SupplementaryHousingFundTotal"},{value:"80",label:"职业年金",type:"System.Decimal",columnName:"OccupationalAnnuity"},{value:"81",label:"补扣职业年金",type:"System.Decimal",columnName:"BackDeductionOccupationalAnnuity"},{value:"82",label:"职业年金合计",type:"System.Decimal",columnName:"OccupationalAnnuityTotal"},{value:"83",label:"会费",type:"System.Decimal",columnName:"MembershipFee"},{value:"84",label:"补扣会费",type:"System.Decimal",columnName:"BackDeductionMembershipFee"},{value:"85",label:"会费合计",type:"System.Decimal",columnName:"MembershipFeeTotal"},{value:"86",label:"房租",type:"System.Decimal",columnName:"Rent"},{value:"87",label:"病假工资",type:"System.Decimal",columnName:"SickLeaveSalary"},{value:"88",label:"补扣病假",type:"System.Decimal",columnName:"BackDeductionSickLeave"},{value:"89",label:"病假工资合计",type:"System.Decimal",columnName:"SickLeaveSalaryTotal"},{value:"90",label:"代扣税金",type:"System.Decimal",columnName:"TaxWithholding"},{value:"91",label:"其它扣",type:"System.Decimal",columnName:"OtherDeduction"},{value:"92",label:"长病假补扣",type:"System.Decimal",columnName:"BackDeductionLongSickLeave"},{value:"93",label:"综合险",type:"System.Decimal",columnName:"ComprehensiveInsurance"},{value:"94",label:"户口挂靠费",type:"System.Decimal",columnName:"HukouHostingFee"},{value:"100",label:"扣款合计",type:"System.Decimal",columnName:"DeductionTotal"},{value:"101",label:"实发工资",type:"System.Decimal",columnName:"NetSalary"},{value:"102",label:"房贴2016",type:"System.Decimal",columnName:"HousingAllowance2016"},{value:"103",label:"养扣基础数据",type:"System.Decimal",columnName:"BasicDataDeduction"},{value:"104",label:"医疗保险基础数据",type:"System.Decimal",columnName:"MedicalInsuranceBasicData"},{value:"105",label:"失业保险基础数据",type:"System.Decimal",columnName:"UnemploymentInsuranceBasicData"},{value:"106",label:"公积金基础数据",type:"System.Decimal",columnName:"HousingFundBasicData"},{value:"107",label:"补充公积金基础数据",type:"System.Decimal",columnName:"SupplementaryHousingFundBasicData"},{value:"108",label:"年金基础数据",type:"System.Decimal",columnName:"PensionBasicData"},{value:"109",label:"养老（申报个税用）",type:"System.Decimal",columnName:"PensionTaxDeclaration"},{value:"110",label:"医疗（申报个税用）",type:"System.Decimal",columnName:"MedicalTaxDeclaration"},{value:"111",label:"失业（申报个税用）",type:"System.Decimal",columnName:"UnemploymentTaxDeclaration"},{value:"112",label:"公扣（申报个税用）",type:"System.Decimal",columnName:"PublicTaxDeclaration"},{value:"113",label:"年金（申报个税用）",type:"System.Decimal",columnName:"OccupationalAnnuityTaxDeclaration"},{value:"114",label:"申报个税四金合计",type:"System.Decimal",columnName:"TaxDeclarationFourGold"},{value:"115",label:"基本工资合计",type:"System.Decimal",columnName:"BasicSalaryTotal"},{value:"116",label:"津贴补贴合计",type:"System.Decimal",columnName:"AllowanceTotal"},{value:"117",label:"对个人及家庭补助",type:"System.Decimal",columnName:"PersonalAndFamilySubsidy"},{value:"118",label:"加班工资",type:"System.Decimal",columnName:"OvertimePay"},{value:"119",label:"奖金合计",type:"System.Decimal",columnName:"BonusTotal"},{value:"120",label:"十三月工资（应发）",type:"System.Decimal",columnName:"ThirteenthMonthSalary"},{value:"121",label:"计税工资2（12月计税工资与十三月工资应发之和）",type:"System.Decimal",columnName:"TaxableWage2"},{value:"122",label:"代扣税金2（以计税工资2计算出的代扣税金）",type:"System.Decimal",columnName:"TaxWithholding2"},{value:"123",label:"十三月工资代扣税金",type:"System.Decimal",columnName:"ThirteenthMonthTaxWithholding"},{value:"124",label:"十三月工资（实发）",type:"System.Decimal",columnName:"ThirteenthMonthSalaryNet"},{value:"125",label:"年终一次性奖励",type:"System.Decimal",columnName:"YearEndBonus"},{value:"126",label:"其他各项奖金",type:"System.Decimal",columnName:"OtherBonusesTotal"},{value:"127",label:"实发奖金",type:"System.Decimal",columnName:"ActualBonus"},{value:"128",label:"税前奖金",type:"System.Decimal",columnName:"PreTaxBonus"},{value:"129",label:"奖金扣税",type:"System.Decimal",columnName:"BonusTax"},{value:"130",label:"累计收入额",type:"System.Decimal",columnName:"AccumulatedIncome"},{value:"131",label:"累计专项扣除",type:"System.Decimal",columnName:"AccumulatedDeduction"},{value:"132",label:"累计专项附加扣除",type:"System.Decimal",columnName:"AccumulatedAdditionalDeduction"},{value:"133",label:"累计应纳税所得额",type:"System.Decimal",columnName:"AccumulatedTaxableIncome"},{value:"134",label:"累计已预扣税额",type:"System.Decimal",columnName:"AccumulatedWithheldTax"},{value:"135",label:"待个人汇算时由税务退还个税",type:"System.Decimal",columnName:"TaxToBeReturnedUponPersonalSettlementByTaxAuthority"},{value:"136",label:"计税工资",type:"System.Decimal",columnName:"TaxableWage"},{value:"137",label:"其他工资",type:"System.Decimal",columnName:"OtherWages"}],selectConditionOptions:[],signDropdown:[{value:!0,label:"有"},{value:!1,label:"无"}]}},created:function(){this.initDialog()},mounted:function(){},methods:{initDialog:function(){this.salaryId=this.$route.query.salaryId,this.salaryId&&(this.showDialog=!0,this.listQuery.salaryId=this.salaryId,this.getSalary(),this.getPageList(),this.loadConditions())},employeeSalaryRecord:function(){this.$refs.employeeSalaryRecordDialog.initDialog(this.salaryId)},openSocialSecurityWithhold:function(){this.$refs.socialSecurityWithhold.initDialog(this.salaryId)},loadConditions:function(){var e=this;u["a"].getEnumInfos({enumType:"Operations"}).then((function(t){e.selectConditionOptions=t.data.datas})).catch((function(e){console.log(e)}))},getSalary:function(){var e=this;r["a"].getSalary({id:this.listQuery.salaryId}).then((function(t){t.succeed?(e.salaryData=t.data,2===e.salaryData.enumStatus?e.isEdit=!0:e.isEdit=!1):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))},closeDialog:function(){this.pageList=[],this.showDialog=!1},search:function(){this.listQuery.pageIndex=1,this.getPageList(),this.clearCheckboxes()},sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.$delete(this.listQuery,"order"),this.search()},getPageList:function(){var e=this;this.searchList.length=0,this.pageList=[],this.listLoading=!0,this.listQuery.entityColumn&&""!==this.listQuery.entityColumn&&this.listQuery.queryCondition.EnumOperation?(this.listQuery.queryCondition.Keywords||(this.listQuery.queryCondition.Keywords=""),this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.columnName,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.ConditionList=[this.listQuery.queryCondition]):(this.listQuery.ConditionList=[],this.listQuery.queryCondition={}),this.listQuery.enumSalaryStatusType=3,r["a"].querySalaryDetail(this.listQuery).then((function(t){t.succeed?(e.pageList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1}))},clearCheckboxes:function(){this.pageList.forEach((function(e){if(!e.id.includes("_lastMonth")){var t=document.getElementById("choose");t&&(t.checked=!1)}}))},sarechLastMonthData:function(e){var t=this;if(this.searchList.includes(e.id)){this.searchList.pop(e.id);var a=this.pageList.findIndex((function(t){return t.id.includes("_lastMonth")&&t.id.split("_lastMonth")[0]===e.id}));-1!==a&&this.pageList.splice(a,1)}else r["a"].queryLastMonthEmployeeSalary({id:e.id}).then((function(a){if(a.succeed){t.lastMonthData=a.data,t.searchList.push(e.id);var l=Object(s["a"])({},e);Object.assign(l,t.lastMonthData),l.id=e.id+"_lastMonth";var i=t.pageList.indexOf(e);t.pageList.splice(i+1,0,l)}else t.$notice.resultTip(a)})).catch((function(e){console.log(e)}))},editEmployeeSalaryDetailDialog:function(e,t){this.$refs.editDialog.salaryId=this.salaryId,this.$refs.editDialog.initDialog(e,t)},onRefresh:function(){this.isEdit&&this.getPageList(),this.dialogEditEmpSalaryVisible=!1},exportData:function(){var e=this;this.searchList.length=0,this.pageList=[],this.listLoading=!0,this.listQuery.entityColumn&&""!==this.listQuery.entityColumn&&this.listQuery.queryCondition.EnumOperation?(this.listQuery.queryCondition.Keywords||(this.listQuery.queryCondition.Keywords=""),this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.columnName,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.ConditionList=[this.listQuery.queryCondition]):(this.listQuery.ConditionList=[],this.listQuery.queryCondition={}),r["a"].exportSalaryDetail(this.listQuery).then((function(t){var l=a("19de"),i="员工薪资"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?l(t.data,i):l(t,i),e.getPageList()}))},exportHRData:function(){var e=this;this.searchList.length=0,this.pageList=[],this.listLoading=!0,r["a"].exportHRData(this.listQuery).then((function(t){var l=a("19de"),i="人事数据"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?l(t.data,i):l(t,i),e.getPageList()}))},getReissueDeduction:function(e,t){var a=e.find((function(e){return e.groupId===t}));return a?a.reissueDeduction:""},getDecimalValueOrDefault:function(e){return 0===e||void 0===e?"":e<0?Math.abs(e).toFixed(2):e.toFixed(2)},getStyle:function(e,t,a){if(void 0!==e&&void 0!==t){var l=e[a],i=t[a],n=e.uid,o=t.uid;if(n===o&&void 0!==l&&void 0!==i&&l!==i)return{color:"red"}}},approvalReturn:function(){var e=this;this.$confirm("确定退回人事吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.salaryData.enumStatus=1,r["a"].approvalSalary(e.salaryData).then((function(t){t.succeed?(e.$notice.message("退回成功","success"),window.close(),e.$emit("refreshData")):t.succeed||e.$notice.message("退回失败，请联系管理员","info")})).catch((function(e){console.log(e)}))})).catch((function(e){console.log(e)}))},approvalPass:function(){var e=this;this.$confirm("确定审批通过吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.salaryData.enumStatus=3,r["a"].approvalSalary(e.salaryData).then((function(t){t.succeed?(e.$notice.message("审批成功","success"),window.close(),e.$emit("refreshData")):t.succeed||e.$notice.message("审批失败，请联系管理员","info")})).catch((function(e){console.log(e)}))})).catch((function(e){console.log(e)}))},getStringWidth:function(e){var t=0;return e.length<3?"auto":3===e.length?(t=30*e.length,t+"px"):4===e.length||5===e.length?(t=25*e.length,t+"px"):e.length>5?(t=20*e.length,t+"px"):void 0}}},J=j,X=(a("f589"),Object(h["a"])(J,z,K,!1,null,"7b2e59cc",null)),Z=X.exports,ee=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container "},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"唯一码"},model:{value:e.listQuery.uid,callback:function(t){e.$set(e.listQuery,"uid",t)},expression:"listQuery.uid"}})],1),a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQuery.displayName,callback:function(t){e.$set(e.listQuery,"displayName",t)},expression:"listQuery.displayName"}})],1),a("el-form-item",[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"关键字"},model:{value:e.listQuery.keywords,callback:function(t){e.$set(e.listQuery,"keywords",t)},expression:"listQuery.keywords"}})],1),a("el-form-item",[a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",clearable:"",placeholder:"请选择字段"},model:{value:e.listQuery.entityColumn,callback:function(t){e.$set(e.listQuery,"entityColumn",t)},expression:"listQuery.entityColumn"}},e._l(e.dataColumns,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e}})})),1)],1),a("el-form-item",[a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"请选择条件"},model:{value:e.listQuery.queryCondition.EnumOperation,callback:function(t){e.$set(e.listQuery.queryCondition,"EnumOperation",t)},expression:"listQuery.queryCondition.EnumOperation"}},e._l(e.selectConditionOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),a("el-form-item",[a("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",placeholder:"请输入关键字"},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1),e._e(),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.downloadexceltemplate}},[e._v("模板下载")])],1),a("el-form-item",[a("el-upload",{attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{slot:"trigger",icon:"el-icon-upload",type:"primary"},slot:"trigger"},[e._v("导入")])],1)],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticClass:"my-table",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{label:"上月数据",width:"100px",align:"center",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[l.id.includes("_lastMonth")?e._e():a("input",{staticClass:"choose",attrs:{id:"choose",type:"checkbox"},on:{input:function(t){return e.sarechLastMonthData(l)}}}),l.id.includes("_lastMonth")?a("span",[e._v(e._s(e.lastMonthData.salaryMonth))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"证件号",sortable:"custom","min-width":e.getStringWidth("证件号"),prop:"Employee.IdentityNumber"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"identityNumber")},[e._v(e._s(l.identityNumber))])]}}])}),a("el-table-column",{attrs:{label:"手机号",sortable:"custom","min-width":e.getStringWidth("手机号"),prop:"Employee.Mobile"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"mobile")},[e._v(e._s(l.mobile))])]}}])}),a("el-table-column",{attrs:{label:"在职方式",sortable:"custom","min-width":e.getStringWidth("在职方式"),prop:"Employee.EmployeeHR.HireStyle.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"hireStyleName")},[e._v(e._s(l.hireStyleName))])]}}])}),a("el-table-column",{attrs:{label:"受雇年份",sortable:"custom","min-width":e.getStringWidth("受雇年份"),prop:"EmployYear"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employYear")},[e._v(e._s(l.employYear))])]}}])}),a("el-table-column",{attrs:{label:"受雇月份",sortable:"custom","min-width":e.getStringWidth("受雇月份"),prop:"EmployMonth"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employMonth")},[e._v(e._s(l.employMonth))])]}}])}),a("el-table-column",{attrs:{label:"本年度受雇月数",sortable:"custom","min-width":e.getStringWidth("本年度受雇月数"),prop:"EmployMonthNumber"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employMonthNumber")},[e._v(e._s(l.employMonthNumber))])]}}])}),a("el-table-column",{attrs:{label:"备注1",sortable:"custom","min-width":e.getStringWidth("备注1"),prop:"Memo1"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"memo1")},[e._v(e._s(l.memo1))])]}}])}),a("el-table-column",{attrs:{label:"备注2",sortable:"custom","min-width":e.getStringWidth("备注2"),prop:"Memo2"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"memo2")},[e._v(e._s(l.memo2))])]}}])}),a("el-table-column",{attrs:{label:"备注3",sortable:"custom","min-width":e.getStringWidth("备注3"),prop:"Memo3"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"memo3")},[e._v(e._s(l.memo3))])]}}])}),a("el-table-column",{attrs:{label:"四金不够扣标记",sortable:"custom","min-width":e.getStringWidth("四金不够扣标记"),align:"center",prop:"FourGoldDeficiencySign"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"fourGoldDeficiencySign")},[e._v(e._s(l.fourGoldDeficiencySign?"有":"无"))])]}}])}),a("el-table-column",{attrs:{label:"社保基数",sortable:"custom","min-width":e.getStringWidth("社保基数"),"header-align":"left",align:"right",prop:"SocialSecurityBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"socialSecurityBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.socialSecurityBase)))])]}}])}),a("el-table-column",{attrs:{label:"公积金基数",sortable:"custom","min-width":e.getStringWidth("公积金基数"),"header-align":"left",align:"right",prop:"HousingFundBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"housingFundBase")},[e._v(e._s(parseFloat(l.housingFundBase).toFixed(0)))])]}}])}),a("el-table-column",{attrs:{label:"岗位级别",sortable:"custom","min-width":e.getStringWidth("岗位级别"),align:"center",prop:"Station.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationName")},[e._v(e._s(l.stationName))])]}}])}),a("el-table-column",{attrs:{label:"薪级",sortable:"custom","min-width":e.getStringWidth("薪级"),align:"center",prop:"SalaryScale.scale"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"scale")},[e._v(e._s(l.scale))])]}}])}),a("el-table-column",{attrs:{label:"工龄",sortable:"custom","min-width":e.getStringWidth("工龄"),align:"center",prop:"SocietyAge"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"societyAge")},[e._v(e._s(l.societyAge))])]}}])}),a("el-table-column",{attrs:{label:"工龄段",sortable:"custom","min-width":e.getStringWidth("工龄段"),align:"center",prop:"SeniorityRangeId"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"seniorityRangeName")},[e._v(e._s(l.seniorityRangeName))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金标记",sortable:"custom","min-width":e.getStringWidth("补充公积金标记"),align:"center",prop:"SupplementaryHousingFundFlag"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFundFlag")},[e._v(e._s(l.supplementaryHousingFundFlag?"有":"无"))])]}}])}),a("el-table-column",{attrs:{label:"职员年金标记",sortable:"custom","min-width":e.getStringWidth("职员年金标记"),align:"center",prop:"EmployeePensionFlag"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"employeePensionFlag")},[e._v(e._s(l.employeePensionFlag?"有":"无"))])]}}])}),a("el-table-column",{attrs:{label:"病产假天数",sortable:"custom","min-width":e.getStringWidth("病产假天数"),align:"center",prop:"SickLeaveDays"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"sickLeaveDays")},[e._v(e._s(e.getDecimalValueOrDefault(l.sickLeaveDays)))])]}}])}),a("el-table-column",{attrs:{label:"休假类型",sortable:"custom","min-width":"300px",align:"center",prop:"SalaryLeaveDesc"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"salaryLeaveDesc")},[e._v(e._s(l.salaryLeaveDesc))])]}}])}),a("el-table-column",{attrs:{label:"基本工资小计",sortable:"custom","min-width":e.getStringWidth("基本工资小计"),"header-align":"left",align:"right",prop:"BasicSalarySubtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicSalarySubtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicSalarySubtotal)))])]}}])}),a("el-table-column",{attrs:{label:"岗资基数",sortable:"custom","min-width":e.getStringWidth("岗资基数"),"header-align":"left",align:"right",prop:"StationWageBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationWageBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationWageBase)))])]}}])}),a("el-table-column",{attrs:{label:"岗资",sortable:"custom","min-width":e.getStringWidth("岗资"),"header-align":"left",align:"right",prop:"StationWage"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationWage")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationWage)))])]}}])}),a("el-table-column",{attrs:{label:"薪资基数",sortable:"custom","min-width":e.getStringWidth("薪资基数"),"header-align":"left",align:"right",prop:"SalaryBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"salaryBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.salaryBase)))])]}}])}),a("el-table-column",{attrs:{label:"薪资",sortable:"custom","min-width":e.getStringWidth("薪资"),"header-align":"left",align:"right",prop:"SalaryMoney"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"salaryMoney")},[e._v(e._s(e.getDecimalValueOrDefault(l.salaryMoney)))])]}}])}),a("el-table-column",{attrs:{label:"基本工资其它加",sortable:"custom","min-width":e.getStringWidth("基本工资其它加"),"header-align":"left",align:"right",prop:"BasicSalaryOtherAdd"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicSalaryOtherAdd")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicSalaryOtherAdd)))])]}}])}),a("el-table-column",{attrs:{label:"津补贴小计",sortable:"custom","min-width":e.getStringWidth("津补贴小计"),"header-align":"left",align:"right",prop:"AllowanceSubtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"allowanceSubtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.allowanceSubtotal)))])]}}])}),a("el-table-column",{attrs:{label:"粮油补贴",sortable:"custom","min-width":e.getStringWidth("粮油补贴"),"header-align":"left",align:"right",prop:"GrainOilSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"grainOilSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.grainOilSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"上下班交通费基数",sortable:"custom","min-width":e.getStringWidth("上下班交通费基数"),"header-align":"left",align:"right",prop:"CommuteSubsidyBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"commuteSubsidyBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.commuteSubsidyBase)))])]}}])}),a("el-table-column",{attrs:{label:"上下班交通费",sortable:"custom","min-width":e.getStringWidth("上下班交通费"),"header-align":"left",align:"right",prop:"CommuteSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"commuteSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.commuteSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"护龄基数",sortable:"custom","min-width":e.getStringWidth("护龄基数"),"header-align":"left",align:"right",prop:"NursingBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"nursingBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.nursingBase)))])]}}])}),a("el-table-column",{attrs:{label:"护龄",sortable:"custom","min-width":e.getStringWidth("护龄"),align:"center",prop:"Nursing"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"nursing")},[e._v(e._s(e.getDecimalValueOrDefault(l.nursing)))])]}}])}),a("el-table-column",{attrs:{label:"独子",sortable:"custom","min-width":e.getStringWidth("独子"),align:"center",prop:"OnlyChild"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"onlyChild")},[e._v(e._s(e.getDecimalValueOrDefault(l.onlyChild)))])]}}])}),a("el-table-column",{attrs:{label:"独子补发",sortable:"custom","min-width":e.getStringWidth("独子补发"),"header-align":"left",align:"right",prop:"BackPayOnlyChild"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backPayOnlyChild")},[e._v(e._s(e.getDecimalValueOrDefault(l.backPayOnlyChild)))])]}}])}),a("el-table-column",{attrs:{label:"独子合计",sortable:"custom","min-width":e.getStringWidth("独子合计"),"header-align":"left",align:"right",prop:"OnlyChildTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"onlyChildTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.onlyChildTotal)))])]}}])}),a("el-table-column",{attrs:{label:"援外津贴",sortable:"custom","min-width":e.getStringWidth("援外津贴"),"header-align":"left",align:"right",prop:"ForeignAidAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"foreignAidAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.foreignAidAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"津补贴其它加",sortable:"custom","min-width":e.getStringWidth("津补贴其它加"),"header-align":"left",align:"right",prop:"AllowanceOtherAdd"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"allowanceOtherAdd")},[e._v(e._s(e.getDecimalValueOrDefault(l.allowanceOtherAdd)))])]}}])}),a("el-table-column",{attrs:{label:"绩效小计",sortable:"custom","min-width":e.getStringWidth("绩效小计"),"header-align":"left",align:"right",prop:"PerformanceSubtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"performanceSubtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.performanceSubtotal)))])]}}])}),a("el-table-column",{attrs:{label:"岗位津贴基数",sortable:"custom","min-width":e.getStringWidth("岗位津贴基数"),"header-align":"left",align:"right",prop:"StationAllowanceBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationAllowanceBase")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationAllowanceBase)))])]}}])}),a("el-table-column",{attrs:{label:"岗位津贴",sortable:"custom","min-width":e.getStringWidth("岗位津贴"),"header-align":"left",align:"right",prop:"StationAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"stationAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.stationAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"工作量津贴",sortable:"custom","min-width":e.getStringWidth("工作量津贴"),"header-align":"left",align:"right",prop:"WorkloadAllowance1"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"workloadAllowance1")},[e._v(e._s(e.getDecimalValueOrDefault(l.workloadAllowance1)))])]}}])}),a("el-table-column",{attrs:{label:"工作量津贴合计",sortable:"custom","min-width":e.getStringWidth("工作量津贴合计"),"header-align":"left",align:"right",prop:"WorkloadAllowance2Subtotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"workloadAllowance2Subtotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.workloadAllowance2Subtotal)))])]}}])}),a("el-table-column",{attrs:{label:"停车补贴",sortable:"custom","min-width":e.getStringWidth("停车补贴"),"header-align":"left",align:"right",prop:"ParkingSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"parkingSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.parkingSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"公派车贴补贴",sortable:"custom","min-width":e.getStringWidth("公派车贴补贴"),"header-align":"left",align:"right",prop:"OfficialCarAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"officialCarAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.officialCarAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"电话费",sortable:"custom","min-width":e.getStringWidth("电话费"),"header-align":"left",align:"right",prop:"TelephoneFee"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"telephoneFee")},[e._v(e._s(e.getDecimalValueOrDefault(l.telephoneFee)))])]}}])}),a("el-table-column",{attrs:{label:"电话费合计",sortable:"custom","min-width":e.getStringWidth("电话费合计"),"header-align":"left",align:"right",prop:"TelephoneFeeTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"telephoneFeeTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.telephoneFeeTotal)))])]}}])}),a("el-table-column",{attrs:{label:"长病假职工最低工资补助",sortable:"custom","min-width":e.getStringWidth("长病假职工最低工资补助"),"header-align":"left",align:"right",prop:"LongSickLeaveMinimumWageSubsidy"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"longSickLeaveMinimumWageSubsidy")},[e._v(e._s(e.getDecimalValueOrDefault(l.longSickLeaveMinimumWageSubsidy)))])]}}])}),a("el-table-column",{attrs:{label:"博士后房帖",sortable:"custom","min-width":e.getStringWidth("博士后房帖"),"header-align":"left",align:"right",prop:"PostdoctoralHousingAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"postdoctoralHousingAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.postdoctoralHousingAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"卫生津贴",sortable:"custom","min-width":e.getStringWidth("卫生津贴"),"header-align":"left",align:"right",prop:"HealthAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"healthAllowance")},[e._v(e._s(e.getDecimalValueOrDefault(l.healthAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"零星补节假日加班绩效",sortable:"custom","min-width":e.getStringWidth("零星补节假日加班绩效"),"header-align":"left",align:"right",prop:"OccasionalHolidayOvertimePerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occasionalHolidayOvertimePerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.occasionalHolidayOvertimePerformance)))])]}}])}),a("el-table-column",{attrs:{label:"节假日加班绩效",sortable:"custom","min-width":e.getStringWidth("节假日加班绩效"),"header-align":"left",align:"right",prop:"HolidayOvertimePerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"holidayOvertimePerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.holidayOvertimePerformance)))])]}}])}),a("el-table-column",{attrs:{label:"中夜班绩效",sortable:"custom","min-width":e.getStringWidth("中夜班绩效"),"header-align":"left",align:"right",prop:"MidnightShiftPerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"midnightShiftPerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.midnightShiftPerformance)))])]}}])}),a("el-table-column",{attrs:{label:"行政值班绩效",sortable:"custom","min-width":e.getStringWidth("行政值班绩效"),"header-align":"left",align:"right",prop:"AdministrativeDutyPerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"administrativeDutyPerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.administrativeDutyPerformance)))])]}}])}),a("el-table-column",{attrs:{label:"一二值班绩效",sortable:"custom","min-width":e.getStringWidth("一二值班绩效"),"header-align":"left",align:"right",prop:"FirstSecondDutyPerformance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"firstSecondDutyPerformance")},[e._v(e._s(e.getDecimalValueOrDefault(l.firstSecondDutyPerformance)))])]}}])}),a("el-table-column",{attrs:{label:"其它加",sortable:"custom","min-width":e.getStringWidth("其它加"),"header-align":"left",align:"right",prop:"OtherAdd"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"otherAdd")},[e._v(e._s(e.getDecimalValueOrDefault(l.otherAdd)))])]}}])}),a("el-table-column",{attrs:{label:"行政聘任补发",sortable:"custom","min-width":e.getStringWidth("行政聘任补发"),"header-align":"left",align:"right",prop:"WorkChangeReissue"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"workChangeReissue")},[e._v(e._s(e.getDecimalValueOrDefault(l.workChangeReissue)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣养扣",sortable:"custom","min-width":e.getStringWidth("退还多扣养扣"),"header-align":"left",align:"right",prop:"ReturnOverDeductPension"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductPension")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductPension)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣医疗",sortable:"custom","min-width":e.getStringWidth("退还多扣医疗"),"header-align":"left",align:"right",prop:"ReturnOverDeductMedical"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductMedical")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductMedical)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣失业",sortable:"custom","min-width":e.getStringWidth("退还多扣失业"),"header-align":"left",align:"right",prop:"ReturnOverDeductUnemployment"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductUnemployment")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductUnemployment)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣公扣",sortable:"custom","min-width":e.getStringWidth("退还多扣公扣"),"header-align":"left",align:"right",prop:"ReturnOverDeductPublic"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductPublic")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductPublic)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣补充公积金",sortable:"custom","min-width":e.getStringWidth("退还多扣补充公积金"),"header-align":"left",align:"right",prop:"ReturnOverDeductSupplementaryHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductSupplementaryHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductSupplementaryHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣年金",sortable:"custom","min-width":e.getStringWidth("退还多扣年金"),"header-align":"left",align:"right",prop:"ReturnOverDeductOccupationalAnnuity"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductOccupationalAnnuity")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductOccupationalAnnuity)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣出国人员扣款",sortable:"custom","min-width":e.getStringWidth("退还多扣出国人员扣款"),"header-align":"left",align:"right",prop:"ReturnOverDeductDeductionForForeigners"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductDeductionForForeigners")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductDeductionForForeigners)))])]}}])}),a("el-table-column",{attrs:{label:"退还多扣病假工资",sortable:"custom","min-width":e.getStringWidth("退还多扣病假工资"),"header-align":"left",align:"right",prop:"ReturnOverDeductSickLeaveSalary"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"returnOverDeductSickLeaveSalary")},[e._v(e._s(e.getDecimalValueOrDefault(l.returnOverDeductSickLeaveSalary)))])]}}])}),a("el-table-column",{attrs:{label:"定级补发",sortable:"custom","min-width":e.getStringWidth("定级补发"),"header-align":"left",align:"right",prop:"BackPayGrading"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backPayGrading")},[e._v(e._s(e.getDecimalValueOrDefault(l.backPayGrading)))])]}}])}),a("el-table-column",{attrs:{label:"应发工资",sortable:"custom","min-width":e.getStringWidth("应发工资"),"header-align":"left",align:"right",prop:"GrossSalary"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"grossSalary")},[e._v(e._s(e.getDecimalValueOrDefault(l.grossSalary)))])]}}])}),a("el-table-column",{attrs:{label:"应发工资（申报个税用）",sortable:"custom","min-width":e.getStringWidth("应发工资（申报个税用）"),"header-align":"left",align:"right",prop:"GrossSalaryForTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"grossSalaryForTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.grossSalaryForTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"养扣",sortable:"custom","min-width":e.getStringWidth("养扣"),"header-align":"left",align:"right",prop:"PensionDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"补扣养扣",sortable:"custom","min-width":e.getStringWidth("补扣养扣"),"header-align":"left",align:"right",prop:"BackDeductionPension"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionPension")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionPension)))])]}}])}),a("el-table-column",{attrs:{label:"养扣合计",sortable:"custom","min-width":e.getStringWidth("养扣合计"),"header-align":"left",align:"right",prop:"PensionDeductionTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionDeductionTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionDeductionTotal)))])]}}])}),a("el-table-column",{attrs:{label:"医疗保险",sortable:"custom","min-width":e.getStringWidth("医疗保险"),"header-align":"left",align:"right",prop:"MedicalInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"补扣医疗保险",sortable:"custom","min-width":e.getStringWidth("补扣医疗保险"),"header-align":"left",align:"right",prop:"BackDeductionMedicalInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionMedicalInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionMedicalInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"医疗保险合计",sortable:"custom","min-width":e.getStringWidth("医疗保险合计"),"header-align":"left",align:"right",prop:"MedicalInsuranceTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalInsuranceTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalInsuranceTotal)))])]}}])}),a("el-table-column",{attrs:{label:"失业保险",sortable:"custom","min-width":e.getStringWidth("失业保险"),"header-align":"left",align:"right",prop:"UnemploymentInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"补扣失业保险",sortable:"custom","min-width":e.getStringWidth("补扣失业保险"),"header-align":"left",align:"right",prop:"BackDeductionUnemploymentInsurance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionUnemploymentInsurance")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionUnemploymentInsurance)))])]}}])}),a("el-table-column",{attrs:{label:"失业保险合计",sortable:"custom","min-width":e.getStringWidth("失业保险合计"),"header-align":"left",align:"right",prop:"UnemploymentInsuranceTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentInsuranceTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentInsuranceTotal)))])]}}])}),a("el-table-column",{attrs:{label:"基本公积金",sortable:"custom","min-width":e.getStringWidth("基本公积金"),"header-align":"left",align:"right",prop:"BasicHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"补扣基本公积金",sortable:"custom","min-width":e.getStringWidth("补扣基本公积金"),"header-align":"left",align:"right",prop:"BackDeductionBasicHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionBasicHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionBasicHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"基本公积金合计",sortable:"custom","min-width":e.getStringWidth("基本公积金合计"),"header-align":"left",align:"right",prop:"HousingFundTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"housingFundTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.housingFundTotal)))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金",sortable:"custom","min-width":e.getStringWidth("补充公积金"),"header-align":"left",align:"right",prop:"SupplementaryHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFund")},[e._v(e._s(parseFloat(l.supplementaryHousingFund).toFixed(0)))])]}}])}),a("el-table-column",{attrs:{label:"补扣补充公积金",sortable:"custom","min-width":e.getStringWidth("补扣补充公积金"),"header-align":"left",align:"right",prop:"BackDeductionSupplementaryHousingFund"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionSupplementaryHousingFund")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionSupplementaryHousingFund)))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金合计",sortable:"custom","min-width":e.getStringWidth("补充公积金合计"),"header-align":"left",align:"right",prop:"SupplementaryHousingFundTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFundTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.supplementaryHousingFundTotal)))])]}}])}),a("el-table-column",{attrs:{label:"职业年金",sortable:"custom","min-width":e.getStringWidth("职业年金"),"header-align":"left",align:"right",prop:"OccupationalAnnuity"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occupationalAnnuity")},[e._v(e._s(e.getDecimalValueOrDefault(l.occupationalAnnuity)))])]}}])}),a("el-table-column",{attrs:{label:"补扣职业年金",sortable:"custom","min-width":e.getStringWidth("补扣职业年金"),"header-align":"left",align:"right",prop:"BackDeductionOccupationalAnnuity"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionOccupationalAnnuity")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionOccupationalAnnuity)))])]}}])}),a("el-table-column",{attrs:{label:"职业年金合计",sortable:"custom","min-width":e.getStringWidth("职业年金合计"),"header-align":"left",align:"right",prop:"OccupationalAnnuityTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occupationalAnnuityTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.occupationalAnnuityTotal)))])]}}])}),a("el-table-column",{attrs:{label:"会费",sortable:"custom","min-width":e.getStringWidth("会费"),"header-align":"left",align:"right",prop:"MembershipFee"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"membershipFee")},[e._v(e._s(parseFloat(l.membershipFee).toFixed(1)))])]}}])}),a("el-table-column",{attrs:{label:"补扣会费",sortable:"custom","min-width":e.getStringWidth("补扣会费"),"header-align":"left",align:"right",prop:"BackDeductionMembershipFee"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionMembershipFee")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionMembershipFee)))])]}}])}),a("el-table-column",{attrs:{label:"会费合计",sortable:"custom","min-width":e.getStringWidth("会费合计"),"header-align":"left",align:"right",prop:"MembershipFeeTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"membershipFeeTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.membershipFeeTotal)))])]}}])}),a("el-table-column",{attrs:{label:"房租",sortable:"custom","min-width":e.getStringWidth("房租"),"header-align":"left",align:"right",prop:"Rent"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"rent")},[e._v(e._s(e.getDecimalValueOrDefault(l.rent)))])]}}])}),a("el-table-column",{attrs:{label:"代扣税金",sortable:"custom","min-width":e.getStringWidth("代扣税金"),"header-align":"left",align:"right",prop:"TaxWithholding"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"taxWithholding")},[e._v(e._s(e.getDecimalValueOrDefault(l.taxWithholding)))])]}}])}),a("el-table-column",{attrs:{label:"其它扣",sortable:"custom","min-width":e.getStringWidth("其它扣"),"header-align":"left",align:"right",prop:"OtherDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"otherDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.otherDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"长病假补扣",sortable:"custom","min-width":e.getStringWidth("长病假补扣"),"header-align":"left",align:"right",prop:"BackDeductionLongSickLeave"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"backDeductionLongSickLeave")},[e._v(e._s(e.getDecimalValueOrDefault(l.backDeductionLongSickLeave)))])]}}])}),a("el-table-column",{attrs:{label:"扣出国人员扣款",sortable:"custom","min-width":e.getStringWidth("扣出国人员扣款"),"header-align":"left",align:"right",prop:"DeductionForForeigners"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"deductionForForeigners")},[e._v(e._s(e.getDecimalValueOrDefault(l.deductionForForeigners)))])]}}])}),a("el-table-column",{attrs:{label:"扣款合计",sortable:"custom","min-width":e.getStringWidth("扣款合计"),"header-align":"left",align:"right",prop:"DeductionTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"deductionTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.deductionTotal)))])]}}])}),a("el-table-column",{attrs:{label:"实发工资",sortable:"custom","min-width":e.getStringWidth("实发工资"),"header-align":"left",align:"right",prop:"NetSalary"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"netSalary")},[e._v(e._s(e.getDecimalValueOrDefault(l.netSalary)))])]}}])}),a("el-table-column",{attrs:{label:"养扣基础数据",sortable:"custom","min-width":e.getStringWidth("养扣基础数据"),"header-align":"left",align:"right",prop:"BasicDataDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicDataDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicDataDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"医疗保险基础数据",sortable:"custom","min-width":e.getStringWidth("医疗保险基础数据"),"header-align":"left",align:"right",prop:"MedicalInsuranceBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalInsuranceBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalInsuranceBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"失业保险基础数据",sortable:"custom","min-width":e.getStringWidth("失业保险基础数据"),"header-align":"left",align:"right",prop:"UnemploymentInsuranceBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentInsuranceBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentInsuranceBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"公积金基础数据",sortable:"custom","min-width":e.getStringWidth("公积金基础数据"),"header-align":"left",align:"right",prop:"HousingFundBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"housingFundBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.housingFundBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"补充公积金基础数据",sortable:"custom","min-width":e.getStringWidth("补充公积金基础数据"),"header-align":"left",align:"right",prop:"SupplementaryHousingFundBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"supplementaryHousingFundBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.supplementaryHousingFundBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"年金基础数据",sortable:"custom","min-width":e.getStringWidth("年金基础数据"),"header-align":"left",align:"right",prop:"PensionBasicData"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionBasicData")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionBasicData)))])]}}])}),a("el-table-column",{attrs:{label:"养老（申报个税用）",sortable:"custom","min-width":e.getStringWidth("养老（申报个税用）"),"header-align":"left",align:"right",prop:"PensionTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"pensionTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.pensionTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"医疗（申报个税用）",sortable:"custom","min-width":e.getStringWidth("医疗（申报个税用）"),"header-align":"left",align:"right",prop:"MedicalTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"medicalTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.medicalTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"失业（申报个税用）",sortable:"custom","min-width":e.getStringWidth("失业（申报个税用）"),"header-align":"left",align:"right",prop:"UnemploymentTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"unemploymentTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.unemploymentTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"公扣（申报个税用）",sortable:"custom","min-width":e.getStringWidth("公扣（申报个税用）"),"header-align":"left",align:"right",prop:"PublicTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"publicTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.publicTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"年金（申报个税用）",sortable:"custom","min-width":e.getStringWidth("年金（申报个税用）"),"header-align":"left",align:"right",prop:"OccupationalAnnuityTaxDeclaration"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"occupationalAnnuityTaxDeclaration")},[e._v(e._s(e.getDecimalValueOrDefault(l.occupationalAnnuityTaxDeclaration)))])]}}])}),a("el-table-column",{attrs:{label:"申报个税四金合计",sortable:"custom","min-width":e.getStringWidth("申报个税四金合计"),"header-align":"left",align:"right",prop:"TaxDeclarationFourGold"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"taxDeclarationFourGold")},[e._v(e._s(e.getDecimalValueOrDefault(l.taxDeclarationFourGold)))])]}}])}),a("el-table-column",{attrs:{label:"基本工资合计",sortable:"custom","min-width":e.getStringWidth("基本工资合计"),"header-align":"left",align:"right",prop:"BasicSalaryTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"basicSalaryTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.basicSalaryTotal)))])]}}])}),a("el-table-column",{attrs:{label:"津贴补贴合计",sortable:"custom","min-width":e.getStringWidth("津贴补贴合计"),"header-align":"left",align:"right",prop:"AllowanceTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"allowanceTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.allowanceTotal)))])]}}])}),a("el-table-column",{attrs:{label:"对个人及家庭补助",sortable:"custom","min-width":e.getStringWidth("对个人及家庭补助"),"header-align":"left",align:"right",prop:"OnlyChildTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"onlyChildTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.onlyChildTotal)))])]}}])}),a("el-table-column",{attrs:{label:"奖金合计",sortable:"custom","min-width":e.getStringWidth("奖金合计"),"header-align":"left",align:"right",prop:"BonusTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"bonusTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.bonusTotal)))])]}}])}),a("el-table-column",{attrs:{label:"年终一次性奖励",sortable:"custom","min-width":e.getStringWidth("年终一次性奖励"),"header-align":"left",align:"right",prop:"YearEndBonus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"yearEndBonus")},[e._v(e._s(e.getDecimalValueOrDefault(l.yearEndBonus)))])]}}])}),a("el-table-column",{attrs:{label:"其他各项奖金",sortable:"custom","min-width":e.getStringWidth("其他各项奖金"),"header-align":"left",align:"right",prop:"OtherBonusesTotal"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"otherBonusesTotal")},[e._v(e._s(e.getDecimalValueOrDefault(l.otherBonusesTotal)))])]}}])}),a("el-table-column",{attrs:{label:"实发奖金",sortable:"custom","min-width":e.getStringWidth("实发奖金"),"header-align":"left",align:"right",prop:"ActualBonus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"actualBonus")},[e._v(e._s(e.getDecimalValueOrDefault(l.actualBonus)))])]}}])}),a("el-table-column",{attrs:{label:"税前奖金",sortable:"custom","min-width":e.getStringWidth("税前奖金"),"header-align":"left",align:"right",prop:"PreTaxBonus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"preTaxBonus")},[e._v(e._s(e.getDecimalValueOrDefault(l.preTaxBonus)))])]}}])}),a("el-table-column",{attrs:{label:"奖金扣税",sortable:"custom","min-width":e.getStringWidth("奖金扣税"),"header-align":"left",align:"right",prop:"BonusTax"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"bonusTax")},[e._v(e._s(e.getDecimalValueOrDefault(l.bonusTax)))])]}}])}),a("el-table-column",{attrs:{label:"累计收入额",sortable:"custom","min-width":e.getStringWidth("累计收入额"),"header-align":"left",align:"right",prop:"AccumulatedIncome"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedIncome")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedIncome)))])]}}])}),a("el-table-column",{attrs:{label:"累计专项扣除",sortable:"custom","min-width":e.getStringWidth("累计专项扣除"),"header-align":"left",align:"right",prop:"AccumulatedDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"累计专项附加扣除",sortable:"custom","min-width":e.getStringWidth("累计专项附加扣除"),"header-align":"left",align:"right",prop:"AccumulatedAdditionalDeduction"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedAdditionalDeduction")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedAdditionalDeduction)))])]}}])}),a("el-table-column",{attrs:{label:"累计应纳税所得额",sortable:"custom","min-width":e.getStringWidth("累计应纳税所得额"),"header-align":"left",align:"right",prop:"AccumulatedTaxableIncome"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedTaxableIncome")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedTaxableIncome)))])]}}])}),a("el-table-column",{attrs:{label:"累计已预扣税额",sortable:"custom","min-width":e.getStringWidth("累计已预扣税额"),"header-align":"left",align:"right",prop:"AccumulatedWithheldTax"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"accumulatedWithheldTax")},[e._v(e._s(e.getDecimalValueOrDefault(l.accumulatedWithheldTax)))])]}}])}),a("el-table-column",{attrs:{label:"计税",sortable:"custom","min-width":e.getStringWidth("计税"),"header-align":"left",align:"right",prop:"TaxCalculation"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"taxCalculation")},[e._v(e._s(l.taxCalculation))])]}}])}),a("el-table-column",{attrs:{label:"应发总计",sortable:"custom","min-width":e.getStringWidth("应发总计"),"header-align":"left",align:"right",prop:"TotalPayable"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"totalPayable")},[e._v(e._s(e.getDecimalValueOrDefault(l.totalPayable)))])]}}])}),a("el-table-column",{attrs:{label:"工行帐号",sortable:"custom","min-width":e.getStringWidth("工行帐号"),align:"left",prop:"ICBCAccount"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,i=t.$index;return[a("span",{style:e.getStyle(l,e.pageList[i-1],"iCBCAccount")},[e._v(e._s(l.iCBCAccount))])]}}])}),a("el-table-column",{attrs:{fixed:"right",label:"操作",align:"left","header-align":"center",width:"150","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[e.isEdit?a("el-button",{staticStyle:{"margin-left":"35px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.editEmployeeSalaryDetailDialog(l,!0)}}},[e._v(" 编辑 ")]):e._e(),e.isEdit?e._e():a("el-button",{staticStyle:{"margin-left":"35px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-view",size:"mini"},on:{click:function(t){return e.editEmployeeSalaryDetailDialog(l,!1)}}},[e._v(" 显示 ")])]}}])}),a("employeeTableColumns")],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),a("editDialog",{ref:"editDialog",on:{refreshData:e.getPageList}}),a("employeeSalaryRecordDialog",{ref:"employeeSalaryRecordDialog"})],1)},te=[],ae={components:{editDialog:b,employeeSalaryRecordDialog:k,employeeTableColumns:v["a"]},data:function(){return{isSalaryMonthComplete:!0,showDialog:!1,isEdit:!1,title:"财务数据",pageList:[],groupList:[],salaryData:{},lastMonthData:{},salaryId:"",listQuery:{queryCondition:{},salaryId:"",total:1,pageIndex:1,pageSize:10},searchList:[{id:""}],lastMonth:!1,listLoading:!1,dialogEditEmpSalaryVisible:!1,dataColumns:[{value:"1",label:"旧社保基数",type:"System.Decimal",columnName:"OldSocialSecurityBase"},{value:"2",label:"社保基数",type:"System.Decimal",columnName:"SocialSecurityBase"},{value:"3",label:"旧公积金基数",type:"System.Decimal",columnName:"OldHousingFundBase"},{value:"4",label:"公积金基数",type:"System.Decimal",columnName:"HousingFundBase"},{value:"5",label:"岗位津贴",type:"System.Decimal",columnName:"StationAllowance"},{value:"7",label:"工龄",type:"System.Int32",columnName:"SocietyAge"},{value:"8",label:"补充公积金标记",type:"System.Boolean",columnName:"SupplementaryHousingFundFlag"},{value:"9",label:"职员年金标记",type:"System.Boolean",columnName:"EmployeePensionFlag"},{value:"10",label:"病假天数",type:"System.Decimal",columnName:"SickLeaveDays"},{value:"11",label:"休假类型",type:"System.String",columnName:"SalaryLeaveDesc"},{value:"12",label:"基本工资小计",type:"System.Decimal",columnName:"BasicSalarySubtotal"},{value:"13",label:"岗资基数",type:"System.Decimal",columnName:"StationWageBase"},{value:"14",label:"岗资",type:"System.Decimal",columnName:"StationWage"},{value:"15",label:"薪资基数",type:"System.Decimal",columnName:"SalaryBase"},{value:"16",label:"薪资",type:"System.Decimal",columnName:"SalaryMoney"},{value:"17",label:"基本工资其它加",type:"System.Decimal",columnName:"BasicSalaryOtherAdd"},{value:"18",label:"津补贴小计",type:"System.Decimal",columnName:"AllowanceSubtotal"},{value:"19",label:"粮油补贴",type:"System.Decimal",columnName:"GrainOilSubsidy"},{value:"20",label:"上下班交通费基数",type:"System.Decimal",columnName:"CommuteSubsidyBase"},{value:"21",label:"上下班交通费",type:"System.Decimal",columnName:"CommuteSubsidy"},{value:"22",label:"护龄基数",type:"System.Decimal",columnName:"NursingBase"},{value:"23",label:"护龄",type:"System.Decimal",columnName:"Nursing"},{value:"24",label:"独子",type:"System.Decimal",columnName:"OnlyChild"},{value:"25",label:"独子补发",type:"System.Decimal",columnName:"BackPayOnlyChild"},{value:"26",label:"独子合计",type:"System.Decimal",columnName:"OnlyChildTotal"},{value:"27",label:"援外津贴",type:"System.Decimal",columnName:"ForeignAidAllowance"},{value:"28",label:"援滇津贴",type:"System.Decimal",columnName:"YunnanAidAllowance"},{value:"29",label:"津补贴其它加",type:"System.Decimal",columnName:"AllowanceOtherAdd"},{value:"30",label:"绩效小计",type:"System.Decimal",columnName:"PerformanceSubtotal"},{value:"31",label:"岗位津贴基数",type:"System.Decimal",columnName:"StationAllowanceBase"},{value:"32",label:"工作量津贴1",type:"System.Decimal",columnName:"WorkloadAllowance1"},{value:"33",label:"工作量津贴2小计",type:"System.Decimal",columnName:"WorkloadAllowance2Subtotal"},{value:"34",label:"停车补贴",type:"System.Decimal",columnName:"ParkingSubsidy"},{value:"35",label:"公派车贴补贴",type:"System.Decimal",columnName:"OfficialCarAllowance"},{value:"36",label:"电话费",type:"System.Decimal",columnName:"TelephoneFee"},{value:"37",label:"补发电话费",type:"System.Decimal",columnName:"BackPayTelephoneFee"},{value:"38",label:"电话费合计",type:"System.Decimal",columnName:"TelephoneFeeTotal"},{value:"39",label:"长病假职工最低工资补助",type:"System.Decimal",columnName:"LongSickLeaveMinimumWageSubsidy"},{value:"40",label:"博士后房帖",type:"System.Decimal",columnName:"PostdoctoralHousingAllowance"},{value:"41",label:"卫生津贴",type:"System.Decimal",columnName:"HealthAllowance"},{value:"42",label:"零星补节假日加班绩效",type:"System.Decimal",columnName:"OccasionalHolidayOvertimePerformance"},{value:"43",label:"节假日加班绩效",type:"System.Decimal",columnName:"HolidayOvertimePerformance"},{value:"44",label:"中夜班绩效",type:"System.Decimal",columnName:"MidnightShiftPerformance"},{value:"45",label:"行政值班绩效",type:"System.Decimal",columnName:"AdministrativeDutyPerformance"},{value:"46",label:"一二值班绩效",type:"System.Decimal",columnName:"FirstSecondDutyPerformance"},{value:"47",label:"急诊拖后值班绩效",type:"System.Decimal",columnName:"EmergencyDelayDutyPerformance"},{value:"48",label:"其它加",type:"System.Decimal",columnName:"OtherAdd"},{value:"52",label:"工改补发",type:"System.Decimal",columnName:"WorkChangeReissue"},{value:"53",label:"退还多扣养扣",type:"System.Decimal",columnName:"ReturnOverDeductPension"},{value:"54",label:"退还多扣医疗",type:"System.Decimal",columnName:"ReturnOverDeductMedical"},{value:"55",label:"退还多扣失业",type:"System.Decimal",columnName:"ReturnOverDeductUnemployment"},{value:"56",label:"退还多扣公扣",type:"System.Decimal",columnName:"ReturnOverDeductPublic"},{value:"57",label:"退还多扣补充公积金",type:"System.Decimal",columnName:"ReturnOverDeductSupplementaryHousingFund"},{value:"58",label:"退还多扣年金",type:"System.Decimal",columnName:"ReturnOverDeductOccupationalAnnuity"},{value:"59",label:"退还多扣出国人员扣款",type:"System.Decimal",columnName:"ReturnOverDeductDeductionForForeigners"},{value:"60",label:"退还多扣病假工资",type:"System.Decimal",columnName:"ReturnOverDeductSickLeaveSalary"},{value:"61",label:"长病假、待退休绩效奖",type:"System.Decimal",columnName:"LongSickLeaveRetirementPerformanceBonus"},{value:"62",label:"定级补发",type:"System.Decimal",columnName:"BackPayGrading"},{value:"63",label:"应发工资",type:"System.Decimal",columnName:"GrossSalary"},{value:"64",label:"应发工资（申报个税用）",type:"System.Decimal",columnName:"GrossSalaryForTaxDeclaration"},{value:"65",label:"养扣",type:"System.Decimal",columnName:"PensionDeduction"},{value:"66",label:"补扣养扣",type:"System.Decimal",columnName:"BackDeductionPension"},{value:"67",label:"养扣合计",type:"System.Decimal",columnName:"PensionDeductionTotal"},{value:"68",label:"医疗保险",type:"System.Decimal",columnName:"MedicalInsurance"},{value:"69",label:"补扣医疗保险",type:"System.Decimal",columnName:"BackDeductionMedicalInsurance"},{value:"70",label:"医疗保险合计",type:"System.Decimal",columnName:"MedicalInsuranceTotal"},{value:"71",label:"失业保险",type:"System.Decimal",columnName:"UnemploymentInsurance"},{value:"72",label:"补扣失业保险",type:"System.Decimal",columnName:"BackDeductionUnemploymentInsurance"},{value:"73",label:"失业保险合计",type:"System.Decimal",columnName:"UnemploymentInsuranceTotal"},{value:"74",label:"基本公积金",type:"System.Decimal",columnName:"BasicHousingFund"},{value:"75",label:"补扣基本公积金",type:"System.Decimal",columnName:"BackDeductionBasicHousingFund"},{value:"76",label:"基本公积金合计",type:"System.Decimal",columnName:"HousingFundTotal"},{value:"77",label:"补充公积金",type:"System.Decimal",columnName:"SupplementaryHousingFund"},{value:"78",label:"补扣补充公积金",type:"System.Decimal",columnName:"BackDeductionSupplementaryHousingFund"},{value:"79",label:"补充公积金合计",type:"System.Decimal",columnName:"SupplementaryHousingFundTotal"},{value:"80",label:"职业年金",type:"System.Decimal",columnName:"OccupationalAnnuity"},{value:"81",label:"补扣职业年金",type:"System.Decimal",columnName:"BackDeductionOccupationalAnnuity"},{value:"82",label:"职业年金合计",type:"System.Decimal",columnName:"OccupationalAnnuityTotal"},{value:"83",label:"会费",type:"System.Decimal",columnName:"MembershipFee"},{value:"84",label:"补扣会费",type:"System.Decimal",columnName:"BackDeductionMembershipFee"},{value:"85",label:"会费合计",type:"System.Decimal",columnName:"MembershipFeeTotal"},{value:"86",label:"房租",type:"System.Decimal",columnName:"Rent"},{value:"87",label:"病假工资",type:"System.Decimal",columnName:"SickLeaveSalary"},{value:"88",label:"补扣病假",type:"System.Decimal",columnName:"BackDeductionSickLeave"},{value:"89",label:"病假工资合计",type:"System.Decimal",columnName:"SickLeaveSalaryTotal"},{value:"90",label:"代扣税金",type:"System.Decimal",columnName:"TaxWithholding"},{value:"91",label:"其它扣",type:"System.Decimal",columnName:"OtherDeduction"},{value:"92",label:"长病假补扣",type:"System.Decimal",columnName:"BackDeductionLongSickLeave"},{value:"93",label:"综合险",type:"System.Decimal",columnName:"ComprehensiveInsurance"},{value:"94",label:"户口挂靠费",type:"System.Decimal",columnName:"HukouHostingFee"},{value:"100",label:"扣款合计",type:"System.Decimal",columnName:"DeductionTotal"},{value:"101",label:"实发工资",type:"System.Decimal",columnName:"NetSalary"},{value:"102",label:"房贴2016",type:"System.Decimal",columnName:"HousingAllowance2016"},{value:"103",label:"养扣基础数据",type:"System.Decimal",columnName:"BasicDataDeduction"},{value:"104",label:"医疗保险基础数据",type:"System.Decimal",columnName:"MedicalInsuranceBasicData"},{value:"105",label:"失业保险基础数据",type:"System.Decimal",columnName:"UnemploymentInsuranceBasicData"},{value:"106",label:"公积金基础数据",type:"System.Decimal",columnName:"HousingFundBasicData"},{value:"107",label:"补充公积金基础数据",type:"System.Decimal",columnName:"SupplementaryHousingFundBasicData"},{value:"108",label:"年金基础数据",type:"System.Decimal",columnName:"PensionBasicData"},{value:"109",label:"养老（申报个税用）",type:"System.Decimal",columnName:"PensionTaxDeclaration"},{value:"110",label:"医疗（申报个税用）",type:"System.Decimal",columnName:"MedicalTaxDeclaration"},{value:"111",label:"失业（申报个税用）",type:"System.Decimal",columnName:"UnemploymentTaxDeclaration"},{value:"112",label:"公扣（申报个税用）",type:"System.Decimal",columnName:"PublicTaxDeclaration"},{value:"113",label:"年金（申报个税用）",type:"System.Decimal",columnName:"OccupationalAnnuityTaxDeclaration"},{value:"114",label:"申报个税四金合计",type:"System.Decimal",columnName:"TaxDeclarationFourGold"},{value:"115",label:"基本工资合计",type:"System.Decimal",columnName:"BasicSalaryTotal"},{value:"116",label:"津贴补贴合计",type:"System.Decimal",columnName:"AllowanceTotal"},{value:"117",label:"对个人及家庭补助",type:"System.Decimal",columnName:"PersonalAndFamilySubsidy"},{value:"118",label:"加班工资",type:"System.Decimal",columnName:"OvertimePay"},{value:"119",label:"奖金合计",type:"System.Decimal",columnName:"BonusTotal"},{value:"120",label:"十三月工资（应发）",type:"System.Decimal",columnName:"ThirteenthMonthSalary"},{value:"121",label:"计税工资2（12月计税工资与十三月工资应发之和）",type:"System.Decimal",columnName:"TaxableWage2"},{value:"122",label:"代扣税金2（以计税工资2计算出的代扣税金）",type:"System.Decimal",columnName:"TaxWithholding2"},{value:"123",label:"十三月工资代扣税金",type:"System.Decimal",columnName:"ThirteenthMonthTaxWithholding"},{value:"124",label:"十三月工资（实发）",type:"System.Decimal",columnName:"ThirteenthMonthSalaryNet"},{value:"125",label:"年终一次性奖励",type:"System.Decimal",columnName:"YearEndBonus"},{value:"126",label:"其他各项奖金",type:"System.Decimal",columnName:"OtherBonusesTotal"},{value:"127",label:"实发奖金",type:"System.Decimal",columnName:"ActualBonus"},{value:"128",label:"税前奖金",type:"System.Decimal",columnName:"PreTaxBonus"},{value:"129",label:"奖金扣税",type:"System.Decimal",columnName:"BonusTax"},{value:"130",label:"累计收入额",type:"System.Decimal",columnName:"AccumulatedIncome"},{value:"131",label:"累计专项扣除",type:"System.Decimal",columnName:"AccumulatedDeduction"},{value:"132",label:"累计专项附加扣除",type:"System.Decimal",columnName:"AccumulatedAdditionalDeduction"},{value:"133",label:"累计应纳税所得额",type:"System.Decimal",columnName:"AccumulatedTaxableIncome"},{value:"134",label:"累计已预扣税额",type:"System.Decimal",columnName:"AccumulatedWithheldTax"},{value:"135",label:"待个人汇算时由税务退还个税",type:"System.Decimal",columnName:"TaxToBeReturnedUponPersonalSettlementByTaxAuthority"},{value:"136",label:"计税工资",type:"System.Decimal",columnName:"TaxableWage"},{value:"137",label:"其他工资",type:"System.Decimal",columnName:"OtherWages"}],selectConditionOptions:[],signDropdown:[{value:!0,label:"有"},{value:!1,label:"无"}]}},created:function(){this.initDialog()},mounted:function(){},methods:{initDialog:function(){this.salaryId=this.$route.query.salaryId,this.salaryId&&(this.showDialog=!0,this.listQuery.salaryId=this.salaryId,this.getSalary(),this.getPageList(),this.loadConditions())},employeeSalaryRecord:function(){this.$refs.employeeSalaryRecordDialog.initDialog(this.salaryId)},loadConditions:function(){var e=this;u["a"].getEnumInfos({enumType:"Operations"}).then((function(t){e.selectConditionOptions=t.data.datas})).catch((function(e){console.log(e)}))},getSalary:function(){var e=this;r["a"].getSalary({id:this.listQuery.salaryId}).then((function(t){t.succeed?(e.salaryData=t.data,2===e.salaryData.enumStatus?e.isEdit=!0:e.isEdit=!1):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))},closeDialog:function(){this.pageList=[],this.showDialog=!1},search:function(){this.listQuery.pageIndex=1,this.getPageList(),this.clearCheckboxes()},sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.$delete(this.listQuery,"order"),this.search()},getPageList:function(){var e=this;this.searchList.length=0,this.pageList=[],this.listLoading=!0,this.listQuery.entityColumn&&""!==this.listQuery.entityColumn&&this.listQuery.queryCondition.EnumOperation?(this.listQuery.queryCondition.Keywords||(this.listQuery.queryCondition.Keywords=""),this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.columnName,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.ConditionList=[this.listQuery.queryCondition]):(this.listQuery.ConditionList=[],this.listQuery.queryCondition={}),this.listQuery.enumSalaryStatusType=2,r["a"].querySalaryDetail(this.listQuery).then((function(t){t.succeed?(e.pageList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1}))},clearCheckboxes:function(){this.pageList.forEach((function(e){if(!e.id.includes("_lastMonth")){var t=document.getElementById("choose");t&&(t.checked=!1)}}))},sarechLastMonthData:function(e){var t=this;if(this.searchList.includes(e.id)){this.searchList.pop(e.id);var a=this.pageList.findIndex((function(t){return t.id.includes("_lastMonth")&&t.id.split("_lastMonth")[0]===e.id}));-1!==a&&this.pageList.splice(a,1)}else r["a"].queryLastMonthEmployeeSalary({id:e.id}).then((function(a){if(a.succeed){t.lastMonthData=a.data,t.searchList.push(e.id);var l=Object(s["a"])({},e);Object.assign(l,t.lastMonthData),l.id=e.id+"_lastMonth";var i=t.pageList.indexOf(e);t.pageList.splice(i+1,0,l)}else t.$notice.resultTip(a)})).catch((function(e){console.log(e)}))},editEmployeeSalaryDetailDialog:function(e,t){this.$refs.editDialog.salaryId=this.salaryId,this.$refs.editDialog.initDialog(e,t)},onRefresh:function(){this.isEdit&&this.getPageList(),this.dialogEditEmpSalaryVisible=!1},exportData:function(){var e=this;this.searchList.length=0,this.pageList=[],this.listLoading=!0,this.listQuery.entityColumn&&""!==this.listQuery.entityColumn&&this.listQuery.queryCondition.EnumOperation?(this.listQuery.queryCondition.Keywords||(this.listQuery.queryCondition.Keywords=""),this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.columnName,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.ConditionList=[this.listQuery.queryCondition]):(this.listQuery.ConditionList=[],this.listQuery.queryCondition={}),r["a"].exportSalaryDetail(this.listQuery).then((function(t){var l=a("19de"),i="员工薪资"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?l(t.data,i):l(t,i),e.getPageList()}))},exportHRData:function(){var e=this;this.searchList.length=0,this.pageList=[],this.listLoading=!0,r["a"].exportHRData(this.listQuery).then((function(t){var l=a("19de"),i="人事数据"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?l(t.data,i):l(t,i),e.getPageList()}))},getReissueDeduction:function(e,t){var a=e.find((function(e){return e.groupId===t}));return a?a.reissueDeduction:""},getDecimalValueOrDefault:function(e){return 0===e||void 0===e?"":e<0?Math.abs(e).toFixed(2):e.toFixed(2)},getStyle:function(e,t,a){if(void 0!==e&&void 0!==t){var l=e[a],i=t[a],n=e.uid,o=t.uid;if(n===o&&void 0!==l&&void 0!==i&&l!==i)return{color:"red"}}},approvalReturn:function(){var e=this;this.$confirm("确定退回人事吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.salaryData.enumStatus=1,r["a"].approvalSalary(e.salaryData).then((function(t){t.succeed?(e.$notice.message("退回成功","success"),window.close(),e.$emit("refreshData")):t.succeed||e.$notice.message("退回失败，请联系管理员","info")})).catch((function(e){console.log(e)}))})).catch((function(e){console.log(e)}))},approvalPass:function(){var e=this;this.$confirm("确定审批通过吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.salaryData.enumStatus=3,r["a"].approvalSalary(e.salaryData).then((function(t){t.succeed?(e.$notice.message("审批成功","success"),window.close(),e.$emit("refreshData")):t.succeed||e.$notice.message("审批失败，请联系管理员","info")})).catch((function(e){console.log(e)}))})).catch((function(e){console.log(e)}))},getStringWidth:function(e){var t=0;return e.length<3?"auto":3===e.length?(t=30*e.length,t+"px"):4===e.length||5===e.length?(t=25*e.length,t+"px"):e.length>5?(t=20*e.length,t+"px"):void 0},downloadexceltemplate:function(){hRManageApi.downlodaImportExcelTemplate({type:"importSalaryExtendedDetails"}).then((function(e){var t=a("19de"),l="SalaryExtendedDetailsTemplate.xlsx";e.data?t(e.data,l):t(e,l)})).catch((function(e){}))},importExcel:function(e){var t=this,a=e.file,l=new FormData;r["a"].importSalaryExtendedDetails(a,l,this.salaryId,2).then((function(e){e.succeed&&(t.$message({message:"导入成功",type:"success"}),t.search())})).catch((function(e){t.search()}))}}},le=ae,ie=(a("db19"),Object(h["a"])(le,ee,te,!1,null,"9f9f6778",null)),ne=ie.exports,oe=a("3143"),se={components:{financeSalary:C,thirteenthFinanceSalary:E,retireSalary:Y,fourGoldInsufficientSalary:Z,minimumWageSubsidy:ne,universalReissueDeduction:oe["a"]},data:function(){return{activeName:"financeSalaryTab",salaryId:"",salaryData:{}}},computed:{isThirteenthMonth:function(){return!(!this.salaryData||!this.salaryData.recordMonth)&&13===this.salaryData.recordMonth}},created:function(){this.init()},mounted:function(){},methods:{init:function(){this.salaryId=this.$route.query.salaryId,this.salaryId?this.getSalary():this.$notice.message("系统错误，请刷新页面重试或联系管理员","error")},getSalary:function(){var e=this;r["a"].getSalary({id:this.salaryId}).then((function(t){t.succeed?(e.salaryData=t.data,e.$refs.retire.init()):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))},tabClick:function(e){switch(e.name){case"financeSalaryTab":this.isThirteenthMonth?this.$refs.thirteenthFinanceSalary&&this.$refs.thirteenthFinanceSalary.init():this.$refs.financeSalary&&this.$refs.financeSalary.init();break;case"retireSalaryTab":this.$refs.retireSalary.init();break;case"fourGoldInsufficientSalaryTab":this.$refs.fourGoldInsufficientSalary.init();break;case"universalReissueDeductionTab":this.$refs.universalReissueDeduction.init(this.salaryId);break;case"minimumWageSubsidyTab":this.$refs.minimumWageSubsidy.getList();break;default:this.isThirteenthMonth?this.$refs.thirteenthFinanceSalary&&this.$refs.thirteenthFinanceSalary.init():this.$refs.financeSalary&&this.$refs.financeSalary.init();break}},approvalReturn:function(){var e=this;this.$confirm("确定退回人事吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.salaryData.enumStatus=1,r["a"].approvalSalary(e.salaryData).then((function(t){t.succeed?(e.$notice.message("退回成功","success"),window.close(),e.$emit("refreshData")):t.succeed||e.$notice.message("退回失败，请联系管理员","info")})).catch((function(e){console.log(e)}))})).catch((function(e){console.log(e)}))},approvalPass:function(){var e=this;this.$confirm("确定审批通过吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.salaryData.enumStatus=3,r["a"].approvalSalary(e.salaryData).then((function(t){t.succeed?(e.$notice.message("审批成功","success"),window.close(),e.$emit("refreshData")):t.succeed||e.$notice.message("审批失败，请联系管理员","info")})).catch((function(e){console.log(e)}))})).catch((function(e){console.log(e)}))}}},re=se,ue=Object(h["a"])(re,l,i,!1,null,null,null);t["default"]=ue.exports},a48c:function(e,t,a){"use strict";var l=a("c3fd"),i=a.n(l);i.a},ab72:function(e,t,a){"use strict";var l=a("5e46"),i=a.n(l);i.a},c3fd:function(e,t,a){},c740:function(e,t,a){"use strict";var l=a("23e7"),i=a("b727").findIndex,n=a("44d2"),o=a("ae40"),s="findIndex",r=!0,u=o(s);s in[]&&Array(1)[s]((function(){r=!1})),l({target:"Array",proto:!0,forced:r||!u},{findIndex:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),n(s)},cf3d:function(e,t,a){},d975:function(e,t,a){"use strict";var l=a("e679"),i=a.n(l);i.a},db19:function(e,t,a){"use strict";var l=a("2508"),i=a.n(l);i.a},e207:function(e,t,a){},e679:function(e,t,a){},f589:function(e,t,a){"use strict";var l=a("e207"),i=a.n(l);i.a}}]);