﻿using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.EntityFrameworkCore.Internal;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.EntityFrameworkCore;
using System.Linq;
using System.Transactions;
using System.Data;
using System.Reflection;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace Renji.JHR.Bll
{
    /// <summary>
    /// 工资管理
    /// </summary>
    public class PayrollSetBll : BaseBll
    {
        #region Constructs

        public PayrollSetBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public PayrollSetBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public PayrollSetBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public PayrollSetBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        #region 行政值班费

        public List<EmployeePayRollSalaryAdd> QueryEmployeePayRollSalaryAdd(
           string? payMonth, bool gtZeroValue, Guid? deptId, string? empcode, string? empname,
           int fromRow, int toRow, out int recored)
        {
            recored = 0;
            var list = new List<EmployeePayRollSalaryAdd>();
            string Proc_Name = (Config.IsDM ? "\"JHR\".usp_Pageing_Query_EmpPayRollSalaryAdd" : "usp_Pageing_Query_EmpPayRollSalaryAdd");

            var p1 = this.CreateParameter((Config.IsDM ? "p_PayMonth" : "@PayMonth"), payMonth);
            var p2 = this.CreateParameter((Config.IsDM ? "p_GtZeroValue" : "@GtZeroValue"), gtZeroValue);
            var p3 = this.CreateParameter((Config.IsDM ? "p_DeptId" : "@DeptId"), deptId);
            var p4 = this.CreateParameter((Config.IsDM ? "p_empcode" : "@empcode"), empcode);
            var p5 = this.CreateParameter((Config.IsDM ? "p_empname" : "@empname"), empname);

            var formrowPara = this.CreateParameter((Config.IsDM ? "p_FromRow" : "@FromRow"), fromRow);
            var toRowPara = this.CreateParameter((Config.IsDM ? "p_ToRow" : "@ToRow"), toRow);

            var outputp1 = this.CreateParameter((Config.IsDM ? "p_Record" : "@Record"), System.Data.DbType.Int32);
            outputp1.Direction = System.Data.ParameterDirection.Output;

            using (var dataSet = this.DbContext.ExecuteDataSet(System.Data.CommandType.StoredProcedure, Proc_Name,
                p1, p2, p3, p4, p5,
              formrowPara, toRowPara, outputp1
                ))
            {
                foreach (DataRow row in dataSet.Tables[0].Rows)
                {
                    EmployeePayRollSalaryAdd employeePayRollSalaryAdd = new EmployeePayRollSalaryAdd();
                    employeePayRollSalaryAdd.Employee = new Employee();
                    Guid empId;
                    if (Guid.TryParse(row["empid"].ToString(), out empId))
                        employeePayRollSalaryAdd.Employee.ID = empId;
                    employeePayRollSalaryAdd.Employee.Uid = (int)row["empuid"];
                    employeePayRollSalaryAdd.Employee.DisplayName = row["empname"].ToString();
                    employeePayRollSalaryAdd.Employee.EmpCode = row["EmpCode"].ToString();
                    employeePayRollSalaryAdd.Employee.Department = new Department();
                    employeePayRollSalaryAdd.Employee.Department.Name = row["DeptName"].ToString();
                    employeePayRollSalaryAdd.EmployeeId = empId;
                    Guid eprsId;
                    if (Guid.TryParse(row["ID"].ToString(), out eprsId))
                        employeePayRollSalaryAdd.ID = eprsId;
                    employeePayRollSalaryAdd.PayMonth = row["PayMonth"].ToString();
                    employeePayRollSalaryAdd.LastEditor = row["LastEditor"].ToString();
                    employeePayRollSalaryAdd.H2 = row["H2"] == DBNull.Value ? default(decimal?) : (decimal)row["H2"];
                    list.Add(employeePayRollSalaryAdd);
                }
            }

            if (outputp1.Value != null)
            {
                recored = (int)outputp1.Value;
            }
            return list;
        }

        public EmployeePayRollSalaryAdd UpdateEmployeePayRollSalaryAdd(EmployeePayRollSalaryAdd entity)
        {
            if (entity.ID == default)
            {
                entity.ID = CombGuid.NewGuid();
                return this.Add(entity);
            }
            else
            {
                //写历史
                var notrackingEntity = this.Find<EmployeePayRollSalaryAdd>(entity.ID);

                if (notrackingEntity != null)
                {
                    this.Add(new EmployeePayRollSalaryAddHis
                    {
                        ID = CombGuid.NewGuid(),
                        Operator = this.OperatorUser?.Username,
                        OperateDate = SysDateTime.Now,
                        EPID = notrackingEntity.ID,
                        EmpCode = notrackingEntity.Employee.EmpCode,
                        H2 = notrackingEntity.H2,
                        Deleted = false,
                    }, false);

                    notrackingEntity.H2 = entity.H2;

                    AddEmployeePayRollSalaryAddSalaryChange(entity, notrackingEntity);
                    this.Update(notrackingEntity, true);

                    
                }

                return entity;
            }
        }


        public bool AddEmployeePayRollSalaryAddSalaryChange(EmployeePayRollSalaryAdd employeePayRollSalaryAdd, EmployeePayRollSalaryAdd dbEntity)
        {
            if (dbEntity != null && employeePayRollSalaryAdd!=null)
            {
                var entity = new EmployeePayRollSalaryAddSalaryChange()
                {
                    ID = CombGuid.NewGuid(),
                    EmployeePayRollSalaryAddId = employeePayRollSalaryAdd.ID,
                    IsCalculate = false,
                    Month = Convert.ToDateTime(employeePayRollSalaryAdd.PayMonth),
                    OldH2 = dbEntity?.H2,
                    NewH2 = employeePayRollSalaryAdd.H2,
                    DifferenceH2 = employeePayRollSalaryAdd.H2 - dbEntity?.H2,
                };
                this.Add(entity);
            }
            return true;
        }

        #endregion 行政值班费
    }
}