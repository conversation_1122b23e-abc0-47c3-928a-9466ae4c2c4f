﻿<#@ import namespace="System.Collections.Generic" #>
<#+
	const string connectionString = "Server=xl7.corp.shinsoft.net,9210;Database=Renji_JHR_Mail; uid=*****;pwd=*****;";

	const string database = "Renji_JHR_Mail";

	const string entityNamespace = "Renji.JHR.Entities";
	const string entityPrefix = "";

	const string dalNamespace = "Renji.JHR.Dal";
	const string dbContextName = "MailDbContext";

	const string companyIdTypeName = "Guid";
	const string companyTypeName = "";


	string[] entitySchemas = new string[]
	{
	};

	string[] entityTables = new string[]
	{
	};

	string[] entityViews = new string[]
	{
	};

	string[] entityViewPrefixes = new string[]
	{
		"Vw"
	};

	string[] enumNamespaces = new string[]
	{
		"Shinsoft.Core.Mail"
	};
#>