﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using Shinsoft.Core;
using Shinsoft.Core.Mvc;

namespace Renji.JHR.Entities
{
    public static class UserExtender
    {
        public static bool HasPermission([NotNull]this User user, [NotNull]string permissionCode)
        {
            return user.Permissions?.Contains(permissionCode) ?? false;
        }

        public static bool HasAnyPermission([NotNull]this User user, [NotNull]IEnumerable<string> permissionCodes)
        {
            return user.Permissions.Any(p => permissionCodes.Contains(p));
        }

        public static bool HasPermission([NotNull]this User user, [NotNull]PermissionAttribute permission)
        {
            var exist = permission.PermissionCodes.All(p => user.Permissions?.Contains(p) ?? false);

            return permission.Behavior == PermissionBehavior.Allow
             ? exist
             : !exist;
        }

        public static bool HasAnyPermission([NotNull]this User user, [NotNull]IEnumerable<PermissionAttribute> permissions)
        {
            return permissions.Any(p => user.HasPermission(p));
        }
    }
}
