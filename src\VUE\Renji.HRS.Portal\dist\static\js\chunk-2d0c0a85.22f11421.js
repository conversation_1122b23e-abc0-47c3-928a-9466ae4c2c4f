(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c0a85"],{"435e":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-row",{staticClass:"filter-container",attrs:{gutter:10,type:"flex"}},[a("el-col",{attrs:{span:4}},[a("el-input",{attrs:{placeholder:"名称",clearable:""},model:{value:e.listQuery.name,callback:function(t){e.$set(e.listQuery,"name",e._n(t))},expression:"listQuery.name"}})],1),a("el-col",{staticClass:"filter-button",attrs:{span:2}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.showDialog()}}},[e._v("添加")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{label:"名称",sortable:"custom",prop:"Name"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.name))])]}}])}),a("el-table-column",{attrs:{label:"津贴",sortable:"custom",prop:"Allowance"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(e._f("formatMoney2")(l.allowance)))])]}}])}),a("el-table-column",{attrs:{label:"备注","min-width":"150px ","header-align":"center",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",{staticStyle:{"white-space":"pre-wrap"}},[e._v(e._s(l.memo))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","header-align":"center",width:"230","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.showDialog(l)}}},[e._v(" 编辑 ")]),a("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{icon:"el-icon-delete",size:"mini",type:"primary"},on:{click:function(t){return e.deleteRecord(l)}}},[e._v(" 删除 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),a("editDialog",{ref:"editDialog",on:{refreshData:e.getPageList}})],1)},o=[],i=(a("ac1f"),a("841c"),a("d368")),n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{title:e.title,visible:e.showDialog,width:"40%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:e.closeDialog}},[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.dataModel,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:20}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"名称",clearable:""},model:{value:e.dataModel.name,callback:function(t){e.$set(e.dataModel,"name",t)},expression:"dataModel.name"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:20}},[a("el-form-item",{attrs:{label:"津贴",prop:"allowance"}},[a("el-input",{attrs:{placeholder:"津贴"},model:{value:e.dataModel.allowance,callback:function(t){e.$set(e.dataModel,"allowance",t)},expression:"dataModel.allowance"}})],1)],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{type:"textarea",rows:3,clearable:"",placeholder:"备注"},model:{value:e.dataModel.memo,callback:function(t){e.$set(e.dataModel,"memo",t)},expression:"dataModel.memo"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.closeDialog}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnSaveLoading},on:{click:e.saveDialog}},[e._v("保 存")])],1)],1)],1)},s=[],r={data:function(){var e=function(e,t,a){/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/.test(t)?a():a(new Error("请输入0以上的数字"))};return{showDialog:!1,title:"",rules:{name:[{required:!0,message:"请输入名称",trigger:"blur"}],allowance:[{required:!0,message:"请输入津贴",trigger:"blur"},{validator:e,trigger:"blur"}]},btnSaveLoading:!1,isEdit:!1,dataModel:{}}},methods:{initDialog:function(e){e?(this.title="编辑电话费",this.isEdit=!0,this.getData(e.id)):(this.title="新增电话费",this.isEdit=!1),this.showDialog=!0},getData:function(e){var t=this;i["a"].getTelephoneFee({id:e}).then((function(e){e.succeed&&(t.dataModel=e.data)})).catch((function(e){}))},saveDialog:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.btnSaveLoading=!0,e.isEdit?i["a"].updateTelephoneFee(e.dataModel).then((function(t){t.succeed&&(e.$message({message:"修改成功",type:"success"}),e.btnSaveLoading=!1,e.$emit("refreshData"),e.closeDialog())})).catch((function(t){e.btnSaveLoading=!1})):i["a"].addTelephoneFee(e.dataModel).then((function(t){t.succeed&&(e.$message({message:"添加成功",type:"success"}),e.btnSaveLoading=!1,e.$emit("refreshData"),e.closeDialog())})).catch((function(t){e.btnSaveLoading=!1})))}))},closeDialog:function(){this.dataModel={},this.showDialog=!1,this.$refs.dataForm.resetFields()}}},c=r,d=a("2877"),u=Object(d["a"])(c,n,s,!1,null,null,null),p=u.exports,g={components:{editDialog:p},data:function(){return{addForm:{},dataList:[],total:0,listQuery:{pageIndex:1,pageSize:10},listLoading:!1,temp:{}}},created:function(){this.getPageList()},methods:{search:function(){this.listQuery.pageIndex=1,this.getPageList()},sortChange:function(e){this.listQuery.pageIndex=1;var t="";"descending"===e.order&&(t="-"),"ascending"===e.order&&(t="+"),this.listQuery.order=t+e.prop,this.getPageList()},getPageList:function(){var e=this;this.listLoading=!0,i["a"].queryTelephoneFee(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.dataList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},showDialog:function(e){this.$refs.editDialog.initDialog(e)},deleteRecord:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){i["a"].deleteTelephoneFee(e).then((function(e){e.succeed?(t.search(),t.$notice.message("删除成功","success")):t.$notice.resultTip(e)})).catch((function(e){console.log(e)}))})).catch((function(e){e.succeed||t.$notice.message("取消删除","info")}))}}},f=g,h=Object(d["a"])(f,l,o,!1,null,null,null);t["default"]=h.exports}}]);