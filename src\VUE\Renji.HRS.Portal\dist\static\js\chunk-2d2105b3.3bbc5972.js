(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d2105b3"],{b818:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-row",{staticClass:"filter-container",attrs:{gutter:10,type:"flex"}},[a("el-col",{attrs:{span:4}},[a("el-select",{attrs:{filterable:"",remote:"","reserve-keyword":"",placeholder:"请输入工龄","remote-method":e.remoteMethod,clearable:""},on:{clear:e.clearWorkAge},model:{value:e.listQuery.workAge,callback:function(t){e.$set(e.listQuery,"workAge",t)},expression:"listQuery.workAge"}},e._l(e.workAgeOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.label}})})),1)],1),a("el-col",{staticClass:"filter-button",attrs:{span:8}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.showDialog()}}},[e._v("添加")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-download"},on:{click:e.downloadexceltemplate}},[e._v("下载模板")]),a("el-upload",{staticStyle:{"margin-left":"10px"},attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{slot:"trigger",icon:"el-icon-upload2",type:"primary"},slot:"trigger"},[e._v("导入")])],1),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-download"},on:{click:e.exportExcel}},[e._v("导出")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{label:"工龄",sortable:"custom",prop:"WorkAge"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.workAge))])]}}])}),a("el-table-column",{attrs:{label:"工作量津贴",sortable:"custom",prop:"WorkAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(e._f("formatMoney2")(o.workAllowance)))])]}}])}),a("el-table-column",{attrs:{label:"护龄工资",sortable:"custom",prop:"NursingAgeWage"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(e._f("formatMoney2")(o.nursingAgeWage)))])]}}])}),a("el-table-column",{attrs:{label:"备注","min-width":"150px ","header-align":"center",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",{staticStyle:{"white-space":"pre-wrap"}},[e._v(e._s(o.memo))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","header-align":"center",width:"230","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("el-button",{staticStyle:{"margin-left":"45px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.showDialog(o)}}},[e._v(" 编辑 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),a("editDialog",{ref:"editDialog",on:{refreshData:e.getPageList}})],1)},i=[],l=(a("4de4"),a("c975"),a("ac1f"),a("841c"),a("d368")),r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{title:e.title,visible:e.showDialog,width:"40%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:e.closeDialog}},[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.dataModel,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:20}},[a("el-form-item",{attrs:{label:"工龄",prop:"workAge"}},[a("el-input",{attrs:{disabled:e.isEdit,placeholder:"工龄",clearable:""},model:{value:e.dataModel.workAge,callback:function(t){e.$set(e.dataModel,"workAge",t)},expression:"dataModel.workAge"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:20}},[a("el-form-item",{attrs:{label:"工作量津贴",prop:"workAllowance"}},[a("el-input",{attrs:{placeholder:"工作量津贴"},model:{value:e.dataModel.workAllowance,callback:function(t){e.$set(e.dataModel,"workAllowance",t)},expression:"dataModel.workAllowance"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:20}},[a("el-form-item",{attrs:{label:"护理工资",prop:"nursingAgeWage"}},[a("el-input",{attrs:{placeholder:"护理工资"},model:{value:e.dataModel.nursingAgeWage,callback:function(t){e.$set(e.dataModel,"nursingAgeWage",t)},expression:"dataModel.nursingAgeWage"}})],1)],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{type:"textarea",rows:3,clearable:"",placeholder:"备注"},model:{value:e.dataModel.memo,callback:function(t){e.$set(e.dataModel,"memo",t)},expression:"dataModel.memo"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.closeDialog}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnSaveLoading},on:{click:e.saveDialog}},[e._v("保 存")])],1)],1)],1)},n=[],s={data:function(){var e=function(e,t,a){/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/.test(t)?a():a(new Error("请输入0以上的数字"))},t=function(e,t,a){/^[+]{0,1}(\d+)$|^[+]{0,1}$/.test(t)?a():a(new Error("请输入正整数"))};return{showDialog:!1,title:"",rules:{workAge:[{required:!0,message:"请输入工龄",trigger:"blur"},{validator:t,trigger:"blur"}],workAllowance:[{required:!0,message:"请输入工作量津贴",trigger:"blur"},{validator:e,trigger:"blur"}],nursingAgeWage:[{required:!0,message:"请输入护龄工资",trigger:"blur"},{validator:e,trigger:"blur"}]},btnSaveLoading:!1,isEdit:!1,dataModel:{}}},methods:{initDialog:function(e){e?(this.title="编辑工龄津贴",this.isEdit=!0,this.getData(e.id)):(this.title="新增工龄津贴",this.isEdit=!1),this.showDialog=!0},getData:function(e){var t=this;l["a"].getSeniority({id:e}).then((function(e){e.succeed&&(t.dataModel=e.data)})).catch((function(e){}))},saveDialog:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.btnSaveLoading=!0,e.isEdit?l["a"].updateSeniority(e.dataModel).then((function(t){t.succeed&&(e.$message({message:"修改成功",type:"success"}),e.btnSaveLoading=!1,e.$emit("refreshData"),e.closeDialog())})).catch((function(t){e.btnSaveLoading=!1})):l["a"].addSeniority(e.dataModel).then((function(t){t.succeed&&(e.$message({message:"添加成功",type:"success"}),e.btnSaveLoading=!1,e.$emit("refreshData"),e.closeDialog())})).catch((function(t){e.btnSaveLoading=!1})))}))},closeDialog:function(){this.dataModel={},this.showDialog=!1,this.$refs.dataForm.resetFields()}}},c=s,d=a("2877"),u=Object(d["a"])(c,r,n,!1,null,null,null),g=u.exports,p={components:{editDialog:g},data:function(){return{addForm:{},dataList:[],workAgeList:[],workAgeOptions:[],total:0,listQuery:{pageIndex:1,pageSize:10},listLoading:!1,temp:{}}},created:function(){this.getWorkAgeist(),this.getPageList()},methods:{search:function(){this.listQuery.pageIndex=1,this.getPageList()},sortChange:function(e){this.listQuery.pageIndex=1;var t="";"descending"===e.order&&(t="-"),"ascending"===e.order&&(t="+"),this.listQuery.order=t+e.prop,this.getPageList()},importExcel:function(e){var t=this,a=e.file,o=new FormData;l["a"].importSeniority(a,o).then((function(e){e.succeed&&(t.$message({message:"导入成功",type:"success"}),t.search())})).catch((function(e){t.search()}))},exportExcel:function(){var e=this;l["a"].exportSeniority(this.listQuery).then((function(t){console.log(t);var o=a("19de"),i="工龄津贴"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?o(t.data,i):o(t,i)}))},downloadexceltemplate:function(){l["a"].downloadSeniorityTemplate().then((function(e){var t=a("19de"),o="SeniorityTemplate.xlsx";e.data?t(e.data,o):t(e,o)})).catch((function(e){}))},getWorkAgeist:function(){var e=this;l["a"].querySenioritySelect().then((function(t){t.succeed?(e.workAgeList=t.data.datas,e.workAgeOptions=t.data.datas):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))},remoteMethod:function(e){this.workAgeOptions=""!==e?this.workAgeList.filter((function(t){return t.label.indexOf(e)>-1})):JSON.parse(JSON.stringify(this.workAgeList))},clearWorkAge:function(){this.workAgeOptions=JSON.parse(JSON.stringify(this.workAgeList))},getPageList:function(){var e=this;this.listLoading=!0,l["a"].querySeniority(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.dataList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},showDialog:function(e){this.$refs.editDialog.initDialog(e)}}},f=p,h=Object(d["a"])(f,o,i,!1,null,null,null);t["default"]=h.exports}}]);