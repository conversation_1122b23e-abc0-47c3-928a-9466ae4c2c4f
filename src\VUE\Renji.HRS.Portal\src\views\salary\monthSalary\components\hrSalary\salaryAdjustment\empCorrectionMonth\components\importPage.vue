<template>
  <div>
    <el-dialog :title="title" :visible.sync="showDialog" width="600px" :close-on-click-modal="false" @close="closeDialog">
      <div style="margin-bottom: 20px;">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          show-icon>
          <div slot="description">
            <p>1. 请先下载模板文件，按照模板格式填写数据</p>
            <p>2. 支持Excel文件格式(.xlsx)</p>
            <p>3. 员工工号必须填写，且必须是系统中存在的有效员工</p>
            <p>4. 岗位等级ID必须填写正确的岗位ID</p>
            <p>5. 金额字段请填写数字，支持小数点后4位</p>
            <p>6. 补发/补扣类型：1=补发，2=补扣</p>
          </div>
        </el-alert>
      </div>

      <div style="margin-bottom: 20px;">
        <el-button type="primary" icon="el-icon-download" @click="downloadTemplate">
          下载模板
        </el-button>
      </div>

      <div style="margin-bottom: 20px;">
        <el-upload
          ref="upload"
          action=""
          :http-request="importExcel"
          accept=".xlsx"
          :show-file-list="false"
          :before-upload="beforeUpload">
          <el-button slot="trigger" icon="el-icon-upload2" type="primary">
            选择文件导入
          </el-button>
          <div slot="tip" class="el-upload__tip">
            只能上传xlsx文件
          </div>
        </el-upload>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import salaryApi from '@/api/salary'
import hRManageApi from '@/api/hRManage'

export default {
  props: {
    salaryId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      showDialog: false,
      title: '导入员工修正(月薪)'
    }
  },
  methods: {
    initDialog() {
      this.showDialog = true
    },
    
    // 下载模板
    downloadTemplate() {
      hRManageApi.downlodaImportExcelTemplate({ type: 'importEmployeeSalaryCorrectionMonth' }).then(res => {
        const fileDownload = require('js-file-download')
        var filename = 'EmployeeSalaryCorrectionMonthTemplate.xlsx'
        if (res.data) {
          fileDownload(res.data, filename)
        } else {
          fileDownload(res, filename)
        }
      }).catch((e) => {
        this.$message.error('模板下载失败')
      })
    },

    // 文件上传前验证
    beforeUpload(file) {
      const isXlsx = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isXlsx) {
        this.$message.error('只能上传 xlsx 格式的文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
        return false
      }
      return true
    },

    // 导入Excel
    importExcel(params) {
      const file = params.file
      const formData = new FormData()
      
      this.$loading({
        lock: true,
        text: '正在导入数据，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      salaryApi.importEmployeeSalaryCorrectionMonth(file, formData, this.salaryId).then(res => {
        this.$loading().close()
        if (res.succeed) {
          this.$message({ message: '导入成功', type: 'success' })
          this.closeDialog()
          this.$emit('refresh')
        } else {
          this.$message.error(res.message || '导入失败')
        }
      }).catch(res => {
        this.$loading().close()
        this.$message.error('导入失败，请检查文件格式和数据')
        this.closeDialog()
      })
    },

    closeDialog() {
      this.showDialog = false
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
