<template>
  <div>
    <el-dialog :title="title" :visible="showDialog" width="30%" :close-on-press-escape="false" :close-on-click-modal="false" @close="closeDialog">
      <el-form ref="dataForm">
        <el-row>
          <el-col :span="24">
            <el-form-item>
              <el-row>
                <el-col :span="6">
                  <el-button
                    type="primary"
                    @click="downloadexceltemplate"
                  >
                    <i class="el-icon-download"></i> 模板下载
                  </el-button>
                </el-col>
                <el-col :span="6">
                  <el-upload action="" :http-request="importExcel" accept=".xlsx" :show-file-list="false">
                    <el-button slot="trigger" icon="el-icon-upload2" type="primary">导入</el-button>
                  </el-upload>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 提示信息 -->
        <el-row>
          <el-col :span="24">
            <div style="margin-top: 20px; background-color: #fff7f7; border: 1px solid #f56c6c; border-radius: 4px; padding: 15px; color: #f56c6c; font-size: 14px;">
              <strong>提示：</strong><br>
              <p>1.导入与导出Excel模板相同。<br>2.导入时仅修改员工修正数据，其他数据将保持不变。</p>
            </div>
          </el-col>
        </el-row>

      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import salaryApi from '@/api/salary'
import hRManageApi from '@/api/hRManage'

export default {
  props: {
    salaryId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      showDialog: false,
      title: '导入员工修正(月薪)'
    }
  },
  methods: {
    initDialog() {
      this.title = '导入员工修正(月薪)'
      this.showDialog = true
    },
    downloadexceltemplate() {
      hRManageApi.downlodaImportExcelTemplate({ type: 'importEmployeeSalaryCorrectionMonth' }).then(res => {
        const fileDownload = require('js-file-download')
        var filename = 'EmployeeSalaryCorrectionMonthTemplate.xlsx'
        if (res.data) {
          fileDownload(res.data, filename)
        } else {
          fileDownload(res, filename)
        }
      }).catch(() => { })
    },
    // 导入
    importExcel(params) {
      const file = params.file
      const formData = new FormData()
      salaryApi.importEmployeeSalaryCorrectionMonth(file, formData, this.salaryId).then(res => {
        if (res.succeed) {
          this.$message({ message: '导入成功', type: 'success' })
          this.closeDialog()
          this.$emit('refresh')
        }
      }).catch(() => {
        this.closeDialog()
      })
    },
    closeDialog() {
      this.showDialog = false
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
