(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7be8f742"],{"06c5":function(t,e,n){"use strict";n.d(e,"a",(function(){return a}));n("a630"),n("fb6a"),n("b0c0"),n("d3b7"),n("25f0"),n("3ca3");var r=n("6b75");function a(t,e){if(t){if("string"===typeof t)return Object(r["a"])(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Object(r["a"])(t,e):void 0}}},3835:function(t,e,n){"use strict";function r(t){if(Array.isArray(t))return t}n.d(e,"a",(function(){return c}));n("a4d3"),n("e01a"),n("d28b"),n("d3b7"),n("3ca3"),n("ddb0");function a(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var n=[],r=!0,a=!1,o=void 0;try{for(var i,c=t[Symbol.iterator]();!(r=(i=c.next()).done);r=!0)if(n.push(i.value),e&&n.length===e)break}catch(l){a=!0,o=l}finally{try{r||null==c["return"]||c["return"]()}finally{if(a)throw o}}return n}}var o=n("06c5");function i(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(t,e){return r(t)||a(t,e)||Object(o["a"])(t,e)||i()}},"6b75":function(t,e,n){"use strict";function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}n.d(e,"a",(function(){return r}))},"91df":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("layout3",{scopedSlots:t._u([{key:"aside",fn:function(){return[n("c-tree",{attrs:{options:t.treeData,props:t.treeProps,"expanded-keys":t.treeExpandedKeys},on:{nodeClick:t.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[n("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.headModel}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"申领月份"}},[n("el-date-picker",{attrs:{type:"month",placeholder:"请选择申领月份","value-format":"yyyy-MM"},on:{change:t.dateChange},model:{value:t.recordMonth,callback:function(e){t.recordMonth=e},expression:"recordMonth"}})],1)],1),0==t.headModel.enumStatus?n("el-col",{attrs:{span:12}},[n("el-button",{attrs:{type:"primary"},on:{click:t.submitRecord}},[t._v("提交")])],1):t._e()],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"填表人"}},[t._v(" "+t._s(t.headModel.documentMaker)+" ")])],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"填表日期"}},[t._v(" "+t._s(t.headModel.documentMakeTimeStr)+" ")])],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"申领表状态"}},[t._v(" "+t._s(t.headModel.enumStatusDesc)+" ")])],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"描述"}},[t._v(" "+t._s(t.headModel.rejectReason)+" ")])],1)],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},data:t.tableData,height:"520"},on:{"sort-change":t.sortChange}},[n("el-table-column",{attrs:{prop:"EmpCode",label:"工号",align:"center",width:"80",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.empCode))])]}}])}),n("el-table-column",{attrs:{prop:"DisplayName",label:"姓名",align:"center",width:"80",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.empName))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"在职方式",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.hireStyleName))])]}}])}),n("el-table-column",{attrs:{label:"班次",align:"center"}},[n("el-table-column",{attrs:{prop:"name",label:"中班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.zb=Math.abs(r.zb)}},model:{value:r.zb,callback:function(e){t.$set(r,"zb",t._n(e))},expression:"row.zb"}}):n("span",[t._v(t._s(r.zb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"夜班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.yb=Math.abs(r.yb)}},model:{value:r.yb,callback:function(e){t.$set(r,"yb",t._n(e))},expression:"row.yb"}}):n("span",[t._v(t._s(r.yb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"24小时班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.b24=Math.abs(r.b24)}},model:{value:r.b24,callback:function(e){t.$set(r,"b24",t._n(e))},expression:"row.b24"}}):n("span",[t._v(t._s(r.b24))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"急诊中班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.jzzb=Math.abs(r.jzzb)}},model:{value:r.jzzb,callback:function(e){t.$set(r,"jzzb",t._n(e))},expression:"row.jzzb"}}):n("span",[t._v(t._s(r.jzzb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"急诊夜班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.jzyb=Math.abs(r.jzyb)}},model:{value:r.jzyb,callback:function(e){t.$set(r,"jzyb",t._n(e))},expression:"row.jzyb"}}):n("span",[t._v(t._s(r.jzyb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"急诊24小时",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.jZ24=Math.abs(r.jZ24)}},model:{value:r.jZ24,callback:function(e){t.$set(r,"jZ24",t._n(e))},expression:"row.jZ24"}}):n("span",[t._v(t._s(r.jZ24))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"护理A档中班",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.hlazb=Math.abs(r.hlazb)}},model:{value:r.hlazb,callback:function(e){t.$set(r,"hlazb",t._n(e))},expression:"row.hlazb"}}):n("span",[t._v(t._s(r.hlazb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"护理A档夜班",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.hlayb=Math.abs(r.hlayb)}},model:{value:r.hlayb,callback:function(e){t.$set(r,"hlayb",t._n(e))},expression:"row.hlayb"}}):n("span",[t._v(t._s(r.hlayb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"护理A档24小时",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.hlA24=Math.abs(r.hlA24)}},model:{value:r.hlA24,callback:function(e){t.$set(r,"hlA24",t._n(e))},expression:"row.hlA24"}}):n("span",[t._v(t._s(r.hlA24))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"其他值班1",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.qT1=Math.abs(r.qT1)}},model:{value:r.qT1,callback:function(e){t.$set(r,"qT1",t._n(e))},expression:"row.qT1"}}):n("span",[t._v(t._s(r.qT1))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"其他值班2",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.qT2=Math.abs(r.qT2)}},model:{value:r.qT2,callback:function(e){t.$set(r,"qT2",t._n(e))},expression:"row.qT2"}}):n("span",[t._v(t._s(r.qT2))])]}}])})],1),n("el-table-column",{attrs:{prop:"name",label:"修改人",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.updator))])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[20,50,100],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.paginationChanged}})]},proxy:!0}])})],1)},a=[],o=(n("99af"),n("d81d"),n("fb6a"),n("a9e3"),n("d3b7"),n("ac1f"),n("25f0"),n("4d90"),n("1276"),n("3835")),i=n("d368"),c=n("cbd2"),l={components:{},data:function(){return{total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,recordMonth:this.getNowTime(),headModel:{},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},currentNode:null,tableData:[],allData:[],treeExpandedKeys:[],tjSuccess:!1}},created:function(){this.loadTree()},methods:{getNowTime:function(){var t=new Date,e=t.getFullYear(),n=t.getMonth();n=n.toString().padStart(2,"0"),"00"===n&&(e-=1,n="12");var r="".concat(e,"-").concat(n);return r},loadTree:function(){var t=this;this.treeLoading=!0,i["a"].queryDeptByUser({}).then((function(e){t.treeData=e.data,t.treeData&&t.treeData.length>0&&t.treeExpandedKeys.push(t.treeData[0].id)})).catch((function(t){console.log(t)})).finally((function(){t.treeLoading=!1})),this.resetCurrentNode()},resetCurrentNode:function(){this.currentNode=null},treeNodeClick:function(t){this.currentNode=t,this.listQuery.pageIndex=1,this.getAttMonthShiftRecord()},dateChange:function(){this.listQuery.pageIndex=1,this.getAttMonthShiftRecord()},sortChange:function(t,e,n){this.listQuery.pageIndex=1;var r="";"descending"===t.order?r="desc":"ascending"===t.order&&(r="asc"),this.listQuery.order=r?t.prop+" "+r:"",this.getAttMonthShiftRecord()},getAttMonthShiftRecord:function(){var t=this;if(this.currentNode){var e={RecordMonth:this.recordMonth,DeptId:this.currentNode.id};c["a"].getAttMonthShiftRecord(e).then((function(e){e.succeed?(t.headModel=e.data,t.queryAttMonthShiftRecordDetail(e.data.id)):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))}else this.$notice.message("请选择部门。","info")},queryAttMonthShiftRecordDetail:function(t){var e=this,n={RecordId:t,DeptId:this.currentNode.id,RecordMonth:this.recordMonth,Order:this.listQuery.order};c["a"].queryAttMonthShiftRecordDetail(n).then((function(t){e.listLoading=!1,t.succeed?(e.allData=t.data.datas,e.total=t.data.recordCount,e.getTableData()):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},paginationChanged:function(){this.getTableData()},getTableData:function(){var t=this.listQuery.pageSize,e=this.listQuery.pageIndex;this.tableData=this.allData.slice((e-1)*t,e*t)},submitRecord:function(){for(var t=0;t<this.allData.length;t++){var e=this.allData[t],n=(e.zb||0)+(e.yb||0)+(e.b24||0)+(e.jzzb||0)+(e.jzyb||0)+(e.jZ24||0)+(e.hlazb||0)+(e.hlayb||0)+(e.hlA24||0)+(e.qT1||0)+(e.qT2||0),r=this.recordMonth+"-01",a=r.split("-").map(Number),i=Object(o["a"])(a,2),c=i[0],l=i[1],u=new Date(c,l,0),s=u.getDate();if(n>s)return this.$message.error("(".concat(e.empCode,")").concat(e.empName,"的月份数据超过当月最多天数，请检查。")),void(this.tjSuccess=!1);this.tjSuccess=!0}this.tjSuccess&&this.submit()},submit:function(){var t=this;this.recordMonth&&this.currentNode&&this.$confirm("本考勤员承诺，此数据已经过科主任确认！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.headModel.details=t.allData,c["a"].submitAttMonthShiftRecord(t.headModel).then((function(e){e.succeed?(t.getAttMonthShiftRecord(),t.$notice.message("操作成功","success")):t.$notice.resultTip(e)})).catch((function(e){t.getAttMonthShiftRecord(),console.log(e)}))})),this.currentNode?this.recordMonth||this.$notice.message("请选择月份","warning"):this.$notice.message("请选择部门","warning")}}},u=l,s=n("2877"),d=Object(s["a"])(u,r,a,!1,null,null,null);e["default"]=d.exports},cbd2:function(t,e,n){"use strict";var r=n("cfe3"),a="AttendanceManage",o=new r["a"](a);e["a"]={getAttMonthShiftRecord:function(t){return o.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return o.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return o.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return o.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return o.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return o.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return o.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return o.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return o.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return o.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return o.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return o.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return o.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return o.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return o.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return o.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return o.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return o.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return o.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return o.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return o.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return o.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return o.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return o.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return o.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return o.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return o.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return o.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return o.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return o.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return o.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return o.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return o.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return o.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return o.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return o.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return o.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return o.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return o.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return o.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return o.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return o.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return o.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return o.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return o.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return o.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return o.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return o.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return o.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return o.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return o.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return o.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return o.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return o.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return o.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return o.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return o.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return o.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return o.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return o.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return o.get("GetSameDeptEmployeeWithHealthAllowance",t)}}}}]);