﻿using Shinsoft.Core;
using System.ComponentModel;

namespace Renji.JHR.Entities
{
    public enum AppStatus
    {
        None = 0,

        /// <summary>
        /// 启用
        /// </summary>
        [Description("启用")]
        Enable = 1,

        /// <summary>
        /// 停用
        /// </summary>
        [Description("停用")]
        [EnumGroup("", Ordinal = 2)]
        Disable = -1,

        /// <summary>
        /// 废弃
        /// </summary>
        [Description("废弃")]
        [EnumGroup("", Ordinal = 10)]
        Disuse = -10,
    }
}
