﻿using Renji.JHR.Entities;
using Shinsoft.Core.DynamicQuery;
using System;

namespace Renji.JHR.Api.Models
{
    public partial class RetiredEmployeeReissueFilter
    {
        /// <summary>
        /// 薪资ID
        /// </summary>
        [DynamicQueryColumn(typeof(MinimumWageSubsidy), MinimumWageSubsidy.Columns.SalaryId, Operation = Operation.Equal)]
        public Guid? SalaryId { get; set; }

        /// <summary>
        /// 员工ID
        /// </summary>
        [DynamicQueryColumn(typeof(MinimumWageSubsidy), MinimumWageSubsidy.Columns.EmployeeId, Operation = Operation.Equal)]
        public Guid? EmployeeId { get; set; }

        /// <summary>
        /// 员工唯一码
        /// </summary>
        public string? Uid { get; set; }

        /// <summary>
        /// 员工工号
        /// </summary>
        public string? EmpCode { get; set; }

        /// <summary>
        /// 员工姓名
        /// </summary>
        public string? DisplayName { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string? DeptName { get; set; }


        /// <summary>
        /// 职别
        /// </summary>
        public string? OfficialRankName { get; set; }
    }
}
