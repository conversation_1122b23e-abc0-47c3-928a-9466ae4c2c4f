<template>
  <div>
    <el-dialog :title="title" :visible="showDialog" width="80%" :close-on-press-escape="false" :close-on-click-modal="false" @close="closeDialog">
      <el-form ref="dataForm" :rules="rules" :model="dataModel" label-width="100px">
        <el-card v-if="isEdit" style="margin-top: 3px;">
          <div slot="header">
            <span>基础信息</span>
          </div>
          <el-row>
            <el-col :span="6">
              <el-form-item label="员工姓名">
                <div>
                  <span>
                    {{ dataModel.employeeModel.empName }}
                  </span>
                  <el-button v-if="!isEdit" style="margin-left: 15px;" type="primary" title="选择员工" @click="selectEmployeeDialog">选 择</el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="唯一码">
                {{ dataModel.employeeModel.empUid }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="工号">
                {{ dataModel.employeeModel.empCode }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="性别">
                {{ dataModel.employeeModel.genderDesc }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        <el-card style="margin-top: 3px;">
          <div slot="header">
            <span>基础参数调整信息</span>
          </div>

          <el-row>
            <el-col :span="6">
              <el-form-item label="基础参数" prop="salaryDataId" label-width="120px">
                <el-select v-model="dataModel.salaryDataId" :disabled="isEdit" clearable placeholder="基础参数" @change="salaryDataChage()">
                  <el-option v-for="item in typeList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!--
          <el-row>
            <el-col :span="6">
              <el-form-item label="原参数值" prop="oldValue">
                <el-input-number v-model="dataModel.oldValue"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="新参数值" prop="newValue">
                <el-input-number v-model="dataModel.newValue"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
              </el-form-item>
            </el-col>
          </el-row>
          -->

          <el-row>
            <el-col :span="12">
              <el-form-item label="补发/补扣月份" prop="monthRange" label-width="120px">
                <el-date-picker
                  v-model="dataModel.monthRange"
                  :disabled="isEdit"
                  style="width: 60%;"
                  type="monthrange"
                  range-separator="至"
                  start-placeholder="开始月份"
                  end-placeholder="结束月份"
                  :picker-options="pickerOptions"
                  format="yyyy 年 MM 月"
                  value-format="yyyy-MM-dd"
                  @change="monthRangeChange"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <!--
          <el-row v-if="isEdit">
            <el-col :span="6">
              <el-form-item label="班次" prop="classes">
                <el-input-number v-model="dataModel.classes"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
              </el-form-item>
            </el-col>
          </el-row>
          -->

          <el-row v-if="isEdit">
            <el-col :span="6">
              <el-form-item label="补发/补扣" prop="reissueDeduction" label-width="120px">
                <el-input-number v-model="dataModel.reissueDeduction"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col>
              <el-form-item label="备注" prop="remark" label-width="120px">
                <el-input v-model="dataModel.remark" type="textarea" :rows="3" maxlength="300" placeholder="备注" />
              </el-form-item>
            </el-col>
          </el-row>

        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" :loading="btnSaveLoading" @click="saveDialog">保 存</el-button>
      </div>
    </el-dialog>
    <selectUserComponent ref="selectEmployee" @selectRow="setEmployee" />
  </div>
</template>
<script>
import salaryApi from '@/api/salary'
import selectUserComponent from '@/views/salary/employeeSalary/components/selectUser'
export default {
  components: {
    selectUserComponent
  },
  data() {
    var validateMoney = (rule, value, callback) => {
      if (!(/^-?(\d|[1-9]\d+)(\.\d{1,2})?$/).test(value)) {
        callback(new Error('请输入正确格式数字,小数不超过2位'))
      } else {
        callback()
      }
    }
    return {
      showDialog: false,
      title: '',
      rules: {
        salaryDataId: [
          { required: true, message: '请选择参数值', trigger: 'blur' }
        ],
        monthRange: [
          { required: true, message: '请选择补发/补扣月份', trigger: 'blur' }
        ],
        remark: [{ required: false, message: '请输入备注', trigger: 'blur' }],
        oldValue: [
          { required: false, message: '请输入旧参数值', trigger: 'blur' },
          { validator: validateMoney, trigger: 'blur' }
        ],
        newValue: [
          { required: false, message: '请输入新参数值', trigger: 'blur' },
          { validator: validateMoney, trigger: 'blur' }
        ],
        classes: [
          { required: false, message: '请输入班次', trigger: 'blur' },
          { validator: validateMoney, trigger: 'blur' }
        ],
        reissueDeduction: [
          { required: true, message: '请输入补发/补扣', trigger: 'blur' },
          { validator: validateMoney, trigger: 'blur' }
        ]
      },
      btnSaveLoading: false,
      isEdit: false,
      isRests: false,
      dataModel: {
        employeeModel: {}
      },
      typeList: [],
      pickerOptions: {
        disabledDate: time => {
          return time.getTime() > Date.now()
        }
      }
    }
  },
  created() {
    this.loadtypeList()
  },
  methods: {
    initDialog(row) {
      this.loadtypeList()
      this.isRests = false
      if (!row) {
        this.title = '新增基础参数调整'
        this.isEdit = false
      } else {
        this.title = '编辑基础参数调整'
        this.isEdit = true
        this.getData(row.id)
      }
      this.dataModel.salaryId = this.salaryId
      this.showDialog = true
    },
    getData(id) {
      salaryApi.getSalaryDataAdjust({ id: id }).then(res => {
        if (res.succeed) {
          this.dataModel = res.data

          this.$set(this.dataModel, 'monthRange', [new Date(this.dataModel.startDate), new Date(this.dataModel.endDate)])

          this.setEmployee(res.data.employee)
        }
      }).catch(res => {
      })
    },
    selectEmployeeDialog() {
      this.$refs.selectEmployee.loadTree()
      this.$refs.selectEmployee.showEmployee = true
    },
    saveDialog() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.btnSaveLoading = true
          if (!this.isEdit) {
            salaryApi.addSalaryDataAdjust(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '添加成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          } else {
            salaryApi.updateSalaryDataAdjust(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '修改成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          }
        }
      })
    },
    loadtypeList() {
      const listQuery = {
        pageIndex: 1,
        pageSize: 100,
        isAdjust: true
      }
      salaryApi.querySalaryData(listQuery).then(result => {
        console.log(result)
        this.typeList = result.data.datas
      }).catch(error => {
        console.log(error)
      })
    },
    closeDialog() {
      this.dataModel = {
        employeeModel: {},
        groupModel: {}
      }
      this.typeList = []
      this.groupList = []
      this.showDialog = false
      this.$refs.dataForm.resetFields()
    },
    salaryDataChage() {
      const item = this.typeList.find(item => item.id === this.dataModel.salaryDataId)
      if (item != null) {
        this.dataModel.newValue = item.value
        this.dataModel.oldValue = item.value
      }
    },
    monthRangeChange(date) {
      if (date) {
          const [start, end] = this.dataModel.monthRange
          this.dataModel.startDate = start
          this.dataModel.endDate = end
      this.$set(this.dataModel, 'classes', 10)
      }
    },
    // 计算
    calculate() {
      const value = (this.dataModel.classes * this.dataModel.oldValue) - (this.dataModel.classes * this.dataModel.newValue)
      this.$set(this.dataModel, 'reissueDeduction', Math.ceil(value * 10) / 10)
    },
    setEmployee(emp) {
      this.dataModel.employeeId = emp.id
      this.dataModel.salaryId = this.salaryId
      this.$set(this.dataModel, 'employeeModel',
        {
          employeeId: emp.id,
          empUid: emp.uid,
          empCode: emp.empCode,
          empName: emp.displayName,
          genderDesc: emp.enumGenderDesc,
          empDept: emp.deptName,
          hospitalAreaNameText: emp.hospitalAreaNameText,
          identityNumber: emp.identityNumber,
          hireStyleName: emp.employeeHR.hireStyleName,
          leaveStyleName: emp.employeeHR.leaveStyleName,
          deadDate: emp.employeeHR.deadDate
        })
    }
  }
}
</script>

<style>
</style>
