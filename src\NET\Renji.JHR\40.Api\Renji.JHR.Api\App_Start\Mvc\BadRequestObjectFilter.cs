﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http;
using Microsoft.Extensions.Primitives;
using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Renji.JHR.Api
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false, Inherited = true)]
    public class BadRequestObjectFilter : ActionFilterAttribute
    {
        public override void OnResultExecuting(ResultExecutingContext context)
        {
            base.OnResultExecuting(context);

            if (context.Result is BadRequestObjectResult result && result.Value is ValidationProblemDetails details)
            {
                foreach (var key in details.Errors.Keys)
                {
                    var errors = details.Errors[key];

                    for (var i = 0; i < errors.Length; i++)
                    {
                        errors[i] = errors[i].ToHtmlEncode();
                    }
                }
            }
            else
            {
                //var request = context.HttpContext.Request;

                //if (request.Method == "GET"
                //    && (!request.Headers.TryGetValue(nameof(HttpRequestHeader.Referer), out StringValues value)
                //        || value.ToString().IsEmpty()
                //        || !value.ToString().StartsWith($"{request.Scheme}://{request.Host.Value}")
                //    ))
                //{
                //    context.Result = new ContentResult()
                //    {
                //        StatusCode = (int)HttpStatusCode.BadRequest,
                //        Content = "禁止伪造站点跨域访问"
                //    };
                //}
            }
        }

        public override void OnActionExecuted(ActionExecutedContext context)
        {
            base.OnActionExecuted(context);

            var response = context.HttpContext.Response;
            response.Headers.Add("X-Frame-Options", "DENY");
        }
    }
}