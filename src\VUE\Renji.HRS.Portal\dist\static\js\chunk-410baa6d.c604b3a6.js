(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-410baa6d"],{"0055":function(t,e,a){"use strict";var r=a("a1aa"),n=a.n(r);n.a},1122:function(t,e,a){},"31d3":function(t,e,a){"use strict";var r=a("4275"),n=a.n(r);n.a},"3b5e":function(t,e,a){"use strict";var r=a("722e"),n=a.n(r);n.a},4275:function(t,e,a){},"4cf0":function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-form",{ref:"dataForm",staticClass:"el-dialogform",attrs:{rules:t.rules,model:t.tempFormModel,"label-position":"right","label-width":"110px"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"员工姓名",prop:"empName"}},[t._v(" "+t._s(t.tempFormModel.empName)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"唯一码"}},[t._v(" "+t._s(t.tempFormModel.empUid)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"工号"}},[t._v(" "+t._s(t.tempFormModel.empCode)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"性别"}},[t._v(" "+t._s(t.tempFormModel.genderDesc)+" ")])],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"部门"}},[t._v(" "+t._s(t.tempFormModel.empDept)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"院区"}},[t._v(" "+t._s(t.tempFormModel.hospitalAreaNameText)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"身份证号"}},[t._v(" "+t._s(t.tempFormModel.identityNumber)+" ")])],1)],1),a("el-divider",{staticClass:"confirm",attrs:{"content-position":"left"}},[a("span",{staticStyle:{color:"blue"}},[t._v("基本信息")])]),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"请假类别",prop:"enumLeaveType"}},[t._v(" "+t._s(t.tempFormModel.enumLeaveTypeDesc)+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"就诊医院",prop:"visitingHospital"}},[t._v(" "+t._s(t.tempFormModel.visitingHospital)+" ")])],1)],1),a("el-row",{staticStyle:{"margin-top":"10px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"就诊科室",prop:"visitingDepartment"}},[t._v(" "+t._s(t.tempFormModel.visitingDepartment)+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"就诊医师",prop:"visitingPhysician"}},[t._v(" "+t._s(t.tempFormModel.visitingPhysician)+" ")])],1)],1),a("el-row",{staticStyle:{"margin-top":"10px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"诊断意见",prop:"diagnostiOpinion"}},[t._v(" "+t._s(t.tempFormModel.diagnostiOpinion)+" ")])],1)],1),a("el-divider",{staticClass:"confirm",attrs:{"content-position":"left"}},[a("span",{staticStyle:{color:"blue"}},[t._v("防保科建议")])]),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"假期类型",prop:"enumHolidayType"}},[t._v(" "+t._s(t.tempFormModel.enumHolidayTypeDesc)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"开具时间",prop:"issuingTime"}},[a("span",[t._v(t._s(t.tempFormModel.issuingTime?new Date(t.tempFormModel.issuingTime).Format("yyyy-MM-dd"):""))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"休假开始日期",prop:"leaveStartDate"}},[a("span",[t._v(t._s(t.tempFormModel.leaveStartDate?new Date(t.tempFormModel.leaveStartDate).Format("yyyy-MM-dd"):""))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"休假结束日期",prop:"leaveEndDate"}},[a("span",[t._v(t._s(t.tempFormModel.leaveEndDate?new Date(t.tempFormModel.leaveEndDate).Format("yyyy-MM-dd"):""))])])],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"假期类型备注",prop:"holidayRemark",rules:3==t.tempFormModel.enumHolidayType?t.rules.holidayRemark:[{required:!1,message:"假期类型备注必填",trigger:"blur"}]}},[t._v(" "+t._s(t.tempFormModel.holidayRemark)+" ")])],1)],1),a("el-divider",{staticClass:"confirm",attrs:{"content-position":"left"}},[a("span",{staticStyle:{color:"blue"}},[t._v("休假详情")])]),a("el-row",{attrs:{gutter:10}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:t.tempFormModel.detail,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},"row-class-name":t.handleRowClass}},[a("el-table-column",{attrs:{label:"月份",prop:"recordMonth","header-align":"center",align:"center","min-width":"90px"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.recordMonth?new Date(r.recordMonth).Format("yyyy-MM"):""))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"病假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.h2)+" ")]}}])}),a("el-table-column",{attrs:{prop:"name",label:"事假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.h3)+" ")]}}])}),a("el-table-column",{attrs:{prop:"name",label:"产假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.h4)+" ")]}}])}),a("el-table-column",{attrs:{prop:"name",label:"哺乳假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.h5)+" ")]}}])}),a("el-table-column",{attrs:{prop:"name",label:"探亲假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.h6)+" ")]}}])}),a("el-table-column",{attrs:{prop:"name",label:"计生假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.h7)+" ")]}}])}),a("el-table-column",{attrs:{prop:"name",label:"婚丧假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.h8)+" ")]}}])}),a("el-table-column",{attrs:{prop:"name",label:"脱产读研",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.h9)+" ")]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因公出国",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.h10)+" ")]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因私出国",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.h11)+" ")]}}])}),a("el-table-column",{key:50000100,attrs:{prop:"name",label:"状态",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.enumStatusDesc))])]}}])})],1)],1)],1)],1)},n=[],o=a("cbd2"),l=(a("f9ac"),{computed:{},name:"detailAttDayOffRecordProphylacticDetail",components:{},props:{showDialog:{type:Boolean,default:!1},id:{type:String,default:""},title:{type:String,default:""}},data:function(){return{span:12,rules:{},tempFormModel:{detail:[],id:"",employeeId:"",empUid:"",empCode:"",empName:"",genderDes:"",empDept:"",identityNumber:""},btnSaveLoading:!1,listLoading:!1}},watch:{id:function(t){this.tempFormModel.id=t},showDialog:{immediate:!0,handler:function(t){!0===t&&this.get()}}},mounted:function(){},created:function(){},methods:{get:function(){var t=this;this.btnSaveLoading=!0,o["a"].getAttDayOffRecordProphylacticCase({id:this.id}).then((function(e){t.tempFormModel=e.data,t.btnSaveLoading=!1})).catch((function(e){t.btnSaveLoading=!1}))},clear:function(){this.$refs["dataForm"]&&this.$refs["dataForm"].resetFields(),this.tempFormModel={detail:[],id:this.id,employeeId:"",empUid:"",empCode:"",empName:"",genderDes:"",empDept:"",identityNumber:""}},close:function(){this.clear(),this.$emit("refresh")},cancle:function(){this.clear(),this.$emit("hidden")},handleRowClass:function(t,e){return t.rowIndex%2===0?"cellStyle":"stripedStyle"}}}),i=l,s=(a("0055"),a("879d"),a("2877")),c=Object(s["a"])(i,r,n,!1,null,"0078dc0d",null);e["a"]=c.exports},"722e":function(t,e,a){},"879d":function(t,e,a){"use strict";var r=a("1122"),n=a.n(r);n.a},"8aeb":function(t,e,a){},a1aa:function(t,e,a){},a459:function(t,e,a){"use strict";var r=a("8aeb"),n=a.n(r);n.a},afb1:function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-dialog",{attrs:{"append-to-body":"",title:t.title,width:"90%","close-on-click-modal":!1,visible:!0},on:{close:function(e){return t.cancle()}}},[a("el-form",{ref:"dataForm",staticClass:"el-dialogform",attrs:{rules:t.rules,model:t.tempFormModel,"label-position":"right","label-width":"110px"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"员工姓名",prop:"empName"}},[a("table",{staticClass:"selectButton",staticStyle:{width:"100%","margin-top":"-3px"}},[a("tr",[a("td",[a("el-input",{attrs:{readonly:!0,placeholder:"请选择员工"},model:{value:t.tempFormModel.empName,callback:function(e){t.$set(t.tempFormModel,"empName",e)},expression:"tempFormModel.empName"}})],1),a("td",{staticStyle:{width:"50px"}},[t.isAdd?a("el-button",{staticStyle:{width:"50px"},attrs:{type:"primary",title:"选择员工"},on:{click:t.selectEmployeeDialog}},[t._v("选 择")]):t._e()],1)])])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"唯一码"}},[t._v(" "+t._s(t.tempFormModel.empUid)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"工号"}},[t._v(" "+t._s(t.tempFormModel.empCode)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"性别"}},[t._v(" "+t._s(t.tempFormModel.genderDesc)+" ")])],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"部门"}},[t._v(" "+t._s(t.tempFormModel.empDept)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"院区"}},[t._v(" "+t._s(t.tempFormModel.hospitalAreaNameText)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"身份证号"}},[t._v(" "+t._s(t.tempFormModel.identityNumber)+" ")])],1)],1),a("el-divider",{staticClass:"confirm",attrs:{"content-position":"left"}},[a("span",{staticStyle:{color:"blue"}},[t._v("基本信息")])]),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"请假类别",prop:"enumLeaveType"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{placeholder:"请假类别"},model:{value:t.tempFormModel.enumLeaveType,callback:function(e){t.$set(t.tempFormModel,"enumLeaveType",e)},expression:"tempFormModel.enumLeaveType"}},t._l(t.leaveTypeList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.desc,value:t.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"就诊医院",prop:"visitingHospital"}},[a("el-input",{attrs:{clearable:"",placeholder:"就诊医院",maxlength:"50"},model:{value:t.tempFormModel.visitingHospital,callback:function(e){t.$set(t.tempFormModel,"visitingHospital",e)},expression:"tempFormModel.visitingHospital"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-top":"10px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"就诊科室",prop:"visitingDepartment"}},[a("el-input",{attrs:{clearable:"",placeholder:"就诊科室",maxlength:"50"},model:{value:t.tempFormModel.visitingDepartment,callback:function(e){t.$set(t.tempFormModel,"visitingDepartment",e)},expression:"tempFormModel.visitingDepartment"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"就诊医师",prop:"visitingPhysician"}},[a("el-input",{attrs:{clearable:"",placeholder:"就诊医师",maxlength:"50"},model:{value:t.tempFormModel.visitingPhysician,callback:function(e){t.$set(t.tempFormModel,"visitingPhysician",e)},expression:"tempFormModel.visitingPhysician"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-top":"10px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"诊断意见",prop:"diagnostiOpinion"}},[a("el-input",{attrs:{type:"textarea",rows:3,clearable:"",placeholder:"诊断意见",maxlength:"500"},model:{value:t.tempFormModel.diagnostiOpinion,callback:function(e){t.$set(t.tempFormModel,"diagnostiOpinion",e)},expression:"tempFormModel.diagnostiOpinion"}})],1)],1)],1),a("el-divider",{staticClass:"confirm",attrs:{"content-position":"left"}},[a("span",{staticStyle:{color:"blue"}},[t._v("防保科建议")])]),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"假期类型",prop:"enumHolidayType"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{placeholder:"假期类型"},on:{change:t.recalculate},model:{value:t.tempFormModel.enumHolidayType,callback:function(e){t.$set(t.tempFormModel,"enumHolidayType",e)},expression:"tempFormModel.enumHolidayType"}},t._l(t.holidayTypeList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.desc,value:t.value}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"开具时间",prop:"issuingTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"开具时间","value-format":"yyyy-MM-dd"},model:{value:t.tempFormModel.issuingTime,callback:function(e){t.$set(t.tempFormModel,"issuingTime",e)},expression:"tempFormModel.issuingTime"}})],1)],1),a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"休假日期",prop:"leaveDateRange"}},[a("el-date-picker",{attrs:{clearable:!1,type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.leaveDateChange},model:{value:t.tempFormModel.leaveDateRange,callback:function(e){t.$set(t.tempFormModel,"leaveDateRange",e)},expression:"tempFormModel.leaveDateRange"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"假期类型备注",prop:"holidayRemark",rules:3==t.tempFormModel.enumHolidayType?t.rules.holidayRemark:[{required:!1,message:"假期类型备注必填",trigger:"blur"}]}},[a("el-input",{attrs:{type:"textarea",rows:3,clearable:"",placeholder:"假期类型备注",maxlength:"500"},model:{value:t.tempFormModel.holidayRemark,callback:function(e){t.$set(t.tempFormModel,"holidayRemark",e)},expression:"tempFormModel.holidayRemark"}})],1)],1)],1),a("el-divider",{staticClass:"confirm",attrs:{"content-position":"left"}},[a("span",{staticStyle:{color:"blue"}},[t._v("休假详情")])]),a("el-row",{attrs:{gutter:10}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:t.tempFormModel.detail,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},"row-class-name":t.handleRowClass}},[a("el-table-column",{attrs:{label:"月份",prop:"recordMonth","header-align":"center",align:"center","min-width":"90px"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.recordMonth?new Date(r.recordMonth).Format("yyyy-MM"):""))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"病假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h2=Math.abs(r.h2)}},model:{value:r.h2,callback:function(e){t.$set(r,"h2",t._n(e))},expression:"row.h2"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"事假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h3=Math.abs(r.h3)}},model:{value:r.h3,callback:function(e){t.$set(r,"h3",t._n(e))},expression:"row.h3"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"产假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h4=Math.abs(r.h4)}},model:{value:r.h4,callback:function(e){t.$set(r,"h4",t._n(e))},expression:"row.h4"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"哺乳假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h5=Math.abs(r.h5)}},model:{value:r.h5,callback:function(e){t.$set(r,"h5",t._n(e))},expression:"row.h5"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"探亲假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h6=Math.abs(r.h6)}},model:{value:r.h6,callback:function(e){t.$set(r,"h6",t._n(e))},expression:"row.h6"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"计生假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h7=Math.abs(r.h7)}},model:{value:r.h7,callback:function(e){t.$set(r,"h7",t._n(e))},expression:"row.h7"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"婚丧假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h8=Math.abs(r.h8)}},model:{value:r.h8,callback:function(e){t.$set(r,"h8",t._n(e))},expression:"row.h8"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"脱产读研",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h9=Math.abs(r.h9)}},model:{value:r.h9,callback:function(e){t.$set(r,"h9",t._n(e))},expression:"row.h9"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因公出国",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h10=Math.abs(r.h10)}},model:{value:r.h10,callback:function(e){t.$set(r,"h10",t._n(e))},expression:"row.h10"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因私出国",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h11=Math.abs(r.h11)}},model:{value:r.h11,callback:function(e){t.$set(r,"h11",t._n(e))},expression:"row.h11"}})]}}])}),t.isAdd?t._e():a("el-table-column",{key:50000100,attrs:{prop:"name",label:"状态",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.enumStatusDesc))])]}}],null,!1,374450834)}),a("el-table-column",{attrs:{label:"操作",fixed:"right",align:"center","header-align":"center",width:"90","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[10!=r.enumStatus?a("el-button",{staticStyle:{"padding-left":"3px !important"},attrs:{size:"mini",type:"danger"},on:{click:function(e){return t.handleDelete(r)}}},[t._v(" 删除 ")]):t._e()]}}])})],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{icon:"el-icon-close"},on:{click:function(e){return t.cancle()}}},[t._v(" 关闭 ")]),a("el-button",{attrs:{loading:t.btnSaveLoading,type:"primary",icon:"el-icon-check"},on:{click:function(e){return t.save()}}},[t._v(" 保存 ")])],1)],1),a("selectusercomponent",{ref:"selectempc",on:{selectRow:t.setEmp}})],1)},n=[],o=(a("4de4"),a("7db0"),a("4160"),a("159b"),a("cbd2")),l=a("b113"),i=a("f9ac"),s={computed:{},name:"modifyAttDayOffRecordProphylacticDetail",components:{selectusercomponent:l["a"]},props:{id:{type:String,default:""},title:{type:String,default:""}},data:function(){var t=function(t,e,a){e&&2==e.length?a():a(new Error("休假日期区间必填"))};return{span:12,isAdd:!1,tempFormModel:{detail:[],leaveDateRange:[],id:"",employeeId:"",empUid:"",empCode:"",empName:"",genderDes:"",empDept:"",hospitalAreaNameText:"",identityNumber:""},rules:{empName:[{required:!0,message:"员工必填",trigger:"change"}],enumLeaveType:[{required:!0,message:"请假类别必填",trigger:"change"}],diagnostiOpinion:[{required:!0,message:"诊断意见必填",trigger:"blur"}],visitingHospital:[{required:!0,message:"就诊医院必填",trigger:"blur"}],enumHolidayType:[{required:!0,message:"假期类型必填",trigger:"change"}],holidayRemark:[{required:!0,message:"其他假期类型时备注必填",trigger:"blur"}],leaveDateRange:[{validator:t,trigger:"change"}],issuingTime:[{required:!0,message:"开具时间必填",trigger:"change"}]},btnSaveLoading:!1,leaveTypeList:[],holidayTypeList:[],listLoading:!1}},watch:{id:function(t){this.tempFormModel.id=t}},mounted:function(){},created:function(){this.initLeaveTypeList(),this.initHolidayTypeList(),this.init()},methods:{leaveDateChange:function(){var t=this,e=[];if(this.tempFormModel.leaveDateRange&&2==this.tempFormModel.leaveDateRange.length){var a=this.$moment(this.tempFormModel.leaveDateRange[0]),r=this.$moment(this.tempFormModel.leaveDateRange[1]),n=a.startOf("month");while(n<=r.endOf("month"))e.push(n.format("YYYY-MM")),n.add(1,"month")}e&&e.length>0&&e.forEach((function(e){var a=t.tempFormModel.detail.find((function(a){return t.$moment(a.recordMonth).format("YYYY-MM")==e}));a||t.tempFormModel.detail.push({recordMonth:t.$moment(e+"-01"),h2:null,h3:null,h4:null,h5:null,h6:null,h7:null,h8:null,h9:null,h10:null,h11:null})})),this.tempFormModel.detail=this.tempFormModel.detail.filter((function(a){var r=e.find((function(e){return t.$moment(a.recordMonth).format("YYYY-MM")==e}));return a.id||!!r})),this.tempFormModel.detail.sort((function(e,a){return t.$moment(e.recordMonth)-t.$moment(a.recordMonth)})),this.recalculate()},recalculate:function(){if(this.tempFormModel&&this.tempFormModel.enumHolidayType&&this.tempFormModel&&this.tempFormModel.leaveDateRange&&0!=this.tempFormModel.leaveDateRange.length){var t=this,e=[],a=(this.$moment(this.tempFormModel.leaveDateRange[0]),this.$moment(this.tempFormModel.leaveDateRange[1])),r=this.$moment(this.tempFormModel.leaveDateRange[0]).startOf("month");while(r<=a.endOf("month"))e.push(r.format("YYYY-MM")),r.add(1,"month");this.tempFormModel.detail.forEach((function(a,r,n){var o=e.find((function(e){return t.$moment(a.recordMonth).format("YYYY-MM")==e}));if(o&&3!=t.tempFormModel.enumHolidayType){var l=t.$moment(a.recordMonth),i=t.$moment(a.recordMonth).endOf("months"),s=t.$moment(t.tempFormModel.leaveDateRange[0]),c=t.$moment(t.tempFormModel.leaveDateRange[1]);l=s>l?s:l,i=c<i?c:i;var d=i.diff(l,"days")+1;1==t.tempFormModel.enumHolidayType?(t.$set(a,"h2",d),t.$set(a,"h4",null)):(t.$set(a,"h2",null),t.$set(a,"h4",d))}else t.$set(a,"h2",null),t.$set(a,"h4",null);t.$set(a,"h3",null),t.$set(a,"h5",null),t.$set(a,"h6",null),t.$set(a,"h7",null),t.$set(a,"h8",null),t.$set(a,"h9",null),t.$set(a,"h10",null),t.$set(a,"h11",null)}))}},init:function(){if(!this.id)return this.isAdd=!0,void this.clear();this.id&&(this.isAdd=!1,this.get(this.id))},get:function(t){var e=this;this.btnSaveLoading=!0,o["a"].getAttDayOffRecordProphylacticCase({id:this.id}).then((function(t){e.tempFormModel=t.data,e.btnSaveLoading=!1})).catch((function(t){e.btnSaveLoading=!1}))},selectEmployeeDialog:function(){this.$refs.selectempc.showEmp=!0},setEmp:function(t){this.tempFormModel.employeeId=t.id,this.tempFormModel.empUid=t.uid,this.tempFormModel.empCode=t.empCode,this.tempFormModel.empName=t.displayName,this.tempFormModel.genderDesc=t.enumGenderDesc,this.tempFormModel.empDept=t.deptName,this.tempFormModel.hospitalAreaNameText=t.hospitalAreaNameText,this.tempFormModel.identityNumber=t.identityNumber},initLeaveTypeList:function(){var t=this,e={enumType:"LeaveType"};i["a"].getEnumInfos(e).then((function(e){t.leaveTypeList=e.data.datas})).catch((function(t){console.log(t)}))},initHolidayTypeList:function(){var t=this,e={enumType:"HolidayType"};i["a"].getEnumInfos(e).then((function(e){t.holidayTypeList=e.data.datas})).catch((function(t){console.log(t)}))},save:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&(t.tempFormModel.leaveStartDate=t.tempFormModel.leaveDateRange[0],t.tempFormModel.leaveEndDate=t.tempFormModel.leaveDateRange[1],t.tempFormModel.id?t.update():t.addNew())}))},addNew:function(){var t=this;this.btnSaveLoading=!0,o["a"].addAttDayOffRecordProphylactic(this.tempFormModel).then((function(e){t.btnSaveLoading=!1,e.succeed?(t.$notice.message("新增成功","success"),t.close(e.data.id)):-3!==e.type&&t.$notice.resultTip(e)})).catch((function(e){t.btnSaveLoading=!1,e.processed||t.$notice.message("新增失败。","error")}))},update:function(){var t=this;this.btnSaveLoading=!0,o["a"].updateAttDayOffRecordProphylactic(this.tempFormModel).then((function(e){t.btnSaveLoading=!1,e.succeed?(t.$notice.message("修改成功","success"),t.close(e.data.id)):-3!==e.type&&t.$notice.resultTip(e)})).catch((function(e){t.btnSaveLoading=!1,e.processed||t.$notice.message("修改失败。","error")}))},handleDelete:function(t){var e=this;this.$confirm("确定删除当前月份防保科考勤申报?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.id?o["a"].deleteAttDayOffRecordProphylacticDetail(t).then((function(a){a.succeed?(e.tempFormModel.detail=e.tempFormModel.detail.filter((function(e){return t.recordMonth!=e.recordMonth})),e.$notice.message("删除成功","success")):-3!==a.type&&e.$notice.resultTip(a)})).catch((function(t){t.processed||e.$notice.message("删除失败","error")})):e.tempFormModel.detail=e.tempFormModel.detail.filter((function(e){return t.recordMonth!=e.recordMonth}))})).catch((function(t){t.succeed}))},handleRowClass:function(t,e){return t.rowIndex%2===0?"cellStyle":"stripedStyle"},clear:function(){this.$refs["dataForm"]&&this.$refs["dataForm"].resetFields(),this.tempFormModel={visitingHospital:"仁济医院",detail:[],leaveDateRange:[],id:this.id,employeeId:"",empUid:"",empCode:"",empName:"",genderDes:"",empDept:"",identityNumber:""}},close:function(t){this.clear(),this.$emit("refresh",{attDayOffRecordProphylacticCaseId:t})},cancle:function(){this.clear(),this.$emit("hidden")}}},c=s,d=(a("a459"),a("3b5e"),a("2877")),p=Object(d["a"])(c,r,n,!1,null,"85424006",null);e["a"]=p.exports},b0f3:function(t,e,a){t.exports=a.p+"static/img/fbkseal.b2cd35da.png"},c126:function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticStyle:{display:"none"}},[r("div",{staticClass:"table-c",staticStyle:{"FONT-FAMILY":"宋体"},attrs:{id:"prophylacticPrintContent",align:"center"}},[r("el-row",{staticStyle:{"margin-top":"10px"}},[r("el-col",{staticStyle:{"font-size":"17px","text-align":"center"},attrs:{span:24}},[t._v(" 上海交通大学医学院附属仁济医院"),r("br"),t._v("职工学生疾病报告单 ")])],1),r("table",{staticStyle:{width:"95%","margin-top":"10px","font-size":"13px"},attrs:{cellspacing:"0",cellpadding:"0"}},[r("tr",{staticClass:"trHieight"},[r("td",{staticClass:"textCenter"},[t._v(" 姓名 ")]),r("td",{staticClass:"textLeft"},[r("span",{staticStyle:{"margin-left":"4px"}},[t._v(t._s(t.model.empName))])]),r("td",{staticClass:"textCenter"},[t._v(" 性别 ")]),r("td",{staticClass:"textLeft"},[r("span",{staticStyle:{"margin-left":"4px"}},[t._v(t._s(t.model.genderDesc))])]),r("td",{staticClass:"textCenter"},[t._v(" 年龄 ")]),r("td",{staticClass:"textLeft"},[r("span",{staticStyle:{"margin-left":"4px"}},[t._v(t._s(t.model.age))])])]),r("tr",{staticClass:"trHieight"},[r("td",{staticClass:"textCenter"},[t._v(" 科室 ")]),r("td",{staticClass:"textLeft"},[r("span",{staticStyle:{"margin-left":"4px"}},[t._v(t._s(t.model.empDept))])]),r("td",{staticClass:"textCenter"},[t._v(" 工号 ")]),r("td",{staticClass:"textLeft"},[r("span",{staticStyle:{"margin-left":"4px"}},[t._v(t._s(t.model.empCode))])]),r("td",{staticClass:"textCenter"},[t._v(" 请假类别 ")]),r("td",{staticClass:"textLeft"},[r("span",{staticStyle:{"margin-left":"4px"}},[t._v(t._s(t.model.enumLeaveTypeDesc))])])]),r("tr",{staticClass:"trHieight"},[r("td",{staticClass:"textCenter"},[t._v(" 诊断 ")]),r("td",{staticClass:"textLeft",attrs:{colspan:"5"}},[r("span",{staticStyle:{"margin-left":"4px"}},[t._v(t._s(t.model.diagnostiOpinion))])])]),r("tr",{staticClass:"trHieight"},[r("td",{staticClass:"textCenter",attrs:{rowspan:"3"}},[t._v(" 防保科建议 ")]),r("td",{staticClass:"textLeft"},[r("span",{staticStyle:{"margin-left":"4px"}},[t._v(t._s(t.model.enumHolidayTypeDesc))])]),r("td",{staticClass:"textLeft",attrs:{colspan:"4"}},[r("span",{staticStyle:{"margin-left":"4px"}},[t._v(t._s(t.model.holidayRemark))])])]),r("tr",{staticClass:"trHieight"},[r("td",{staticClass:"textLeft",attrs:{colspan:"5"}},[r("span",{staticStyle:{"margin-left":"4px"}},[t._v("休假日期: 自 "+t._s(t.model.leaveStartDate?new Date(t.model.leaveStartDate).Format("yyyy-MM-dd"):"")+" 至 "+t._s(t.model.leaveEndDate?new Date(t.model.leaveEndDate).Format("yyyy-MM-dd"):""))])])]),r("tr",{staticClass:"trHieight"},[r("td",{staticClass:"textCenter"},[t._v("病假单开具时间")]),r("td",{staticClass:"textLeft"},[r("span",{staticStyle:{"margin-left":"4px"}},[t._v(t._s(t.model.issuingTime?new Date(t.model.issuingTime).Format("yyyy-MM-dd"):""))])]),r("td",{staticClass:"textCenter"},[t._v("病假单开具医院")]),r("td",{staticClass:"textLeft",attrs:{colspan:"2"}},[r("span",{staticStyle:{"margin-left":"4px"}},[t._v(t._s(t.model.visitingHospital))])])]),r("tr",{staticClass:"trHieight"},[r("td",{staticClass:"textCenter"},[t._v(" 假期详情 ")]),r("td",{staticClass:"textLeft",attrs:{colspan:"5"}},[this.totle.h2?r("span",{staticStyle:{"margin-left":"4px"}},[t._v(" 病假: "+t._s(this.totle.h2)+" ")]):t._e(),this.totle.h3?r("span",{staticStyle:{"margin-left":"4px"}},[t._v(" 事假: "+t._s(this.totle.h3)+" ")]):t._e(),this.totle.h4?r("span",{staticStyle:{"margin-left":"4px"}},[t._v(" 产假: "+t._s(this.totle.h4)+" ")]):t._e(),this.totle.h5?r("span",{staticStyle:{"margin-left":"4px"}},[t._v(" 哺乳假:"+t._s(this.totle.h5)+" ")]):t._e(),this.totle.h6?r("span",{staticStyle:{"margin-left":"4px"}},[t._v(" 探亲假:"+t._s(this.totle.h6)+" ")]):t._e(),this.totle.h7?r("span",{staticStyle:{"margin-left":"4px"}},[t._v(" 计生假:"+t._s(this.totle.h7)+" ")]):t._e(),this.totle.h8?r("span",{staticStyle:{"margin-left":"4px"}},[t._v(" 婚丧假:"+t._s(this.totle.h8)+" ")]):t._e(),this.totle.h9?r("span",{staticStyle:{"margin-left":"4px"}},[t._v(" 脱产读研:"+t._s(this.totle.h9)+" ")]):t._e(),this.totle.h10?r("span",{staticStyle:{"margin-left":"4px"}},[t._v(" 因公出国:"+t._s(this.totle.h10)+" ")]):t._e(),this.totle.h11?r("span",{staticStyle:{"margin-left":"4px"}},[t._v("因私出国:"+t._s(this.totle.h11)+" ")]):t._e()])]),r("tr",{staticStyle:{height:"200px"}},[r("td",{staticClass:"textCenter"},[t._v("科室领导审核意见:")]),r("td",{attrs:{colspan:"5"}},[r("div",{staticClass:"outer"},[r("img",{staticClass:"img",staticStyle:{width:"181px",height:"133px"},attrs:{src:a("b0f3")}})])])])])],1),r("el-button",{directives:[{name:"print",rawName:"v-print",value:t.prophylacticprintObj,expression:"prophylacticprintObj"}],ref:"btnPrint"})],1)},n=[],o=(a("4160"),a("159b"),{data:function(){return{prophylacticprintObj:{id:"prophylacticPrintContent",popTitle:"",extraCss:"",extraHead:'<meta http-equiv="Content-Language" content="zh-cn"/>'},model:{},totle:{h2:0,h3:0,h4:0,h5:0,h6:0,h7:0,h8:0,h9:0,h10:0,h11:0}}},methods:{Print:function(t){var e=this;e.totle={h2:0,h3:0,h4:0,h5:0,h6:0,h7:0,h8:0,h9:0,h10:0,h11:0},t.detail.forEach((function(t,a,r){e.totle.h2+=t.h2?t.h2:0,e.totle.h3+=t.h3?t.h3:0,e.totle.h4+=t.h4?t.h4:0,e.totle.h5+=t.h5?t.h5:0,e.totle.h6+=t.h6?t.h6:0,e.totle.h7+=t.h7?t.h7:0,e.totle.h8+=t.h8?t.h8:0,e.totle.h9+=t.h9?t.h9:0,e.totle.h10+=t.h10?t.h10:0,e.totle.h11+=t.h11?t.h11:0}),0),this.model=t,this.$refs.btnPrint.$el.click()}}}),l=o,i=(a("31d3"),a("2877")),s=Object(i["a"])(l,r,n,!1,null,null,null);e["a"]=s.exports},cbd2:function(t,e,a){"use strict";var r=a("cfe3"),n="AttendanceManage",o=new r["a"](n);e["a"]={getAttMonthShiftRecord:function(t){return o.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return o.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return o.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return o.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return o.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return o.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return o.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return o.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return o.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return o.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return o.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return o.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return o.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return o.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return o.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return o.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return o.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return o.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return o.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return o.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return o.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return o.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return o.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return o.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return o.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return o.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return o.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return o.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return o.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return o.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return o.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return o.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return o.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return o.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return o.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return o.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return o.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return o.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return o.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return o.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return o.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return o.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return o.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return o.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return o.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return o.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return o.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return o.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return o.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return o.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return o.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return o.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return o.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return o.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return o.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return o.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return o.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return o.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return o.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return o.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return o.get("GetSameDeptEmployeeWithHealthAllowance",t)}}}}]);