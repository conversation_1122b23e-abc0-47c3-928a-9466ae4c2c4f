﻿using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.EntityFrameworkCore.Internal;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.EntityFrameworkCore;
using System.Linq;
using Microsoft.EntityFrameworkCore;

namespace Renji.JHR.Bll
{
    public class SysManageBll : BaseBll
    {
        #region Constructs

        public SysManageBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public SysManageBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public SysManageBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public SysManageBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        #region Dict

        public BizResult<Dict> AddDict(Dict entity)
        {
            var result = new BizResult<Dict>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            if (entity.Code.IsEmpty())
            {
                result.Error("字典类型编号不可以为空");
            }
            if (entity.Name.IsEmpty())
            {
                result.Error("简称不可以为空");
            }
            if (entity.FullName.IsEmpty())
            {
                result.Error("全称不可以为空");
            }

            if (result.Succeed)
            {
                result.Data = this.Add(entity);
            }

            return result;
        }

        public BizResult<Dict> UpdateDict(Dict entity)
        {
            var result = new BizResult<Dict>();

            var id = entity.ID;
            var dbEntity = this.Get<Dict>(id);

            if (dbEntity == null)
            {
                result.Error("字典项不存在");
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    if (entity.Code.IsEmpty())
                    {
                        result.Error("字典项不可以为空");
                    }
                    if (entity.Name.IsEmpty())
                    {
                        result.Error("简称不可以为空");
                    }
                    if (entity.FullName.IsEmpty())
                    {
                        result.Error("全称不可以为空");
                    }

                    if (result.Succeed)
                    {
                        result.Data = entity;

                        this.SaveChanges();
                    }
                    else
                    {
                        this.Detach(entity);
                    }
                }
                else
                {
                    result.Data = dbEntity;
                }
            }

            return result;
        }

        public BizResult DeleteDict(Dict entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<Dict>(id);

            if (dbEntity == null)
            {
                result.Error("字典项不存在");
            }
            else
            {
                //逻辑删除职位
                this.Delete(entity, false);

                this.SaveChanges();
            }

            return result;
        }

        #endregion Dict

        #region SysSetting

        public BizResult<SysSetting> AddSysSetting(SysSetting entity)
        {
            var result = new BizResult<SysSetting>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            if (entity.Name.IsEmpty())
            {
                result.Error("名称不可以为空");
            }
            if (entity.Key.IsEmpty())
            {
                result.Error("主键不可以为空");
            }
            if (entity.Value.IsEmpty())
            {
                result.Error("值不可以为空");
            }

            var existes = this.GetEntities<SysSetting>(p => p.Key == entity.Key && p.Name == entity.Name);

            if (existes.Any())
            {
                result.Error("系统配置重复");
            }

            if (result.Succeed)
            {
                result.Data = this.Add(entity);
            }

            return result;
        }

        public BizResult<SysSetting> UpdateSysSetting(SysSetting entity)
        {
            var result = new BizResult<SysSetting>();

            var id = entity.ID;
            var dbEntity = this.Get<SysSetting>(id);

            if (dbEntity == null)
            {
                result.Error("系统配置项不存在");
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    if (entity.Name.IsEmpty())
                    {
                        result.Error("名称不可以为空");
                    }
                    if (entity.Key.IsEmpty())
                    {
                        result.Error("主键不可以为空");
                    }
                    if (entity.Value.IsEmpty())
                    {
                        result.Error("值不可以为空");
                    }

                    var exists = this.GetEntities<SysSetting>(p => p.ID != id && (p.Name == entity.Name && p.Key == entity.Key));

                    if (exists.Any())
                    {
                        result.Error("系统配置重复");
                    }

                    if (result.Succeed)
                    {
                        result.Data = entity;

                        this.SaveChanges();
                    }
                    else
                    {
                        this.Detach(entity);
                    }
                }
                else
                {
                    result.Data = dbEntity;
                }
            }

            return result;
        }

        public BizResult DeleteSysSetting(SysSetting entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<SysSetting>(id);

            if (dbEntity == null)
            {
                result.Error("系统配置项不存在");
            }
            else
            {
                //逻辑删除职位
                this.Delete(entity, false);

                this.SaveChanges();
            }

            return result;
        }

        #endregion SysSetting

        #region 用户组

        public void DeleteUserGroup(Role entity)
        {
            this.Delete(entity);
        }

        public void SaveUserGroup(Role entity)
        {
            if (entity.ID == default)
            {
                this.Add(entity);
            }
            else
            {
                this.Update(entity);
            }
        }

        public Role? GetUserGroupByCode(string code)
        {
            return this.GetEntity<Role>(s => s.Code == code);
        }

        public IList<Role> GetUserGroups()
        {
            return this.GetEntitiesOrderBy<Role, string>(p => p.Code);
        }

        #endregion 用户组

        #region 用户管理

        public void DeleteUser(User entity)
        {
            this.Delete(entity);
        }

        public void SaveUser(User entity)
        {
            if (entity.ID == default)
            {
                entity.ID = CombGuid.NewGuid();
                this.Add(entity);
            }
            else
            {
                this.Update(entity);
            }
        }

        public void UpdateUserRole(User entity, List<RoleMember>? roleMembers)
        {
            if (roleMembers != null && roleMembers.Count > 0)
            {
                var dbRoleMembers = this.GetEntities<RoleMember>(p => p.UserId == entity.ID);

                var delRoleMembers = dbRoleMembers.Where(p => !roleMembers.Any(p1 => p1.RoleId == p.RoleId)).ToList();

                foreach (var del in delRoleMembers)
                {
                    this.Remove(del, false);
                }

                foreach (var roleMember in roleMembers)
                {
                    var dbRoleMember = dbRoleMembers.Where(p => p.RoleId == roleMember.RoleId).FirstOrDefault();

                    if (dbRoleMember == null)
                    {
                        roleMember.ID = CombGuid.NewGuid();
                        roleMember.UserId = entity.ID;
                        roleMember.EnumType = RoleMemberType.User;
                        this.Add(roleMember, false);
                    }
                    else
                    {
                        this.Update(dbRoleMember, false);
                    }
                }
            }

            this.SaveChanges();
        }

        public User? GetUserByUsername(string username)
        {
            return this.GetEntity<User>(s => s.Username == username);
        }

        public Department? GetDepartmentById(Guid id)
        {
            return this.Find<Department>(id);
        }

        #endregion 用户管理

        #region 用户组权限管理

        /// <summary>
        /// 保存用户组和模块的关系
        /// </summary>
        /// <param name="rolePermissions"></param>
        public void SaveRightSetting(IEnumerable<RolePermission> rolePermissions, Guid usergroupid)
        {
            var rs = this.GetEntities<RolePermission>(predicate: s => s.RoleId == usergroupid);
            if (rs != null && rs.Count > 0)
                this.DbContext.RemoveRange(rs);
            if (rolePermissions != null && rolePermissions.Any())
            {
                this.AddRange(rolePermissions, false);
            }
            this.SaveChanges();
        }

        /// <summary>
        /// 保存用户组和部门的关系
        /// </summary>
        /// <param name="rightOfDepts"></param>
        /// <param name="usergroupid"></param>
        public void SaveRightOfDept(IEnumerable<RightOfDept> rightOfDepts, Guid usergroupid)
        {
            var rs = this.GetEntities<RightOfDept>(predicate: s => s.RoleId == usergroupid);
            this.DbContext.RemoveRange(rs);
            if (rightOfDepts != null && rightOfDepts.Any())
            {
                this.AddRange(rightOfDepts, false);
            }
            this.SaveChanges();
        }

        /// <summary>
        /// 人事分页
        /// </summary>
        /// <param name="usergroupid"></param>
        /// <returns></returns>
        public List<Guid> GetControlRightPanelIdsByUserGroupId(Guid usergroupid)
        {
            var entities = this.GetEntities<ControlRight>(p => p.RoleId == usergroupid);

            return entities.Select(s => s.ControlRightPanelId).ToList();
        }

        public IList<ControlRightPanel> GetAllControlRightPanel()
        {
            return this.GetEntities<ControlRightPanel>();
        }

        public void AddControlRight(IEnumerable<ControlRight> list)
        {
            this.AddRange(list);
        }

        public void UpdateControlRights(IEnumerable<ControlRight> list)
        {
            foreach (var item in list)
            {
                this.Update(item, false);
            }
            this.SaveChanges();
        }

        /// <summary>
        /// 当前用户 人事分页
        /// </summary>
        /// <param name="crpType"></param>
        /// <returns></returns>
        public IList<ControlRight> GetControlRightsByCurrentUser(ControlRightPanelType? crpType = null)
        {
            return (from c in this.DbContext.ControlRight
                    from crp in this.DbContext.ControlRightPanel.Where(s => s.ID == c.ControlRightPanelId && s.Deleted == false && (!crpType.HasValue || s.EnumType == crpType.Value))
                    from rm in this.DbContext.RoleMember.Where(s => s.RoleId == c.RoleId && s.UserId == this.OperatorUserId)
                    select new ControlRight
                    {
                        EnumUserRight = c.EnumUserRight,
                        ControlRightPanel = new ControlRightPanel
                        {
                            Code = crp.Code,
                            Desc = crp.Desc,
                            EnumType = crp.EnumType
                        }
                    })
                          .AsNoTracking()
                          // .AsSplitQuery()
                          .ToList();
        }

        #endregion 用户组权限管理
    }
}