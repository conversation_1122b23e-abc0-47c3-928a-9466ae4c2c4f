(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0b652b"],{"1d57":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.socialInsuranceModel}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"年份"}},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份",editable:!1,clearable:!1,"value-format":"yyyy"},model:{value:e.socialInsuranceModel.year,callback:function(t){e.$set(e.socialInsuranceModel,"year",t)},expression:"socialInsuranceModel.year"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"部门"}},[a("c-select-tree",{attrs:{options:e.treeData,"tree-props":e.treeProps},model:{value:e.socialInsuranceModel.deptId,callback:function(t){e.$set(e.socialInsuranceModel,"deptId",t)},expression:"socialInsuranceModel.deptId"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"工号"}},[a("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.socialInsuranceModel.empCode,callback:function(t){e.$set(e.socialInsuranceModel,"empCode",t)},expression:"socialInsuranceModel.empCode"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"姓名"}},[a("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.socialInsuranceModel.displayName,callback:function(t){e.$set(e.socialInsuranceModel,"displayName",t)},expression:"socialInsuranceModel.displayName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:""}},[a("el-button",{attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")]),a("el-button",{attrs:{type:"primary"},on:{click:e.exportData}},[e._v("导出")])],1)],1)],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}}},[a("el-table-column",{attrs:{prop:"date",label:"唯一码",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.uid))])]}}])}),a("el-table-column",{attrs:{prop:"date",label:"工号",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.empCode))])]}}])}),a("el-table-column",{attrs:{prop:"data",label:"工资单号",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.paySlipNumber))])]}}])}),a("el-table-column",{attrs:{prop:"date",label:"姓名",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.displayName))])]}}])}),a("el-table-column",{attrs:{prop:"date",label:"参加工作日期",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.SocietyDate))])]}}])}),a("el-table-column",{attrs:{prop:"date",label:"部门",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.empDept))])]}}])}),a("el-table-column",{attrs:{prop:"data",label:"年份",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.year))])]}}])}),a("el-table-column",{attrs:{prop:"data",label:"上年度实际月工资",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(e){e.row;return[a("span")]}}])}),a("el-table-column",{attrs:{prop:"data",label:"本年度月缴费基数",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(e){e.row;return[a("span")]}}])}),a("el-table-column",{attrs:{prop:"data",label:"个人养老保险缴纳额",align:"center",width:"130"},scopedSlots:e._u([{key:"default",fn:function(e){e.row;return[a("span")]}}])}),a("el-table-column",{attrs:{prop:"data",label:"个人医疗保险缴纳额",align:"center",width:"130"},scopedSlots:e._u([{key:"default",fn:function(e){e.row;return[a("span")]}}])}),a("el-table-column",{attrs:{prop:"data",label:"个人失业保险缴纳额",align:"center",width:"130"},scopedSlots:e._u([{key:"default",fn:function(e){e.row;return[a("span")]}}])}),a("el-table-column",{attrs:{prop:"data",label:"缴费总计",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(e){e.row;return[a("span")]}}])}),a("el-table-column",{attrs:{prop:"data",label:"缴费状态",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(e){e.row;return[a("span")]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.search}})]},proxy:!0}])})],1)},n=[],r=(a("99af"),a("d3b7"),a("25f0"),a("4d90"),a("d368")),o={components:{},data:function(){return{socialInsuranceModel:{},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},total:0,listQuery:{pageIndex:1,pageSize:10},listLoading:!1,tableData:[]}},created:function(){this.loadTree()},methods:{getNowTime:function(){var e=new Date,t=e.getFullYear(),a=e.getMonth();a+=1,a=a.toString().padStart(2,"0");var l="".concat(t,"-").concat(a);return l},loadTree:function(){var e=this;r["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data})).catch((function(e){}))},search:function(){},exportData:function(){}}},s=o,i=a("2877"),c=Object(i["a"])(s,l,n,!1,null,null,null);t["default"]=c.exports}}]);