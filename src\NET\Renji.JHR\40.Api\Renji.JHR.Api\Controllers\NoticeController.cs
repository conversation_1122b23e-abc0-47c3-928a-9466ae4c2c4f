﻿using Microsoft.AspNetCore.Mvc;
using Renji.JHR.Api.Models;
using Renji.JHR.Bll;
using Renji.JHR.Common.Utility;
using Renji.JHR.Entities;
using Shinsoft.Core;
using Shinsoft.Core.Mvc;
using Shinsoft.Core.NLog;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using System.IO;
using System.Data;
using System.Linq.Expressions;
using Renji.JHR.Common;
using Microsoft.AspNetCore.Authorization;

namespace Renji.JHR.Api.Controllers
{
    /// <summary>
    /// 消息通知
    /// </summary>
    public class NoticeController : BaseApiController<NoticeBll>
    {
        #region 公告发布

        [HttpPost]
        [LogApi(ApiType.Query, Operate = "查询公告")]
        public QueryResult<MsgCompanyModel> QueryMsgCompany([FromBody] MsgCompanyFilter filter)
        {
            var exps = this.NewExps<MsgCompany>();

            if (filter.ConditionList != null && filter.ConditionList.Any())
            {
                var expression = new Common.DynamicQuery().GetDynamicQuery<MsgCompany>(filter.ConditionList);
                exps.And(expression);
            }
            //this.CurrentUser.
            //人力资源处 看到所有的
            var dept = this.Repo.GetCurrentDept();
            if (dept == null || dept.Code != Depts.HumanResourcesDivisionCode)  //filter.IsCurrentUser
            {
                var username = this.CurrentUser?.Username;
                exps.Add(s => s.Creator == username);
            }

            if (filter.Order.IsEmpty())
            {
                filter.Order = "id desc";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<MsgCompanyModel>();

            return this.QueryResult(models, recoredCount, filter);
        }

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "维护公告")]
        [Permission(Permissions.MsgComList.Msglist)]
        public BizResult SaveMsgCompany([FromBody] MsgCompanyModel model)
        {
            var entity = model.Map<MsgCompany>();
            return this.Repo.SaveMsgCompany(entity);
        }

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除公告")]
        [Permission(Permissions.MsgComList.Msglist)]
        public BizResult DeleteMsgCompany([FromBody] MsgCompanyModel model)
        {
            var entity = model.Map<MsgCompany>();
            return this.Repo.DeleteMsgCompany(entity);
        }

        [HttpPost]
        [LogApi(ApiType.Query, Operate = "查询当前用户可以查看的公告（首页使用）")]
        public QueryResult<MsgCompanyHomeQuery> QueryMsgCompanyToEffective([FromBody] MsgCompanyFilter filter)
        {
            var exps = this.NewExps<MsgCompany>();

            if (filter.ConditionList != null && filter.ConditionList.Any())
            {
                var expression = new Common.DynamicQuery().GetDynamicQuery<MsgCompany>(filter.ConditionList);
                exps.And(expression);
            }
            var dept = this.Repo.GetCurrentDept();
            exps.And(s => s.AvailEndDate >= SysDateTime.Now && s.AvailStartDate <= SysDateTime.Now);

            if (dept == null)
            {
                exps.And(s => !s.DeptId.HasValue);
            }
            else
            {
                exps.And(s => !s.DeptId.HasValue || (s.Department != null && dept.UidPath.Contains(s.Department.Uid.ToString())));
            }

            if (filter.Order.IsEmpty())
            {
                filter.Order = $"{nameof(MsgCompany.AvailStartDate)} desc";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<MsgCompanyHomeQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询单个公告（首页使用）")]
        public BizResult<MsgCompanyModel> GetMsgCompanyModelById(Guid id)
        {
            var result = new BizResult<MsgCompanyModel>();
            var entity = this.Repo.Find<MsgCompany>(id);
            var model = entity?.Map<MsgCompanyModel>();
            if (model != null)
            {
                var creator = this.Repo.GetUserByUserName(model.Creator);
                model.CreatorName = creator?.DisplayName;
                result.Data = model;
            }
            return result;
        }

        #endregion 公告发布

        #region 消息发布

        [HttpPost]
        [LogApi(ApiType.Query, Operate = "查询当前用户可以查看的消息（首页使用） ")]
        public QueryResult<MsgPersonHomeQuery> QueryMsgPersonToEffective([FromBody] MsgPersonFilter filter)
        {
            var exps = this.NewExps<MsgPerson>();

            if (filter.ConditionList != null && filter.ConditionList.Any())
            {
                var expression = new Common.DynamicQuery().GetDynamicQuery<MsgPerson>(filter.ConditionList);
                exps.And(expression);
            }
            // var dept = this.Repo.GetCurrentDept();
            exps.And(s => s.AvailEndDate >= SysDateTime.Now && s.AvailStartDate <= SysDateTime.Now);
            var uid = this.CurrentUser?.Uid;
            var empCode = this.CurrentUser?.EmpCode;
            exps.And(s => s.MsgReadInfo.Any(r => r.Employee.Uid == uid && r.Employee.EmpCode == empCode && r.ReadFlag == 0));

            if (filter.Order.IsEmpty())
            {
                filter.Order = $"{nameof(MsgPerson.AvailStartDate)} desc";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<MsgPersonHomeQuery>();

            return this.QueryResult(models, recoredCount, filter);
        }

        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询单个消息（首页使用）")]
        public BizResult<MsgPersonModel> GetMsgPersonModelById(Guid id)
        {
            var result = new BizResult<MsgPersonModel>();
            var entity = this.Repo.Find<MsgPerson>(id);
            var model = entity?.Map<MsgPersonModel>();
            if (model != null)
            {
                var creator = this.Repo.GetUserByUserName(model.Creator);
                model.CreatorName = creator?.DisplayName;
                result.Data = model;
            }
            return result;
        }

        [HttpPost]
        [LogApi(ApiType.Query, Operate = "查询消息")]
        public QueryResult<MsgPersonModel> QueryMsgPerson([FromBody] MsgPersonFilter filter)
        {
            var exps = this.NewExps<MsgPerson>();

            if (filter.ConditionList != null && filter.ConditionList.Any())
            {
                var expression = new Common.DynamicQuery().GetDynamicQuery<MsgPerson>(filter.ConditionList);
                exps.And(expression);
            }
            var dept = this.Repo.GetCurrentDept();
            if (dept == null || dept.Code != Depts.HumanResourcesDivisionCode)  //filter.IsCurrentUser
            {
                var username = this.CurrentUser?.Username;
                exps.Add(s => s.Creator == username);
            }

            if (filter.Order.IsEmpty())
            {
                filter.Order = "id desc";
            }

            int recoredCount = -1;
            var entities = filter.RequireRecordCount
                 ? this.Repo.GetEntities(exps, filter.Order, out recoredCount, filter.PageIndex, filter.PageSize)
                 : this.Repo.GetEntities(exps, filter.Order, filter.PageIndex, filter.PageSize);

            var models = entities.Maps<MsgPersonModel>();

            return this.QueryResult(models, recoredCount, filter);
        }

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除消息")]
        [Permission(Permissions.MsgComList.MsgPerlist)]
        public BizResult DeleteMsgPerson([FromBody] MsgPersonModel model)
        {
            var entity = model.Map<MsgPerson>();
            return this.Repo.DeleteMsgPerson(entity);
        }

        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询消息关联的人员")]
        [Permission(Permissions.MsgComList.MsgPerlist)]
        public BizResult<List<MsgReadInfoModel>> GetMsgReadInfoByMsgPerson(Guid id)
        {
            var list = this.Repo.GetEntities<MsgReadInfo>(predicate: s => s.MsgPersonId == id);
            var models = list.Maps<MsgReadInfoModel>();
            return new BizResult<List<MsgReadInfoModel>>(models);
        }

        [HttpPost]
        [LogApi(ApiType.Save, Operate = "维护公告")]
        [Permission(Permissions.MsgComList.MsgPerlist)]
        public BizResult SaveMsgPerson([FromBody] MsgPersonModel model)
        {
            var result = new BizResult();
            var entity = model.Map<MsgPerson>();
            if (true == model.IsSendMsgToMP)
            {
                //查询 人事部 人员
                var emps = this.Repo.GetEmpByDeptCode(Depts.HumanResourcesDivisionCode);
                if (emps == null || emps.Count == 0)
                {
                    result.Error("人事部没有配置人员信息！");
                    return result;
                }
                else
                {
                    entity.MsgReadInfo = emps.Select(s => new MsgReadInfo { EmployeeId = s.ID }).ToList();
                }
            }
            else
            {
                entity.MsgReadInfo = model.MsgReadInfos?.Maps<MsgReadInfo>();
            }
            result = this.Repo.SaveMsgPerson(entity);
            return result;
        }

        [HttpGet]
        [LogApi(ApiType.Query, Operate = "根据部门查询人员")]
        [Permission(Permissions.MsgComList.MsgPerlist)]
        public BizResult<IList<SelectModel>> GetEmpByDept(Guid id)
        {
            var list = this.Repo.GetEmpByDeptID(id);
            var models = list.Select(s => new SelectModel { Label = s.DisplayName, Value = s.ID.ToString() }).ToList();
            return new BizResult<IList<SelectModel>>(models);
        }

        #endregion 消息发布
    }
}