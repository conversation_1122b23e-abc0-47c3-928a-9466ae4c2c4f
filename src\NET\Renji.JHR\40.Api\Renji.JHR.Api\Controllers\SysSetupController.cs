﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shinsoft.Core.Configuration.Sections;
using Shinsoft.Core.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Reflection;
using System.Text;
using ColumnAttribute = Shinsoft.Core.EntityFrameworkCore.ColumnAttribute;

namespace Renji.JHR.Api.Controllers
{
    /// <summary>
    /// 系统设置
    /// </summary>
    [ApiExplorerSettings(GroupName = "设置")]
    public class SysSetupController : BaseApiController<SysBll>
    {
        [AllowAnonymous]
        [JwtIgnore]
        [HttpGet]
        public string GetTableScript(string entity, string? db)
        {
            if (db.IsEmpty())
            {
                db = "Biz";
            }

            var dbContext = $"{db}DbContext";

            var dbConfig = Config.GetConnectionSetting(dbContext);
            var assembly = Assembly.Load("Renji.JHR.Entities");
            var type = assembly.GetType($"Renji.JHR.Entities.{entity}");

            if (type == null)
            {
                return $"没有找到类型{entity}";
            }
            else
            {
                var result = this.GetTableScript(dbConfig, type);

                if (result.Succeed)
                {
                    return result.Data!;
                }
                else
                {
                    return result.Messages.First();
                }
            }
        }

        [AllowAnonymous]
        [JwtIgnore]
        [HttpGet]
        public string GetAllTableScript(string? db)
        {
            var scripts = new StringBuilder();

            if (db.IsEmpty())
            {
                db = "Biz";
            }

            var dbContext = $"{db}DbContext";

            var dbConfig = Config.GetConnectionSetting(dbContext);
            var assembly = Assembly.Load("Renji.JHR.Entities");
            var dalAssembly = Assembly.Load("Renji.JHR.Dal");
            var dbContextType = dalAssembly.GetType($"Renji.JHR.Dal.{dbContext}");

            if (dbContextType != null)
            {
                var entityTypes = dbContextType.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                    .Where(p => p.PropertyType.IsGenericType
                        && p.PropertyType.GenericTypeArguments.Count() == 1)
                    .Select(p => p.PropertyType.GenericTypeArguments.First())
                    .Where(p => typeof(ITable).IsAssignableFrom(p))
                    .ToList();

                foreach (var type in assembly.GetTypes())
                {
                    if (type.Namespace == "Renji.JHR.Entities")
                    {
                        var result = this.GetTableScript(dbConfig, type, entityTypes);

                        if (result.Succeed)
                        {
                            scripts.AppendLine(result.Data);
                        }
                    }
                }
            }

            return scripts.ToString();
        }

        private BizResult<string> GetTableScript(ConnectionSettingSection? dbConfig, Type type, List<Type>? entityTypes = null)
        {
            var result = new BizResult<string>();

            if (!typeof(ITable).IsAssignableFrom(type))
            {
                result.Error($"类型{type.Name}不是数据库表类型");
            }
            else
            {
                var tableAttr = type.GetAttribute<TableAttribute>(true);

                if (tableAttr == null)
                {
                    result.Error($"类型{type.Name}没有定义TableAttribute");
                }
                else
                {
                    var scripts = new StringBuilder();

                    var tableSchema = tableAttr.GetTableSchema(dbConfig);
                    var tableName = tableAttr.GetTableName(dbConfig);

                    if (entityTypes == null || entityTypes.Contains(type))
                    {
                        scripts.AppendLine($"CREATE TABLE \"{tableSchema}\".\"{tableName}\" (");

                        var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);

                        var props = properties
                            .Where(p => (p.PropertyType.IsValueOrString() || p.PropertyType == typeof(byte[])) && p.CanWrite && p.CanRead)
                            .Select(p => new
                            {
                                Prop = p,
                                NotMapped = p.GetAttribute<NotMappedAttribute>(),
                            })
                            .Where(tmp => tmp.NotMapped == null)
                            .Select(tmp => tmp.Prop)
                            .ToList();

                        var keyCloumns = new List<string>();

                        foreach (var prop in props)
                        {
                            var colAttr = prop.GetAttribute<ColumnAttribute>(true) ?? new ColumnAttribute(prop.Name);
                            var keyAttr = prop.GetAttribute<KeyAttribute>(true);
                            var dbGenAttr = prop.GetAttribute<DatabaseGeneratedAttribute>(true);

                            colAttr.PropName = prop.Name;
                            colAttr.PropType = prop.PropertyType;
                            colAttr.IsEnum = prop.PropertyType.IsEnum;
                            colAttr.IsDatabaseGenerated = dbGenAttr?.DatabaseGeneratedOption == DatabaseGeneratedOption.Identity
                                || dbGenAttr?.DatabaseGeneratedOption == DatabaseGeneratedOption.Computed;

                            var isIdentity = dbGenAttr?.DatabaseGeneratedOption == DatabaseGeneratedOption.Identity;

                            var colName = colAttr.GetColumnName(dbConfig);

                            if (keyAttr == null)
                            {
                                colAttr.IsPrimaryKey = false;
                            }
                            else
                            {
                                colAttr.IsPrimaryKey = true;
                                keyCloumns.Add(colName);
                            }

                            var dbType = string.Empty;

                            switch (colAttr.DbType)
                            {
                                case DbType.Guid:
                                    dbType = "CHAR(36)";
                                    break;

                                case DbType.Int32:
                                    dbType = "INT";
                                    break;

                                case DbType.Int64:
                                    dbType = "BIGINT";
                                    break;

                                case DbType.Boolean:
                                    dbType = "BIT";
                                    break;

                                case DbType.DateTime:
                                    dbType = "DATETIME";
                                    break;

                                case DbType.Byte:
                                    dbType = "BLOB";
                                    break;

                                case DbType.Decimal:
                                    if (colAttr.NumPrecision > 0 && colAttr.NumScale > 0)
                                    {
                                        dbType = $"DECIMAL({colAttr.NumPrecision},{colAttr.NumScale})";
                                    }
                                    else if (colAttr.NumPrecision > 0)
                                    {
                                        dbType = $"DECIMAL({colAttr.NumPrecision})";
                                    }
                                    break;

                                case DbType.String:
                                    if (colAttr.MaxLength > 0 && colAttr.MaxLength <= 4000)
                                    {
                                        dbType = $"NVARCHAR({colAttr.MaxLength})";
                                    }
                                    else
                                    {
                                        dbType = "TEXT";
                                    }
                                    break;

                                default:
                                    dbType = "缺少定义，请更改代码或者自行填充";
                                    break;
                            }

                            var isNull = colAttr.IsNullable || (!colAttr.IsPrimaryKey && colAttr.DbType == DbType.String) ? "NULL" : "NOT NULL";

                            if (isIdentity)
                            {
                                scripts.AppendLine($"\t\"{colName}\"\t{dbType}\t{isNull}\tidentity(1, 1),");
                            }
                            else
                            {
                                scripts.AppendLine($"\t\"{colName}\"\t{dbType}\t{isNull},");
                            }
                        }

                        if (keyCloumns.Any())
                        {
                            scripts.AppendLine($"\tCLUSTER PRIMARY KEY(\"{string.Join("\",\"", keyCloumns)}\")");
                        }
                        else
                        {
                            scripts.Remove(scripts.Length - 3, 1);
                        }

                        scripts.AppendLine(")");

                        scripts.AppendLine($"STORAGE(ON \"{dbConfig?.TableSpace}\", CLUSTERBTR);");

                        result.Data = scripts.ToString();
                    }
                    else
                    {
                        result.Error($"Schedma不符合");
                    }
                }
            }

            return result;
        }
    }
}