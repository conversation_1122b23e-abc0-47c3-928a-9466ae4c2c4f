using Renji.JHR.Entities;
using Shinsoft.Core.DynamicQuery;
using System;

namespace Renji.JHR.Api.Models
{
    /// <summary>
    /// 十三薪查询过滤器
    /// </summary>
    public partial class ThirteenthSalaryFilter
    {
        /// <summary>
        /// 计算类型
        /// </summary>
        [DynamicQueryColumn(typeof(ThirteenthSalary), ThirteenthSalary.Columns.EnumThirteenthSalaryCalculationType, Operation = Operation.Equal)]
        public ThirteenthSalaryCalculationType? EnumCalculationType { get; set; }

        /// <summary>
        /// 人员范围
        /// </summary>
        [DynamicQueryColumn(typeof(ThirteenthSalary), ThirteenthSalary.Columns.EnumThirteenthSalaryEmployeeStatus, Operation = Operation.Equal)]
        public ThirteenthSalaryEmployeeStatus? EnumThirteenthSalaryEmployeeStatus { get; set; }
        /// <summary>
        /// 月薪ID
        /// </summary>
        [DynamicQueryColumn(typeof(ThirteenthSalary), ThirteenthSalary.Foreigns.Salary, Salary.Columns.ID, Operation = Operation.Equal)]
        public Guid? SalaryId { get; set; }

        /// <summary>
        /// 员工Id
        /// </summary>
        [DynamicQueryColumn(typeof(ThirteenthSalary), ThirteenthSalary.Columns.EmployeeId, Operation = Operation.Equal)]
        public Guid? EmployeeId { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        [DynamicQueryColumn(typeof(ThirteenthSalary), ThirteenthSalary.Foreigns.Employee, Entities.Employee.Inverses.EmployeeSalary, EmployeeSalary.Foreigns.Employee, Employee.Columns.EmpCode, Operation = Operation.StringContains)]
        public string? EmpCode { get; set; }

        /// <summary>
        /// 唯一码
        /// </summary>
        [DynamicQueryColumn(typeof(ThirteenthSalary), ThirteenthSalary.Foreigns.Employee, Entities.Employee.Inverses.EmployeeSalary, EmployeeSalary.Foreigns.Employee, Employee.Columns.Uid, Operation = Operation.Equal)]
        public int? Uid { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [DynamicQueryColumn(typeof(ThirteenthSalary), ThirteenthSalary.Foreigns.Employee, Entities.Employee.Inverses.EmployeeSalary, EmployeeSalary.Foreigns.Employee, Employee.Columns.DisplayName, Operation = Operation.StringContains)]
        public string? DisplayName { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        [DynamicQueryColumn(typeof(ThirteenthSalary), ThirteenthSalary.Foreigns.Employee, Employee.Foreigns.Department, Department.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? DeptName { get; set; }

        /// <summary>
        /// 职别
        /// </summary>
        [DynamicQueryColumn(typeof(ThirteenthSalary), ThirteenthSalary.Foreigns.Employee, Employee.Inverses.EmployeeHR, EmployeeHR.Foreigns.OfficialRank, Dict.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? OfficialRankName { get; set; }

        /// <summary>
        /// 在职方式
        /// </summary>
        [DynamicQueryColumn(typeof(ThirteenthSalary), ThirteenthSalary.Foreigns.Employee, Employee.Inverses.EmployeeHR, EmployeeHR.Columns.HireStyleId, Operation = Operation.Equal)]
        public Guid? HireStyleId { get; set; }
    }
}
