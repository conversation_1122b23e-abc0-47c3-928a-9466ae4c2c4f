﻿using Renji.JHR.Common.Configration;
using Shinsoft.Core;
using Shinsoft.Core.Json;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Text.Json;

namespace Renji.JHR.Common
{
    public static class ImportMapping
    {
        //实际费用数据映射
        public static Dictionary<string, string> BudgetingActualMapping = new Dictionary<string, string>
        {
            {"岗位类型", ""},
            {"薪级", ""},
            {"工资标准", ""},
            {"备注", ""}
        };

        //工龄津贴数据映射
        public static Dictionary<string, string> SeniorityMapping = new Dictionary<string, string>
        {
            {"工龄", ""},
            {"工作量津贴", ""},
            {"护龄工资", ""},
            {"备注", ""}
        };

        //一值班二值班费数据映射
        public static Dictionary<string, string> ShiftAllowanceMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"一值班二值班费（元）", ""},
            {"备注", ""}
        };

        //行政值班费数据映射
        public static Dictionary<string, string> GeneralHospitalAdminDutyAllowanceMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"行政值班费（元）", ""},
            {"备注", ""}
        };

        //中夜班费数据映射
        public static Dictionary<string, string> MiddleNightShiftAllowanceMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"中班", ""},
            {"夜班", ""},
            {"24小时班", ""},
            {"急诊中班", ""},
            {"急诊夜班", ""},
            {"急诊24小时班", ""},
            {"护理A挡中班", ""},
            {"护理A挡夜班", ""},
            {"护理A挡24小时班", ""},
            {"中夜班费（元）", ""},
            {"备注", ""}
        };

        //考勤卫生津贴数据映射
        public static Dictionary<string, string> AttendanceHealthAllowanceMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"考勤月份", ""},
            {"卫生津贴标准", ""},
            {"卫生津贴（元）", ""},
            {"备注", ""}
        };

        //加班费数据映射
        public static Dictionary<string, string> OvertimeAllowanceMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"节假日班次", ""},
            {"周末班次", ""},
            {"加班费（元）", ""},
            {"备注", ""}
        };

        //病产假数据映射
        public static Dictionary<string, string> SalaryLeaveMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"折扣比例", ""},
            {"请假类型", ""},
            {"连续病假天数", ""},
            {"连续事假天数", ""},
            {"卫生津贴", ""},
            {"病假", ""},
            {"事假", ""},
            {"产假", ""},
            {"哺乳假", ""},
            {"探亲假", ""},
            {"计生假", ""},
            {"婚丧假", ""},
            {"补发/补扣", ""},
            {"备注", ""}
        };

        //非月薪病产假数据映射
        public static Dictionary<string, string> NonMonthlySalaryLeaveMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"上下班交通费", ""},
            {"请假类型", ""},
            {"备注", ""}
        };

        //零星补病产假数据映射
        public static Dictionary<string, string> SporadicSalaryLeaveMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"补扣金额", ""},
            {"备注", ""}
        };

        //援外数据映射
        public static Dictionary<string, string> AssistanceForeignMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"援外起始时间", ""},
            {"援外结束时间", ""},
            {"类型", ""},
            {"生活通信补贴", ""},
            {"置装一次性补贴", ""},
            {"补发", ""},
            {"津贴", ""},
            {"备注", ""}
        };

        //援滇数据映射
        public static Dictionary<string, string> AssistanceYunnanMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"起始时间", ""},
            {"结束时间/回岗时间", ""},
            {"类型", ""},
            {"生活通信补贴", ""},
            {"置装一次性补贴", ""},
            {"补发", ""},
            {"补发月份数", ""},
            {"备注", ""}
        };

        //博士后房贴数据映射
        public static Dictionary<string, string> PostdoctoralHousingAllowanceMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"在站到期时间", ""},
            {"待遇类别", ""},
            {"待遇金额", ""},
            {"导师支出单", ""},
            {"补发", ""},
            {"补发月份数", ""},
            {"备注", ""}
        };

        //通用补发/补扣数据映射
        public static Dictionary<string, string> UniversalReissueDeductionMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"补发/补扣名称", ""},
            {"补发/补扣类型", ""},
            {"补发/补扣", ""},
            {"备注", ""}
        };

        //回国人员恢复工资补发数据映射
        public static Dictionary<string, string> EmployeeOverseasReissueDetailMapping = new Dictionary<string, string>
        {
            {"年月", ""},
            {"社保基数", ""},
            {"公积金基数", ""},
            {"岗位级别", ""},
            {"薪级", ""},
            {"工龄", ""},
            {"工龄段", ""},
            {"岗资基数", ""},
            {"薪资基数", ""},
            {"粮油补贴", ""},
            {"上下班交通费基数", ""},
            {"护龄基数", ""},
            {"独子基数", ""},
            {"岗位津贴基数", ""},
            {"工作量津贴1", ""},
            {"停车补贴", ""},
            {"公车补贴", ""},
            {"电话费", ""},
            {"卫生津贴", ""},
            {"博士后房贴", ""},
            {"其他加", ""},
            {"其他扣", ""}
        };

        //员工薪资基数数据映射
        public static Dictionary<string, string> EmployeeSalaryBaseMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"薪资状态", ""},
            {"社保基数", ""},
            {"公积金基数", ""},
            {"备注", ""},
        };

        //工龄更新映射
        public static Dictionary<string, string> EmployeeDeductMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"工号", ""},
            {"姓名", ""},
            {"不计算工龄", ""},
            {"不计算薪级", ""}
        };


        //薪资明细数据映射
        public static Dictionary<string, string> SalaryDetailMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"奖金合计", ""},
            {"年终一次性奖励", ""},
            {"其他各项奖金", ""},
            {"实发奖金", ""},
            {"税前奖金", ""},
            {"奖金扣税", ""},
            {"累计收入额", ""},
            {"累计专项扣除", ""},
            {"累计专项附加扣除", ""},
            {"累计应纳税所得额", ""},
            {"累计已预扣税额", ""}
        };

        //员工修正(月薪)数据映射
        public static Dictionary<string, string> EmployeeSalaryCorrectionMonthMapping = new Dictionary<string, string>
        {
            {"员工工号", ""},
            {"新岗位等级ID", ""}
        };

        //退休人员补发数据映射
        public static Dictionary<string, string> RetiredEmployeeReissueMapping = new Dictionary<string, string>
        {
            {"唯一码", ""},
            {"金额", ""},
            {"备注", ""}
        };
    }
}