(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-09c4cee2"],{ac51:function(e,t,a){"use strict";var l=a("ddd9"),o=a.n(l);o.a},ddd9:function(e,t,a){},fd2e:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[a("el-form-item",[a("el-input",{attrs:{clearable:"",placeholder:"唯一码"},model:{value:e.listQuery.uid,callback:function(t){e.$set(e.listQuery,"uid",t)},expression:"listQuery.uid"}})],1),a("el-form-item",[a("el-input",{attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),a("el-form-item",[a("el-input",{attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQuery.displayName,callback:function(t){e.$set(e.listQuery,"displayName",t)},expression:"listQuery.displayName"}})],1),a("el-form-item",[a("el-select",{attrs:{clearable:"",placeholder:"薪资类型"},model:{value:e.listQuery.enumSalaryType,callback:function(t){e.$set(e.listQuery,"enumSalaryType",t)},expression:"listQuery.enumSalaryType"}},e._l(e.salaryTypes,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),a("el-form-item",[a("el-select",{attrs:{clearable:"",placeholder:"薪资状态"},model:{value:e.listQuery.enumEmployeeSalaryStatus,callback:function(t){e.$set(e.listQuery,"enumEmployeeSalaryStatus",t)},expression:"listQuery.enumEmployeeSalaryStatus"}},e._l(e.salaryStatuss,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.importDialog}},[e._v("导入基数")])],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.exportData}},[e._v("导出")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{label:"唯一码",sortable:"custom",prop:"Uid"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.uid))])]}}])}),a("el-table-column",{attrs:{label:"工号",sortable:"custom",prop:"EmpCode"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.empCode))])]}}])}),a("el-table-column",{attrs:{label:"姓名",sortable:"custom",prop:"DisplayName"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.displayName))])]}}])}),a("el-table-column",{attrs:{label:"社保基数",sortable:"custom",prop:"EmployeeSalary.SocialSecurityBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(e.getDecimalValueOrDefault(l.socialSecurityBase)))])]}}])}),a("el-table-column",{attrs:{label:"公积金基数",sortable:"custom",prop:"EmployeeSalary.HousingFundBase"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(e.getDecimalValueOrDefault(l.housingFundBase)))])]}}])}),a("el-table-column",{attrs:{label:"薪资类型",sortable:"custom",prop:"EmployeeSalary.EnumSalaryType"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.enumSalaryTypeDesc))])]}}])}),a("el-table-column",{attrs:{label:"薪资状态",sortable:"custom",prop:"EmployeeSalary.EnumEmployeeSalaryStatus"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.enumEmployeeSalaryStatusDesc))])]}}])}),a("el-table-column",{attrs:{fixed:"right",label:"操作",align:"left","header-align":"center",width:"150","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-button",{staticStyle:{"margin-left":"45px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.editEmployeeSalaryDialog(l,!0)}}},[e._v(" 编辑 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),e.dialogEditEmpSalaryVisible?a("editDialog",{ref:"editDialog",attrs:{"employee-salary-id":e.employeeSalaryId,"is-edit":e.isEdit},on:{refresh:function(t){return e.onRefresh()}}}):e._e(),a("importPage",{ref:"importPage",on:{refresh:function(t){return e.onImportRefresh()}}})],1)},o=[],i=(a("b680"),a("d3b7"),a("ac1f"),a("841c"),a("2efc")),r=a("f9ac"),s=a("6b3f"),n=a("e44c"),c=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{title:e.title,visible:e.showDialog,width:"30%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:e.closeDialog}},[a("el-form",{ref:"dataForm"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-button",{attrs:{type:"primary"},on:{click:e.downloadexceltemplate}},[a("i",{staticClass:"el-icon-download"}),e._v(" 模板下载 ")])],1),a("el-col",{attrs:{span:6}},[a("el-upload",{attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{slot:"trigger",icon:"el-icon-upload2",type:"primary"},slot:"trigger"},[e._v("导入")])],1)],1)],1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{staticStyle:{"margin-top":"20px","background-color":"#fff7f7",border:"1px solid #f56c6c","border-radius":"4px",padding:"15px",color:"#f56c6c","font-size":"14px"}},[a("strong",[e._v("提示：")]),a("br"),a("p",[e._v("1.导入与导出Excel模板相同。"),a("br"),e._v('2.导入时仅修改"社保基数"和"公积金基数"数据，其他数据将保持不变。')])])])],1)],1),a("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.closeDialog}},[e._v(" 关 闭 ")])],1)],1)],1)},u=[],p={components:{},data:function(){return{showDialog:!1,title:""}},methods:{initDialog:function(){this.title="导入员工基数",this.showDialog=!0},downloadexceltemplate:function(){n["a"].downlodaImportExcelTemplate({type:"importEmployeeSalaryBase"}).then((function(e){var t=a("19de"),l="EmployeeSalaryBaseTemplate.xlsx";e.data?t(e.data,l):t(e,l)})).catch((function(e){}))},importExcel:function(e){var t=this,a=e.file,l=new FormData;i["a"].importEmployeeSalaryBase(a,l).then((function(e){e.succeed&&(t.$message({message:"导入成功",type:"success"}),t.closeDialog())})).catch((function(e){t.closeDialog()}))},closeDialog:function(){this.showDialog=!1,this.$emit("refresh")}}},d=p,m=(a("ac51"),a("2877")),y=Object(m["a"])(d,c,u,!1,null,"22f9cf12",null),f=y.exports,g={components:{editDialog:s["a"],importPage:f},data:function(){return{isEdit:!1,pageList:[],listQuery:{uid:"",empCode:"",displayName:"",enumSalaryType:"",enumSalaryStatus:"",total:1,pageIndex:1,pageSize:10},listLoading:!1,salaryTypes:[],salaryStatuss:[],dialogEditEmpSalaryVisible:!1,employeeSalaryId:""}},created:function(){this.initSalaryTypeList(),this.initSalaryStatusList(),this.getPageList()},methods:{sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.$delete(this.listQuery,"order"),this.search()},initSalaryTypeList:function(){var e=this,t={enumType:"SalaryType"};r["a"].getEnumInfos(t).then((function(t){e.salaryTypes=t.data.datas})).catch((function(e){console.log(e)}))},initSalaryStatusList:function(){var e=this,t={enumType:"EmployeeSalaryStatus"};r["a"].getEnumInfos(t).then((function(t){e.salaryStatuss=t.data.datas})).catch((function(e){console.log(e)}))},search:function(){this.listQuery.pageIndex=1,this.getPageList()},getPageList:function(){var e=this;this.listLoading=!0,i["a"].queryEmployeeSalary(this.listQuery).then((function(t){t.succeed?(e.pageList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1}))},editEmployeeSalaryDialog:function(e,t){this.employeeSalaryId=e.id,this.isEdit=t,this.dialogEditEmpSalaryVisible=!0},closeEditEmployeeSalaryDialog:function(){this.$refs["ref_remindForm"].resetFields(),this.$refs["ref_remindForm"].clearValidate()},onRefresh:function(){this.isEdit&&this.getPageList(),this.dialogEditEmpSalaryVisible=!1},onImportRefresh:function(){this.getPageList()},downloadexceltemplate:function(){n["a"].downlodaImportExcelTemplate({type:"importemployeeSalaryBase"}).then((function(e){var t=a("19de"),l="EmployeeSalaryBaseTemplate.xlsx";e.data?t(e.data,l):t(e,l)})).catch((function(e){}))},importExcel:function(e){var t=this,a=e.file,l=new FormData;i["a"].importEmployeeSalaryBase(a,l).then((function(e){e.succeed&&(t.$message({message:"导入成功",type:"success"}),t.search(),t.queryData())})).catch((function(e){t.search()}))},exportData:function(){var e=this;this.$confirm("确定导出员工薪资数据吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){i["a"].exportEmployeeSalary(e.listQuery).then((function(t){var l=a("19de"),o="员工薪资"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?l(t.data,o):l(t,o)})).catch((function(t){e.listLoadingForArticle=!1,console.log(t)}))})).catch((function(t){e.listLoadingForArticle=!1,t.succeed||e.$notice.message("取消导出","info")}))},getDecimalValueOrDefault:function(e){return 0===e||null===e||void 0===e?"":e.toFixed(2)},importDialog:function(){this.$refs.importPage.initDialog()}}},h=g,S=Object(m["a"])(h,l,o,!1,null,null,null);t["default"]=S.exports}}]);