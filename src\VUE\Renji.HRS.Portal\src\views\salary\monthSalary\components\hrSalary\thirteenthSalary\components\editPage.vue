<template>
  <div>
    <el-dialog :title="title" :visible="showDialog" width="90%" :top="'10vh'" :close-on-press-escape="false" :close-on-click-modal="false" @close="closeDialog">
      <el-form ref="dataForm" :rules="rules" :model="dataModel" label-width="120px" class="el-dialogform">
        <el-card style="margin-top: 3px;">
          <div slot="header">
            <span>基础信息</span>
          </div>
          <el-row>
            <el-col :span="6">
              <el-form-item label="员工姓名">
                <div>
                  <span>
                    {{ dataModel.employeeModel.empName }}
                  </span>
                  <el-button v-if="!isEdit" style="margin-left: 15px;" type="primary" title="选择员工" @click="selectEmployeeDialog">选 择</el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="唯一码">
                {{ dataModel.employeeModel.empUid }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="工号">
                {{ dataModel.employeeModel.empCode }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="部门">
                {{ dataModel.employeeModel.empDept }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="职别">
                {{ dataModel.employeeModel.officialRankName }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="性别">
                {{ dataModel.employeeModel.genderDesc }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="在职方式">
                {{ dataModel.employeeModel.hireStyleName }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="入职日期">
                {{ dataModel.employeeModel.effHireDate ? (new Date(dataModel.employeeModel.effHireDate)).toLocaleDateString() : '' }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card style="margin-top: 3px;">
          <div slot="header">
            <span>十三月工资信息</span>
          </div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="标准" prop="enumThirteenthSalaryCalculationType">
                <el-select v-model="dataModel.enumThirteenthSalaryCalculationType" clearable placeholder="请选择计算类型" >
                  <el-option v-for="item in calculationTypeOptions" :key="item.value" :label="item.desc" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="人员范围" prop="enumThirteenthSalaryEmployeeStatus">
                <el-select v-model="dataModel.enumThirteenthSalaryEmployeeStatus" clearable placeholder="请选择员工状态">
                  <el-option v-for="item in employeeStatusOptions" :key="item.value" :label="item.desc" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="计算金额" prop="calculateAmount" label-width="120px">
                {{ dataModel.calculateAmount | formatMoney2 }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实际金额" prop="actualAmount" label-width="120px">
                <el-input-number v-model="dataModel.actualAmount"  :min="0" :precision="2" :controls="false" placeholder="实际金额" style="width: 60%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col>
              <el-form-item label="计算说明">
                {{ dataModel.calculateRemark }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col>
              <el-form-item label="备注" :prop="isRemarkRequired ? 'remark' : ''">
                <el-input v-model="dataModel.remark" type="textarea" :rows="3" maxlength="500" placeholder="当实际金额与计算金额不一致时，必须填写备注说明原因" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="btnCalculateLoading" @click="calculateAmount">计算</el-button>
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" :loading="btnSaveLoading" @click="saveDialog">保 存</el-button>
      </div>
    </el-dialog>

    <selectUserComponent ref="selectEmployee" @selectRow="setEmployee" />
  </div>
</template>

<script>
import salaryApi from '@/api/salary'
import sysManageApi from '@/api/sysManage'
import selectUserComponent from '@/views/salary/employeeSalary/components/selectUser'
import { calculatePercentage } from '@/utils/number'

export default {
  components: {
    selectUserComponent
  },
  data() {
    var validateMoney = (rule, value, callback) => {
      if (value !== null && value !== undefined && !(/^-?(\d|[1-9]\d+)(\.\d{1,2})?$/).test(value)) {
        callback(new Error('请输入正确格式数字,小数不超过2位'))
      } else {
        callback()
      }
    }
    return {
      salaryId: '',
      showDialog: false,
      title: '',
      rules: {
        stationWage: [
          { validator: validateMoney, trigger: 'blur' }
        ],
        scaleWage: [
          { validator: validateMoney, trigger: 'blur' }
        ],
        stationAllowance: [
          { validator: validateMoney, trigger: 'blur' }
        ],
        calculatedAmount: [
          { validator: validateMoney, trigger: 'blur' }
        ],
        actualAmount: [
          { validator: validateMoney, trigger: 'blur' }
        ]
      },
      btnSaveLoading: false,
      btnCalculateLoading: false,
      isEdit: false,
      dataModel: {
        employeeModel: {},
        enumCalculationType: 1,
        enumThirteenthSalaryEmployeeStatus: 1
      },
      calculationTypeOptions: [],
      employeeStatusOptions: []
    }
  },
  created() {
    this.loadCalculationTypeOptions()
    this.loadEmployeeStatusOptions()
  },
  methods: {
    initDialog(row) {
      if (!row) {
        this.title = '新增十三月工资'
        this.isEdit = false
      } else {
        this.title = '编辑十三月工资'
        this.isEdit = true
        this.getData(row.id)
      }
      this.dataModel.salaryId = this.salaryId
      this.showDialog = true
    },
    getData(id) {
      salaryApi.getThirteenthSalary({ id: id }).then(res => {
        if (res.succeed) {
          this.dataModel = res.data
          this.setEmployeeModel(res.data.employee)
        }
      }).catch(res => {
        console.log(res)
      })
    },
    saveDialog() {
      this.$refs['dataForm'].validate(valid => {
        
        if (parseFloat(this.dataModel.calculateAmount) !== parseFloat(this.dataModel.actualAmount)) {
          if (this.dataModel.actualAmount === undefined) {
            this.$message.error('实际金额不可为空，请重试')
            return
          }
          if (this.dataModel.remark === '' || this.dataModel.remark === undefined) {
            this.$message.error('计算金额与实际金额不一致，请备注原因后再提交')
            return
          }
        }

        if (valid) {
          this.btnSaveLoading = true
          if (!this.isEdit) {
            salaryApi.addThirteenthSalary(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '添加成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          } else {
            salaryApi.updateThirteenthSalary(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '修改成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          }
        }
      })
    },
    closeDialog() {
      this.dataModel = {
        employeeModel: {},
        enumCalculationType: 1,
        enumThirteenthSalaryEmployeeStatus: 1
      }
      this.showDialog = false
      this.$refs.dataForm.resetFields()
    },
    selectEmployeeDialog() {
      this.$refs.selectEmployee.loadTree()
      this.$refs.selectEmployee.showEmployee = true
    },
    loadCalculationTypeOptions() {
      sysManageApi.getEnumInfos({ enumType: 'ThirteenthSalaryCalculationType' }).then(result => {
        this.calculationTypeOptions = result.data.datas
      }).catch(error => {
        console.log(error)
      })
    },
    loadEmployeeStatusOptions() {
      sysManageApi.getEnumInfos({ enumType: 'ThirteenthSalaryEmployeeStatus' }).then(result => {
        this.employeeStatusOptions = result.data.datas
      }).catch(error => {
        console.log(error)
      })
    },
    calculateAmount() {
      if (!this.dataModel.employeeId) {
        this.$message.warning('请先选择员工')
        return
      }
      if (!this.dataModel.enumThirteenthSalaryCalculationType) {
        this.$message.warning('请先选择标准')
        return
      }
      var amount = 0
      if (this.dataModel.enumThirteenthSalaryCalculationType == 1) {
        amount = this.dataModel.baseMoney
      } else if (this.dataModel.enumThirteenthSalaryCalculationType == 2) {
        amount = calculatePercentage(this.dataModel.baseMoney / 2, 100, 2)
      } else if (this.dataModel.enumThirteenthSalaryCalculationType == 4) {
        amount = this.dataModel.baseMoney
      }

      this.$set(this.dataModel, 'calculateAmount',amount)
      this.$set(this.dataModel, 'actualAmount',amount)
    },
    calculateThirteenthSalary(emp) {
      this.dataModel.employeeId = emp.id
      this.dataModel.salaryId = this.salaryId
      salaryApi.calculateThirteenthSalary(this.dataModel).then(res => {
        if (res.succeed) {
          this.dataModel = res.data

          this.setEmployeeModel(emp)
        }
      }).catch(res => {
        console.log(res)
      })
    },
    setEmployee(emp) {
      this.dataModel = { employeeModel: {}}
      this.dataModel.employeeId = emp.id
      this.dataModel.salaryId = this.salaryId
      this.calculateThirteenthSalary(emp)
    },
    setEmployeeModel(emp) {
      this.dataModel.employeeId = emp.id
      this.dataModel.salaryId = this.salaryId
      this.$set(this.dataModel, 'employeeModel',
        {
          employeeId: emp.id,
          empUid: emp.uid,
          empCode: emp.empCode,
          empName: emp.displayName,
          genderDesc: emp.enumGenderDesc,
          empDept: emp.deptName,
          hospitalAreaNameText: emp.hospitalAreaNameText,
          identityNumber: emp.identityNumber,
          hireStyleName: emp.employeeHR.hireStyleName,
          leaveStyleName: emp.employeeHR.leaveStyleName,
          effHireDate: emp.employeeHR.effHireDate,
          officialRankName: emp.employeeHR.officialRankName
        })
    }
  }
}
</script>

<style>
</style>
