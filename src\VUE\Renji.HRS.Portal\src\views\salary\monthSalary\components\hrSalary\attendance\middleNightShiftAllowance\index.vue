<template>
  <div class="app-container">
    <layout4>
      <template #main>
        <el-form v-if="!employeeId" ref="ref_searchFrom" :inline="true" :model="listQuery">
          <el-form-item>
            <el-input v-model="listQuery.uid" clearable placeholder="唯一码" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.empCode" clearable placeholder="工号" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.displayName" clearable placeholder="姓名" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.deptName" clearable placeholder="部门" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.officialRankName" clearable placeholder="职别" />
          </el-form-item>
          <el-form-item>
            <el-select v-model="listQuery.hireStyleId" clearable placeholder="请选择在职方式">
              <el-option v-for="item in hireStyleOptions" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-date-picker v-model="listQuery.attMonth" type="month" placeholder="考勤月份" value-format="yyyy-MM-dd" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
            <!-- <el-button v-if="salaryData.enumStatus == 1" type="primary" icon="el-icon-plus" @click="showDialog()">新增</el-button> -->
            <el-button v-if="salaryData.enumStatus == 1" type="primary" icon="el-icon-upload" @click="autoImport()">自动导入</el-button>
            <el-button v-if="salaryData.enumStatus == 1" type="primary" @click="downloadexceltemplate">模板下载</el-button>
            <el-upload v-if="salaryData.enumStatus == 1" action="" style="float:right;margin-left:10px;" :http-request="importExcel" accept=".xlsx" :show-file-list="false">
              <el-button slot="trigger" icon="el-icon-upload" type="primary">导入</el-button>
            </el-upload>
          </el-form-item>
        </el-form>
        <el-table ref="tableList" v-loading="listLoading" :data="pageList" border stripe fit highlight-current-row style="width: 100%;" :header-cell-style="{ background: '#F5F7FA', color: '#606266' }" @sort-change="sortChange">
          <el-table-column label="部门" prop="Employee.Department.Name" sortable="custom">
            <template slot-scope="{ row }">
              <span>{{ row.deptName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="职别" prop="Employee.EmployeeHR.OfficialRank.Name" sortable="custom">
            <template slot-scope="{ row }">
              <span>{{ row.officialRankName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="在职方式" prop="Employee.EmployeeHR.HireStyle.Name" sortable="custom" :min-width="100">
            <template slot-scope="{ row }">
              <span>{{ row.hireStyleName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="AttMonth" label="考勤月份" width="120" sortable="custom">
            <template slot-scope="{row}">
              <span>{{ row.attMonth ? (new Date(row.attMonth)).Format('yyyy-MM') : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="MiddleShift" label="中班" width="120" sortable="custom" header-align="left" align="right">
            <template slot-scope="{row}">
              <span>{{ row.middleShift }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="NightShift" label="夜班" header-align="left" align="right" width="120" sortable="custom">
            <template slot-scope="{row}">
              <span>{{ row.nightShift }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="AllDayShift" label="24小时班" header-align="left" align="right" width="120" sortable="custom">
            <template slot-scope="{row}">
              <span>{{ row.allDayShift }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="MiddleShiftEmergency" label="急诊中班" header-align="left" align="right" width="120" sortable="custom">
            <template slot-scope="{row}">
              <span>{{ row.middleShiftEmergency }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="NightShiftEmergency" label="急诊夜班" header-align="left" align="right" width="120" sortable="custom">
            <template slot-scope="{row}">
              <span>{{ row.nightShiftEmergency }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="AllDayShiftEmergency" label="急诊24小时" header-align="left" align="right" width="120" sortable="custom">
            <template slot-scope="{row}">
              <span>{{ row.allDayShiftEmergency }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="NursingGradeAMiddleShift" label="护理A档中班" header-align="left" align="right" width="120" sortable="custom">
            <template slot-scope="{row}">
              <span>{{ row.nursingGradeAMiddleShift }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="NursingGradeANightShift" label="护理A档夜班" header-align="left" align="right" width="120" sortable="custom">
            <template slot-scope="{row}">
              <span>{{ row.nursingGradeANightShift }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="NursingGradeAAllDayShift" label="护理A档24小时" header-align="left" align="right" width="140" sortable="custom">
            <template slot-scope="{row}">
              <span>{{ row.nursingGradeAAllDayShift }}</span>
            </template>
          </el-table-column>
          <!--<el-table-column prop="Money" label="计算金额" header-align="left" align="right" width="120" sortable="custom">
            <template slot-scope="{row}">
              <span>{{ row.money | formatMoney2 }}</span>
            </template>
          </el-table-column>-->
          <el-table-column prop="ActualAmount" label="实际金额" header-align="left" align="right" width="120" sortable="custom">
            <template slot-scope="{row}">
              <span>{{ row.actualAmount | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="150px" prop="Remark" sortable="custom">
            <template slot-scope="{ row } ">
              <span>{{ row.remark }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" header-align="center" width="150" class-name="small-padding fixed-width">
            <template slot-scope="{ row }">
              <el-button v-if="salaryData.enumStatus == 1" type="primary" style="margin-left:5px !important; padding-left: 5px !important;" size="mini" @click="showDialog(row)">
                编辑
              </el-button>
              <el-button v-if="salaryData.enumStatus == 1" style="padding-left: 5px !important;" size="mini" type="danger" @click="deleteMiddleNightShiftAllowance(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
          <employeeTableColumns />
        </el-table>
        <c-pagination v-show="listQuery.total > 0" :total="listQuery.total" :page-sizes="[10, 20, 50]" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getPageList" />
        <el-row>
          <el-col style="width: 210px;">
            <div
              style="
                margin-top: 20px;
                background-color: #fff7f7;
                border: 1px solid #f56c6c;
                border-radius: 4px;
                padding: 15px;
                color: #f56c6c;
                font-size: 14px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                line-height: 1.6;
              "
            >
              <i class="el-icon-warning" style="margin-right: 8px;"></i>
              <strong>提示：</strong>
              <ol style="padding-left: 20px; margin-top: 8px;">
                <li>金额 = 各班次 × 对应班费</li>
              </ol>
            </div>
          </el-col>
        </el-row>
      </template>
    </layout4>

    <editDialog ref="editDialog" @refreshData="getPageList" />
  </div>
</template>

<script>
import hRManageApi from '@/api/hRManage'
import salaryApi from '@/api/salary'
import editDialog from './components/editPage'
import employeeTableColumns from '@/views/salary/monthSalary/components/hrSalary/employeeTableColumns'

export default {
  components: {
    editDialog,
    employeeTableColumns
  },
  props: {
    employeeId: {
      type: String,
      default: '',
      required: false
    }
  },
  data() {
    return {
      salaryId: '',
      pageList: [],
      listQuery: {
        total: 1,
        pageIndex: 1,
        pageSize: 10,
        employeeId: '',
        attMonth: null
      },
      listLoading: false,
      hireStyleOptions: [],
      salaryData: {}
    }
  },
  methods: {
    init() {
      this.salaryId = this.$route.query.salaryId
      if (this.salaryId) {
        this.getSalary()
        if (this.employeeId) {
          this.listQuery.employeeId = this.employeeId
        }
      } else {
        this.$notice.message('系统错误，请刷新页面重试或联系管理员', 'error')
      }
      this.loadEmployeeHireStyle()
      this.getPageList()
    },
    getSalary() {
      salaryApi.getSalary({ id: this.salaryId }).then(result => {
        if (result.succeed) {
          this.salaryData = result.data
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    sortChange(c) {
      if (c.order === 'ascending') {
        this.listQuery.order = c.prop + ' ' + 'asc'
      } else if (c.order === 'descending') {
        this.listQuery.order = c.prop + ' ' + 'desc'
      } else {
        this.$delete(this.listQuery, 'order')
      }
      this.search()
    },
    search() {
      this.listQuery.pageIndex = 1
      this.getPageList()
    },
    getPageList() {
      this.listLoading = true
      this.listQuery.salaryId = this.salaryId
      salaryApi.queryMiddleNightShiftAllowance(this.listQuery).then(result => {
        if (result.succeed) {
          this.pageList = result.data.datas
          this.listQuery.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      }).finally(() => {
        this.listLoading = false
      })
    },
    loadEmployeeHireStyle() {
      hRManageApi.queryHireStyle().then(result => {
        this.hireStyleOptions = result.data.datas
      }).catch(error => {
        console.log(error)
      })
    },
    deleteMiddleNightShiftAllowance(data) {
      this.$confirm('确定删除这条记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        salaryApi.deleteMiddleNightShiftAllowance(data).then(result => {
          if (result.succeed) {
            this.getPageList()
            this.$notice.message('删除成功', 'success')
          } else {
            if (!result.succeed) {
              this.$notice.message('删除失败，请联系管理员', 'info')
            }
          }
        })
          .catch(error => {
            this.listLoading = false
            console.log(error)
          })
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    showDialog(row) {
      this.$refs.editDialog.salaryId = this.salaryId
      this.$refs.editDialog.initDialog(row)
    },
    autoImport() {
      this.autoMiddleNightShiftAllowance()
    },
    autoMiddleNightShiftAllowance() {
      this.$confirm('确定导入中夜班费数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        salaryApi.autoMiddleNightShiftAllowance({ salaryId: this.salaryId }).then(result => {
          if (result.succeed) {
            this.getPageList()
            this.$notice.message('导入成功', 'success')
          } else {
            if (!result.succeed) {
              this.$notice.message('导入失败，请联系管理员', 'info')
            }
          }
        })
          .catch(error => {
            this.listLoading = false
            console.log(error)
          })
      })
    },
    downloadexceltemplate() {
      hRManageApi.downlodaImportExcelTemplate({ type: 'importMiddleNightShiftAllowance' }).then(res => {
        const fileDownload = require('js-file-download')
        var filename = 'MiddleNightShiftAllowanceTemplate.xlsx'
        if (res.data) {
          fileDownload(res.data, filename)
        } else {
          fileDownload(res, filename)
        }
      }).catch((e) => { })
    },
    // 导入
    importExcel(params) {
      const file = params.file
      const formData = new FormData()
      salaryApi.importMiddleNightShiftAllowance(file, formData, this.salaryId).then(res => {
        if (res.succeed) {
          this.$message({ message: '导入成功', type: 'success' })
          this.search()
        }
      }).catch(res => {
        this.search()
      })
    }
  }
}
</script>

<style></style>
