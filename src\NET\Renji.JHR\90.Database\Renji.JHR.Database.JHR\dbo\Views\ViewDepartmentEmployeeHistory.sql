﻿CREATE VIEW [dbo].ViewDepartmentEmployeeHistory
	AS SELECT  DepartmentEmployeeHistory.ID, dbo.Department.HospitalCode AS HisCode, dbo.Department.Name AS DepartmentName, 
                dbo.Employee.Uid AS PrePrincipaUid, dbo.Employee.EmpCode AS PrePrincipEmpCode, 
				dbo.Employee.DisplayName PrePrincipName, 
                Employee_1.Uid AS NewPrincipaUid, Employee_1.EmpCode AS NewPrincipaEmpCode, 
                Employee_1.DisplayName AS NewPrincipaName
FROM      dbo.DepartmentEmployeeHistory INNER JOIN
                dbo.Department 
				ON dbo.DepartmentEmployeeHistory.DepartmentId = dbo.Department.ID 
				LEFT JOIN
                dbo.Employee ON 
				dbo.DepartmentEmployeeHistory.PreEmployeeId = Employee.ID
				 LEFT JOIN
                dbo.Employee AS Employee_1
				 ON dbo.DepartmentEmployeeHistory.NewEmployeeId = Employee_1.ID