﻿using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Renji.JHR.Entities
{
    /// <summary>
    /// 薪资状态
    /// </summary>
    public enum SalaryStatus
    {
        [Description(" ")]
        None = 0,

        /// <summary>
        /// HR 阶段
        /// </summary>
        [Description("HR阶段")]
        HR = 1,

        /// <summary>
        /// 财务阶段
        /// </summary>
        [Description("财务阶段")]
        Finance = 2,

        /// <summary>
        /// 完成
        /// </summary>
        [Description("完成")]
        Complete = 3,

        /// <summary>
        /// 计算错误
        /// </summary>
        [Description("计算错误")]
        [EnumGroup(Ordinal = 900)]
        CalculatedError = -1,
    }
}
