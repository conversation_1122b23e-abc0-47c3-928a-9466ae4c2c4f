(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5981dbfd"],{"084d":function(t,e,a){},1980:function(t,e,a){},4926:function(t,e,a){"use strict";var r=a("84ad"),o=a.n(r);o.a},5794:function(t,e,a){"use strict";var r=a("1980"),o=a.n(r);o.a},"6a2c":function(t,e,a){"use strict";var r=a("084d"),o=a.n(r);o.a},"84ad":function(t,e,a){},cbd2:function(t,e,a){"use strict";var r=a("cfe3"),o="AttendanceManage",n=new r["a"](o);e["a"]={getAttMonthShiftRecord:function(t){return n.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return n.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return n.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return n.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return n.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return n.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return n.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return n.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return n.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return n.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return n.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return n.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return n.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return n.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return n.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return n.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return n.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return n.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return n.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return n.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return n.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return n.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return n.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return n.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return n.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return n.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return n.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return n.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return n.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return n.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return n.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return n.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return n.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return n.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return n.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return n.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return n.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return n.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return n.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return n.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return n.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return n.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return n.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return n.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return n.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return n.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return n.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return n.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return n.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return n.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return n.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return n.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return n.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return n.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return n.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return n.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return n.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return n.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return n.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return n.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return n.get("GetSameDeptEmployeeWithHealthAllowance",t)}}},f827:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("layout3",{scopedSlots:t._u([{key:"aside",fn:function(){return[a("el-button",{on:{click:t.syncTreeColor}},[t._v("颜色同步")]),a("c-tree",{attrs:{options:t.treeData,props:t.treeProps,"expand-all":!0},on:{nodeClick:t.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.headModel}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{staticClass:"input_class",attrs:{label:"月份"}},[a("el-date-picker",{attrs:{clearable:!1,type:"month",placeholder:"请选择申领月份","value-format":"yyyy-MM"},on:{change:t.dateChange},model:{value:t.recordMonth,callback:function(e){t.recordMonth=e},expression:"recordMonth"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"申领表数据状态"}},[a("span",{staticStyle:{color:"#369","font-weight":"bold"}},[t._v(t._s(t.headModel.enumStatusDesc)+" ")])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"防保科数据状态"}},[a("span",{staticStyle:{color:"#369","font-weight":"bold"}},[t._v(t._s(t.headModel.attDayOffRecordProphylacticStatusDesc)+" ")])])],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注"}},[a("span",[t._v(t._s(t.headModel.remark))])])],1)],1),a("el-row",[2==t.headModel.enumStatus&&t.headModel.isAllowReject?a("el-col",{attrs:{span:12}},[a("el-button",{attrs:{type:"primary"},on:{click:t.reject}},[t._v("考勤员数据退回")])],1):t._e()],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:t.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"}},[a("el-table-column",{attrs:{fixed:"",label:"序号",index:t.indexMethod,type:"index",align:"center"}}),a("el-table-column",{attrs:{prop:"date",label:"唯一码",align:"center",width:"80",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",{class:r.attDayOffRecordDetailStatus&&1==r.attDayOffRecordDetailStatus?"diffColor":""},[t._v(t._s(r.empUid))])]}}])}),a("el-table-column",{attrs:{prop:"date",label:"工号",align:"center",width:"80",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",{class:r.attDayOffRecordDetailStatus&&1==r.attDayOffRecordDetailStatus?"diffColor":""},[t._v(t._s(r.empCode))])]}}])}),a("el-table-column",{attrs:{prop:"date",label:"姓名",align:"center",width:"80",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.empName))])]}}])}),a("el-table-column",{attrs:{prop:"date",label:"在职方式",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.hireStyleName))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"应发公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(0===r.generalHoliday?"":r.generalHoliday))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"剩余公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(0===r.historyH12?"":r.historyH12))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.h12))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"上月卫贴标准",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.preMonthH1))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"本月卫贴标准",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.h1))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"病假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.attDayOffRecordDetailStatus&&1==r.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(r.h2)),a("br")]),r.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(r.prophylacticFilling.h2))]):t._e()]):a("span",[t._v(t._s(r.h2))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"事假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.attDayOffRecordDetailStatus&&1==r.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(r.h3)),a("br")]),r.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(r.prophylacticFilling.h3))]):t._e()]):a("span",[t._v(t._s(r.h3))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"产假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.attDayOffRecordDetailStatus&&1==r.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(r.h4)),a("br")]),r.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(r.prophylacticFilling.h4))]):t._e()]):a("span",[t._v(t._s(r.h4))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"哺乳假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.attDayOffRecordDetailStatus&&1==r.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(r.h5)),a("br")]),r.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(r.prophylacticFilling.h5))]):t._e()]):a("span",[t._v(t._s(r.h5))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"探亲假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.attDayOffRecordDetailStatus&&1==r.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(r.h6)),a("br")]),r.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(r.prophylacticFilling.h6))]):t._e()]):a("span",[t._v(t._s(r.h6))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"计生假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.attDayOffRecordDetailStatus&&1==r.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(r.h7)),a("br")]),r.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(r.prophylacticFilling.h7))]):t._e()]):a("span",[t._v(t._s(r.h7))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"婚丧假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.attDayOffRecordDetailStatus&&1==r.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(r.h8)),a("br")]),r.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(r.prophylacticFilling.h8))]):t._e()]):a("span",[t._v(t._s(r.h8))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"脱产读研",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.attDayOffRecordDetailStatus&&1==r.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(r.h9)),a("br")]),r.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(r.prophylacticFilling.h9))]):t._e()]):a("span",[t._v(t._s(r.h9))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因公出国",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.attDayOffRecordDetailStatus&&1==r.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(r.h10)),a("br")]),r.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(r.prophylacticFilling.h10))]):t._e()]):a("span",[t._v(t._s(r.h10))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因私出国",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.attDayOffRecordDetailStatus&&1==r.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(r.h11)),a("br")]),r.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(r.prophylacticFilling.h11))]):t._e()]):a("span",[t._v(t._s(r.h11))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"修改人",align:"right","header-align":"center","min-width":"140px"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.attDayOffRecordDetailStatus&&1==r.attDayOffRecordDetailStatus?a("span",[a("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(r.updator)),a("br")]),r.prophylacticFilling?a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(r.prophylacticFilling.updator))]):t._e()]):2!=t.headModel.enumStatus&&r.updator?a("span",[a("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(r.updator)),a("br")])]):a("span",[t._v(t._s(r.updator))])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[20,50,100],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.paginationChanged}})]},proxy:!0}])})],1)},o=[],n=(a("99af"),a("4de4"),a("fb6a"),a("d3b7"),a("25f0"),a("4d90"),a("cbd2")),c={components:{},data:function(){return{total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,recordMonth:this.getNowTime(),monthTotalDay:0,headModel:{remark:"",statue:""},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeLoading:!1,currentNode:null,tableData:[],allData:[],treeExpandedKeys:[],status:"",statusDept:"",statusDeptList:[],allStatusDepts:[]}},created:function(){this.syncTreeColor()},methods:{indexMethod:function(t){return(this.listQuery.pageIndex-1)*this.listQuery.pageSize+t+1},getNowTime:function(){var t=new Date,e=t.getFullYear(),a=t.getMonth();a=a.toString().padStart(2,"0"),"00"===a&&(e-=1,a="12");var r="".concat(e,"-").concat(a);return r},resetCurrentNode:function(){this.currentNode=null},treeNodeClick:function(t){this.currentNode=t,this.listQuery.pageIndex=1,this.getAttDayOffRecord()},dateChange:function(){this.listQuery.pageIndex=1,this.syncTreeColor(),this.getAttDayOffRecord()},statusChanged:function(t){var e=this.allStatusDepts.filter((function(e,a,r){return e.status===t}))[0];e&&e.depts.length>0?this.statusDeptList=e.depts:(this.statusDeptList=[],this.statusDept="")},getAttDayOffRecord:function(){var t=this;if(this.currentNode){var e={RecordMonth:this.recordMonth,DeptId:this.currentNode.id};n["a"].getAttDayOffRecord(e).then((function(e){e.succeed?(t.headModel=e.data,t.queryPersonnelAttendanceData(e.data.id)):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))}else this.$notice.message("请选择部门。","info")},queryPersonnelAttendanceData:function(t){var e=this,a={RecordId:t,DeptId:this.currentNode.id,RecordMonth:this.recordMonth};n["a"].queryPersonnelAttendanceData(a).then((function(t){e.listLoading=!1,t.succeed?(e.allData=t.data.datas,e.total=t.data.recordCount,e.getTableData()):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},paginationChanged:function(){this.getTableData()},getTableData:function(){var t=this.listQuery.pageSize,e=this.listQuery.pageIndex;this.tableData=this.allData.slice((e-1)*t,e*t)},syncTreeColor:function(){var t=this,e={RecordMonth:this.recordMonth};n["a"].getColorDeptTree_DayOff(e).then((function(e){e.succeed&&(t.treeLoading=!1,t.treeData=e.data.colorDepts,t.treeExpandedKeys.push(e.data.colorDepts[0].id),t.allStatusDepts=e.data.statusDepts,t.currentNode&&t.$refs.tree.setCurrentKey(t.currentNode.id))})).catch((function(e){t.treeLoading=!1,console.log(e)}))},reject:function(){var t=this;this.recordMonth&&this.currentNode&&this.$confirm("确认退回吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.headModel.fillings=t.allData,n["a"].rejectAttDayOffRecord(t.headModel).then((function(e){e.succeed?(t.getAttDayOffRecord(),t.syncTreeColor(),t.$notice.message("操作成功","success")):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))})),this.currentNode?this.recordMonth||this.$notice.message("请选择申请日期","warning"):this.$notice.message("请选择部门","warning")}}},l=c,i=(a("6a2c"),a("4926"),a("5794"),a("2877")),s=Object(i["a"])(l,r,o,!1,null,"3e643e94",null);e["default"]=s.exports}}]);