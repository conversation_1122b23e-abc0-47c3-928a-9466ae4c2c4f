﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Microsoft.EntityFrameworkCore;
using Shinsoft.Core.EntityFrameworkCore;
using Renji.JHR.Entities;

namespace Renji.JHR.Dal
{
    public partial class BizDbContext : BaseDbContext
    {
	    public BizDbContext(DbContextOptions options)
			: base(options)
        { 
		}

        /// <summary>
        /// 行政聘任
        /// </summary>
		public DbSet<AdministrativeAppointment> AdministrativeAppointment { get; set; }

        /// <summary>
        /// 援外津贴
        /// </summary>
		public DbSet<AssistanceForeign> AssistanceForeign { get; set; }

        /// <summary>
        /// 援滇津贴
        /// </summary>
		public DbSet<AssistanceYunnan> AssistanceYunnan { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
		public DbSet<Attachment> Attachment { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttDayOffRecord> AttDayOffRecord { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttDayOffRecordDetail> AttDayOffRecordDetail { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttDayOffRecordDetailSalaryChange> AttDayOffRecordDetailSalaryChange { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttDayOffRecordFilling> AttDayOffRecordFilling { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttDayOffRecordPost> AttDayOffRecordPost { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttDayOffRecordProphylactic> AttDayOffRecordProphylactic { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttDayOffRecordProphylacticCase> AttDayOffRecordProphylacticCase { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttDayOffRecordProphylacticDetail> AttDayOffRecordProphylacticDetail { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttendanceHealthAllowance> AttendanceHealthAllowance { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttendanceHealthAllowanceSalaryChange> AttendanceHealthAllowanceSalaryChange { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttHolidayOTRecord> AttHolidayOTRecord { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttHolidayOTRecordDetail> AttHolidayOTRecordDetail { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttMonthShiftRecord> AttMonthShiftRecord { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttMonthShiftRecordDetail> AttMonthShiftRecordDetail { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttMonthShiftRecordDetailSalaryChange> AttMonthShiftRecordDetailSalaryChange { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttMonthWatchRecord> AttMonthWatchRecord { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AttMonthWatchRecordSalaryChange> AttMonthWatchRecordSalaryChange { get; set; }

        /// <summary>
        /// 日历
        /// </summary>
		public DbSet<Calendar> Calendar { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CarSubsidy> CarSubsidy { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ControlRight> ControlRight { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ControlRightPanel> ControlRightPanel { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<DataType> DataType { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<DataTypeOperation> DataTypeOperation { get; set; }

        /// <summary>
        /// 科室
        /// </summary>
		public DbSet<Department> Department { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<DepartmentEmployee> DepartmentEmployee { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<DepartmentEmployeeHistory> DepartmentEmployeeHistory { get; set; }

        /// <summary>
        /// 字典
        /// </summary>
		public DbSet<Dict> Dict { get; set; }

        /// <summary>
        /// 员工
        /// </summary>
		public DbSet<Employee> Employee { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeAbroadInfo> EmployeeAbroadInfo { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeAccident> EmployeeAccident { get; set; }

        /// <summary>
        /// 年度社保基数修正
        /// </summary>
		public DbSet<EmployeeAnnualSocialSecurityBaseCorrection> EmployeeAnnualSocialSecurityBaseCorrection { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeArticle> EmployeeArticle { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeAssessment> EmployeeAssessment { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeAward> EmployeeAward { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeBenefit> EmployeeBenefit { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeCertify> EmployeeCertify { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeCertifyTemp> EmployeeCertifyTemp { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeClass> EmployeeClass { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeContract> EmployeeContract { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeContractTemp> EmployeeContractTemp { get; set; }

        /// <summary>
        /// 已故
        /// </summary>
		public DbSet<EmployeeDeceased> EmployeeDeceased { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeDeduct> EmployeeDeduct { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeDeductCalculate> EmployeeDeductCalculate { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeDeductWorkingAge> EmployeeDeductWorkingAge { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeDeptHistory> EmployeeDeptHistory { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeEducation> EmployeeEducation { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeEducationTemp> EmployeeEducationTemp { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeHealth> EmployeeHealth { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeHighTalent> EmployeeHighTalent { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeHR> EmployeeHR { get; set; }

        /// <summary>
        /// 所在支部
        /// </summary>
		public DbSet<EmployeeHRAffiliatedBranch> EmployeeHRAffiliatedBranch { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeHRDict> EmployeeHRDict { get; set; }

        /// <summary>
        /// 党员奖惩
        /// </summary>
		public DbSet<EmployeeHRPartyMemberHonor> EmployeeHRPartyMemberHonor { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeHRTemp> EmployeeHRTemp { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeIncentive> EmployeeIncentive { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeList> EmployeeList { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeOAInsertFail> EmployeeOAInsertFail { get; set; }

        /// <summary>
        /// 职业年金补扣
        /// </summary>
		public DbSet<EmployeeOccupationalAnnuityBackDeduction> EmployeeOccupationalAnnuityBackDeduction { get; set; }

        /// <summary>
        /// 独子费调整
        /// </summary>
		public DbSet<EmployeeOnlyChildFeeAdjustment> EmployeeOnlyChildFeeAdjustment { get; set; }

        /// <summary>
        /// 其他停工资
        /// </summary>
		public DbSet<EmployeeOtherConditionsStopSalary> EmployeeOtherConditionsStopSalary { get; set; }

        /// <summary>
        /// 出国人员多扣款
        /// </summary>
		public DbSet<EmployeeOverseasExcessDeduction> EmployeeOverseasExcessDeduction { get; set; }

        /// <summary>
        /// 出国人员月薪扣款
        /// </summary>
		public DbSet<EmployeeOverseasMonthlyDeduction> EmployeeOverseasMonthlyDeduction { get; set; }

        /// <summary>
        /// 回国补发明细
        /// </summary>
		public DbSet<EmployeeOverseasReissueDetail> EmployeeOverseasReissueDetail { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeOverseasReturnReissue> EmployeeOverseasReturnReissue { get; set; }

        /// <summary>
        /// 出国人员停工资
        /// </summary>
		public DbSet<EmployeeOverseasSuspensionSalary> EmployeeOverseasSuspensionSalary { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeePatent> EmployeePatent { get; set; }

        /// <summary>
        /// 缴金补扣
        /// </summary>
		public DbSet<EmployeePaymentBackPayDeduction> EmployeePaymentBackPayDeduction { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeePayrollHistory> EmployeePayrollHistory { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeePayrollPrint> EmployeePayrollPrint { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeePayRollSalaryAdd> EmployeePayRollSalaryAdd { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeePayRollSalaryAddHis> EmployeePayRollSalaryAddHis { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeePayRollSalaryAddSalaryChange> EmployeePayRollSalaryAddSalaryChange { get; set; }

        /// <summary>
        /// 博士后出站
        /// </summary>
		public DbSet<EmployeePostdoctorOutbound> EmployeePostdoctorOutbound { get; set; }

        /// <summary>
        /// 博士后两年
        /// </summary>
		public DbSet<EmployeePostdoctorTwoYears> EmployeePostdoctorTwoYears { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeQuerySetting> EmployeeQuerySetting { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeQuerySettingItem> EmployeeQuerySettingItem { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeRelation> EmployeeRelation { get; set; }

        /// <summary>
        /// 辞职
        /// </summary>
		public DbSet<EmployeeResign> EmployeeResign { get; set; }

        /// <summary>
        /// 退休
        /// </summary>
		public DbSet<EmployeeRetire> EmployeeRetire { get; set; }

        /// <summary>
        /// 员工薪资
        /// </summary>
		public DbSet<EmployeeSalary> EmployeeSalary { get; set; }

        /// <summary>
        /// 员工修正月薪
        /// </summary>
		public DbSet<EmployeeSalaryCorrectionMonth> EmployeeSalaryCorrectionMonth { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeSalaryRecord> EmployeeSalaryRecord { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeSocialInsurance> EmployeeSocialInsurance { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeSocialInsuranceHistory> EmployeeSocialInsuranceHistory { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeSocialInsuranceTemp> EmployeeSocialInsuranceTemp { get; set; }

        /// <summary>
        /// 社保基数修正
        /// </summary>
		public DbSet<EmployeeSocialSecurityBaseCorrection> EmployeeSocialSecurityBaseCorrection { get; set; }

        /// <summary>
        /// 员工岗位
        /// </summary>
		public DbSet<EmployeeStation> EmployeeStation { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeStationCurrent> EmployeeStationCurrent { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeStationNotice> EmployeeStationNotice { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeStationTemp> EmployeeStationTemp { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeTeach> EmployeeTeach { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeTeacher> EmployeeTeacher { get; set; }

        /// <summary>
        /// 员工
        /// </summary>
		public DbSet<EmployeeTemp> EmployeeTemp { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeTrain> EmployeeTrain { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeWage> EmployeeWage { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeWageHistory> EmployeeWageHistory { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeWork> EmployeeWork { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeWorkTemp> EmployeeWorkTemp { get; set; }

        /// <summary>
        /// 总院行政值班费
        /// </summary>
		public DbSet<GeneralHospitalAdminDutyAllowance> GeneralHospitalAdminDutyAllowance { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<HROnAccount> HROnAccount { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<HRStatisticsReport> HRStatisticsReport { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<Information> Information { get; set; }

        /// <summary>
        /// 专业技术职称代码
        /// </summary>
		public DbSet<MajorTechnical> MajorTechnical { get; set; }

        /// <summary>
        /// 中夜班费
        /// </summary>
		public DbSet<MiddleNightShiftAllowance> MiddleNightShiftAllowance { get; set; }

        /// <summary>
        /// 最低工资补助表
        /// </summary>
		public DbSet<MinimumWageSubsidy> MinimumWageSubsidy { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<MsgCompany> MsgCompany { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<MsgPerson> MsgPerson { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<MsgReadInfo> MsgReadInfo { get; set; }

        /// <summary>
        /// 新进员工
        /// </summary>
		public DbSet<NewEmployee> NewEmployee { get; set; }

        /// <summary>
        /// 非月薪病产假
        /// </summary>
		public DbSet<NonMonthlySalaryLeave> NonMonthlySalaryLeave { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<OtherEmployeeInfo> OtherEmployeeInfo { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<OtherEmployeeInfoTemp> OtherEmployeeInfoTemp { get; set; }

        /// <summary>
        /// 加班费
        /// </summary>
		public DbSet<OvertimeAllowance> OvertimeAllowance { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<PasswordReset> PasswordReset { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<PayRollCompGroup> PayRollCompGroup { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<PayRollOrgClass> PayRollOrgClass { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<PayRollOrgPositionSalarys> PayRollOrgPositionSalarys { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<PayRollOrgSalary> PayRollOrgSalary { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<PayRollOrgSalaryLevel> PayRollOrgSalaryLevel { get; set; }

        /// <summary>
        /// 权限
        /// </summary>
		public DbSet<Permission> Permission { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
		public DbSet<Position> Position { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<PositionStation> PositionStation { get; set; }

        /// <summary>
        /// 博士后房贴
        /// </summary>
		public DbSet<PostdoctoralHousingAllowance> PostdoctoralHousingAllowance { get; set; }

        /// <summary>
        /// 博士后修正年薪
        /// </summary>
		public DbSet<PostdoctoralSalaryCorrectionYear> PostdoctoralSalaryCorrectionYear { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<QueryColumnInfo> QueryColumnInfo { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<QuerySetting> QuerySetting { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<QuerySettingColumn> QuerySettingColumn { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<QueryTableInfo> QueryTableInfo { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<RenjiLog> RenjiLog { get; set; }

        /// <summary>
        /// 退休人员补发
        /// </summary>
		public DbSet<RetiredEmployeeReissue> RetiredEmployeeReissue { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<RetireType> RetireType { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<RetireYear> RetireYear { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<RightOfDept> RightOfDept { get; set; }

        /// <summary>
        /// 角色
        /// </summary>
		public DbSet<Role> Role { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<RoleEmployeeList> RoleEmployeeList { get; set; }

        /// <summary>
        /// 角色成员
        /// </summary>
		public DbSet<RoleMember> RoleMember { get; set; }

        /// <summary>
        /// 角色权限
        /// </summary>
		public DbSet<RolePermission> RolePermission { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<Salary> Salary { get; set; }

        /// <summary>
        /// 工资模块
        /// </summary>
		public DbSet<SalaryData> SalaryData { get; set; }

        /// <summary>
        /// 工资模块
        /// </summary>
		public DbSet<SalaryDataAdjust> SalaryDataAdjust { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<SalaryDetail> SalaryDetail { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<SalaryExtendedColumn> SalaryExtendedColumn { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<SalaryExtendedDetails> SalaryExtendedDetails { get; set; }

        /// <summary>
        /// 病产假
        /// </summary>
		public DbSet<SalaryLeave> SalaryLeave { get; set; }

        /// <summary>
        /// 工资模块
        /// </summary>
		public DbSet<SalaryMonthData> SalaryMonthData { get; set; }

        /// <summary>
        /// 员工薪资
        /// </summary>
		public DbSet<SalaryMonthEmployee> SalaryMonthEmployee { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<SalaryParameter> SalaryParameter { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<SalaryScale> SalaryScale { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<SalaryScaleStarting> SalaryScaleStarting { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<Seniority> Seniority { get; set; }

        /// <summary>
        /// 一值班二值班费
        /// </summary>
		public DbSet<ShiftAllowance> ShiftAllowance { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<SocialSecurityWithhold> SocialSecurityWithhold { get; set; }

        /// <summary>
        /// 零星补病产假
        /// </summary>
		public DbSet<SporadicSalaryLeave> SporadicSalaryLeave { get; set; }

        /// <summary>
        /// 岗位
        /// </summary>
		public DbSet<Station> Station { get; set; }

        /// <summary>
        /// 岗位津贴
        /// </summary>
		public DbSet<StationAllowance> StationAllowance { get; set; }

        /// <summary>
        /// 系统设置
        /// </summary>
		public DbSet<SysSetting> SysSetting { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<TelephoneFee> TelephoneFee { get; set; }

        /// <summary>
        /// 十三薪
        /// </summary>
		public DbSet<ThirteenthSalary> ThirteenthSalary { get; set; }

        /// <summary>
        /// 用户
        /// </summary>
		public DbSet<User> User { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewDegreeApi> ViewDegreeApi { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewDepartmentApi> ViewDepartmentApi { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewDepartmentEmployeeHistory> ViewDepartmentEmployeeHistory { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewEducationApi> ViewEducationApi { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewEmployeeAbroadInfoApi> ViewEmployeeAbroadInfoApi { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewEmployeeApi> ViewEmployeeApi { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewEmployeeClassApi> ViewEmployeeClassApi { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewEmployeeEducationApi> ViewEmployeeEducationApi { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewEmployeeHRDict> ViewEmployeeHRDict { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewEmployeeHRDictExtend> ViewEmployeeHRDictExtend { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewEmployeeInfo> ViewEmployeeInfo { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewEmployeeStation> ViewEmployeeStation { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewEmpSubInfoList> ViewEmpSubInfoList { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewHROnAccount> ViewHROnAccount { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewJobTitleApi> ViewJobTitleApi { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewLevelApi> ViewLevelApi { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewPersonnelInformationExport> ViewPersonnelInformationExport { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ViewUserInfo> ViewUserInfo { get; set; }

	}
}