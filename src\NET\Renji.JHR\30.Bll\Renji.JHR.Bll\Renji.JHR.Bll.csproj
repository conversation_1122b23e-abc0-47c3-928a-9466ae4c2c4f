﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
	<NoWarn>1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
	<None Remove="Renji.JHR.Bll.csproj.vspscc" />
	<None Remove="Renji.JHR.Bll.xml" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Renji.JHR.Common.Configration" />
    <Using Include="Renji.JHR.Entities" />
    <Using Include="Shinsoft.Core" />
	<Using Include="Shinsoft.Core.EntityFrameworkCore" />
  </ItemGroup>

  <ItemGroup>
	<FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
	<Reference Include="Shinsoft.Core">
	  <HintPath>..\..\00.Reference\net6.0\Shinsoft.Core.dll</HintPath>
	</Reference>
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="dmdbms.DmProvider" Version="1.1.0.20739" />
	<PackageReference Include="dmdbms.Microsoft.EntityFrameworkCore.Dm" Version="6.0.28.20604" />
	<PackageReference Include="EFCore.BulkExtensions" Version="6.7.16" />
	<PackageReference Include="MailKit" Version="4.8.0" />
	<PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.28" />
	<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="6.0.28" />
	<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.28" />
	<PackageReference Include="MimeKit" Version="4.8.0" />
  </ItemGroup>

  <ItemGroup>
	<ProjectReference Include="..\..\10.Common\Renji.JHR.Common\Renji.JHR.Common.csproj" />
	<ProjectReference Include="..\..\20.EntityFrameworkCore\Renji.JHR.Dal\Renji.JHR.Dal.csproj" />
	<ProjectReference Include="..\..\20.EntityFrameworkCore\Renji.JHR.Entities\Renji.JHR.Entities.csproj" />
  </ItemGroup>
</Project>
