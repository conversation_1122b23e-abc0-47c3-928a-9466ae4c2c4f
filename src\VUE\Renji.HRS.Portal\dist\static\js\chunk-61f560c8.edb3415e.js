(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-61f560c8"],{"9c2b":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("layout4",{scopedSlots:t._u([{key:"main",fn:function(){return[n("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.headModel}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"月份"}},[n("el-date-picker",{staticClass:"input_class",attrs:{type:"month",placeholder:"请选择月份","value-format":"yyyy-MM",size:"small",clearable:!1},model:{value:t.headModel.recordMonth,callback:function(e){t.$set(t.headModel,"recordMonth",e)},expression:"headModel.recordMonth"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"部门"}},[n("c-select-tree",{attrs:{options:t.treeData,"tree-props":t.treeProps},model:{value:t.headModel.dept,callback:function(e){t.$set(t.headModel,"dept",e)},expression:"headModel.dept"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"工号"}},[n("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:t.headModel.empCode,callback:function(e){t.$set(t.headModel,"empCode",e)},expression:"headModel.empCode"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"姓名"}},[n("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:t.headModel.empName,callback:function(e){t.$set(t.headModel,"empName",e)},expression:"headModel.empName"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:""}},[n("el-button",{attrs:{type:"primary"},on:{click:t.search}},[t._v("查询")])],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"显示有假期项"}},[n("el-checkbox",{model:{value:t.headModel.onlyDayOffValue,callback:function(e){t.$set(t.headModel,"onlyDayOffValue",e)},expression:"headModel.onlyDayOffValue"}})],1),n("el-form-item",{attrs:{label:"显示有卫贴项"}},[n("el-checkbox",{model:{value:t.headModel.onlyH1Value,callback:function(e){t.$set(t.headModel,"onlyH1Value",e)},expression:"headModel.onlyH1Value"}})],1)],1)],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",attrs:{data:t.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"}},[n("el-table-column",{attrs:{prop:"date",label:"唯一码",align:"center",width:"120",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.empUid))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"工号",align:"center",width:"120",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.empCode))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"姓名",align:"center",width:"120",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.empName))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"部门",align:"center",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.empDept))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"应发公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(0===a.generalHoliday?"":a.generalHoliday))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"剩余公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.historyH12=Math.abs(a.historyH12)}},model:{value:a.historyH12,callback:function(e){t.$set(a,"historyH12",t._n(e))},expression:"row.historyH12"}}):n("span",[t._v(t._s(0===a.historyH12?"":a.historyH12))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h12=Math.abs(a.h12)}},model:{value:a.h12,callback:function(e){t.$set(a,"h12",t._n(e))},expression:"row.h12"}}):n("span",[t._v(t._s(a.h12))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"上月卫贴标准",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.preMonthH1))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"本月卫贴标准",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h1=Math.abs(a.h1)}},model:{value:a.h1,callback:function(e){t.$set(a,"h1",t._n(e))},expression:"row.h1"}}):n("span",[t._v(t._s(a.h1))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"病假",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h2=Math.abs(a.h2)}},model:{value:a.h2,callback:function(e){t.$set(a,"h2",t._n(e))},expression:"row.h2"}}):n("span",[t._v(t._s(a.h2))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"事假",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h3=Math.abs(a.h3)}},model:{value:a.h3,callback:function(e){t.$set(a,"h3",t._n(e))},expression:"row.h3"}}):n("span",[t._v(t._s(a.h3))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"产假",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h4=Math.abs(a.h4)}},model:{value:a.h4,callback:function(e){t.$set(a,"h4",t._n(e))},expression:"row.h4"}}):n("span",[t._v(t._s(a.h4))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"哺乳假",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h5=Math.abs(a.h5)}},model:{value:a.h5,callback:function(e){t.$set(a,"h5",t._n(e))},expression:"row.h5"}}):n("span",[t._v(t._s(a.h5))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"探亲假",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h6=Math.abs(a.h6)}},model:{value:a.h6,callback:function(e){t.$set(a,"h6",t._n(e))},expression:"row.h6"}}):n("span",[t._v(t._s(a.h6))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"计生假",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h7=Math.abs(a.h7)}},model:{value:a.h7,callback:function(e){t.$set(a,"h7",t._n(e))},expression:"row.h7"}}):n("span",[t._v(t._s(a.h7))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"婚丧假",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h8=Math.abs(a.h8)}},model:{value:a.h8,callback:function(e){t.$set(a,"h8",t._n(e))},expression:"row.h8"}}):n("span",[t._v(t._s(a.h8))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"脱产读研",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h9=Math.abs(a.h9)}},model:{value:a.h9,callback:function(e){t.$set(a,"h9",t._n(e))},expression:"row.h9"}}):n("span",[t._v(t._s(a.h9))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"因公出国",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h10=Math.abs(a.h10)}},model:{value:a.h10,callback:function(e){t.$set(a,"h10",t._n(e))},expression:"row.h10"}}):n("span",[t._v(t._s(a.h10))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"因私出国",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h11=Math.abs(a.h11)}},model:{value:a.h11,callback:function(e){t.$set(a,"h11",t._n(e))},expression:"row.h11"}}):n("span",[t._v(t._s(a.h11))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"当前考勤员状态",align:"center",width:"110"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.statue))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"当前防保科状态",align:"center",width:"110"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.prophylacticStatusDesc))])]}}])}),n("el-table-column",{attrs:{align:"center",label:"操作",width:"170"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.edit?n("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(e){return t.confirmEdit(a)}}},[t._v(" 更新 ")]):t._e(),a.edit?n("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.cancelEdit(a)}}},[t._v(" 取消 ")]):t._e(),a.edit?t._e():n("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(e){return t.Edit(a)}}},[t._v(" 编辑 ")])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[20,50,100],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.paginationChanged}})]},proxy:!0}])})],1)},r=[],o=(n("99af"),n("d81d"),n("d3b7"),n("25f0"),n("4d90"),n("d368")),i=n("cbd2"),l={components:{},data:function(){return{headModel:{recordMonth:this.getNowTime()},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeLoading:!1,treeExpandedKeys:[],currentNode:null,total:0,listQuery:{pageIndex:1,pageSize:10},listLoading:!1,tableData:[],editOringinData:{}}},created:function(){this.loadTree()},methods:{getNowTime:function(){var t=new Date,e=t.getFullYear(),n=t.getMonth();n=n.toString().padStart(2,"0"),"00"===n&&(e-=1,n="12");var a="".concat(e,"-").concat(n);return a},loadTree:function(){var t=this;o["a"].queryDeptByUser({}).then((function(e){t.treeData=e.data,t.treeExpandedKeys.push(e.data[0].id)})).catch((function(t){console.log(t)})),this.resertCurrentNode()},resertCurrentNode:function(){this.currentNode=null},treeNodeClick:function(t){},Edit:function(t){t.edit=!t.edit},cancelEdit:function(t){t.edit=!1,t.h1=t.originalH1,t.h2=t.originalH2,t.h3=t.originalH3,t.h4=t.originalH4,t.h5=t.originalH5,t.h6=t.originalH6,t.h7=t.originalH7,t.h8=t.originalH8,t.h9=t.originalH9,t.h10=t.originalH10,t.h11=t.originalH11,t.h12=t.originalH12,t.historyH12=t.originalHistoryH12},confirmEdit:function(t){var e=this,n=this;t.edit=!1,t.recordMonth=this.headModel.recordMonth,i["a"].updateAttDayOffRecordDetail(t).then((function(a){if(a.succeed){var r=a.data;t.id=r.id,t.recordId=r.recordId,n.$set(t,"statue",r.statue),n.$set(t,"prophylacticStatusDesc",r.prophylacticStatusDesc),t.updator=r.updator,t.originalH1=r.h1,t.originalH2=r.h2,t.originalH3=r.h3,t.originalH4=r.h4,t.originalH5=r.h5,t.originalH6=r.h6,t.originalH7=r.h7,t.originalH8=r.h8,t.originalH9=r.h9,t.originalH10=r.h10,t.originalH11=r.h11,t.originalH12=r.h12,t.originalHistoryH12=r.historyH12}else e.$notice.resultTip(a)})).catch((function(t){e.getSearchResult(),console.log(t)}))},search:function(){this.listQuery.pageIndex=1,this.getSearchResult()},getSearchResult:function(){var t=this,e={RecordMonth:this.headModel.recordMonth,DeptId:this.headModel.dept,EmpCode:this.headModel.empCode,EmpName:this.headModel.empName,OnlyDayOffValue:this.headModel.onlyDayOffValue,OnlyH1Value:this.headModel.onlyH1Value,PageIndex:this.listQuery.pageIndex,PageSize:this.listQuery.pageSize};i["a"].searchAttDayOffRecordDetail_Update(e).then((function(e){t.listLoading=!1,e.succeed?(t.total=e.data.recordCount,t.tableData=e.data.datas.map((function(e){return t.$set(e,"edit",!1),e.originalH1=e.h1,e.originalH2=e.h2,e.originalH3=e.h3,e.originalH4=e.h4,e.originalH5=e.h5,e.originalH6=e.h6,e.originalH7=e.h7,e.originalH8=e.h8,e.originalH9=e.h9,e.originalH10=e.h10,e.originalH11=e.h11,e.originalH12=e.h12,e.originalHistoryH12=e.historyH12,e}))):t.$notice.resultTip(e)})).catch((function(e){console.log(e),t.listLoading=!1}))},paginationChanged:function(){this.getSearchResult()}}},c=l,u=n("2877"),s=Object(u["a"])(c,a,r,!1,null,null,null);e["default"]=s.exports},cbd2:function(t,e,n){"use strict";var a=n("cfe3"),r="AttendanceManage",o=new a["a"](r);e["a"]={getAttMonthShiftRecord:function(t){return o.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return o.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return o.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return o.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return o.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return o.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return o.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return o.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return o.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return o.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return o.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return o.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return o.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return o.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return o.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return o.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return o.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return o.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return o.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return o.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return o.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return o.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return o.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return o.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return o.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return o.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return o.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return o.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return o.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return o.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return o.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return o.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return o.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return o.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return o.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return o.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return o.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return o.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return o.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return o.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return o.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return o.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return o.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return o.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return o.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return o.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return o.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return o.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return o.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return o.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return o.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return o.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return o.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return o.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return o.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return o.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return o.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return o.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return o.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return o.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return o.get("GetSameDeptEmployeeWithHealthAllowance",t)}}}}]);